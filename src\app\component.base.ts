import { Component, Inject, Injectable, Injector, OnInit } from "@angular/core";
import { ActivatedRoute, NavigationEnd, Router, RouterModule } from "@angular/router";
import DataPage from "./service/data.page";
import { TranslateService } from "./service/comon/translate.service";
import { UtilService } from "./service/comon/util.service";
import { SessionService } from "./service/session/SessionService";
import { MessageCommonService } from "./service/comon/message-common.service";
import { DebounceInputService } from "./service/comon/debounce.input.service";
import { RouterService } from "./service/comon/router.service";
import { ObservableService } from "./service/comon/observable.service";
import { CONSTANTS } from "./service/comon/constants";
import {DataPageWithUserType} from "./service/DataPageWithUserType";

export class ComponentBase{
    public tranService: TranslateService;
    public utilService: UtilService;
    protected messageCommonService: MessageCommonService;
    protected sessionService: SessionService;
    protected debounceService: DebounceInputService;
    protected router: Router;
    protected route: ActivatedRoute;
    protected routerService: RouterService;
    protected observableService: ObservableService;

    pathNotCheckPolicy: Array<string> = ["/policies","/error", "/access", "/login", "/reset-password", "/notfound", "/landing"];
    pathNotCheckLogin: Array<string> = ["/error", "/access", "/login", "/reset-password", "/notfound", "/landing"];

    constructor(injector: Injector) {
        this.router = injector.get(Router);
        this.route = injector.get(ActivatedRoute);
        this.tranService = injector.get(TranslateService);
        this.utilService = injector.get(UtilService);
        this.messageCommonService = injector.get(MessageCommonService);
        this.sessionService = injector.get(SessionService);
        this.debounceService = injector.get(DebounceInputService);
        this.routerService = injector.get(RouterService);
        this.observableService = injector.get(ObservableService);
        let me = this;
        this.router.events.subscribe((event:NavigationEnd)=>{
            // console.log(event.url, me.router.url, event.url == me.router.url)
            if(event.url == me.router.url){
                if(!this.pathNotCheckLogin.includes(event.urlAfterRedirects.split("?")[0])){
                    if(!localStorage.getItem("token")){
                        window.location.hash = '/login';
                    }
                }
                me.executeChangeRouting(event);
                if(!me.pathNotCheckPolicy.includes(event.url) && !me.pathNotCheckPolicy.includes(event.url.split(";")[0]) && !me.pathNotCheckPolicy.includes(event.url.split("?")[0])){
                    if(me.observableService.next){
                        me.observableService.next(CONSTANTS.OBSERVABLE.KEY_LOAD_CONFIRM_POLICY_HISTORY, {forceClose: false});
                    }
                }else{
                    if(me.observableService.next){
                        me.observableService.next(CONSTANTS.OBSERVABLE.KEY_LOAD_CONFIRM_POLICY_HISTORY, {forceClose: true});
                    }
                }
            }
        })
    }

    private executeChangeRouting(event:NavigationEnd){
        let dataPage: DataPage = this.routerService.getDataPage();
        if(dataPage == null || dataPage == undefined) return;
        let title: string = dataPage.pageTitle;
        if((title || "").length > 0){
            document.title = this.tranService.translate(title);
        }else{
            document.title = "CMP";
        }
        let result = this.checkAuthen(dataPage.authorities);

        if (result && dataPage instanceof DataPageWithUserType) {
            result = this.checkType(dataPage.userTypes)
        }

        if(!result){
            window.location.hash = "/access";
        }
    }

    public checkAuthen(authens: Array<string>, isReportDynamic: boolean = false):boolean{
        if((authens || []).length == 0) return true;
        if(this.sessionService.userInfo.authorities == undefined){
            window.location.hash = '/login';
        }
        try {
            let userAuthens = this.sessionService.userInfo.authorities;
            if((userAuthens || []).length == 0){
                return false;
            }
            let hasViewContentReportDynamic = false;
            if((authens || []).length > 0){
                let flag = false;
                for(let i = 0; i < authens.length;i++){
                    if(userAuthens.includes(authens[i])){
                        flag = true;
                        break;
                    }
                }
                if(!flag){
                    if(isReportDynamic){
                        for(let i = 0; i < userAuthens.length;i++){
                            if(userAuthens[i].startsWith("getReport")){
                                return true;
                            }
                        }
                    }else if(authens[0] == "getReport"){
                        let url = window.location.hash.split("?")[0];
                        let idReport = url.substring(url.lastIndexOf("/")+1);
                        console.log(idReport)
                        for(let i = 0; i < userAuthens.length;i++){
                            if(userAuthens[i] == `getReport_${idReport}`){
                                return true;
                            }
                        }
                    }
                    return false;
                }
            }
            return true;
        } catch (error) {
            window.location.hash = '/login';
            return false;
        }
    }

    public checkType(userTypes: Array<number>) {
        if(this.sessionService.userInfo.type == undefined){
            window.location.hash = '/login';
        }
        try {
            let type = this.sessionService.userInfo.type;
            return userTypes.includes(type) ? true : false
        }catch (error) {
            window.location.hash = '/login';
            return false;
        }
    }
}
