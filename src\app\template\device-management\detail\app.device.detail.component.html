<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{ tranService.translate("global.menu.listdevice") }}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
</div>
<p-card styleClass="mt-3">
    <form [formGroup]="formDetailDevice">
        <div class="grid mx-4 my-3">
            <!--            imei-->
            <div class="col-3 ">
                <span class="p-float-label">
                    <input class="w-full"
                           pInputText id="imei"
                           [(ngModel)]="deviceInfo.imei"
                           formControlName="imei"
                           pattern="^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]{2,255}$" readonly
                    >
                    <label htmlFor="imei">{{ tranService.translate("device.label.imei") }}</label>
                </span>
            </div>
            <div class="col-3">
                <span class="p-float-label">
                    <input class="w-full"
                           pInputText id="deviceType"
                           [(ngModel)]="deviceInfo.deviceType"
                           formControlName="deviceType"
                           pattern="^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]{2,255}$" [readonly]="true"
                           autofocus
                    >
            <label htmlFor="deviceType">{{ tranService.translate("device.label.deviceType") }}</label>
            </span>
            </div>
            <div class="col-3">
                <span class="p-float-label">
                    <input class="w-full"
                           pInputText id="msisdn"
                           [(ngModel)]="deviceInfo.msisdn"
                           formControlName="msisdn"
                           readonly
                    >
            <label htmlFor="msisdn">{{ tranService.translate("device.label.msisdn") }}</label>
            </span>
            </div>
            <div class="col-3">
                <span class="p-float-label">
                    <input class="w-full"
                           pInputText id="country"
                           [(ngModel)]="deviceInfo.country"
                           formControlName="country"
                           pattern="^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]{2,255}$" [readonly]="true"
                           [disabled]="true">
            <label htmlFor="country">{{ tranService.translate("device.label.country") }}</label>
            </span>
            </div>
            <div class="col-3 ">
                <span class="p-float-label" disabled="true">
                    <p-calendar styleClass="w-full"
                                id="expiredDate"
                                [(ngModel)]="deviceInfo.expiredDate"
                                formControlName="expiredDate"
                                [disabled]="true"
                                disabledDays="1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31"
                                [readonlyInput]="true"
                                [minDate]="minDateTo"
                                [maxDate]="maxDateTo"
                    ></p-calendar>
            <label htmlFor="expiredDate">{{ tranService.translate("device.label.expireDate") }}</label>
            </span>
            </div>


            <!--            vị trí-->
            <div class="col-3">
                <span class="p-float-label">
                    <input class="w-full"
                           pInputText id="location"
                           [(ngModel)]="deviceInfo.location"
                           formControlName="location"
                           pattern="^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]{2,255}$" readonly>

            <label htmlFor="location">{{ tranService.translate("device.label.location") }}</label>
            </span>
            </div>

            <div class="col-3">
                <label class="flex align-items-center">
                    <p-checkbox [disabled]="true" [readonly]="true" formControlName="iotLink" id="iotLink"
                                inputId="iotLink" [binary]="true" [ngStyle]="{'margin': '6px'}"></p-checkbox>
                    <label for="iotLink">{{ tranService.translate("device.label.iotLink") }}</label>
                </label>
            </div>
        </div>
        <div class="col-offset-3 col-6">
            <iframe [src]="safeUrl" class="w-full" style="border:0;" allowfullscreen="" loading="lazy"
                    referrerpolicy="no-referrer-when-downgrade"></iframe>
        </div>
        <!--        <div class="flex align-items-center justify-content-center">-->
        <!--            <iframe [src]="safeUrl" class="w-full" style="border: 0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>-->
        <!--        </div>-->

        <div class="flex flex-row justify-content-center align-items-center mt-3">
            <p-button styleClass="p-button-info "
                      (click)="goUpdate()">{{ tranService.translate("global.button.update") }}
            </p-button>
        </div>
    </form>
</p-card>

