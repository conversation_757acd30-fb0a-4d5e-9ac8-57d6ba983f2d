<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{tranService.translate("global.menu.listplan")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
    <div class="col-5 flex flex-row justify-content-end align-items-center">
        <p-button styleClass="mr-2 p-button-info" *ngIf="checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN.UPDATE])" [label]="tranService.translate('global.button.edit')" icon="pi pi-pencil" (click)="onEdit()" ></p-button>
        <p-button styleClass="mr-2 p-button-secondary" *ngIf="ratingPlanInfo.status != planStatuses.ACTIVATED && checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN.DELETE])" (click)="deletePlan()" [label]="tranService.translate('global.button.delete')" icon="pi pi-trash" ></p-button>
        <p-button styleClass="mr-2 p-button-secondary" *ngIf="ratingPlanInfo.status == planStatuses.CREATE_NEW || ratingPlanInfo.status == planStatuses.DEACTIVATED && checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN.ACTIVE])" [label]="tranService.translate('global.button.active')" icon=""  (click)="active()" ></p-button>
        <p-button styleClass="mr-2 p-button-secondary" *ngIf="ratingPlanInfo.status == planStatuses.PENDING && checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN.APPROVE])" [label]="tranService.translate('global.button.approve')" icon=""  (click)="approve()" ></p-button>
        <p-button styleClass="mr-2 p-button-secondary" *ngIf="ratingPlanInfo.status == planStatuses.ACTIVATED && checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN.SUPPEND])" [label]="tranService.translate('global.button.suspend')" icon=""  (click)="suspend()" ></p-button>
    </div>
</div>

<div class="grid mt-1 h-auto" style="width: calc(100% + 16px);">
    <div class="col ratingPlan-detail pr-0 " >
        <p-card  styleClass="h-full" [style]="{'width': 'calc(100% + 16px)'}">
            <div class="col ratingPlan-detail pr-0 flex" style="border:1px solid black; margin-bottom: 20px">
                <div class="flex-1">
                    <div class="grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.planCode")}}</span>
                        <span class="inline-block col-fixed">{{ratingPlanInfo.code}}</span>
                    </div>
                    <div class=" grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.planName")}}</span>
                        <span class="inline-block col-fixed">{{ratingPlanInfo.name}}</span>
                    </div>
                    <div class=" grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.status")}}</span>
                        <div  class="text-white w-auto col">
                            <span [class]="getClassStatus(ratingPlanInfo.status)">{{getNameStatus(ratingPlanInfo.status)}}</span>
                        </div>
                    </div>
                    <div class=" grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.dispatchCode")}}</span>
                        <span class="inline-block col-fixed">{{ratingPlanInfo.dispatchCode}}</span>
                    </div>
                    <div class=" grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.customerType")}}</span>
                        <span class="inline-block col-fixed">{{getNameCustomerType(ratingPlanInfo.customerType)}}</span>
                    </div>
                    <div class=" grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.description")}}</span>
                        <span class="inline-block col-fixed">{{ratingPlanInfo.description}}</span>
                    </div>
<!--                    <div class="mt-1 grid">-->
<!--                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.motachuaco")}}</span>-->
<!--                    </div>-->
                </div>
                <div class="flex-1">
                    <div class="grid">
                        <span style="min-width: 200px;max-width: 200px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.subscriptionFee")}}</span>
                        <span class="inline-block col-fixed">{{ratingPlanInfo.subscriptionFee}}&nbsp; &nbsp; &nbsp; &nbsp; {{tranService.translate("ratingPlan.text.textDong")}}&nbsp;{{tranService.translate("ratingPlan.text.vat")}}</span>
                    </div>
                    <div class="grid">
                        <span class="inline-block col-fixed">
                            <span>
                                <p-radioButton [disabled]="true" [value]="subscriptionTypes[0].ip" [(ngModel)]="ratingPlanInfo.subscriptionType" inputId="typeIp1"></p-radioButton>
                                &nbsp;
                                <span>{{tranService.translate("ratingPlan.subscriptionType.post")}}</span>
                            </span>
                        </span>
                        <span class="inline-block col-fixed" style="padding-left: 108px">
                            <span>
                                <p-radioButton [disabled]="true" [value]="subscriptionTypes[1].ip" [(ngModel)]="ratingPlanInfo.subscriptionType" inputId="typeIp2"></p-radioButton>
                                &nbsp;
                                <span>{{tranService.translate("ratingPlan.subscriptionType.pre")}}</span>
                            </span>
                        </span>
                    </div>
                    <div class="grid">
                        <span style="min-width: 200px;max-width: 200px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.ratingScope")}}</span>
                        <span class="inline-block col-fixed">{{getRatingScope(ratingPlanInfo.ratingScope)}}</span>
                    </div>
                    <div class="grid" *ngIf="ratingPlanInfo.ratingScope == planScopes.CUSTOMER">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.province")}}</span>
                        <span class="inline-block col-fixed">{{myProvices}}</span>
                    </div>

                    <div class="grid">
                        <span style="min-width: 200px;max-width: 200px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.cycle")}}</span>
                        <span class="inline-block col-fixed">{{getCycleTimeUnit(ratingPlanInfo.cycleTimeUnit)}}</span>
                    </div>
                    <div class="grid">
                        <span style="min-width: 200px;max-width: 200px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.cycleInterval")}}</span>
                        <span class="inline-block col-fixed">{{ratingPlanInfo.cycleInterval}}</span>
                    </div>
                    <div class="grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.reload")}}</span>
                        <div style="padding-top: 10px; padding-left: 13px">
                            <p-inputSwitch [(ngModel)]="checkedReload" [disabled]="true"></p-inputSwitch>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mt-1 grid">
                <span style="font-size: 20px;margin-left: 10px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.flat")}}</span>
            </div>
            <div class="col ratingPlan-detail pr-0 flex"  style="border:1px solid black; margin-bottom: 20px" id = "name">
                <div class="flex-1">
                    <div class="grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.limitDataUsage")}}</span>
                        <span class="inline-block col-fixed">{{ratingPlanInfo.limitDataUsage}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.limitSmsOutside")}}</span>
                        <span class="inline-block col-fixed">{{ratingPlanInfo.limitSmsOutside}}</span>
                    </div>
                </div>
                <div class="flex-1">
                    <div class="grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.limitSmsInside")}}</span>
                        <span class="inline-block col-fixed">{{ratingPlanInfo.limitSmsInside}}</span>
                    </div>
                </div>
            </div>

            <div class="mt-1 grid">
                <span style="font-size: 20px;margin-left: 10px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.flexible")}}</span>
                <div style="padding-top: 18px">
                    <p-inputSwitch [(ngModel)]="checkedFlexible" [disabled]="true"></p-inputSwitch>
                </div>
            </div>
            <div class="col ratingPlan-detail pr-0 flex"  style="border:1px solid black;" id = "flexible">
                <div class="flex-1">
                    <div class="grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.feePerDataUnit")}}</span>
                        <span class="inline-block col-fixed">{{ratingPlanInfo.feePerDataUnit}}</span>
                    </div>
                    <div class=" grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.squeezedSpeed")}}</span>
                        <span class="inline-block col-fixed">{{ratingPlanInfo.downSpeed}}</span>
                    </div>
                    <div class=" grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.feeSmsInside")}}</span>
                        <span class="inline-block col-fixed">{{ratingPlanInfo.feeSmsInside}}</span>
                    </div>
                    <div class=" grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.feeSmsOutside")}}</span>
                        <span class="inline-block col-fixed">{{ratingPlanInfo.feeSmsOutside}}</span>
                    </div>
                    <div class=" grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.maximumFee")}}</span>
                        <span class="inline-block col-fixed">{{ratingPlanInfo.maximumFee}}</span>
                    </div>
                </div>
                <div class="flex-1">
                    <div class="grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">/&nbsp; &nbsp; &nbsp; {{ratingPlanInfo.dataRoundUnit}} &nbsp; &nbsp; &nbsp; &nbsp;   KB</span>
                    </div>
                    <div class="grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">/&nbsp; &nbsp; &nbsp; {{ratingPlanInfo.squeezedSpeed}}</span>
                    </div>
                </div>
            </div>
        </p-card>
    </div>
</div>


