import {Component, Inject, OnInit, AfterContentChecked, Injector} from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { MenuItem, TreeNode } from "primeng/api";
import { MessageCommonService } from 'src/app/service/comon/message-common.service';
import { TranslateService } from "src/app/service/comon/translate.service";
import { RolesService} from "src/app/service/account/RolesService"
import { ColumnInfo, OptionTable } from 'src/app/template/common-module/table/table.component';
import { UtilService } from 'src/app/service/comon/util.service';
import { Router } from '@angular/router';
import { CONSTANTS } from 'src/app/service/comon/constants';
import {ComponentBase} from "../../../../component.base";

@Component({
  selector: 'app-app.roles.list',
  templateUrl: './app.roles.list.component.html',
})
export class AppRolesListComponent extends ComponentBase implements OnInit, AfterContentChecked{
      home: MenuItem
      items: MenuItem[];
      formSearchRoles: any;
      pageNumber: number;
      pageSize: number;
      sort: string;
      selectItems: Array<any>;
      columns: Array<ColumnInfo>;
      optionTable: OptionTable;
      roleType: Array<any>;
    isShowDialogChangeManageLevel: boolean = false;
      accountId: number;


      dataSet: {
        content: Array<any>,
        total: number
      };
      dataSetForDetail: {
        content: TreeNode[],
        total: number
      };
      searchInfo: {
          name: string|null,
          type: number|null,

          status:string|null,
      };
    userType: number;
    userTypes: Array<any>;
    isShowModalDetail: boolean = false;
    roleInfo: {
        name: string| null,
        type: number|null,
        status: number|null,
        description: string|null
        roles: Array<any>,
        permissionIds: Array<any>,
    };
    roleId: number;
    // tranService: TranslateService;
      constructor(
            @Inject(RolesService) private rolesService: RolesService,
            private formBuilder: FormBuilder,
            private injector: Injector
      ) {
          super(injector);
      }
      //Danh sách nhóm quyền của tài khoản đang xem
      roles: Array<any> = [];
      ngOnInit(): void {
        let me = this;
          this.userType = this.sessionService.userInfo.type;
          //Lấy nhóm quyền của tk
          this.roles = this.sessionService.userInfo.roles;
          this.items = [{ label: this.tranService.translate("global.menu.accountmgmt") }, { label: this.tranService.translate("global.menu.listroles") },];
        this.home = { icon: 'pi pi-home', routerLink: '/' };
        this.searchInfo = {
          name: null,
          type: null,
          status: null,
        }
        this.formSearchRoles = this.formBuilder.group(this.searchInfo);
        this.pageNumber = 0;
        this.pageSize= 10;
        this.sort = "name,asc"
        this.dataSet ={
          content: [],
          total: 0
        };
        this.dataSetForDetail = {
            content: [],
            total: 0
        };
        this.dataSetForDetail = {
              content: [{
                  label: this.tranService.translate("global.text.all"),
                  key: "all",
                  children: null,
                  data: null
              }],
              total: 0
        }
          let fullTypeRole = [
              {name: this.tranService.translate("roles.type.admin"),value:CONSTANTS.ROLE_TYPE.ADMIN, accepts:[CONSTANTS.ROLE_TYPE.ADMIN]},
              {name: this.tranService.translate("roles.type.all"),value:CONSTANTS.ROLE_TYPE.ALL, accepts:[]},
              {name: this.tranService.translate("roles.type.customer"),value:CONSTANTS.ROLE_TYPE.CUSTOMER,accepts:[CONSTANTS.ROLE_TYPE.ADMIN, CONSTANTS.ROLE_TYPE.CUSTOMER, CONSTANTS.ROLE_TYPE.PROVINCE, CONSTANTS.ROLE_TYPE.TELLER]},
              {name: this.tranService.translate("roles.type.province"),value:CONSTANTS.ROLE_TYPE.PROVINCE,accepts:[CONSTANTS.ROLE_TYPE.ADMIN]},
              {name: this.tranService.translate("roles.type.teller"),value:CONSTANTS.ROLE_TYPE.TELLER,accepts:[CONSTANTS.ROLE_TYPE.ADMIN, CONSTANTS.ROLE_TYPE.PROVINCE]},
              {name: this.tranService.translate("roles.type.agency"),value:CONSTANTS.ROLE_TYPE.AGENCY,accepts:[CONSTANTS.ROLE_TYPE.ADMIN, CONSTANTS.ROLE_TYPE.PROVINCE, CONSTANTS.ROLE_TYPE.TELLER, CONSTANTS.ROLE_TYPE.AGENCY]},
          ]
          this.roleType = fullTypeRole.filter(el => el.accepts.includes(this.userType) || el.accepts.length == 0);
        this.selectItems = [];
        this.roleInfo = {
          name: null,
          type: null,
          status: null,
          roles: null,
          description: null,
          permissionIds: null,
        };
        this.optionTable = {
          hasClearSelected: true,
          hasShowChoose: false,
          hasShowIndex: true,
          hasShowToggleColumn: false,
          action: [
              {
                  icon: "pi pi-pencil",
                  tooltip: this.tranService.translate("global.button.edit"),
                  func: function(id, item){
                      me.router.navigate([`/roles/edit/${id}`]);
                  },
                  funcAppear: function(id, item) {
                    //   console.log(me.userType);
                      //tài khoản khách hàng
                      if (me.userType == CONSTANTS.USER_TYPE.CUSTOMER) {
                          //Nếu nhóm quyền này đang được gán với tài khoản khách hàng này thì không cho tác động
                          if ((me.roles || []).includes(item.name)) {
                              return false;
                          } else {
                              return true;
                          }
                      }
                      return (me.userType == CONSTANTS.USER_TYPE.ADMIN ||  item.type != CONSTANTS.ROLE_TYPE.ALL) && item.createdBy != 0;;
                  }
              },
              {
                  icon: "pi pi-lock",
                  tooltip: this.tranService.translate("global.button.changeStatus"),
                  func: function(id, item){
                      me.messageCommonService.confirm(
                          me.tranService.translate("global.message.titleConfirmChangeStatusRole"),
                          me.tranService.translate("global.message.confirmChangeStatusRole"),
                          {
                              ok:()=>{
                                  me.rolesService.changeStatusRole(id, (response)=>{
                                      me.messageCommonService.success(me.tranService.translate("global.message.changeStatusSuccess"));
                                      me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);

                                  })
                                //   me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);
                              },
                              cancel: ()=>{
                                  // me.messageCommonService.error(me.tranService.translate("global.message.changeStatusFail"));
                              }
                          }
                      )
                  },
                  funcAppear: function(id, item) {
                      //tài khoản khách hàng
                      if (me.userType == CONSTANTS.USER_TYPE.CUSTOMER) {
                          //Nếu nhóm quyền này đang được gán với tài khoản khách hàng này thì không cho tác động
                          if ((me.roles || []).includes(item.name)) {
                              return false;
                          } else {
                              return true;
                          }
                      }
                      return (item.status == CONSTANTS.ROlES_STATUS.ACTIVE && (me.userType == CONSTANTS.USER_TYPE.ADMIN ||  item.type != CONSTANTS.ROLE_TYPE.ALL)) && item.createdBy != 0;
                  }
              },
              {
                  icon: "pi pi-lock-open",
                  tooltip: this.tranService.translate("global.button.changeStatus"),
                  func: function(id, item){
                      me.messageCommonService.confirm(
                          me.tranService.translate("global.message.titleConfirmChangeStatusRole"),
                          me.tranService.translate("global.message.confirmChangeStatusRole"),
                          {
                              ok:()=>{
                                //   console.log(id)
                                  me.rolesService.changeStatusRole(id, (response)=>{
                                      me.messageCommonService.success(me.tranService.translate("global.message.changeStatusSuccess"));
                                      me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);
                                  })
                                  me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);
                              },
                              cancel: ()=>{
                                  // me.messageCommonService.error(me.tranService.translate("global.message.changeStatusFail"));
                              }
                          }
                      )
                  },
                  funcAppear: function(id, item) {
                      return (item.status == CONSTANTS.ROlES_STATUS.INACTIVE && (me.userType == CONSTANTS.USER_TYPE.ADMIN || item.type != CONSTANTS.ROLE_TYPE.ALL)) && item.createdBy != 0;
                  }
              },
              {
                  icon: "pi pi-trash",
                  tooltip: this.tranService.translate("global.button.delete"),
                  func: function(id, item){
                      me.messageCommonService.confirm(
                          me.tranService.translate("global.message.titleConfirmDeleteRoles"),
                          me.tranService.translate("global.message.confirmDeleteRoles"),
                          {
                              ok:()=>{
                                  me.rolesService.deleteRole(id, (response)=>{
                                      me.messageCommonService.success(me.tranService.translate("global.message.deleteSuccess"));
                                      me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);
                                  })
                              },
                              cancel: ()=>{
                                  // me.messageCommonService.error(me.tranService.translate("global.message.deleteFail"));
                              }
                          }
                      )
                  },
                  funcAppear: function(id, item) {
                      //tài khoản khách hàng
                      if (me.userType == CONSTANTS.USER_TYPE.CUSTOMER) {
                          //Nếu nhóm quyền này đang được gán với tài khoản khách hàng này thì không cho tác động
                          if ((me.roles || []).includes(item.name)) {
                              return false;
                          } else {
                              return true;
                          }
                      }
                      return (me.userType == CONSTANTS.USER_TYPE.ADMIN || item.type != CONSTANTS.ROLE_TYPE.ALL) && item.createdBy != 0;
                  }
              },
          ]
      },

        this.columns = [
          {
              name: this.tranService.translate("roles.label.rolename"),
              key: "name",
              size: "30%",
              align: "left",
              isShow: true,
              isSort: true,
              style:{
                  cursor: "pointer",
             color: "var(--mainColorText)"
              },
              funcClick(id, item) {
                  me.roleId = id;
                  me.getDetail();
                  me.isShowModalDetail = true;
              },
          },
          {
              name: this.tranService.translate("roles.label.status"),
              key: "status",
              size: "25%",
              align: "left",
              isShow: true,
              isSort: true,
              style: {
                  color: "white"
              },
              funcGetClassname: (value) => {
                  if(value == 1){
                      return ['p-2',"text-green-800", "bg-green-100","border-round","inline-block"];
                  }else if(value == CONSTANTS.ROlES_STATUS.INACTIVE){
                      return ['p-2', "text-red-700", "bg-red-100", "border-round","inline-block"];
                  }
                  return [];
              },
              funcConvertText: (value)=>{
                  if(value == 1){
                      return me.tranService.translate("roles.status.active");
                  }else if(value == CONSTANTS.ROlES_STATUS.INACTIVE){
                      return me.tranService.translate("roles.status.inactive");
                  }
                  return "";
              }
            },
            {
                name: this.tranService.translate("roles.label.usertype"),
                key: "type",
                size: "25%",
                align: "left",
                isShow: true,
                isSort: true,
                funcConvertText(value) {
                    if(value == CONSTANTS.ROLE_TYPE.ADMIN){
                        return me.tranService.translate("roles.type.admin");
                    }else if(value == CONSTANTS.ROLE_TYPE.CUSTOMER){
                        return me.tranService.translate("roles.type.customer");
                    }else if(value == CONSTANTS.ROLE_TYPE.PROVINCE){
                        return me.tranService.translate("roles.type.province");
                    }else if(value == CONSTANTS.ROLE_TYPE.TELLER){
                        return me.tranService.translate("roles.type.teller");
                    }else if(value == CONSTANTS.ROLE_TYPE.AGENCY){
                        return me.tranService.translate("roles.type.agency");
                    }else if(value == CONSTANTS.ROLE_TYPE.ALL){
                        return me.tranService.translate("roles.type.all");
                    }else {
                        return "";
                    }
                },
            },
        ]

        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);

        // throw new Error('Method not implemented.');
      }
      onSubmitSearch(){
        this.pageNumber = 0;
        this.search(0, this.pageSize, this.sort, this.searchInfo);
     }

    search(page, limit, sort, params){
        this.pageNumber = page;
        this.pageSize = limit;
        this.sort = sort;
        let me = this;
        let dataParams = {
            page,
            size: limit,
            sort,
        }

        Object.keys(this.searchInfo).forEach(key => {
            if(this.searchInfo[key] != null){
                dataParams[key] = this.searchInfo[key];
            }
        })

        me.messageCommonService.onload();
        this.rolesService.search(dataParams, (response)=>{
            me.dataSet = {
                content: response.content,
                total: response.totalElements
            }
        }, null, ()=>{
          me.messageCommonService.offload();
        })
    }

    getType(value){
        if(value == CONSTANTS.ROLE_TYPE.ADMIN){
            return this.tranService.translate("roles.type.admin");
        }else if(value == CONSTANTS.ROLE_TYPE.CUSTOMER){
            return this.tranService.translate("roles.type.customer");
        }else if(value == CONSTANTS.ROLE_TYPE.PROVINCE){
            return this.tranService.translate("roles.type.province");
        }else if(value == CONSTANTS.ROLE_TYPE.TELLER){
            return this.tranService.translate("roles.type.teller");
        }else if(value == CONSTANTS.ROLE_TYPE.AGENCY){
            return this.tranService.translate("roles.type.agency");
        }else if(value == CONSTANTS.ROLE_TYPE.ALL){
            return this.tranService.translate("roles.type.all");
        }
        return "";
    }

    getStatus(value){
        if(value == CONSTANTS.ROlES_STATUS.INACTIVE){
            return this.tranService.translate("roles.status.inactive");
        }else if(value == CONSTANTS.ROlES_STATUS.ACTIVE){
            return this.tranService.translate("roles.status.active");
        }
        return "";
    }

    getDetail(){
        let me = this;
        me.messageCommonService.onload();
        me.rolesService.getById(me.roleId, (response)=>{
            me.roleInfo.name = response.name
            me.roleInfo.type = response.type
            me.roleInfo.status = response.status
            me.roleInfo.permissionIds = response.permissionIds
            me.getTreeRoles();
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }

    getTreeRoles(){
        let me = this;
        me.rolesService.getTreeRoles((response)=>{
            response.forEach(el => {
                el.label = me.tranService.translate(`permission.${el.key}.${el.key}`)
                el.partialSelected = true;
                if(el.children){
                    el.children.forEach(item => {
                        item.label = me.tranService.translate(`permission.${el.key}.${item.key}`, {}, item.data?.description)
                    })
                }
            });
            me.dataSetForDetail = {
                content: [{
                    label: this.tranService.translate("global.text.all"),
                    key: "all",
                    children: response,
                    data: null,
                    expanded: true,
                    partialSelected: true
                }],
                total: 0
            }
            // let permissionIds = [1, 2, 3, 4, 5];
            me.roleInfo.roles = [];
            let totalOfTotal = 0;
            me.dataSetForDetail.content[0].children.forEach(el => {
                if(el.children != null){
                    let total = 0;
                    el.children.forEach(item => {
                        if(this.roleInfo.permissionIds.includes(item.data.id)){
                            me.roleInfo.roles.push(item);
                            total ++;
                        }
                    });
                    if(total != 0 && total == el.children.length){
                        me.roleInfo.roles.push(el);
                        el.partialSelected = false;
                        totalOfTotal ++;
                    }else if(total == 0){
                        el.partialSelected = false;
                    }
                }
            })
            if(totalOfTotal != 0 && totalOfTotal == me.dataSetForDetail.content[0].children.length){
                let element = me.dataSetForDetail.content[0];
                element.partialSelected = false;
                me.roleInfo.roles.push(element);
            }
        })

    }

    closeForm(){
        this.router.navigate(['/roles'])
    }

      ngAfterContentChecked(): void {
        // throw new Error('Method not implemented.');
      }

    protected readonly CONSTANTS = CONSTANTS;
}
