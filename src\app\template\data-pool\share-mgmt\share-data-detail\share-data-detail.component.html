
<div *ngIf="isPrivilage">
    <div class="mb-3 flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
        <div class="">
            <div class="text-xl font-bold mb-1">{{this.tranService.translate("datapool.label.shareInfo")}}</div>
            <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
        </div>
    <!--    <div>-->
    <!--        <p-button icon="pi pi-pencil" label="Sửa thông tin" (onClick)="visible = true"></p-button>-->
    <!--    </div>-->
    </div>
    <!-- <p-fieldset class="vnpt-field-set" [styleClass]="'mt-4'" [legend]="tranService.translate('global.text.filter')" [toggleable]="true">

    </p-fieldset> -->

    <p-card>
        <!-- <div class="font-bold text-lg mb-2">{{tranService.translate("datapool.label.shareInfo")}}</div> -->
        <div class="flex flex-row surface-200 p-4 border-round">
            <div class="flex-1">
                <div class="font-medium text-base">{{tranService.translate('device.label.msisdn')}}</div>
                <div class="font-semibold text-lg">{{phone}}</div>
            </div>
            <div class="flex-1">
                <div class="font-medium text-base">{{tranService.translate('datapool.label.fullName')}}</div>
                <div class="font-semibold text-lg">{{name}}</div>
            </div>
            <div class="flex-1">
                <div class="font-medium text-base">Email</div>
                <div class="font-semibold text-lg">{{email}}</div>
            </div>
        </div>
    </p-card>

    <search-filter-separate
            [searchList]="searchList"
            [filterList]="filterList"
            (searchDetail)="catchSearchDetail($event)">
    </search-filter-separate>

    <table-vnpt
        [tableId]="'tableDetailSharing'"
        [fieldId]="'walletCode'"
        [(selectItems)]="selectItems"
        [columns]="columns"
        [dataSet]="dataSet"
        [options]="optionTable"
        [loadData]="search.bind(this)"
        [pageNumber]="pageNumber"
        [pageSize]="pageSize"
        [sort]="sort"
        [labelTable]="this.tranService.translate('datapool.label.shareInfo')"
    ></table-vnpt>

    <!--<p-dialog header="Sửa thông tin" [modal]="true" [(visible)]="visible" [style]="{ width: '35rem' }" [draggable]="false">-->
    <!--    <form action="" [formGroup]="formUpdatePerson" (submit)="onSubmitUpdatePerson()">-->
    <!--        <span class="p-text-secondary block mb-5">Chỉnh sửa người được chia sẻ</span>-->
    <!--        <div class="flex align-items-center gap-3 mb-3">-->
    <!--            <label for="username" class="font-semibold w-6rem">Họ tên</label>-->
    <!--            <input pInputText formControlName="fullName" id="username" class="flex-auto" autocomplete="off" />-->
    <!--        </div>-->
    <!--        <div class="flex align-items-center gap-3 mb-5">-->
    <!--            <label for="phone" class="font-semibold w-6rem">SĐT <span class="text-red-500">*</span></label>-->
    <!--            <input disabled="true" pInputText formControlName="phone" id="phone" class="flex-auto" autocomplete="off" />-->
    <!--        </div>-->
    <!--        <div class="flex align-items-center gap-3 mb-5">-->
    <!--            <label for="email" class="font-semibold w-6rem">Email</label>-->
    <!--            <input pInputText formControlName="email" id="email" class="flex-auto" autocomplete="off" />-->
    <!--        </div>-->
    <!--        <div class="flex justify-content-end gap-2">-->
    <!--            <button type="button" pButton class="p-button-outlined" (click)="visible = false">Cancel</button>-->
    <!--            <p-button label="Save" type="submit" (click)="visible = false" />-->
    <!--        </div>-->
    <!--    </form>-->
    <!--</p-dialog>-->
</div>
