import {Component, Inject, Injector, <PERSON><PERSON><PERSON><PERSON>, ViewChild} from '@angular/core';
import {ComponentBase} from "../../../../component.base";
import {ShareManagementService} from "../../../../service/datapool/ShareManagementService";
import {TrafficWalletService} from "../../../../service/datapool/TrafficWalletService";
import {FormControl, FormGroup, Validators} from "@angular/forms";
import {PhoneInfo, ShareDetail, Wallet} from "../../data-pool.type-data";
import {MenuItem} from "primeng/api";
import {digitValidator} from 'src/app/template/common-module/validatorCustoms';
import {CONSTANTS, isVinaphoneNumber} from "../../../../service/comon/constants";
import {ComboLazyControl} from "../../../common-module/combobox-lazyload/combobox.lazyload";
import {ColumnInfo, OptionTable} from "../../../common-module/table/table.component";
import {convert84to0PhoneNumber} from "../../../common-module/utils/util";
import * as XLSX from 'xlsx-js-style';
import * as FileSaver from "file-saver";

@Component({
    selector: 'app-share-wallet',
    templateUrl: './share-wallet.component.html',
    styleUrls: ['./share-wallet.component.scss']
})
export class ShareWalletComponent extends ComponentBase implements OnDestroy {
    // @ViewChild('dropdown', {static: false}) dropdown: Dropdown;

    constructor(@Inject(ShareManagementService) private shareService: ShareManagementService,
                @Inject(TrafficWalletService) private walletService: TrafficWalletService,
                injector: Injector,
    ) {
        super(injector);
    }

    shareWalletDataForm = new FormGroup({
        walletValue: new FormControl(null, [Validators.required]),
        dateShare: new FormControl(),
        definedData: new FormControl(),
        description: new FormControl(null, [Validators.maxLength(255)]),
        otp: new FormControl(null, [Validators.required, digitValidator(6)])
    });

    items: MenuItem[];
    home: MenuItem;
    remainDataTotal: number;
    totalData: number;
    shareList: ShareDetail[];
    phoneList: PhoneInfo[];
    selectedWallet: Wallet;
    walletName: String;
    isSubmit: boolean = false;
    isError = false;
    countdown: number;
    phoneReceiptSelectControl = new ComboLazyControl();
    interval: any;
    isShowDefined: boolean = false;
    subCodeId: string;
    apiPhone: string;
    typeOfTraffic: string;
    remainInit: number;
    isShowDialogImportByFile: boolean = false;
    isShowErrorUpload: boolean = false;
    shareListFromFile: ShareDetail[];
    originRemain: number;
    isDefineValueError: boolean = false
    phoneReceiptSelect: any = "";

    optionTableError: OptionTable = {
        hasClearSelected: false,
        hasShowChoose: false,
        hasShowIndex: true,
        hasShowToggleColumn: false,
    };
    pageNumberError: number = 0;
    pageSizeError: number = 10;
    sortError: string = "";
    dataSetError: {
        content: Array<any>,
        total: number
    } = {
        content: [],
        total: 0
    };
    FulldataSetError : any
    searchInfoError: {
        value?: string,
    } = {
        value: ""
    };
    columnsError: Array<ColumnInfo> = [
        {
            name: this.tranService.translate("datapool.label.phone"),
            key: "phoneReceipt",
            size: "50%",
            align: "left",
            isShow: true,
            isSort: false,
        },
        {
            name: this.tranService.translate("datapool.label.description"),
            key: "description",
            size: "50%",
            align: "left",
            isShow: true,
            isSort: false
        }];

    isClickCreate: boolean = true;
    isValidPhone: boolean = true;
    isValidDataFixed: boolean = false;
    canShare = false;
    canView = false

    selectedTableData = [];
    dataForLabel: string = "Data";

    isNonUseOTP = false
    isAutoWallet = true


    ngOnInit() {
        this.items = [{label: this.tranService.translate(`global.menu.trafficManagement`)}, {
            label: this.tranService.translate("global.menu.walletList"),
            routerLink: ["/data-pool/walletMgmt/list"]
        }, {label: this.tranService.translate("datapool.label.shareData")}];
        this.home = {icon: 'pi pi-home', routerLink: '/'};
        this.phoneList = [];
        this.shareWalletDataForm.controls['walletValue'].setValue(1);
        this.subCodeId = this.route.snapshot.paramMap.get("id");
        this.getDetailShareWallet();
        // this.getListShareInfoCbb();
        this.shareList = [];
        this.observableService.subscribe(CONSTANTS.OBSERVABLE.UPDATE_SHARE_INFO, {
            next: this.loadData.bind(this)
        })
    }

    loadData(data): void {
        if (data) {
            if(data.length > 1000){
                this.messageCommonService.error(this.tranService.translate("error.invalid.file.form.maxrow"))
                return
            }
            this.shareList = data;
            let oldSelectData = this.selectedTableData
            this.selectedTableData = []
            //data.isAuto.toLowerCase()==="có" || data.isAuto.toLowerCase()=="co"
            data.forEach((item, index) => {
                if(item.isAuto && (item.isAuto.localeCompare("Có", 'vi', { sensitivity: 'base' }) === 0 || item.isAuto.localeCompare("Co", 'vi', { sensitivity: 'base' }) === 0)){

                    this.selectedTableData.push(item)
                }
            })
            this.selectedTableData = [
                ...this.selectedTableData,
                ...oldSelectData
            ]
        }
        this.caculateRemain()
        if(!this.isAutoWallet){
            this.selectedTableData = []
            this.messageCommonService.warning("Ví chưa đăng ký chia sẻ không cần OTP nên các thuê bao đã tích chọn tự động sẽ bị bỏ tích");
        }
    }

    submitForm() {
        let me = this;
        const convertShareInfo = this.shareList.map((e) => ({
            phone: String(e?.phoneReceipt),
            trafficType: "DATA",
            numOfShare: Number(e?.data),
            expireDate: '30 ngày',
            name: e?.name,
            email: e?.email,
            type: 0,
            isAuto: !!this.selectedTableData.find(item=> item.phoneReceipt=== e.phoneReceipt),
        }))
        console.log(convertShareInfo);
        const dataBody = {
            subCode: this.subCodeId, // chỗ này thay bằng subCode lấy từ màn list
            otp: this.shareWalletDataForm.get('otp').value,
            reason: this.shareWalletDataForm.get('description').value,
            sharingDay: null,
            shareInfos: convertShareInfo,
            transId: ''
        }

        me.messageCommonService.onload();
        this.shareService.shareTraffic(dataBody, (res) => {

            if (res.error_code === 'BSS-00000000') {
                let ok = 1
                let dataErrorTable = []
                res.data.forEach(element => {
                    if(element.status == 0 && element.error != ""){
                        // me.messageCommonService.error("Thuê bao "+element.sdt+" có lỗi ("+element.error+")");
                        dataErrorTable.push({
                            phoneReceipt: convert84to0PhoneNumber(element.sdt),
                            // description: element.error
                            description: "Lỗi do thuê bao không hợp lệ"
                        })
                        ok = 0
                    }
                });
                if(ok == 0){
                    this.isError = true
                    this.dataSetError = {
                        content: dataErrorTable,
                        total: dataErrorTable.length,
                    }
                    this.FulldataSetError = {...this.dataSetError}
                    me.messageCommonService.error(me.tranService.translate("datapool.message.shareNotifyFail", {success: this.shareList.length - this.dataSetError.content.length, total : this.shareList.length}), null, 10000);
                }else {
                    me.messageCommonService.success(me.tranService.translate("datapool.message.shareNotifySuccess", {success: this.shareList.length, total : this.shareList.length}), null, 10000);
                    this.router.navigate(['/data-pool/walletMgmt/list']);
                }
            }else if (res.error_code === 'Success') {
                me.messageCommonService.success(me.tranService.translate("datapool.message.shareNotifyBackground"),null, 15000);
                this.router.navigate(['/data-pool/walletMgmt/list']);
            } else {
                this.messageCommonService.error(res.message)
            }
        }, null, () => {
            me.messageCommonService.offload();
        })
    }

    getListShareInfoCbb(params, callback) {
        return this.shareService.getListShareInfoCbb(params, (response)=>{
            this.phoneList = response.content;
            callback(response)
        });
    }


    getDetailShareWallet() {
        this.messageCommonService.onload()
        this.walletService.getById({subCode: this.subCodeId}, (res) => {
            if(res.canShare){
              this.canShare = true
            }else{
              this.router.navigate(['/sims'])
            }
            this.walletName = res?.packageName;
            this.totalData = res?.purchasedTraffic;
            this.remainDataTotal = res?.totalRemainingTraffic;
            this.originRemain = res?.totalRemainingTraffic
            this.apiPhone = res?.phoneActive
            this.typeOfTraffic = res?.trafficType
            this.canView = true
            if(res.autoType != 2){
                this.isNonUseOTP = true
            }else{
                this.isAutoWallet = false
            }
            if((this.typeOfTraffic || '').toUpperCase().includes('Gói SMS'.toUpperCase())){
                this.dataForLabel = "SMS"
            }else if (this.typeOfTraffic == "Gói Data"){
                this.dataForLabel = "MB"
            }
        }, null, ()=>{
            this.messageCommonService.offload()
        })
    }

    onHideImport(){
      this.isShowDialogImportByFile = false
    }

    addPhone(data, onSelected?){
        let me = this;
        if(data === null || data === undefined || data === ""){
          return
        }
        if(this.shareList.length > 999){
            this.messageCommonService.error(this.tranService.translate("error.invalid.file.form.maxrow"))
            return
        }
        this.isClickCreate = false
        this.shareList = this.cleanArray(this.shareList)
        const value = this.phoneList.find(dta => dta.phoneReceipt === data)
        if(this.shareList.find(i => i?.phoneReceipt === data)){
          this.messageCommonService.error(this.tranService.translate("datapool.message.existed"))
        }else{
            this.addPhoneTable(value, data)
            /**
             * bỏ check số vina
             */
            // if (onSelected) {
            //     this.addPhoneTable(value, data)
            // } else {
            //     const phone = String(data)?.replace(/^0/, "84");
            //     this.messageCommonService.onload()
            //     this.walletService.checkParticipant({phoneNumber: phone},
            //         (response) => {
            //             if (response.error_code === "0" && (response.result === "02" || response.result === "11")) {
            //                 this.addPhoneTable(value, data)
            //             } else if (response.error_code === "0" && response.result === "0") {
            //                 if (isVinaphoneNumber(data)) {
            //                     this.addPhoneTable(value, data)
            //                 } else {
            //                     this.messageCommonService.error(this.tranService.translate("datapool.message.notValidPhone"))
            //                 }
            //             } else {
            //                 this.messageCommonService.error(this.tranService.translate("datapool.message.notValidPhone"))
            //             }
            //         },
            //         null, () => {
            //             this.messageCommonService.offload();
            //         })
            // }
        }
        this.phoneReceiptSelectControl.reload();
        this.phoneReceiptSelectControl.clearValue();
        this.phoneReceiptSelectControl.clearFilter();
        this.phoneReceiptSelectControl.reloadOption();
        this.phoneReceiptSelect = ""
      }

      addPhoneTable(value, data){
          let me = this;
          let dataValue;
          if(this.shareList.length > 999){
              this.messageCommonService.error(this.tranService.translate("error.invalid.file.form.maxrow"))
              return
          }
          if(this.typeOfTraffic == "Gói SMS"){
              dataValue = 10
          }else if(this.typeOfTraffic == "Gói Data"){
              dataValue = 100
          }else{
              dataValue = null
          }
        if(value){
          if(value?.percent){
            value.percent=null;
          }
            value.data = dataValue;
          this.shareList.push(value)
          console.log(this.shareList)
          setTimeout(function(){
            me.phoneReceiptSelect=""
          },100);
          this.isClickCreate = true
        }else{
          let pushData: ShareDetail = {
            phoneReceipt: data,
            name:"",
            email:"",
            data:dataValue
          }
          this.shareList.push(pushData)
        }
      }

    cleanArray(arr: any[]): any[] {
        return arr.filter(item => item);
    }

      checkValidAdd(){
        this.isClickCreate = true
        if(!this.phoneList.find(dta => dta.phoneReceipt === this.phoneReceiptSelect)){
          this.isClickCreate = false
        }else{
          this.isClickCreate = true
        }
        if(this.phoneReceiptSelect == ""|| this.phoneReceiptSelect == null || this.phoneReceiptSelect === undefined){
          this.isClickCreate = true
        }

        const regex = /^(\+84|84|0){1}(([0-9]{9})|([0-9]{10}))$/;
        const inputValue = this.phoneReceiptSelect;
        this.isValidPhone = regex.test(inputValue);
      }

      checkValidData(){
        if(this.shareList.length == 0){
          return true
        }
        let result
        this.shareList.forEach((item,index) => {
            if(!this.checkDataCondition(index)){
                result = true
                return
            }
          if(!item.name || item.name == null)
            item.name = ""
          if(!item. email || item.email == null)
            item.email = ""
          if(!item.data){
            result = true
            return
          } else if(this.typeOfTraffic == "Gói Data" && item.data <100){
            result= true; return
          } else if (this.typeOfTraffic == "Gói SMS" && item.data <10){
            result = true
            return
          } else if(item.name.length>=50 || item.email.length>= 100 || this.isMailInvalid(item.email)){
            result = true
            return
          } else{
            result = false
            return
          }
        });
        return result
      }


    isMailInvalid(email:string){
      if (!email){
        return false
      }
      const pattern:RegExp = /^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$/
      return !pattern.test(email);
    }

    changeData(event, i) {
        const shareValue = event.target.value
        this.shareList[i].data = shareValue
        this.shareList[i].percent = Math.round((shareValue / this.originRemain) * 100 * 100) / 100;
        this.caculateRemain();
      }

      onKeyDown(event, i){
        const key = event.key;
        const isNumber = /^[0-9]$/.test(key);

        if (key === "-"){
            return false;
        }

        let total=0;
        this.shareList.forEach((item,index) => {
          let value
          if(!item.data||item.data == null|| item.data == undefined || i == index){
            value = 0
          }else{
            value = item.data
          }
          total = Number(total) + Number(value);
        });
        const shareValue = event.target.value;
        const nextValue = parseInt(shareValue + key, 10);
        let totalUsedValue = nextValue+total
        if(this.originRemain - totalUsedValue < 0){
          this.messageCommonService.error(this.tranService.translate("datapool.message.exceededData"))
          return false;
        }
        return true;
      }

      changeDataName(event, i){
        const shareValue = event.target.value;
        this.shareList[i].name = shareValue;
      }

      changeDataMail(event, i){
        const shareValue = event.target.value;
        this.shareList[i].email = shareValue;
      }

    deleteItem(i){
        this.messageCommonService.confirm(this.tranService.translate("datapool.button.delete"),
        this.tranService.translate("datapool.message.confirmDelete"),{
          ok: ()=>{
            const a = this.shareList[i].data
            if(a){
              this.shareList[i].data = null
              this.shareList[i].percent = null
            }
            this.shareList = this.shareList.filter((item,index) => index != i);
            this.caculateRemain();
          }
        })
    }

    definedDataChange() {
        this.shareList.filter(e => e.locked == false || !e.locked).forEach(element => {
            element.data = this.shareWalletDataForm.controls['definedData'].value;
            element.percent = Math.round((this.shareWalletDataForm.controls['definedData'].value / this.originRemain) * 100 * 100) / 100;
        });
        this.caculateRemain();
        this.isShowDefined = !this.isShowDefined
    }

    shareData() {
        let totalDataLocked = 0;
        let lengthRemain = this.shareList.length
        this.shareList.filter(element => element.locked === true).forEach(element => {
            totalDataLocked = Number(totalDataLocked) + Number(element.data);
            lengthRemain = lengthRemain - 1;
        });
        let remainAfterLocked = this.originRemain - totalDataLocked
        let intPart = Math.floor(remainAfterLocked/lengthRemain);
        let remainderPart = remainAfterLocked % lengthRemain
        this.shareList.filter(element => element.locked === false || !element.locked).forEach(element => {
            element.data = intPart
            element.percent = Math.round((intPart / this.originRemain) * 100 * 100) / 100;
        });
        this.remainDataTotal = remainderPart
    };

    caculateRemain() {
        let totalQuantity = 0;
        this.shareList.forEach(item => {
          let value
          if(!item.data||item.data == null|| item.data == undefined){
            value = 0
          }else{
            value = item.data
          }
            totalQuantity = Number(totalQuantity) + Number(value);
        });
        this.remainDataTotal = this.originRemain - totalQuantity
      }

    resetData() {
        this.messageCommonService.confirm(this.tranService.translate("datapool.button.revokeSharing"),
        this.tranService.translate("datapool.message.confirmRevoke"),{
            ok:() => {
            this.remainDataTotal = this.originRemain
            this.shareList.forEach(item => {
                item.data = null;
                item.percent = null;
            });
            }
        })
    };

    checkDataCondition(index: number): boolean {
        if (this.typeOfTraffic === 'Gói Data') {
            return this.shareList[index].data % 100 === 0;
        }
        if((this.typeOfTraffic || '').toUpperCase().includes('Gói SMS'.toUpperCase())){
            return this.shareList[index].data % 5 === 0;
        }
        return true;
    };

    onHideDefine(){
        this.shareWalletDataForm.controls['definedData'].reset()
    }

    startCountdown() {
        this.interval = setInterval(() => {
            if (this.countdown > 0) {
                this.countdown--;
            } else {
                clearInterval(this.interval);
            }
        }, 1000);
    }

    resetTimer() {
        this.countdown = 60;
        clearInterval(this.interval);
        this.startCountdown();
        this.sendOTP();
    }

    handleShareData(){
        if (this.isNonUseOTP){
            this.submitForm();
        }else{
            this.sendOTP()
        }
    }

    // send OTP
    sendOTP() {
        let body = {};
        let me = this;
        const firstCharInPhone = this.apiPhone?.charAt(0);
        if (firstCharInPhone === '0') {
            body = {
                phoneNumber: this.apiPhone?.replace(firstCharInPhone, '84')
            }
        }
        me.messageCommonService.onload();
        this.shareService.sendOTP(body, (res) => {

            if (res.errorCode === 'BSS-00000000') {
                me.openSubmit();
            }
        }, null, () => {
            me.messageCommonService.offload()
        })
    }

    checkValidDefine(event){
        let lengthRemain = this.shareList.length
        this.shareList.filter(element => element.locked === true).forEach(element => {
          lengthRemain = lengthRemain - 1;
        });
        if((this.shareWalletDataForm.controls['definedData'].value*lengthRemain)>this.originRemain){
          this.isDefineValueError = true
          return
        }else{
          this.isDefineValueError = false
        }
        if (this.typeOfTraffic === 'Gói Data') {
            if (this.shareWalletDataForm.controls['definedData'].value % 100 !== 0) {
                this.isValidDataFixed = true;
            } else {
                this.isValidDataFixed = false;
            }
        }
        if((this.typeOfTraffic || '').toUpperCase().includes('Gói SMS'.toUpperCase())){
            if (this.shareWalletDataForm.controls['definedData'].value % 5 !== 0) {
                this.isValidDataFixed = true;
            } else {
                this.isValidDataFixed = false;
            }
        }
      }

    defineData(){
        this.isShowDefined = true
    };

    onClickSelection(){
        if(!this.isAutoWallet){
            this.messageCommonService.warning(this.tranService.translate("datapool.text.hasntAuto"));
        }
    }

    openSubmit() {
        this.isSubmit = true
        this.countdown = 60
        this.startCountdown()
    }

    importByFile() {
        let me = this;
        me.isShowDialogImportByFile = true;
        me.observableService.next(CONSTANTS.OBSERVABLE.KEY_INPUT_FILE_VNPT, {});
        me.isShowErrorUpload = false;
    }

    ngOnDestroy() {
        clearInterval(this.interval);
    }

    onHideError(){
        window.location.reload();
    }

    downloadErrorFile() {
        this.exportToExcel(this.FulldataSetError.content);
    }

    exportToExcel(data) {
        // Chuẩn bị dữ liệu và tiêu đề cột
        const header = ['STT', 'SĐT', 'Mô tả'];
        const excelData = data.map((item, index) => [index+1, item.phoneReceipt, item.description]);

        // Tạo sheet và thêm tiêu đề
        const ws: XLSX.WorkSheet = XLSX.utils.aoa_to_sheet([header, ...excelData]);

        ws['!cols'] = [{wch: 5}, {wch :21}, {wch:30}]

        // Bôi đậm tiêu đề
        const headerCells = ['A1', 'B1', 'C1']; // Các ô tiêu đề trong sheet (tương ứng với cột A, B, C)
        headerCells.forEach(cell => {
            if (ws[cell]) {
                ws[cell].s = {
                    font: {
                        bold: true, // Bôi đậm chữ
                    },
                    alignment: {
                        horizontal: 'center', // Căn giữa theo chiều ngang
                        vertical: 'center', // Căn giữa theo chiều dọc
                    },
                };
            }
        });

        // Căn giữa cho các ô dữ liệu
        const rowCount = data.length;
        for (let row = 2; row <= rowCount + 1; row++) {
            for (let col = 0; col < header.length; col++) {
                const cellRef = XLSX.utils.encode_cell({ r: row - 1, c: col });
                if (ws[cellRef]) {
                    ws[cellRef].s = {
                        alignment: {
                            horizontal: 'center', // Căn giữa theo chiều ngang
                            vertical: 'center', // Căn giữa theo chiều dọc
                        },
                    };
                }
            }
        }

        // Tạo workbook và xuất file
        const wb: XLSX.WorkBook = {
            Sheets: { 'Danh sách SĐT chia sẻ lỗi': ws },
            SheetNames: ['Danh sách SĐT chia sẻ lỗi'],
        };

        const excelBuffer: any = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
        this.saveAsExcelFile(excelBuffer, 'Danh_sach_loi_');
    }

    private saveAsExcelFile(buffer: any, fileName: string): void {
        const data: Blob = new Blob([buffer], { type: EXCEL_TYPE });
        FileSaver.saveAs(data, fileName + '_' + formatDateToDDMMYYYYHHMMSS(new Date().getTime()) + EXCEL_EXTENSION);
    }

    pagingDataError(pageNumber, pageSize){
        const startIndex = pageNumber * pageSize;
        const endIndex = startIndex + pageSize;
        this.dataSetError.content = this.FulldataSetError.content.slice(startIndex, endIndex);
        console.log(this.dataSetError)
        this.dataSetError = {...this.dataSetError}
    }

    formatNumber(value: number): string {
        return new Intl.NumberFormat('vi-VN').format(value);
    }
}

const EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
const EXCEL_EXTENSION = '.xlsx';

function formatDateToDDMMYYYYHHMMSS(timestamp: number): string {
    const date = new Date(timestamp);

    const dd = String(date.getDate()).padStart(2, '0');
    const MM = String(date.getMonth() + 1).padStart(2, '0'); // Tháng bắt đầu từ 0
    const yyyy = date.getFullYear();
    const HH = String(date.getHours()).padStart(2, '0');
    const mm = String(date.getMinutes()).padStart(2, '0');
    const ss = String(date.getSeconds()).padStart(2, '0');

    return `${dd}${MM}${yyyy}${HH}${mm}${ss}`;
}
