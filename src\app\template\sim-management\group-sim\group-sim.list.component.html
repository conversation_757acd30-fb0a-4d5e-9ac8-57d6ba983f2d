<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{tranService.translate("groupSim.breadCrumb.group")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
    <div class="col-5 flex flex-row justify-content-end align-items-center">
        <p-splitButton *ngIf="checkAuthen([allPermissions.GROUP_SIM.CREATE])" styleClass="p-button-success" [label]="tranService.translate('global.button.add')" icon="pi pi-plus" [model]="itemAddGroups"></p-splitButton>
    </div>
</div>
<!-- <p-fieldset class="vnpt-field-set" [styleClass]="'mt-4'" [legend]="tranService.translate('global.text.filter')" [toggleable]="true">

</p-fieldset> -->
<p-panel class="vnpt-field-set" [toggleable]="true" [header]="tranService.translate('global.text.filter')" [styleClass]="'pb-2 pt-3'">
    <div class="grid">
        <div class="col-3">
            <span class="p-float-label">
                <input (keyup.enter)="onSearch()" class="w-full" pInputText id="groupKey" [(ngModel)]="searchInfo.groupKey" />
                <label htmlFor="groupKey">{{tranService.translate("groupSim.label.groupKey")}}</label>
            </span>
        </div>
        <div class="col-3">
            <span class="p-float-label">
                <input (keyup.enter)="onSearch()" class="w-full" pInputText id="name" [(ngModel)]="searchInfo.name" />
                <label htmlFor="groupName">{{tranService.translate("groupSim.label.groupName")}}</label>
            </span>
        </div>
        <!-- <div class="col-3">
            <span class="p-float-label">
                <input class="w-full" pInputText id="customer" [(ngModel)]="searchInfo.customer" />
                <label htmlFor="customer">{{tranService.translate("groupSim.label.customer")}}</label>
            </span>
        </div> -->

        <div class="col-3" *ngIf="isShowGroupScope">
            <span class="p-float-label">
                <p-dropdown styleClass="w-full" [showClear]="true"
                        [autoDisplayFirst]="false"
                        [(ngModel)]="searchInfo.scope"
                        [options]="groupScopes"
                        optionLabel="name"
                        optionValue="value"
                ></p-dropdown>
                <label for="scope">{{tranService.translate("groupSim.label.groupScope")}}</label>
            </span>
        </div>

        <div class="col-3 pb-0">
            <p-button icon="pi pi-search"
                        styleClass="p-button-rounded p-button-secondary p-button-text button-search"
                        (click)="onSearch()"
            ></p-button>
        </div>
    </div>
</p-panel>
<!-- <div>{{selectItems.length}}</div> -->
<table-vnpt
    [fieldId]="'id'"
    [(selectItems)]="selectItems"
    [columns]="columns"
    [dataSet]="dataSet"
    [options]="optionTable"
    [loadData]="search.bind(this)"
    [pageNumber]="pageNumber"
    [pageSize]="pageSize"
    [sort]="sort"
    [params]="searchInfo"
    [labelTable]="tableLabel"
></table-vnpt>
<p-dialog [header]="tranService.translate('groupSim.breadCrumb.detail')" [(visible)]="isShowModalDetailGroupSim" [modal]="true" [style]="{ width: '980px', height: '720px' }" [draggable]="false" [resizable]="true" styleClass="custom-dialog">
    <div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
        <div class="col-5 flex flex-row justify-content-start align-items-center">
            <button pButton *ngIf="checkAuthen([CONSTANTS.PERMISSIONS.GROUP_SIM.UPDATE])" class="p-button-info mr-2" (click)="showOverLayEdit()">{{this.tranService.translate("groupSim.label.buttonEdit")}}</button>
            <button pButton *ngIf="checkAuthen([CONSTANTS.PERMISSIONS.GROUP_SIM.DELETE])" class="p-button-secondary mr-2" (click)="deleteGroupSim()">{{this.tranService.translate("global.button.delete")}}</button>
        </div>
        <div class="col-5 flex flex flex-row justify-content-end align-items-center responsive-button-container">
            <button pButton *ngIf="checkAuthen([CONSTANTS.PERMISSIONS.GROUP_SIM.UPDATE])" class="p-button-secondary mr-2 center-button equal-button" (click)="removeMany()" [disabled]="selectItemsDetail.length == 0">{{this.tranService.translate("groupSim.label.buttonDelete")}}</button>
            <button pButton *ngIf="checkAuthen([CONSTANTS.PERMISSIONS.GROUP_SIM.UPDATE])" class="p-button-success mr-2 equal-button" (click)="showOverLayAddSim()">{{this.tranService.translate("groupSim.label.buttonAddSim")}}</button>
        </div>
    </div>

    <p-card [styleClass]="'my-4 py-0'">
        <div class="grid dialog-sim-list-grid-1 ">
            <div class="col-6 pt-0 pb-0">
                <div class="flex-1 flex justify-content-between col-12 sm:col-8 md:col-12 py-0">
                    <label htmlFor="groupCode" class="m-0 p-0 text-lg font-medium" style="min-width: 130px; align-self: flex-end;">{{this.tranService.translate("groupSim.label.groupKey")}}:</label>
                    <div class="col-9 sm:col-6 md:col-10 py-0 text-lg font-medium ">
                        {{groupKey}}
                    </div>
                </div>
            </div>
            <div class="col-6 pt-0 pb-0">
                <div class="flex-1 flex justify-content-between col-12 md:col-12 py-0">
                    <label htmlFor="groupScope" class="m-0 p-0 text-lg font-medium " style="min-width: 130px; align-self: flex-end;">{{this.tranService.translate("groupSim.label.groupScope")}}:</label>
                    <div class="col-9 md:col-10 py-0 text-lg font-medium span-responsive">
                        <span *ngIf="groupScope == groupScopeObjects.GROUP_ADMIN">{{tranService.translate("groupSim.scope.admin")}}</span>
                        <span *ngIf="groupScope == groupScopeObjects.GROUP_PROVINCE">{{tranService.translate("groupSim.scope.province")}}</span>
                        <span *ngIf="groupScope == groupScopeObjects.GROUP_CUSTOMER">{{tranService.translate("groupSim.scope.customer")}}</span>
                    </div>
                </div>
            </div>
            <div class="col-6 pt-0 pb-0">
                <div class="flex-1 flex justify-content-between col-12 md:col-12 py-0">
                    <label htmlFor="groupCode" class="m-0 p-0 text-lg font-medium" style="min-width: 130px; align-self: flex-end;">{{this.tranService.translate("groupSim.label.groupName")}}:</label>
                    <div class="col-9 md:col-10 pb-0 text-lg font-medium">
                        {{groupName}}
                    </div>
                </div>
            </div>
            <div class="col-6 pt-0 pb-0" *ngIf="groupScope == groupScopeObjects.GROUP_CUSTOMER">
                <div class="flex-1 flex justify-content-between col-12 md:col-12 py-0">
                    <label htmlFor="groupCode" class="m-0 p-0 text-lg font-medium" style="min-width: 130px; align-self: flex-end;">{{this.tranService.translate("groupSim.label.customer")}}:</label>
                    <div class="col-9 md:col-10 pb-0 text-lg font-medium">
                        {{customer}}
                    </div>
                </div>
            </div>
            <div class="col-6 pt-0 pb-0" *ngIf="groupScope == groupScopeObjects.GROUP_CUSTOMER && contractCode">
                <div class="flex-1 flex justify-content-between col-12 md:col-12 py-0">
                    <label htmlFor="groupCode" class="m-0 p-0 text-lg font-medium" style="min-width: 130px; align-self: flex-end;">{{this.tranService.translate("groupSim.label.contractCode")}}:</label>
                    <div class="col-9 md:col-10 pb-0 text-lg font-medium">
                        {{contractCode}}
                    </div>
                </div>
            </div>
            <div class="col-6 pt-0 pb-0" *ngIf="groupScope == groupScopeObjects.GROUP_PROVINCE">
                <div class="flex-1 flex justify-content-between col-12 md:col-12 py-0">
                    <label htmlFor="groupCode" class="m-0 p-0 text-lg font-medium" style="min-width: 130px; align-self: flex-end;">{{this.tranService.translate("account.label.province")}}:</label>
                    <div class="col-9 md:col-10 pb-0 text-lg font-medium">
                        {{province}}
                    </div>
                </div>
            </div>
            <div class="col-6 pt-0 pb-0" *ngIf="groupScope != groupScopeObjects.GROUP_ADMIN">
                <div class="flex-1 flex justify-content-between col-12 md:col-12 py-0">
                    <label htmlFor="groupCode" class="m-0 p-0 text-lg font-medium" style="min-width: 130px; align-self: flex-end;"></label>
                    <div class="col-9 md:col-10 pb-0 text-lg font-medium">

                    </div>
                </div>
            </div>
            <div class="col-6 pt-0 pb-0">
                <div class="flex-1 flex justify-content-between col-12 md:col-12 py-0">
                    <label htmlFor="groupCode" class="m-0 p-0 text-lg font-medium " style="min-width: 130px; align-self: flex-end;">{{this.tranService.translate("groupSim.label.description")}}:</label>
                    <div class="col-9 md:col-10 pb-0 text-lg font-medium">
                        {{description}}<span>&nbsp;</span>
                    </div>
                </div>
            </div>
        </div>
    </p-card>


    <table-vnpt
        [fieldId]="'msisdn'"
        [(selectItems)]="selectItemsDetail"
        [columns]="columnsDetail"
        [dataSet]="dataSetDetail"
        [options]="optionTableDetail"
        [loadData]="searchDetail.bind(this)"
        [pageNumber]="pageNumberDetail"
        [pageSize]="pageSizeDetail"
        [sort]="sortDetail"
    ></table-vnpt>

    <p-dialog #overlayGanSim [header]="headerSim" [(visible)]="displayAddSim" [modal]="true" [draggable]="false" [resizable]="false" showEffect="fade" [style]="{width: '45vw'}" [breakpoints]="{'960px': '75vw'}">

        <div class="flex justify-content-center align-items-center col-12 md:col-12 py-0">
            <label htmlFor="groupCode" class="my-auto" style="min-width: 150px;">{{tranService.translate("groupSim.detail.subNumber")}}<span class="text-red-500">*</span></label>
            <div class="flex-grow-1">
                <vnpt-select
                    [control]="boxSimAddController"
                    class="w-full"
                    [(value)]="selectedSimItems"
                    [placeholder]="placeholderSIM"
                    objectKey="sim"
                    paramKey="msisdn"
                    keyReturn="msisdn"
                    displayPattern="${msisdn}"
                    typeValue="primitive"
                    [paramDefault]="paramSearchSim"
                    [loadData]="loadSimNotInGroup.bind(this)"
                ></vnpt-select>
            </div>
        </div>
        <div class="pt-4 flex flex-row gap-3 justify-content-center">
            <button pButton (click)="handleModelClose()" class="p-button-secondary p-button-outlined">{{this.tranService.translate("groupSim.label.buttonCancel")}}</button>
            <button pButton (click)="handleSavetoGroup()" [label]="buttonSaveSimToGroup" [disabled]="selectedSimItems.length == 0"></button>
        </div>
    </p-dialog>

</p-dialog>
