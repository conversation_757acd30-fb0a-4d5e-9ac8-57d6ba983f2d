<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{tranService.translate("global.menu.alertList")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
    <div class="col-5 flex flex-row justify-content-end align-items-center">
        <p-button styleClass="p-button-info" [label]="tranService.translate('global.button.create')"
                  *ngIf="checkAuthen([CONSTANTS.PERMISSIONS.ALERT.CREATE, CONSTANTS.PERMISSIONS.ALERT.CREATE_WALLET_EXPIRY, CONSTANTS.PERMISSIONS.ALERT.CREATE_WALLET_THRESHOLD])"
                  icon="pi pi-plus" [routerLink]="['/alerts/create']" routerLinkActive="router-link-active" ></p-button>
    </div>
</div>

<form [formGroup]="formSearchAlert" (ngSubmit)="onSubmitSearch()" class="pb-2 pt-3 vnpt-field-set">
    <p-panel [toggleable]="true" [header]="tranService.translate('global.text.filter')">
        <div class="grid search-grid-3">
            <!-- Ten canh bao -->
            <div class="col-3">
                <span class="p-float-label">
                    <input pInputText
                           class="w-full"
                           pInputText id="name"
                           [(ngModel)]="searchInfo.name"
                           formControlName="name"
                    />
                    <label htmlFor="name">{{tranService.translate("alert.label.name")}}</label>
                </span>
            </div>
            <!-- loai -->
            <div class="col-3">
                <span class="p-float-label">
                <p-dropdown styleClass="w-full"
                            [showClear]="true" [autoDisplayFirst]="false"
                            id="ruleCategory"
                            [(ngModel)]="searchInfo.ruleCategory"
                            formControlName="ruleCategory"
                            [options]="ruleOptions"
                            optionLabel="name"
                            optionValue="value"
                            (onChange)="onRuleCategoryChange()"
                ></p-dropdown>
                    <label for="ruleCategory"> {{tranService.translate('alert.label.rule')}}</label>
                </span>
            </div>
            <!-- dieu kien -->
            <div class="col-3">
                <span class="relative">
                    <vnpt-select
                                 class="w-full"
                                 [(value)]="searchInfo.eventType"
                                 paramKey="name"
                                 keyReturn="value"
                                 displayPattern="${name}"
                                 [options]="getOptionEventType()"
                                 [isFilterLocal]="true"
                                 [lazyLoad]="false"
                                 [isMultiChoice]="false"
                                 [placeholder]="tranService.translate('alert.text.eventType')"
                                 [floatLabel]="true"
                                 [stylePositionBoxSelect]="getBoxSelectStyle()"
                    ></vnpt-select>
                </span>
            </div>
            <!-- loai hanh dong -->
            <div class="col-3">
                <span class="p-float-label">
                    <p-dropdown styleClass="w-full" [autoDisplayFirst]="false"
                                [showClear]="true"
                                id="actionType"
                                [(ngModel)]="searchInfo.actionType"
                                formControlName="actionType"
                                [options]="actionOptions"
                                optionLabel="name"
                                optionValue="value"
                    ></p-dropdown>
                    <label for="actionType"> {{tranService.translate('alert.label.action')}}</label>
                </span>
            </div>
            <!-- Trang thai -->
            <div class="col-3">
                <span class="p-float-label">
                    <p-dropdown styleClass="w-full" [showClear]="true"
                                id="status" [autoDisplayFirst]="false"
                                [(ngModel)]="searchInfo.status"
                                formControlName="status"
                                [options]="statusAlert"
                                optionLabel="name"
                                optionValue="value"
                    ></p-dropdown>
                    <label htmlFor="status">{{tranService.translate("alert.label.status")}}</label>
                </span>
            </div>
            <!-- muc do -->
            <div class="col-3">
                <span class="p-float-label">
                <p-dropdown styleClass="w-full"
                            [showClear]="true"
                            id="severity" [autoDisplayFirst]="false"
                            [(ngModel)]="searchInfo.severity"
                            formControlName="severity"
                            [options]="severityOptions"
                            optionLabel="name"
                            optionValue="value"
                ></p-dropdown>
                    <label for="severity"> {{tranService.translate('alert.label.level')}}</label>
                </span>
            </div>
            <div class="col-3 pb-0">
                <p-button icon="pi pi-search"
                          styleClass="p-button-rounded p-button-secondary p-button-text button-search"
                          type="submit"
                ></p-button>
            </div>
        </div>
    </p-panel>
</form>
<div class="flex justify-content-center dialog-vnpt ">
    <p-dialog [header]="tranService.translate('global.button.view')" [(visible)]="isShowModalDetail" [modal]="true" [style]="{ width: '1200px' }" [draggable]="false" [resizable]="false">
        <p-card class="p-4">
            <form action="" [formGroup]="formAlertDetail" *ngIf="isShowModalDetail">
                <div class="p-3 pt-0 shadow-2 border-round-md m-1 flex p-fluid p-formgrid grid">
                    <!-- ten canh bao -->
                    <div class="col-4 flex flex-row justify-content-between align-items-center pb-0">
                        <label htmlFor="name" style="width:90px">{{tranService.translate("alert.label.name")}}<span class="text-red-500">*</span></label>
                        <div style="width: calc(100% - 90px)" class="relative">
                            <input class="w-full"
                                   pInputText id="name"
                                   [(ngModel)]="alertInfo.name"
                                   formControlName="name"
                                   [required]="true"
                                   [maxLength]="255"
                                   pattern="^[a-zA-Z0-9\-_]*$"
                                   [placeholder]="tranService.translate('alert.text.inputName')"
                            />
                        </div>
                    </div>
                    <!-- loai -->
                    <div class="col-4 flex flex-row justify-content-between align-items-center pb-0">
                        <label for="ruleCategory" style="width:90px">{{tranService.translate("alert.label.rule")}}<span class="text-red-500">*</span></label>
                        <div style="width: calc(100% - 90px)">
                            <p-dropdown styleClass="w-full"
                                        id="ruleCategory" [autoDisplayFirst]="false"
                                        [(ngModel)]="alertInfo.ruleCategory"
                                        [required]="true"
                                        formControlName="ruleCategory"
                                        [options]="ruleOptions"
                                        optionLabel="name"
                                        optionValue="value"
                                        [placeholder]="tranService.translate('alert.text.rule')"
                            ></p-dropdown>
                        </div>
                    </div>
                    <!-- dieu kien kich hoat -->
                    <div class="col-4 flex flex-row justify-content-between align-items-center pb-0">
                        <label for="eventType" style="width:90px">{{tranService.translate("alert.label.event")}}<span class="text-red-500">*</span></label>
                        <div style="width: calc(100% - 90px)">
                            <p-dropdown *ngIf="alertInfo.ruleCategory == CONSTANTS.ALERT_RULE_CATEGORY.MANAGEMENT" styleClass="w-full"
                                        class="left-side"
                                        id="eventType" [autoDisplayFirst]="false"
                                        [(ngModel)]="alertInfo.eventType"
                                        [required]="true"
                                        formControlName="eventType"
                                        [options]="eventOptionManagement"
                                        optionLabel="name"
                                        optionValue="value"
                                        [placeholder]="tranService.translate('alert.text.eventType')"
                                        [virtualScroll]="false"
                            ></p-dropdown>
                            <p-dropdown *ngIf="alertInfo.ruleCategory == CONSTANTS.ALERT_RULE_CATEGORY.MONITORING" styleClass="w-full"
                                        class="left-side"
                                        id="eventType" [autoDisplayFirst]="false"
                                        [(ngModel)]="alertInfo.eventType"
                                        [required]="true"
                                        formControlName="eventType"
                                        [options]="eventOptionMonitoring"
                                        optionLabel="name"
                                        optionValue="value"
                                        [placeholder]="tranService.translate('alert.text.eventType')"
                                        [virtualScroll]="false"
                            ></p-dropdown>
                        </div>
                    </div>
                    <div class="col-4 flex flex-row p-0 w-full" *ngIf="formAlertDetail.controls.name.invalid || formAlertDetail.controls.severity.invalid || formAlertDetail.controls.statusSim.invalid || isAlertNameExisted">
                        <div class="flex-1 py-0 col-4 flex flex-row justify-content-between align-items-center w-full" style="height: fit-content"
                             *ngIf="formAlertDetail.controls.name.invalid || formAlertDetail.controls.severity.invalid || formAlertDetail.controls.statusSim.invalid || isAlertNameExisted">
                            <label htmlFor="name" style="width:130px; height: fit-content"></label>
                            <div style="width: calc(100% - 130px)">
                                <small class="text-red-500" *ngIf="formAlertDetail.controls.name.dirty && formAlertDetail.controls.name.errors?.required">{{tranService.translate("global.message.required")}}</small>
                                <small class="text-red-500" *ngIf="formAlertDetail.controls.name.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:255})}}</small>
                                <small class="text-red-500" *ngIf="formAlertDetail.controls.name.errors?.pattern">{{tranService.translate("global.message.formatCode")}}</small>
                                <small class="text-red-500" *ngIf="isAlertNameExisted">{{tranService.translate("global.message.exists",{type: tranService.translate("alert.label.name").toLowerCase()})}}</small>
                            </div>
                        </div>

                        <div class="flex-1 py-0 col-4 flex flex-row justify-content-between align-items-center w-full" style="height: fit-content"
                             *ngIf="formAlertDetail.controls.name.invalid || formAlertDetail.controls.severity.invalid || formAlertDetail.controls.statusSim.invalid || isAlertNameExisted">
                            <label htmlFor="severity" style="width:90px; height: fit-content"></label>
                            <div style="width: calc(100% - 90px)">
                                <small class="text-red-500" *ngIf="formAlertDetail.controls.severity.dirty && formAlertDetail.controls.severity.errors?.required">{{tranService.translate("global.message.required")}}</small>
                            </div>
                        </div>

                        <div class="flex-1 py-0 col-4 flex flex-row justify-content-between align-items-center w-full" style="height: fit-content"
                             *ngIf="formAlertDetail.controls.name.invalid || formAlertDetail.controls.severity.invalid || formAlertDetail.controls.statusSim.invalid || isAlertNameExisted">
                            <label htmlFor="statusSim"  style="width:150px; height: fit-content"></label>
                            <div style="width: calc(100% - 150px)">
                                <small class="text-red-500" *ngIf="formAlertDetail.controls.statusSim.dirty && formAlertDetail.controls.statusSim.errors?.required">{{tranService.translate("global.message.required")}}</small>
                            </div>
                        </div>
                    </div>
                    <!-- muc do -->
                    <div class="col-4 flex flex-row justify-content-between align-items-center pb-0 pt-3">
                        <label for="severity" style="width:90px">{{tranService.translate("alert.label.level")}}<span class="text-red-500">*</span></label>
                        <div style="width: calc(100% - 90px)">
                            <p-dropdown styleClass="w-full"
                                        id="severity" [autoDisplayFirst]="false"
                                        [(ngModel)]="alertInfo.severity"
                                        [required]="true"
                                        formControlName="severity"
                                        [options]="severityOptions"
                                        optionLabel="name"
                                        optionValue="value"
                                        [placeholder]="tranService.translate('alert.text.inputlevel')"
                            ></p-dropdown>
                        </div>
                    </div>
                    <div class="col-4 flex flex-row p-0 w-full" *ngIf="formAlertDetail.controls.name.invalid || formAlertDetail.controls.severity.invalid || formAlertDetail.controls.statusSim.invalid || isAlertNameExisted">
                        <!-- error muc do -->
                        <div class="flex-1 py-0 col-4 flex flex-row justify-content-between align-items-center w-full" style="height: fit-content"
                             *ngIf="formAlertDetail.controls.name.invalid || formAlertDetail.controls.severity.invalid || formAlertDetail.controls.statusSim.invalid || isAlertNameExisted">
                            <label htmlFor="severity" style="width:90px; height: fit-content"></label>
                            <div style="width: calc(100% - 90px)">
                                <small class="text-red-500" *ngIf="formAlertDetail.controls.severity.dirty && formAlertDetail.controls.severity.errors?.required">{{tranService.translate("global.message.required")}}</small>
                            </div>
                        </div>
                    </div>
                    <!-- trang thai -->
                    <div class="col-4 flex flex-row align-items-center pb-0 pt-3">
                        <label for="status" style="width:90px">{{tranService.translate("alert.label.status")}}</label>
                        <div style="width: calc(100% - 90px);" class="flex flex-row align-items-center">
                            <span *ngIf="statusAlertForDetail.ACTIVE == statusTemp" [class]="['p-2','text-green-800', 'bg-green-100','border-round','inline-block']">{{tranService.translate("alert.status.active")}}</span>
                            <span *ngIf="statusAlertForDetail.INACTIVE == statusTemp" [class]="['p-2', 'text-red-700', 'bg-red-100', 'border-round','inline-block']">{{tranService.translate("alert.status.inactive")}}</span>
                            <p-inputSwitch *ngIf="checkChangeStatus()" pTooltip="{{alertInfo.status == CONSTANTS.ALERT_STATUS.ACTIVE?tranService.translate('alert.label.inactivePopup') : tranService.translate('alert.label.activePopup')}}" tooltipPosition="right" tooltipStyleClass="absolute"
                                           class="ml-4 mt-2" (onChange)="showConfimChangeStatus()"
                                           [trueValue]="statusAlertForDetail.ACTIVE" [falseValue]="statusAlertForDetail.INACTIVE" [(ngModel)]="alertInfo.status" formControlName="status"/>
                        </div>
                    </div>
                    <!-- mo ta -->
                    <div class="col-8 flex flex-row justify-content-between align-items-center pt-3">
                        <label htmlFor="description" style="width:90px">{{tranService.translate("alert.label.description")}}</label>
                        <div style="width: calc(100% - 90px)">
                            <input class="w-full input-full-v2"
                                   pInputText id="description"
                                   [(ngModel)]="alertInfo.description"
                                   formControlName="description"
                                   [maxLength]="255"
                                   [placeholder]="tranService.translate('alert.text.inputDescription')"
                            />
                        </div>
                    </div>

                </div>

                <h4 class="ml-2">{{tranService.translate("alert.text.filterApplieInfo")}}</h4>
                <div *ngIf="alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP  && alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD" class="p-3 pt-0 shadow-2 border-round-md m-1 flex p-fluid p-formgrid grid">
                    <!-- khach hang -->
                    <div class="col-4 flex flex-row justify-content-between align-items-center pb-0">
                        <label for="customerId"  style="width:130px">{{tranService.translate("alert.label.customer")}}<span class="text-red-500">*</span></label>
                        <div style="width: calc(100% - 130px)">
                            {{alertInfo?.customerName + ' - ' + alertInfo?.customerCode}}
                        </div>
                    </div>
                    <div class="col-4 flex flex-row justify-content-between align-items-center pb-0" *ngIf="alertInfo.contractCode">
                        <label for="contractCode"  style="width:130px">{{tranService.translate("alert.label.contractCode")}}<span class="text-red-500">*</span></label>
                        <div style="width: calc(100% - 130px)">
                            {{alertInfo?.contractCode}}
                        </div>
                    </div>
                    <!-- nhom thue bao -->
                    <div class="col-4 flex flex-row justify-content-between align-items-center pb-0">
                        <label for="groupId" style="width:150px">{{tranService.translate("alert.label.group")}}<span class="text-red-500">*</span></label>
                        <div style="width: calc(100% - 150px)">
                            {{alertInfo.groupName}}
                        </div>
                    </div>
                    <!--so thue bao -->
                    <div class="col-4 flex flex-row justify-content-between align-items-center pb-0">
                        <label for="subscriptionNumber" style="width:130px">{{tranService.translate("alert.label.subscriptionNumber")}}<span class="text-red-500">*</span></label>
                        <div style="width: calc(100% - 130px)">
                            {{alertInfo.subscriptionNumber}}
                        </div>
                    </div>
                    <div class="col-4 flex flex-row p-0 w-full" *ngIf="comboSelectCustomerControl.error.required || comboSelectSubControl.error.required || comboSelectGroupSubControl.error.required">
                        <!-- error khach hang -->
                        <div class="flex-1 py-0 col-4 flex flex-row justify-content-between align-items-center w-full" style="height: fit-content">
                            <label htmlFor="customerId" class="col-fixed py-0" style="width:130px"></label>
                            <div style="width: calc(100% - 130px)" class="py-0">
                                <small class="text-red-500" *ngIf="comboSelectCustomerControl.dirty && comboSelectCustomerControl.error.required">{{tranService.translate("global.message.required")}}</small>
                            </div>
                        </div>
                        <!-- error nhom thue bao -->
                        <div class="flex-1 py-0 col-4 flex flex-row justify-content-between align-items-center w-full" style="height: fit-content">
                            <label htmlFor="groupId" class="col-fixed p-0" style="width:130px"></label>
                            <div style="width: calc(100% - 150px)" class="py-0">
                                <small class="text-red-500" *ngIf="comboSelectSubControl.dirty && comboSelectSubControl.error.required">{{tranService.translate("global.message.required")}}</small>
                            </div>
                        </div>
                        <!-- error so thue bao -->
                        <div class="flex-1 py-0 col-4 flex flex-row justify-content-between align-items-center w-full" style="height: fit-content">
                            <label htmlFor="subscriptionNumber" class="col-fixed p-0" style="width:130px"></label>
                            <div style="width: calc(100% - 150px)" class="py-0">
                                <small class="text-red-500" *ngIf="comboSelectGroupSubControl.dirty && comboSelectGroupSubControl.error.required">{{tranService.translate("global.message.required")}}</small>
                            </div>
                        </div>
                    </div>
                    <!-- gia tri -->
                    <div class="col-4 flex flex-row gap-3 justify-content-start align-items-center pb-0"
                         [class]="alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_VALUE ||
                        alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_PACKAGE ||
                        alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_VALUE ||
                        alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_PACKAGE? '' : 'hidden'" >
                        <label *ngIf="alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_PACKAGE" for="value">{{tranService.translate("alert.label.exceededPakage")}}<span class="text-red-500">*</span></label>
                        <label *ngIf="alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_VALUE" for="value">{{tranService.translate("alert.label.exceededValue")}}<span class="text-red-500">*</span></label>
                        <label *ngIf="alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_PACKAGE" for="value">{{tranService.translate("alert.label.smsExceededPakage")}}<span class="text-red-500">*</span></label>
                        <label *ngIf="alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_VALUE" for="value">{{tranService.translate("alert.label.smsExceededValue")}}<span class="text-red-500">*</span></label>
                        <div style="width: 80px">
                            {{alertInfo.value}}
                        </div>
                    </div>
                    <div class="col-4 flex flex-row p-0 w-full" *ngIf="isPlanExisted || formAlertDetail.controls.value.dirty && formAlertDetail.controls.value.errors?.max">
                        <div *ngIf="alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_PACKAGE|| alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_PACKAGE" class="flex-1 p-0 col-4 flex flex-row justify-content-between align-items-center w-full" style="height: fit-content">
                            <label htmlFor="groupId" class="col-fixed py-0" style="width:150px"></label>
                            <div class="col py-0">
                                <small class="text-red-500" *ngIf="isPlanExisted">{{tranService.translate("alert.message.existedPlan")}}</small>
                            </div>
                        </div>
                        <div class="flex-1 p-0 col-4 flex flex-row justify-content-between align-items-center w-full" style="height: fit-content">
                            <label htmlFor="customerId" class="col-fixed py-0"></label>
                            <div style="width: 80" class="py-0">
                                <small class="text-red-500" *ngIf="formAlertDetail.controls.value.dirty && formAlertDetail.controls.value.errors?.max">{{tranService.translate("global.message.twentydigitlength")}}</small>
                            </div>
                        </div>
                        <div class="flex-1 p-0 col-4 flex flex-row justify-content-between align-items-center w-full" style="height: fit-content" *ngIf="alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_VALUE || alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_PACKAGE"></div>
                        <div class="flex-1 p-0 col-4 flex flex-row justify-content-between align-items-center w-full" style="height: fit-content">
                            <label htmlFor="groupId" class="col-fixed py-0" style="width:150px"></label>
                            <div class="col py-0"></div>
                        </div>
                    </div>
                </div>

                <div *ngIf="alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP" class="pb-3 shadow-2 border-round-md m-1 flex p-fluid p-formgrid grid">
                    <!-- goi cuoc dang dung -->
                    <div class="col-4 pb-0 flex flex-row justify-content-between align-items-center">
                        <label for="appliedPlan"  style="width:150px">{{tranService.translate("alert.label.appliedPlan")}}<span class="text-red-500">*</span></label>
                        <div style="width: calc(100% - 150px)">
                            <p-multiSelect styleClass="w-full"
                                           id="appliedPlan" [autoDisplayFirst]="false"
                                           [(ngModel)]="alertInfo.appliedPlan"
                                           formControlName="appliedPlan"
                                           [options]="appliedPlanOptions"
                                           [filter]="true"
                                           filterBy="code"
                                           [placeholder]="tranService.translate('alert.text.appliedPlan')"
                                           optionLabel="code"
                                           optionValue="code"
                                           [required]="true"
                            ></p-multiSelect>
                            <small class="text-red-500" *ngIf="isPlanExisted">{{tranService.translate("alert.message.existedPlan")}}</small>
                            <small class="text-red-500" *ngIf="formAlertDetail.controls.appliedPlan.dirty && formAlertDetail.controls.appliedPlan.errors?.required">{{tranService.translate("global.message.required")}}</small>
                        </div>
                    </div>
                </div>
                <div
                    *ngIf="alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD">
                    <div class="pb-3 shadow-2 border-round-md m-1 flex p-fluid p-formgrid grid">
                        <!-- Ví áp dụng -->
                        <div class="col-6 pb-0 flex flex-row justify-content-between">
                            <label class="mt-2"
                                   style="width:200px">{{ tranService.translate("alert.label.wallet") }}<span
                                class="text-red-500">*</span></label>
                            <div style="width: calc(100% - 200px)">
<!--                                <vnpt-select-->
<!--                                    [control]="controlComboSelectWallet"-->
<!--                                    [(value)]="alertInfo.walletSubCode"-->
<!--                                    class="w-full"-->
<!--                                    [placeholder]="tranService.translate('alert.label.wallet')"-->
<!--                                    objectKey="walletToAlert"-->
<!--                                    paramKey="subCode"-->
<!--                                    keyReturn="subCode"-->
<!--                                    typeValue="primitive"-->
<!--                                    [required]="true"-->
<!--                                    [isMultiChoice] = "false"-->
<!--                                    [disabled]="true"-->
<!--                                ></vnpt-select>-->
                                <p-dropdown styleClass="w-full"
                                               id="appliedPlan" [autoDisplayFirst]="false"
                                               [(ngModel)]="alertInfo.walletSubCode"
                                               formControlName="walletSubCode"
                                               [options]="walletOptions"
                                               [filter]="true"
                                               filterBy="subCode"
                                               [placeholder]="tranService.translate('alert.text.appliedPlan')"
                                               optionLabel="subCode"
                                               optionValue="subCode"
                                               [readonly]="true"
                                               [required]="true"
                                >
                                    <ng-template let-option pTemplate="selectedItem">
                                        {{ option.subCode }} - {{ option.packageCode }}
                                    </ng-template>
                                    <ng-template let-option pTemplate="item">
                                        {{ option.subCode }} - {{ option.packageCode }}
                                    </ng-template>
                                </p-dropdown>
                            </div>
                        </div>

                        <!-- Giá trị ngưỡng -->
                        <div class="col-4 pb-0 flex flex-row justify-content-between">
                            <label class="mt-2" for="walletValue"
                                   style="width:200px">{{ tranService.translate("alert.label.thresholdValue") }}<span
                                class="text-red-500">*</span></label>
                            <div style="width: calc(100% - 200px)">
                                <input pInputText type="number"
                                       id="walletValue"
                                       [(ngModel)]="alertInfo.value"
                                       [required]="true"
                                       [min]="1"
                                       formControlName="value"
                                       class="w-full">

                            </div>
                        </div>

                        <!-- Đơn vị -->
                        <div class="col-2 pb-0 flex flex-row justify-content-between">
                            <p-dropdown
                                id="unit"
                                [options]="unitWalletOptions"
                                optionLabel="label"
                                optionValue="value"
                                [(ngModel)]="alertInfo.unit"
                                formControlName="unit"
                            />
                        </div>

                        <!-- Email và Số điện thoại -->
                        <div class="col-6 pb-0 flex flex-row justify-content-between">
                            <label class="mt-2">{{ tranService.translate("alert.label.walletEmail") }}</label>
                            <span class="mt-2" style="width: calc(100% - 200px)">{{ alertInfo.emailList }}</span>
                        </div>
                        <div class="col-4 pb-0 flex flex-row justify-content-between">
                            <label class="mt-2">{{ tranService.translate("alert.label.walletPhone") }}</label>
                            <span class="mt-2" style="width: calc(100% - 200px)">{{ alertInfo.smsList }}</span>
                        </div>
                    </div>
                </div>

                <div class="ml-2 my-4 flex flex-row justify-content-start align-items-center gap-3">
                    <h4 for="actionType" class="mb-0">{{tranService.translate("alert.label.action")}}</h4>
                    <div>
                        <p-dropdown styleClass="w-full"
                                    id="actionType" [autoDisplayFirst]="false"
                                    [(ngModel)]="alertInfo.actionType"
                                    [required]="true"
                                    formControlName="actionType"
                                    [options]="actionOptions"
                                    optionLabel="name"
                                    optionValue="value"
                                    [placeholder]="tranService.translate('alert.text.actionType')"
                        ></p-dropdown>
                    </div>
                </div>
                <div [class]="alertInfo.actionType == CONSTANTS.ALERT_ACTION_TYPE.ALERT ? '' : 'hidden'" class="pt-0 shadow-2 border-round-md m-1 flex flex-column p-fluid p-formgrid grid">
                    <div class="flex flex-row gap-4">
                        <div class="flex-1">
                            <div *ngIf="alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP" class="col-12 flex flex-row justify-content-start align-items-center pt-4 pr-4">
                                <label class="col-fixed" htmlFor="value">{{tranService.translate("alert.text.sendNotifyExpiredData")}}</label>
                                <div>
                                    <input  class="w-full" style="resize: none;"
                                            rows="5"
                                            pInputText
                                            [autoResize]="false"
                                            pInputTextarea id="value"
                                            [(ngModel)]="alertInfo.value"
                                            formControlName="value"
                                            type="number"
                                    />
                                </div>
                                <label class="col-fixed" htmlFor="value">{{tranService.translate("alert.text.day")}}</label>
                            </div>
                        </div>
                        <div class="flex-1" *ngIf="alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP">
                            <div class="col-12 flex flex-row justify-content-start align-items-center pb-0">
                                <div>
                                    <p-checkbox
                                        [(ngModel)]="repeat"
                                        formControlName="notifyRepeat"
                                        [binary]="true"
                                        inputId="binary" />
                                </div>
                                <label class="col-fixed" htmlFor="notifyRepeat">{{tranService.translate("alert.label.repeat")}}</label>
                                <label class="col-fixed" [style.color]="!repeat ? '#a1a1a1' : '#495057'" htmlFor="notifyInterval">{{tranService.translate("alert.label.frequency")}}</label>
                                <div class="col pl-0 pr-0" style="padding-right: 8px;">
                                    <input class="w-full"
                                           pInputText id="notifyInterval"
                                           [(ngModel)]="alertInfo.notifyInterval"
                                           formControlName="notifyInterval"
                                           type="number"
                                    />
                                </div>
                                <label class="col-fixed" [style.color]="!repeat ? '#a1a1a1' : '#495057'" for="notifyInterval">{{tranService.translate('alert.text.day')}}</label>
                            </div>
                        </div>
                    </div>

                    <div [class]="alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP  && alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD ? '' : 'hidden'" class="flex flex-row">
                        <div style="width: 50px">
                            <div class="col px-4 py-5">
                                <p-checkbox
                                    [(ngModel)]="alertInfo.typeAlert"
                                    name="Group"
                                    formControlName="typeAlert"
                                    value="Group"
                                    [required]="alertInfo.actionType == CONSTANTS.ALERT_ACTION_TYPE.ALERT && alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP  && alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD "
                                ></p-checkbox>
                            </div>
                        </div>
                        <div class="flex-1">
                            <!-- nhom nhan canh bao-->
                            <div class="col-12 flex flex-row justify-content-start align-items-center pb-0 group-alert-div">
                                <label for="listAlertReceivingGroupId" class="col-fixed" style="width:180px">{{tranService.translate("alert.label.groupReceiving")}}<span class="text-red-500"></span></label>
                                <div class="col pl-0 pr-0 pb-0">
                                    <vnpt-select
                                        class="w-full"
                                        [(value)]="alertInfo.listAlertReceivingGroupId"
                                        [placeholder]="tranService.translate('alert.text.inputgroupReceiving')"
                                        objectKey="receivingGroupAlert"
                                        paramKey="name"
                                        keyReturn="id"
                                        displayPattern="${name}"
                                        typeValue="primitive"
                                        [disabled]="true"
                                    ></vnpt-select>
                                </div>
                            </div>
                        </div>
                        <div style="width: 50px;">

                        </div>
                        <div class="flex-1">

                        </div>
                    </div>

                    <div [class]="alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP && alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD ? '' : 'hidden'" class="flex flex-row">
                        <div class="alert-checkbox-email" style="width: 50px">
                            <div class="col px-4 py-5">
                                <p-checkbox
                                    [(ngModel)]="alertInfo.typeAlert"
                                    name="Email"
                                    formControlName="typeAlert"
                                    value="Email"
                                    [required]="alertInfo.actionType == CONSTANTS.ALERT_ACTION_TYPE.ALERT && alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP && alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD "
                                />
                            </div>
                        </div>
                        <div class="flex-1">
                            <!-- email -->
                            <div class="col-12 flex flex-row justify-content-start pb-0 alert-creation-div">
                                <label class="col-fixed" htmlFor="emailList" style="width:180px; height: fit-content;">{{tranService.translate("alert.label.emails")}}<span class="text-red-500">*</span></label>
                                <div style="width: calc(100% - 180px)">
                            <textarea  class="w-full" style="resize: none;"
                                       rows="5"
                                       [autoResize]="false"
                                       pInputTextarea id="emailList"
                                       [(ngModel)]="alertInfo.emailList"
                                       formControlName="emailList"
                                       [placeholder]="tranService.translate('alert.text.inputemails')"
                                       pattern="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(?:, ?[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})*$"
                                       [required]="true"
                            ></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="alert-checkbox-sms" style="width: 50px">
                            <div class="col px-4 py-5">
                                <p-checkbox
                                    [(ngModel)]="alertInfo.typeAlert"
                                    name="SMS"
                                    formControlName="typeAlert"
                                    value="SMS"
                                    [required]="alertInfo.actionType == CONSTANTS.ALERT_ACTION_TYPE.ALERT && alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP && alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD ">
                                </p-checkbox>
                            </div>
                        </div>
                        <div class="flex-1">
                            <!-- sms -->
                            <div class="col-12 flex flex-row justify-content-start pb-0 alert-creation-div">
                                <label class="col-fixed sms-label" htmlFor="smsList" style="width:180px; height: fit-content;">{{tranService.translate("alert.label.sms")}}<span class="text-red-500">*</span></label>
                                <div style="width: calc(100% - 150px)">
                            <textarea  class="w-full" style="resize: none;"
                                       rows="5"
                                       [autoResize]="false"
                                       pInputTextarea id="smsList"
                                       [(ngModel)]="alertInfo.smsList"
                                       formControlName="smsList"
                                       [placeholder]="tranService.translate('alert.text.inputsms')"
                                       pattern="^(?:0|84)\d{9,10}(?:, ?(?:0|84)\d{9,10})*$"
                                       [required]="true"
                            ></textarea>
                                </div>
                            </div>
                        </div>

                    </div>

                    <div [class]="alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP && alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD? '' : 'hidden'" class="flex flex-row">
                        <div style="width: 50px">

                        </div>
                        <div class="flex-1 alert-email-content">
                            <!-- noi dung email -->
                            <div class="col-12 flex flex-row justify-content-start pb-0 alert-creation-div-content">
                                <label class="col-fixed" htmlFor="emailContent" style="width:180px; height: fit-content;">{{tranService.translate("alert.label.contentEmail")}}<span class="text-red-500">*</span></label>
                                <div style="width: calc(100% - 180px);">
                            <textarea  class="w-full" style="resize: none;"
                                       rows="5"
                                       [autoResize]="false"
                                       pInputTextarea id="emailContent"
                                       [(ngModel)]="alertInfo.emailContent"
                                       formControlName="emailContent"
                                       [maxlength]="255"
                                       [placeholder]="tranService.translate('alert.text.inputcontentEmail')"
                                       [required]="true"
                            ></textarea>
                                    <div class="field" *ngIf="formAlertDetail.controls.emailContent.dirty && formAlertDetail.controls.emailContent.errors?.required">
                                        <small class="text-red-500" *ngIf="formAlertDetail.controls.emailContent.dirty && formAlertDetail.controls.emailContent.errors?.required">{{tranService.translate("global.message.required")}}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="alert-hide-div" style="width: 50px">

                        </div>
                        <div class="flex-1 alert-sms-content">
                            <!-- noi dung sms -->
                            <div class="col-12 flex flex-row pb-0 alert-creation-div-content">
                                <label class="col-fixed" htmlFor="smsContent" style="width:180px; height: fit-content;">{{tranService.translate("alert.label.contentSms")}}<span class="text-red-500">*</span></label>
                                <div style="width: calc(100% - 180px);">
                            <textarea  class="w-full" style="resize: none;"
                                       rows="5"
                                       [autoResize]="false"
                                       pInputTextarea id="smsContent"
                                       [(ngModel)]="alertInfo.smsContent"
                                       formControlName="smsContent"
                                       [maxlength]="255"
                                       [placeholder]="tranService.translate('alert.text.inputcontentSms')"
                                       [required]="true"
                            ></textarea>
                                    <!-- error noi dung sms -->
                                    <div class="field"
                                         *ngIf="formAlertDetail.controls.smsContent.dirty && formAlertDetail.controls.smsContent.errors?.required">
                                        <small class="text-red-500" *ngIf="formAlertDetail.controls.smsContent.dirty && formAlertDetail.controls.smsContent.errors?.required">{{tranService.translate("global.message.required")}}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--            error checkbox-->
                    <div class="col" *ngIf="alertInfo.actionType == CONSTANTS.ALERT_ACTION_TYPE.ALERT && alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP">
                        <small class="text-red-500" *ngIf="formAlertDetail.controls.typeAlert.dirty && formAlertDetail.controls.typeAlert.errors?.required">{{tranService.translate("alert.message.checkboxRequired")}}</small>
                    </div>

                    <div *ngIf="alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP || alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD" class="flex flex-row gap-4 p-5 pt-0">
                        <div class="text-xl font-bold">{{tranService.translate("alert.text.sendType")}}</div>
                    </div>

                    <div *ngIf="alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP || alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD" class="flex flex-row gap-4 p-5 pt-0">
                        <div class="flex-1 flex justify-content-center">
                            <p-checkbox
                                [binary]="true"
                                inputId="binary"
                                formControlName="sendTypeEmail"/>
                            <div>&nbsp;Email</div>
                        </div>
                        <div class="flex-1 flex justify-content-center">
                            <p-checkbox
                                [binary]="true"
                                inputId="binary"
                                formControlName="sendTypeSMS" />
                            <div>&nbsp;SMS</div>
                        </div>
                    </div>
                </div>

                <div [class]="alertInfo.actionType == CONSTANTS.ALERT_ACTION_TYPE.API ? '' : 'hidden'" class="pt-0 pb-2 shadow-2 border-round-md m-1 flex p-fluid p-formgrid grid">
                    <div class="flex-1">
                        <!-- url -->
                        <div class="field  px-4 pt-4  flex-row ">
                            <div class="col-12 flex flex-row justify-content-between align-items-center pb-0">
                                <label htmlFor="url" style="width:90px">{{tranService.translate("alert.label.url")}}<span class="text-red-500">*</span></label>
                                <div style="width: calc(100% - 90px)">
                                    <input class="w-full"
                                           [required]="alertInfo.actionType == CONSTANTS.ALERT_ACTION_TYPE.API"
                                           pInputText id="url"
                                           [(ngModel)]="alertInfo.url"
                                           formControlName="url"
                                           [maxLength]="255"
                                           pattern="^(https?|ftp):\/\/[^\s/$.?#].[^\s]*$|^www\.[^\s/$.?#].[^\s]*$|^localhost[^\s]*$|^(?:\d{1,3}\.){3}\d{1,3}[^\s]*$"
                                           [placeholder]="tranService.translate('alert.text.inputurl')"
                                    />
                                </div>
                            </div>
                            <div class="field grid px-4 flex flex-row flex-nowrap pb-2">
                                <label htmlFor="name" style="width:90px; height: fit-content"></label>
                                <div style="width: calc(100% - 90px);padding-right: 8px;">
                                    <small *ngIf="formAlertDetail.controls.url.dirty && formAlertDetail.controls.url.errors?.required" class="text-red-500">{{tranService.translate("global.message.required")}}</small>
                                    <small *ngIf="formAlertDetail.controls.url.errors?.pattern" class="text-red-500">{{tranService.translate("global.message.urlNotValid")}}</small>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </form>
        </p-card>
    </p-dialog>
</div>

<table-vnpt
    [fieldId]="'id'"
    [(selectItems)]="selectItems"
    [columns]="columns"
    [dataSet]="dataSet"
    [options]="optionTable"
    [loadData]="search.bind(this)"
    [pageNumber]="pageNumber"
    [pageSize]="pageSize"
    [sort]="sort"
    [params]="searchInfo"
    [labelTable]="this.tranService.translate('global.menu.alertList')"
></table-vnpt>

<div class="flex justify-content-center">
    <p-dialog class [header]="tranService.translate('global.message.titleConfirmChangeStatusAlert')" [(visible)]="isShowConfimChangeStatus" [modal]="true" [style]="{ width: '750px',  top: '5%'  }" [draggable]="false" [resizable]="false" (onHide)="revertStatus()">
        <div class="flex flex-row justify-content-start align-items-center" >
            <i class="pi pi-exclamation-triangle mr-2" style="font-size: 1.5rem"></i>
            <p>{{tranService.translate("global.message.confirmChangeStatusAlert")}}</p>
        </div>
        <div class="flex flex-row justify-content-end align-items-end mt-3">
            <p-button icon="pi pi-times" styleClass="mr-2 p-button-secondary" [label]="tranService.translate('global.button.no')" (click)="isShowConfimChangeStatus = false">
            </p-button>
            <p-button icon="pi pi-check" styleClass="p-button-info" [label]="tranService.translate('global.button.yes')" (onClick)="changeStatus()" ></p-button>
        </div>
    </p-dialog>
</div>

