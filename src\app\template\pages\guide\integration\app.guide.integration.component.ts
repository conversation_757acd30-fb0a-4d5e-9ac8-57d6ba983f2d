import { Component, ElementRef, Injector, OnInit } from "@angular/core";
import { MenuItem } from "primeng/api";
import { ComponentBase } from "src/app/component.base";
import { LayoutService } from "src/app/service/app.layout.service";
import { GuideService } from "src/app/service/guide/GuideService";
import {Title} from "@angular/platform-browser";
import {CONSTANTS} from "../../../../service/comon/constants";

@Component({
    selector: "app-guide-integration",
    templateUrl: "./app.guide.integration.component.html"
})
export class AppGuideIntegrationComponent extends ComponentBase implements OnInit {
    userInfo: any = {};
    projectInfo: any = {};
    projectTitle: string = "";
    pageInfo: any = {};
    listPages: Array<any> = [];
    listSortedPages: Array<any> = [];
    pathIntegration:string = "/docs/integration"
    keyProject: string = 'integration';
    currentPath: string = this.pathIntegration;
    items: MenuItem[] = [];
    listItems: MenuItem[] = [];
    tabindex: number = 0;
    styleActive = {
        'outline': '0 none',
        'outline-offset': 0,
        'box-shadow': '0 0 0 0.2rem #C7D2FE'
    };
    home: MenuItem = null;
    pathItems: MenuItem[] = [];
    pageTitle: string = null;
    listPageChildren: MenuItem[] = [];
    pageNext: MenuItem = null;
    pagePrevious: MenuItem = null;
    pageContent: string = null;

    constructor(injector: Injector, public layoutService: LayoutService, private guideService: GuideService, private eRef: ElementRef, private titleService:Title) {
        super(injector);
    }

    ngOnInit(): void {
        let me = this;
        this.userInfo = this.sessionService.userInfo;
        this.currentPath = this.router.url
        this.init();
    }

    logout(){
        let me = this;
        this.messageCommonService.onload();
        setTimeout(()=>{
            localStorage.clear();
            me.messageCommonService.offload();
            window.location.hash = '/login';
        }, 500)
    }

    init(){
        this.getProjectInfo();
    }

    getProjectInfo(){
        let me = this;
        me.messageCommonService.onload();
        this.guideService.getProjectInfo({keyProject: me.keyProject}, (response)=>{
            // console.log(response)
            me.projectInfo = response;
            me.projectTitle = me.projectInfo.title["vi"];
            // console.log(me.projectTitle);
            me.pageTitle = me.projectTitle;
            this.titleService.setTitle(this.pageTitle)
            me.getListPage();
        }, null, (type)=>{
            if(type === "error"){
                me.messageCommonService.offload();
            }
        })
    }

    getListPage(){
        let me = this;
        me.messageCommonService.onload();
        this.guideService.getListPage({keyProject: me.keyProject}, (response)=> {
            me.listPages = response;
            me.handleListPage();
            if(me.currentPath != me.pathIntegration){
                me.getPageInfo()
            }else{
                me.goPageHome();
                me.messageCommonService.offload();
            }
        }, null, (type)=>{
            if(type === "error"){
                me.messageCommonService.offload();
            }
        })
    }

    handleListPage(){
        this.sortPage();
    }

    sortPage(){
        let tree = {
            "root": []
        };
        let me = this;
        this.listPages.forEach(page => {
            if(page.parentId == null){
                tree["root"].push(page);
            }else {
                if(page.parentId in tree){
                    tree[page.parentId].push(page);
                }else{
                    tree[page.parentId] = [page];
                }
            }
        });
        Object.keys(tree).forEach(parentKey => {
            let listNode = tree[parentKey];
            let arrSort = [];
            let firstNodes = listNode.filter(el => el.previous == null);
            firstNodes.forEach(node => {
                let n = node;
                while (n != null) {
                    arrSort.push(n);
                    if(n.next != null){
                        n = listNode[listNode.findIndex(el => el.id == n.next)];
                    }else{
                        n = null;
                    }
                }
            });
            tree[parentKey] = arrSort;
        })
        tree["root"].forEach(node => {
            me.addListSortAndLoadChilden(null, node, tree);
        });
        this.items = [...this.items]
        this.tabindex = this.listSortedPages[0].id;
    }

    addListSortAndLoadChilden(parent: MenuItem, node, tree){
        let me = this;
        me.listSortedPages.push(node);
        let mItem: MenuItem = {
            id: node.id,
            label: node.title["vi"],
            routerLink: [`/docs/integration${node.path}`],
            tabindex: node.id,
            title: node.title["vi"],
            command(event) {
                me.handleClickMenu(event,0);
            }
        };
        me.listItems.push(mItem);
        if(node.id in tree){
            tree[node.id].forEach(n => {
                me.addListSortAndLoadChilden(mItem, n, tree);
            })
        }
        if(parent != null){
            if((parent.items || []).length == 0){
                parent.items = [mItem];
            }else{
                parent.items.push(mItem);
            }
        }else{
            me.items.push(mItem);
        }
    }

    handleClickMenu(event, type: 0 | 1){
        let me = this;
        let item: MenuItem = event.item;
        if((item.routerLink || []).length == 0) return;
        this.currentPath = item.routerLink[0];
        this.router.navigate(item.routerLink);
        if(type == 0){//menu
            this.getPageInfo();
            item.style = this.styleActive;
        }else{//breadcrumb
            if(this.currentPath != me.pathIntegration){
                this.getPageInfo();
            }else{
                this.goPageHome();
            }
        }
    }

    clearStyleActive(){
        this.listItems.forEach(el => {
            el.style = null;
            el.expanded = false;
        })
    }

    getPageInfo(){
        console.log("aaaa")
        let me = this;
        me.pageTitle = me.projectTitle;
        me.messageCommonService.onload();
        this.guideService.getPageInfo(this.currentPath.replace(this.pathIntegration, ''), "vi", this.keyProject, (response)=> {
            me.pageInfo = response;
            me.pageTitle = me.pageInfo.title["vi"];
            this.titleService.setTitle(this.pageTitle)
            me.handlePageInfo();
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }

    handlePageInfo(){
        let me = this;
        this.clearStyleActive();
        this.home = { icon: 'pi pi-home', routerLink: ['/docs/integration'], command(event) {
            me.handleClickMenu(event,1);
        }, };
        this.pageContent = null;
        if(this.pageInfo.contentHtml != null){
            this.pageContent = this.pageInfo.contentHtml["vi"];
        }
        let element:Element = this.eRef.nativeElement.querySelector("#page-content");
        if(element != null){
            element.innerHTML = this.pageContent;
        }
        this.pathItems = [];
        let node = this.listPages[this.listPages.findIndex(el => el.id == me.pageInfo.id)];
        let indexSortPage = this.listItems.findIndex(el => el.id == node.id);
        let firstNode = true;
        while(node != null){
            this.pathItems.push({
                label: node.title["vi"],
                routerLink: firstNode ? null : [`/docs/integration${node.path}`],
                command(event) {
                    me.handleClickMenu(event,1);
                },
                tooltip: node.title["vi"].length > 50 ? node.title["vi"] : null
            });
            if(firstNode == true){
                let item = this.listItems[this.listItems.findIndex(el => el.id == node.id)];
                item.style = this.styleActive;
            }
            this.listItems[this.listItems.findIndex(el => el.id == node.id)].expanded = true;
            firstNode = false;
            if(node.parentId != null){
                node = this.listPages[this.listPages.findIndex(el => el.id == node.parentId)];
            }else{
                node = null;
            }
        }
        this.pathItems.reverse();
        if(indexSortPage === 0) this.pagePrevious = null;
        else this.pagePrevious = this.listItems[indexSortPage - 1];
        if(indexSortPage === this.listItems.length - 1) this.pageNext = null;
        else this.pageNext = this.listItems[indexSortPage + 1];
        this.listPageChildren = this.listItems[indexSortPage].items || [];
        this.items = [...this.items];
    }

    goPageHome(){
        this.clearStyleActive();
        this.pageContent = null;
        this.pageTitle = this.projectTitle;
        this.home = null;
        this.pathItems = [];
        this.pageNext = null;
        this.pagePrevious = null;
        this.listPageChildren = [...this.items];
        this.items = [...this.items];
    }

    protected readonly CONSTANTS = CONSTANTS;
}
