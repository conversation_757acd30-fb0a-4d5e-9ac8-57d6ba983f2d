import {NgModule} from "@angular/core";
import {AccountService} from "src/app/service/account/AccountService";
import {CommonModule} from "@angular/common";
import {BreadcrumbModule} from "primeng/breadcrumb";
import {FieldsetModule} from "primeng/fieldset";
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {InputTextModule} from "primeng/inputtext";
import {ButtonModule} from "primeng/button";
import {CommonVnptModule} from "../common-module/common.module";
import {SplitButtonModule} from "primeng/splitbutton";
import {AutoCompleteModule} from "primeng/autocomplete";
import {CalendarModule} from "primeng/calendar";
import {DropdownModule} from "primeng/dropdown";
import {CardModule} from "primeng/card";
import {PanelModule} from "primeng/panel";
import {DialogModule} from "primeng/dialog";
import {InputTextareaModule} from 'primeng/inputtextarea';
import {MultiSelectModule} from 'primeng/multiselect';
import {ListTicketConfigComponent} from "./list-config/app.config.list.component";
import {AppTicketRouting} from "./app.ticket-routing";
import {TicketService} from "../../service/ticket/TicketService";
import {UpdateTicketConfigComponent} from "./update-config/app.config.update.component";
import {ListReplaceSimTicketComponent} from "./list/app.list.replace-sim.component";
import {ListTestSimTicketComponent} from "./list/app.list.test-sim.component";
import { ChipsModule } from 'primeng/chips';
import {ListOrderSimTicketComponent} from "./list/order-sim/app.list.order-sim.component";
import {InputNumberModule} from "primeng/inputnumber";
import {ListActiveSimTicketComponent} from "./list/active-sim/app.list.active-sim.component";
import {ListSimIssuedComponent} from "./list/order-sim/sim-ticket/app.list.sim-issued.component";
import {SimTicketService} from "../../service/ticket/SimTicketService";
import {TableModule} from "primeng/table";
import {LogHandleTicketService} from "../../service/ticket/LogHandleTicketService";
import {ListDiagnoseTicketComponent} from "./list/diagnose/app.list.diagnose.component";

@NgModule({
    imports: [
        AppTicketRouting,
        CommonModule,
        BreadcrumbModule,
        FieldsetModule,
        FormsModule,
        ReactiveFormsModule,
        InputTextModule,
        ButtonModule,
        CommonVnptModule,
        SplitButtonModule,
        AutoCompleteModule,
        CalendarModule,
        DropdownModule,
        CardModule,
        DialogModule,
        InputTextareaModule,
        MultiSelectModule,
        PanelModule,
        ChipsModule,
        InputNumberModule,
        TableModule,
    ],
  declarations: [
    ListTicketConfigComponent,
    UpdateTicketConfigComponent,
    ListReplaceSimTicketComponent,
    ListTestSimTicketComponent,
    ListOrderSimTicketComponent,
    ListActiveSimTicketComponent,
    ListSimIssuedComponent,
    ListDiagnoseTicketComponent,
  ],
  exports: [],
  providers: [
    TicketService,
    AccountService,
    SimTicketService,
    LogHandleTicketService,
  ]
})
export class AppTicketModule {
}
