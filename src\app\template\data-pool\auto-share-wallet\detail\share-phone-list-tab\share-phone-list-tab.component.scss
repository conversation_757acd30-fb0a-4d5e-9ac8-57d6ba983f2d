:host ::ng-deep #ptree {

  .p-treetable-scrollable-header{
    position: sticky;
    top: 0;
    z-index: 1;


    .p-treetable-scrollable-header-box{
      padding-right: 0 !important;
    }

    .p-treetable-thead{
      position: sticky;
      top: 0;

      th{
        background: var(--neutral-40, #F5F7FA);
      }

      th-action{
        width: 80px;

        div{
          padding: 0 16px;
          border-left: 1px solid var(--basic-white, #FFF);
        }
      }
    }
  }

  .p-treetable-scrollable-body{
    tr:focus {
      background: unset !important;
    }

  }
}

.td-action{
  width: 80px;
}

.label-data {
  margin: 1px 5px 1px 0px;
  padding: 0px 5px;
  //font-size: 12px !important;
  //text-transform: none !important;
  border-radius: 0;
  background-color: #f5f5f5;
  //color: #333333;
  //border-left-width: 2px;
}
