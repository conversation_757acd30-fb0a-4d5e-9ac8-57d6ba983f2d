import {AfterContentChecked, Compo<PERSON>, Element<PERSON><PERSON>, Inject, OnInit, Renderer2} from "@angular/core";
import {MenuItem} from "primeng/api";
import {TranslateService} from "../../../service/comon/translate.service";
import {UtilService} from "../../../service/comon/util.service";
import {ActivatedRoute, Route, Router} from "@angular/router";
import {DeviceService} from "../../../service/device/DeviceService";
import {MessageCommonService} from "../../../service/comon/message-common.service";
import {FormBuilder, FormGroup} from "@angular/forms";
import {DomSanitizer, SafeResourceUrl} from '@angular/platform-browser';

@Component({
    selector: "app-apn-sim-list",
    templateUrl: './app.device.detail.component.html',
})
export class AppDeviceDetailComponent implements OnInit, AfterContentChecked {
    safeUrl: SafeResourceUrl;
    items: MenuItem[];
    msisdn = this.activeroute.snapshot.paramMap.get("msisdn");
    home: MenuItem;
    deviceInfo: {
        imei: string | null,
        location: string | null,
        msisdn: number | null,
        country: string | null,
        category: string | null,
        expiredDate: Date | string | null,
        deviceType: string | null,
        iotLink: number | null,
    }
    findCellIDDto: any;
    formDetailDevice: FormGroup;
    minDateTo: Date | number | string | null = null;
    maxDateTo: Date | number | string | null = null;

    constructor(@Inject(TranslateService) public tranService: TranslateService,
                @Inject(UtilService) private utilService: UtilService,
                @Inject(DeviceService) private deviceService: DeviceService,
                private activeroute: ActivatedRoute,
                private messageCommonService: MessageCommonService,
                private formBuilder: FormBuilder,
                private router: Router,
                private sanitizer: DomSanitizer,
                private renderer: Renderer2,
                private el: ElementRef
    ) {
    }

    ngOnInit(): void {
        let me = this;
        this.home = {icon: 'pi pi-home', routerLink: '/'};
        this.items = [{label: this.tranService.translate("global.menu.devicemgmt"), routerLink: "/devices"},
            {label: this.tranService.translate("global.menu.listdevice"), routerLink: "/devices"},
            {label: this.tranService.translate("global.menu.devicedetail")}];
        this.findCellIDDto = null
        // 124 Hoàng Quốc Việt
        // const url = `https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3723.6482875031816!2d105.79183499999999!3d21.046754399999994!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3135ab303bc5f991%3A0x938485f81ec15900!2zMTI0IEhvw6BuZyBRdeG7kWMgVmnhu4d0LCBD4buVIE5odeG6vywgQ-G6p3UgR2nhuqV5LCBIw6AgTuG7mWkgMTAwMDA!5e0!3m2!1svi!2s!4v1715067085700!5m2!1svi!2s`
        //Hà Nội
        const url = `https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d42135.60828498614!2d105.78743105312334!3d21.020807357074563!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3135ab9bd9861ca1%3A0xe7887f7b72ca17a9!2sHanoi%2C%20Vietnam!5e0!3m2!1sen!2s!4v1713255802111!5m2!1sen!2s`;
        this.safeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(url);
        this.deviceInfo = {
            imei: null,
            location: null,
            msisdn: null,
            country: null,
            category: null,
            expiredDate: null,
            deviceType: null,
            iotLink: null,
        }
        this.getDetailDevice();
    }

    goUpdate() {
        this.router.navigate([`/devices/edit/${this.msisdn}`]);
    }

    getDetailDevice() {
        let me = this;
        me.messageCommonService.onload()
        this.deviceService.detailDevice(Number(this.msisdn), (response) => {
            me.deviceInfo = {
                ...response
            }
            if (response.expiredDate != null && response.expiredDate != "") {
                me.deviceInfo.expiredDate = new Date(response.expiredDate);
                me.minDateTo = me.deviceInfo.expiredDate;
                me.maxDateTo = me.deviceInfo.expiredDate;
            } else {
                me.deviceInfo.expiredDate = null;
            }
            me.initForm();
        }, null, () => {
            me.messageCommonService.offload();
        })

        this.deviceService.getLocation(Number(this.msisdn), (response) => {
            if (response != null) {
                me.findCellId(response.mediaDtoResp.cell_lac);
            } else {
                me.deviceInfo.location = " "
                const url = `https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d42135.60828498614!2d105.78743105312334!3d21.020807357074563!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3135ab9bd9861ca1%3A0xe7887f7b72ca17a9!2sHanoi%2C%20Vietnam!5e0!3m2!1sen!2s!4v1713255802111!5m2!1sen!2s`;
                this.safeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(url);
            }
        })
    }

    findCellId(cell_lac){
        let me = this;
        this.deviceService.findCellId({loc: cell_lac.split(":")[1], cell: cell_lac.split(":")[0]}, (response)=>{
            if(response && response.status == "ok"){
                me.findCellIDDto = {
                    lat: response.lat,
                    lng: response.lng
                }
                me.findAddress(response.lat, response.lng)
            }
        }, null, null)
    }

    findAddress(lat, lon){
        let me = this;
        this.deviceService.findAddress(lat, lon, (response)=>{
            me.deviceInfo.location = response.display_name
            const url = `https://www.google.com/maps?q=${me.findCellIDDto.lat},${me.findCellIDDto.lng}&output=embed`;
            me.safeUrl = me.sanitizer.bypassSecurityTrustResourceUrl(url);
        }, null, null)
    }

    initForm() {
        let me = this;
        me.formDetailDevice = me.formBuilder.group({
            imei: [me.deviceInfo.imei],
            location: [me.deviceInfo.location],
            msisdn: [me.deviceInfo.msisdn],
            country: [me.deviceInfo.country],
            expiredDate: [me.utilService.convertDateToString(new Date(this.deviceInfo.expiredDate))],
            deviceType: [me.deviceInfo.deviceType],
            iotLink: [me.deviceInfo.iotLink],
        });
        // me.formDetailDevice.controls["msisdn"].disable();
        me.formDetailDevice.controls["location"].disable();
        if (me.deviceInfo.iotLink === 1) {
            me.formDetailDevice.get("iotLink").setValue(true);
        }
    }

    ngAfterContentChecked(): void {
    }

    ngAfterViewInit() {
        this.setIframeHeight();
    }

    setIframeHeight() {
        const iframe = this.el.nativeElement.querySelector('iframe');
        const width = iframe.offsetWidth;
        this.renderer.setStyle(iframe, 'height', `${width}px`);
    }
}
