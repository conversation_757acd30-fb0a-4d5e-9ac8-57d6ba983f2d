export default{
    label:{
        shareMgmt:"Quản lý chia sẻ",
        sharePhoneNumber:"Số điện thoại chia sẻ",
        shareData:"Chia sẻ dữ liệu",
        fullName:"Họ và tên",
        email:"Email",
        generalInfomation:"Thông tin chung",
        dataWallet:"Ví lưu lượng",
        dataWalletPlaceHolder:"Vui lòng chọn ví lưu lượng",
        shareDate:"Ngày chia sẻ",
        description:"Mô tả",
        receiverPhone:"SĐT người nhận",
        remainData:"Lưu lượng còn lại",
        purchasedData:"Lưu lượng đã mua",
        revokeMessage:"Bạn có muốn thu hồi chia sẻ không ?",
        phone:"SĐT",
        phoneFull:"Số điện thoại",
        revokeData:"Số ${data} cố định",
        sharingData:"<PERSON><PERSON><PERSON> lượng chia sẻ (${type})",
        sharingDataNotType:"<PERSON>ưu lượng chia sẻ",
        percentage:"Tương đương",
        shareInfo:"Thông tin chia sẻ",
        walletCode: "Mã ví",
        packageName: "Gói cước",
        detailSharing:"Chi tiết chia sẻ",
        payCode: "Mã thanh toán",
        trafficType: "Loại lưu lượng",
        packageCode: "Mã gói cước",
        accuracyDateFrom: "Ngày xác thực từ",
        accuracyDateTo: "Ngày xác thực đến",
        usedTimeFrom: "Ngày sử dụng từ",
        usedTimeTo: "Ngày sử dụng đến",
        usedTime: "Thời gian sử dụng",
        accuracyDate: "Ngày xác thực",
        tax: "Mã số thuế",
        accuracyWallet: "Xác thực ví",
        sendType:"Hình thức gửi thông báo",
        appliedSubscription:"Gói cước áp dụng",
        cycle:"Chu kỳ",
        sendNoticeExpired:"Gửi thông báo khi gói sắp hết hiệu lực",
        sendNoticeExpiredBefore:"Gửi thông báo khi gói sắp hết hiệu lực trước",
        day:"Ngày",
        noticeFrequency: "Tần suất tự động gửi thông báo",
        time:"Lần",
        sharedPhone:"SĐT được chia sẻ",
        creator:"Người tạo",
        otpCode:"Mã xác thực",
        enterOtpCode:"Nhập mã xác thưc (OTP)",
        authenMethod: "Phương thức xác thực",
        subCode:"Mã ví",
        stt: "STT",
        created: "Thời gian",
        activity: "Hoạt động",
        content: "Nội dung",
        operator: "Người thao tác",
        sharedTime: "Thời gian chia sẻ",
        usedDate: "Thời gian sử dụng đến",
        shared: "Dung lượng chia sẻ",
        detailWallet: "Chi tiết ví lưu lượng",
        selectWallet:"Chọn ví",
        transactionCode:"Mã giao dịch",
        groupCode: "Mã nhóm",
        groupName: "Tên nhóm",
        detailGroup: "Chi tiết nhóm chia sẻ",
        editGroupShare: "Sửa nhóm chia sẻ",
        createGroupShare: "Tạo nhóm chia sẻ",
        shareGroup: "Nhóm chia sẻ",
        nameSub: "Tên thuê bao",
        shareNormal: "Chia sẻ thường",
        shareByGroup: "Chia sẻ theo nhóm",
        generalInfo: "Thông tin chung",
        listSubOfGroup: "Danh sách thuê bao thuộc nhóm",
        autoShareWalletDetail : "Chi tiết nhóm chia sẻ tự động",
        autoShareWallet : "Danh sách nhóm ví chia sẻ tự động",
        walletList : "Danh sách ví",
        sharePhoneList : "Danh sách SĐT chia sẻ",
        methodAutoShare: "Phương thức chia sẻ tự động",
        notification: "Thông báo",
        registerByPayCode: "Đăng ký theo Mã thanh toán",
        registerBySubCode: "Đăng ký theo Mã ví",
        autoShareGroup: "Danh sách nhóm chia sẻ tự động",
        autoShareGroupDetail: "Chi tiết nhóm ví chia sẻ tự động",
        lstWallet: "Danh sách ví",
        lstSub: "Danh sách SĐT chia sẻ",
        renewalStatus: "Trạng thái gia hạn",
        dateFrom: "Từ ngày",
        dateTo: "Đến ngày",
        timeUpTo: "Thời gian sử dụng đến",
        autoSharing:"Chia sẻ tự động ",
        typeShare: "Loại chia sẻ",
        active: "Hoạt động",
        listShareError : "Danh sách SĐT chia sẻ lỗi",
        downloadErrorFile : "Tải file lỗi",
        addSharePhone : "Thêm người nhận chia sẻ",
        walletCodeShare : "Mã ví/ Mã thanh toán",
        viewPhoneList: "Xem danh sách",
        phoneShareList: "Danh sách số điện thoại chia sẻ",
        status: "Trạng thái",
        msisdnShareSucess: "SĐT chia sẻ thành công",
        msisdnShareFail: "SĐT chia sẻ thất bại",
    },
    button:{
        share:"Chia sẻ lưu lượng",
        equalSharing:"Chia đều",
        fixedAllocation:"Chia cố định",
        revokeSharing:"Thu hồi chia sẻ",
        createWallet: "Thêm ví",
        shareTraffic: "Chia sẻ lưu lượng",
        add:"Thêm",
        delete:"Xoá",
        importFile:"Import người nhận",
        shareByGroup: "Chia sẻ theo nhóm",
        deleteSub: "Xóa thuê bao",
        editSub: "Sửa thuê bao",
        addSharePhone : "Thêm SĐT nhận chia sẻ tự động",
        removeSharePhone : "Xóa SĐT nhận chia sẻ tự động",
        dateFrom : "Thời gian sử dụng đến - Từ ngày",
        dateTo : "Thời gian sử dụng đến - Đến ngày",
        no: "KHÔNG",
        register: "ĐĂNG KÝ",
        yes: "CÓ",
        addSubToGroupAuto: "Thêm SĐT chia sẻ tự động",
        deleteSubInGroupAuto: "Xóa SĐT chia sẻ tự động",
        addShare : "Thêm từng thuê bao"
    },
    text: {
        tax: "Nhập mã số thuế",
        payCode: "Nhập mã thanh toán",
        subCode: "Nhập mã ví",
        importByFile: "Nhập thông tin chia sẻ bằng file",
        importReceive: "Import thông tin người nhận",
        learnMore : "Tìm hiểu thêm",
        notify : "Thông báo",
        nonOTPPayCode: "Sẽ áp dụng chia sẻ không cần OTP cho tất cả các ví thuộc mã thanh toán này",
        nonOTPSubCode: "Sẽ áp dụng chia sẻ không cần OTP cho mã ví này",
        noteAutoShare: "(Khi đăng ký chia sẻ không cần OTP sẽ cho phép ví chia sẻ tự động)",
        noteRegisPayCode: "(Khi đăng ký chia sẻ không cần OTP sẽ cho phép tất cả các ví thuộc Mã thanh toán này chia sẻ tự động)",
        noteCancelSubCode: "(Khi hủy chia sẻ không cần OTP sẽ không cho phép ví chia sẻ tự động. Các thuê bao đang được chia sẻ tự động từ ví này sẽ bị hủy)",
        noteCancelPayCode: "(Khi hủy chia sẻ không cần OTP theo Mã thanh toán, các ví thuộc mã này sẽ không được chia sẻ tự động. Các thuê bao đang được chia sẻ tự động từ các ví này sẽ bị hủy)",
        hasPayCode: "Ví này đã đăng ký chia sẻ không cần OTP theo Mã thanh toán '${payCode}'",
        hasntAuto: "Ví này chưa đăng ký chia sẻ tự động",
        chooseRegistrationMethod : "Hãy chọn một cách đăng ký",
        downloadErrorMessage : "Danh sách có trường không hợp lệ. Hãy kiểm tra file tải về để biết thêm chi tiết"
    },
    placeholder:{
        fullName:"Nhập họ tên",
        phone:"Nhập số điện thoại",
        email:"Nhập email",
        registerShare: "Đăng ký chia sẻ không cần OTP",
        cancelShare: "Hủy chia sẻ không cần OTP",
        activeType: "Chọn loại hoạt động",
        typeShare: "Chọn loại chia sẻ",
    },
    message:{
        otp:"Bạn sẽ nhận được tin nhắn có mã kích hoạt từ M2M",
        resendOtp:"Gửi lại mã xác thực",
        in:"trong",
        sec:"giây",
        patternError:"Sai định dạng. Chỉ cho phép (a-z, A-Z, 0-9, - _t)",
        digitError: "Số điện thoại phải là số có đầu 0 (10-11 kí tự)",
        delete: "Xoá thông tin chia sẻ",
        confirmDelete: "Bạn có muốn xoá thông tin chia sẻ này chứ ?",
        confirmRevoke:"Bạn có muốn thu hồi dung lượng chia sẻ không ?",
        miniumSMS:"Lưu lượng chia sẻ tối thiểu là 10 SMS",
        miniumData:"Lưu lượng chia sẻ tối thiểu là 100 MB",
        existed:"Thuê bao đã tồn tại trong danh sách chia sẻ",
        errorSearch:"Vui lòng chọn ít nhất 1 giá trị tìm kiếm",
        exceededData:"Lưu lượng chia sẻ vượt quá lưu lượng còn lại",
        notValidPhone:"Vui lòng nhập số điện thoại thuộc nhà mạng Vinaphone để thực hiện chia sẻ",
        existedPhone:"Đã tồn tại số điện thoại này",
        otpIncorrect: "Mã OTP chưa chính xác. Vui lòng kiểm tra lại!",
        dataError: "Lưu lượng dữ liệu tối thiểu là 100 MB và là bội số của 100",
        smsError: "Lưu lượng SMS tối thiểu là 10 SMS và là bội số của 5",
        deleteSub: "Bạn có chắc chắn muốn xóa thuê bao đã chọn không?",
        maximumSubAdd: "Chỉ được thêm tối đa 50 thuê bao",
        maximumSubDisplay: "Mỗi nhóm chỉ được thêm tối đa 3000 thuê bao",
        duplicateSub: "Mỗi thuê bao chỉ được phép thuộc 1 nhóm, thuê bao ${data} đang thuộc nhóm '${groupName}' nên không thể thêm vào nhóm này",
        dublicateShareInfo: "Số thuê bao đã được thêm trong danh sách",
        invalidDataUsage : "Lưu lượng tối thiểu là 100MB và là bội số của 100",
        invalidSmsUsage : "Lưu lượng tối thiểu là 10 SMS và là bội số của 5",
        existSharePhone : "Lưu ý: Số điện thoại ${phoneNumber} đã có trong nhóm, Nếu thêm nhiều lần sẽ chia sẻ tự động nhiều lần",
        deleteAutoShare : "Bạn chắc chắn muốn xóa chia sẻ tự động các số đã chọn cho nhóm ví này không?",
        deleteWarnAutoShare : "Nếu xóa khỏi nhóm sẽ ngừng chia sẻ tự động cho SĐT từ nhóm ví này",
        confirmRegisterAutoShareWallet: "Bạn có muốn đăng ký chia sẻ không cần OTP cho ví này không?",
        confirmRegisterAutoSharePayCode: "Bạn có muốn đăng ký chia sẻ không cần OTP cho Mã thanh toán này không?",
        readMore: "Tìm hiểu thêm",
        confirmCancelSubCode:"Bạn có muốn hủy chia sẻ không cần OTP cho ví này không?",
        confirmCancelPayCode:"Bạn có muốn hủy chia sẻ không cần OTP cho Mã thanh toán '${payCode}' không?",
        registerSuccess: "Đăng ký thành công",
        cancelSuccess: "Hủy thành công",
        existPhoneInTable: "Số điện thoại ${phoneNumber} đã có trong bảng",
        exceedTrafficShare: "Lưu lượng chia sẻ cho các thuê bao đã vượt quá ${remainDataWallet} ${typeWallet} còn lại trong ví. Vui lòng giảm lưu lượng chia sẻ hoặc xóa bớt thuê bao hoặc chọn một ví khác",
        shareNotifySuccess : "Đã chia sẻ và thêm vào nhóm thành công cho ${success}/${total} thuê bao có thông tin hợp lệ",
        shareNotifyFail : "Đã chia sẻ và thêm vào nhóm thành công cho ${success}/${total} thuê bao có thông tin hợp lệ. Các thông tin không hợp lệ ở danh sách lỗi",
        addSuccess : "Thêm thành công",
        shareOK: "Chia sẻ thành công",
        shareNotifyBackground: "Yêu cầu đang được thực hiện, quá trình xử lý sẽ mất vài phút do số lượng thuê bao được chia sẻ lớn hơn 50 thuê bao. Vui lòng kiểm tra kết quả sau trong Lịch sử hoạt động"
    },
    activeType:{
      buy: "Mua lưu lượng",
      share: "Chia sẻ lưu lượng",
      accuracy: "Xác thực ví lưu lượng",
      registerNonOTP: "Đăng ký chia sẻ không OTP",
      cancelRegisterNonOTP: "Hủy đăng ký chia sẻ không OTP",
    },
    error:{
        existedGroupCode: "Đã tồn tại mã nhóm chia sẻ này"
    },
    renewStatus : {
        notDueYet : "Chưa đến hạn",
        dueDate : "Đến hạn",
        expired : "Quá hạn"
    },
    methodAutoShare: {
        none: "Chưa đăng ký",
        subCode: "Theo mã ví",
        payCode: "Theo mã thanh toán",
    },
    renewalStatus: {
        notDueYet: "Chưa đến hạn",
        due: "Đến hạn",
        expired: "Hết hạn",
    },
    typeShare:{
        manual: "Thủ công",
        auto: "Tự động"
    },
    activityHistoryStatus: {
        fail: "Thất bại",
        success: "Thành công",
        processing: "Đang xử lý",
        completed: "Hoàn thành",
    }
}
