import { AfterContentChecked, Component, OnInit } from "@angular/core";
import { FormBuilder } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { MenuItem } from "primeng/api";
import { AutoCompleteCompleteEvent } from "primeng/autocomplete";
import { AccountService } from "src/app/service/account/AccountService";
import { CONSTANTS } from "src/app/service/comon/constants";
import { DebounceInputService } from "src/app/service/comon/debounce.input.service";
import { MessageCommonService } from "src/app/service/comon/message-common.service";
import { TranslateService } from "src/app/service/comon/translate.service";
import { UtilService } from "src/app/service/comon/util.service";
import {SessionService} from "../../../service/session/SessionService";
import { CustomerService } from "src/app/service/customer/CustomerService";
import {ColumnInfo, OptionTable} from "../../common-module/table/table.component";
import {ContractService} from "../../../service/contract/ContractService";

@Component({
    selector: "app-account-edit",
    templateUrl: './app.profile.edit.component.html'
})
export class AppProfileEditComponent implements OnInit, AfterContentChecked{
    constructor(private route: ActivatedRoute,
                private router: Router,
                public utilService: UtilService,
                public accountService: AccountService,
                private customerService: CustomerService,
                private contractService: ContractService,
                public tranService: TranslateService,
                public messageCommonService: MessageCommonService,
                private formBuilder: FormBuilder,
                private debounceService: DebounceInputService,
                private sessionService: SessionService) {

    }
    userInfo : any
    items: Array<MenuItem>;
    home: MenuItem;
    accountInfo: {
        accountName: string| null,
        fullName: string|null,
        email: string|null,
        phone: string|null,
        userType: number| null,
        province: any,
        roles: Array<any>,
        description: string|null,
        manager: any,
        customers: Array<any>
    };
    formAccount: any;
    statusAccounts: Array<any>;
    listRole: Array<any>;
    listProvince: Array<any>;
    listCustomer: Array<any>;
    userType: number;
    optionUserType: any;
    isUsernameExisted: boolean = false;
    isEmailExisted: boolean = false;
    isPhoneExisted: boolean = false;
    oldUserType: number | null = null;
    accountResponse: any;
    paginationCustomer: {
        page: number|null,
        size: number|null,
        sortBy: string|null,
    }
    paramQuickSearchCustomer: {
        keyword: string|null,
        accountRootId: number| null,
    }
    dataSetCustomer: {
        content: Array<any>,
        total: number,
    }
    paginationContract: {
        page: number|null,
        size: number|null,
        sortBy: string|null,
    }
    paramQuickSearchContract: {
        keyword: string|null,
        accountRootId: number| null,
        customerIds: Array<{ id: number }>|null,
    }
    dataSetContract: {
        content: Array<any>,
        total: number,
    }
    columnInfoCustomer: Array<ColumnInfo>;
    optionTableCustomer: OptionTable;
    columnInfoContract: Array<ColumnInfo>;
    optionTableContract: OptionTable;
    accountId: number | string;

    isShowSecretKey = true
    listModule = []
    //sẽ lưu lại list api sau khi đã chọn
    selectItemGrantApi: Array<any> = []
    paginationGrantApi: {
        page: number|null,
        size: number|null,
        sortBy: string|null,
    }
    columnInfoGrantApi: Array<ColumnInfo>;

    dataSetGrantApi: {
        content: Array<any>,
        total: number,
    }
    optionTableGrantApi: OptionTable;

    paramsSearchGrantApi = {api : null, module : null}

    genGrantApi = {clientId: '', secretKey: ''}

    statusGrantApi : any;

    isChangeSecretKey : boolean = false;

    ngOnInit(): void {
        this.userInfo = this.sessionService.userInfo;
        this.userType = this.userInfo.type;
        this.accountId = this.userInfo.id;
        this.optionUserType = CONSTANTS.USER_TYPE;
        this.items = [
            { label: this.tranService.translate("global.menu.account"), routerLink:"/profile"  },
            { label: this.tranService.translate("global.menu.editAccount") }
        ];
        this.home = { icon: 'pi pi-home', routerLink: '/' };

        let fullTypeAccount = [
            {name: this.tranService.translate("account.usertype.admin"),value:CONSTANTS.USER_TYPE.ADMIN},
            {name: this.tranService.translate("account.usertype.customer"),value:CONSTANTS.USER_TYPE.CUSTOMER},
            {name: this.tranService.translate("account.usertype.province"),value:CONSTANTS.USER_TYPE.PROVINCE},
            {name: this.tranService.translate("account.usertype.district"),value:CONSTANTS.USER_TYPE.DISTRICT},
            {name: this.tranService.translate("account.usertype.agency"),value:CONSTANTS.USER_TYPE.AGENCY},
        ]
        this.statusAccounts = fullTypeAccount;
        this.accountInfo = {
            accountName: null,
            fullName: null,
            email: null,
            phone: null,
            userType: this.statusAccounts[0].value,
            province: null,
            roles: null,
            description: null,
            manager: null,
            customers: null
        }
        this.paginationGrantApi = {
            page: 0,
            size: 10,
            sortBy: "id,desc",
        }
        this.columnInfoGrantApi = [
            {
                name: "API",
                key: "name",
                size: "30%",
                align: "left",
                isShow: true,
                isSort: true,
            },
            {
                name: "Module",
                key: "module",
                size: "50%",
                align: "left",
                isShow: true,
                isSort: true,
            }
        ]

        this.dataSetGrantApi = {
            content: [],
            total: 0,
        }

        this.optionTableGrantApi = {
            hasClearSelected: false,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
        }
        this.getDetail();
        this.paramQuickSearchCustomer = {
            keyword: null,
            accountRootId: Number(this.accountId),
        }
        this.columnInfoCustomer = [
            {
                name: this.tranService.translate("customer.label.customerCode"),
                key: "code",
                size: "30%",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("customer.label.customerName"),
                key: "name",
                size: "50%",
                align: "left",
                isShow: true,
                isSort: false,
            },
        ]
        this.dataSetCustomer = {
            content: [],
            total: 0,
        }
        this.paginationCustomer = {
            page: 0,
            size: 10,
            sortBy: "name,asc;id,asc",
        }
        this.optionTableCustomer = {
            hasClearSelected: false,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
        }
        this.paramQuickSearchContract = {
            keyword: null,
            accountRootId: Number(this.accountId),
            customerIds: [],
        }
        this.columnInfoContract = [
            {
                name: this.tranService.translate("customer.label.customerCode"),
                key: "customerCode",
                size: "30%",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("customer.label.customerName"),
                key: "customerName",
                size: "50%",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("contract.label.contractCode"),
                key: "contractCode",
                size: "50%",
                align: "left",
                isShow: true,
                isSort: false,
            },
        ]
        this.dataSetContract = {
            content: [],
            total: 0,
        }
        this.paginationContract = {
            page: 0,
            size: 10,
            sortBy: "customerName,asc;id,asc",
        }
        this.optionTableContract = {
            hasClearSelected: false,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
        }
    }

    ngAfterContentChecked(): void {
        if(this.accountInfo.userType != this.oldUserType && this.formAccount){
            this.oldUserType = this.accountInfo.userType;
            this.formAccount.get("province").reset();
            this.formAccount.get("customers").reset();
        }
    }

    checkExistAccount(type){
        let email = null;
        let username = null;
        if(type == "accountName"){
            this.isUsernameExisted = false;
            username = this.accountInfo.accountName;
            if(username == this.accountResponse.username) return;
        }else if(type == "email"){
            this.isEmailExisted = false;
            email = this.accountInfo.email;
            if(email == this.accountResponse.email) return;
        }

        let me = this;

        this.debounceService.set(type, this.accountService.checkAccount.bind(this.accountService), email, username,(response)=>{
            if(response >= 1){
                if(type == "accountName"){
                    me.isUsernameExisted = true;
                }else{
                    me.isEmailExisted = true;
                }
            }
        })
    }

    onSubmitCreate(){
        let dataBody = {
            username: this.accountInfo.accountName,
            fullName: this.accountInfo.fullName,
            description: this.accountInfo.description,
            email: this.accountInfo.email,
            phone: this.accountInfo.phone,
            type: this.accountInfo.userType,
            provinceCode: this.accountInfo.province,
            roleLst: (this.accountInfo.roles|| []).map(el => el.id),
            customerIdLst: (this.accountInfo.customers || []).map(el => el.id),
            statusApi : this.statusGrantApi,
            secretId : this.genGrantApi.secretKey,
            isChangeSecretKey: this.isChangeSecretKey
        }
        if(dataBody.phone != null){
            if(dataBody.phone.startsWith('0')){
                dataBody.phone = "84"+dataBody.phone.substring(1, dataBody.phone.length);
            }else if(dataBody.phone.length == 9 || dataBody.phone.length == 10){
                dataBody.phone = "84"+dataBody.phone;
            }
        }
        this.messageCommonService.onload();
        let me = this;
        this.accountService.updateProfile(dataBody, (response)=>{
            me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"));
            me.router.navigate(['/profile']);
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }

    closeForm(){
        this.router.navigate(['/profile'])
    }

    getDetail(){
        let me = this;
        let accountid = this.userInfo.id;
        this.accountService.viewProfile( (response)=>{
            me.accountResponse = response;
            me.accountInfo.accountName = response.username;
            me.accountInfo.fullName = response.fullName;
            me.accountInfo.email = response.email;
            me.accountInfo.description = response.description;
            me.accountInfo.phone = response.phone;
            me.accountInfo.province = response.provinceCode;
            me.accountInfo.userType = response.type;
            me.formAccount = me.formBuilder.group(me.accountInfo);
            me.formAccount.controls.accountName.disable();
            me.formAccount.controls.userType.disable();
            // me.formAccount.controls.provinceCode.disable();
            if (me.accountInfo.userType == CONSTANTS.USER_TYPE.CUSTOMER) {
                me.resetPaginationCustomerAndContract()
                me.paramQuickSearchCustomer.accountRootId = Number(me.accountId)
                me.paramQuickSearchContract.accountRootId = Number(me.accountId)
                me.paramQuickSearchContract.customerIds = (me.accountResponse.customers|| []).map(customer => customer.customerId)
            }
            me.statusGrantApi = response.statusApi
            me.selectItemGrantApi = response.listApiId? response.listApiId.map(el=> ({id: el})) : [{id:-99}]
            me.genGrantApi.secretKey = response.secretId
            me.genGrantApi.clientId = response.username
            setTimeout(function(){
                me.getListRole(false);
            },100)
        })
    }

    getListRole(isClear){
        let me = this;
        this.accountService.getListRole(this.accountInfo.userType, (response)=>{
            me.listRole = response.map(el => {
                return {
                    id: el.id,
                    name: el.name
                }
            });
            me.formAccount.controls.roles.disable();
            if(isClear){
                me.accountInfo.roles = null;
            }else{
                let roleIds = (me.accountResponse.roles || []).map(el => el.roleId);
                me.accountInfo.roles = me.listRole.filter(el => roleIds.includes(el.id));
            }
        })
    }

    getStringCustomers(){
        return (this.accountResponse.customers || []).map(el => el.customerName + ' - ' + el.customerCode).toLocaleString();
    }

    getStringUserType(value) {
        if(value == CONSTANTS.USER_TYPE.ADMIN){
            return this.tranService.translate("account.usertype.admin");
        }else if(value == CONSTANTS.USER_TYPE.CUSTOMER){
            return this.tranService.translate("account.usertype.customer");
        }else if(value == CONSTANTS.USER_TYPE.PROVINCE){
            return this.tranService.translate("account.usertype.province");
        }else if(value == CONSTANTS.USER_TYPE.DISTRICT){
            return this.tranService.translate("account.usertype.district");
        }else if(value == CONSTANTS.USER_TYPE.AGENCY){
            return this.tranService.translate("account.usertype.agency");
        }else{
            return "";
        }
    }

    getStringRoles(){
        return (this.accountResponse.roles || []).map(el => el.roleName).toLocaleString()
    }
    onTabChange(event) {
        const tabName = event.originalEvent.target.innerText;
        let me = this;
        if (event && tabName.includes(this.tranService.translate('account.text.grantApi'))) {
            me.onSearchGrantApi()
        } else if (event && tabName.includes(this.tranService.translate('global.menu.listbill'))) {
            me.onSearchContract()
        } else if (event && tabName.includes(this.tranService.translate('global.menu.listcustomer'))) {
            me.onSearchCustomer()
        }
    }
    onSearchCustomer(back?) {
        let me = this;
        if (back) {
            me.paginationCustomer.page = 0;
        }
        me.searchCustomer(me.paginationCustomer.page, me.paginationCustomer.size, me.paginationCustomer.sortBy, me.paramQuickSearchCustomer);
    }
    onSearchContract(back?) {
        let me = this;
        if (back) {
            me.paginationContract.page = 0;
        }
        me.searchContract(me.paginationContract.page, me.paginationContract.size, me.paginationContract.sortBy, me.paramQuickSearchContract);
    }
    searchCustomer(page, limit, sort, params){
        let me = this;
        this.paginationCustomer.page = page;
        this.paginationCustomer.size = limit;
        this.paginationCustomer.sortBy = sort;
        let dataParams = {
            page,
            size: limit,
            sort
        }
        Object.keys(this.paramQuickSearchCustomer).forEach(key => {
            if(this.paramQuickSearchCustomer[key] != null){
                dataParams[key] = this.paramQuickSearchCustomer[key];
            }
        })
        me.messageCommonService.onload();
        this.customerService.quickSearchCustomer(dataParams, this.paramQuickSearchCustomer,(response)=>{
            me.dataSetCustomer = {
                content: response.content,
                total: response.totalElements
            }
        }, null, ()=>{
            me.messageCommonService.offload();
        })
        // console.log(this.selectItemCustomer)
    }
    searchContract(page, limit, sort, params){
        let me = this;
        this.paginationContract.page = page;
        this.paginationContract.size = limit;
        this.paginationContract.sortBy = sort;
        let dataParams = {
            page,
            size: limit,
            sort
        }
        // Object.keys(this.paramQuickSearchContract).forEach(key => {
        //     if(this.paramQuickSearchContract[key] != null){
        //         dataParams[key] = this.paramQuickSearchContract[key];
        //     }
        // })
        me.messageCommonService.onload();
        this.contractService.quickSearchContract(dataParams, this.paramQuickSearchContract,(response)=>{
            me.dataSetContract = {
                content: response.content,
                total: response.totalElements
            }
            // console.log(response)
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }
    resetPaginationCustomerAndContract() {
        this.paginationCustomer = {
            page: 0,
            size: 10,
            sortBy: "name,asc;id,asc",
        }
        this.paginationContract = {
            page: 0,
            size: 10,
            sortBy: "customerName,asc;id,asc",
        }
    }

    searchGrantApi(page, limit, sort, params){
        let me = this;
        this.paginationGrantApi.page = page;
        this.paginationGrantApi.size = limit;
        this.paginationGrantApi.sortBy = sort;
        let dataParams = {
            page,
            size: limit,
            sort,
            selectedApiIds: this.selectItemGrantApi.map(el=>el.id).join(',')
        }
        Object.keys(this.paramsSearchGrantApi).forEach(key => {
            if(this.paramsSearchGrantApi[key] != null){
                dataParams[key] = this.paramsSearchGrantApi[key];
            }
        })
        console.log(dataParams)
        me.messageCommonService.onload();
        this.accountService.searchGrantApi(dataParams,(response)=>{
            me.dataSetGrantApi = {
                content: response.content,
                total: response.totalElements
            }
        }, null, ()=>{
            me.messageCommonService.offload();
        })
        let copyParam = {...dataParams};
        copyParam.size = *********;
        this.accountService.searchGrantApi(copyParam,(response)=>{
            me.listModule = [...new Set(response.content.map(el=>el.module))]
            me.listModule = me.listModule.map(el=>({
                name : el,
                value : el
            }))
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }

    generateToken(n) {
        var chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        var token = '';
        for(var i = 0; i < n; i++) {
            token += chars[Math.floor(Math.random() * chars.length)];
        }
        return token;
    }

    genToken(){
        this.genGrantApi.secretKey = this.generateToken(20);
    }

    onSearchGrantApi(back?) {
        let me = this;
        console.log(me.paramsSearchGrantApi)
        if(back) {
            me.paginationGrantApi.page = 0;
        }
        me.searchGrantApi(me.paginationGrantApi.page, me.paginationGrantApi.size, me.paginationGrantApi.sortBy, me.paramsSearchGrantApi);
    }

    protected readonly CONSTANTS = CONSTANTS;
}
