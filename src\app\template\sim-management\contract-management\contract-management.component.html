
<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{this.tranService.translate("contract.label.title")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
<!--    <div class="col-5 flex flex-row justify-content-end align-items-center"></div>-->
</div>
<p-panel class="vnpt-field-set" [styleClass]="'pt-3 pb-2'" [toggleable]="true" [header]="tranService.translate('global.text.filter')">
    <div class="grid">
        <div class="col-3">
            <span class="p-float-label">
                <input class="w-full" (keyup.enter)="onSearch()" pInputText id="centerCode" [(ngModel)]="searchInfo.centerCode" />
                <label htmlFor="centerCode">{{tranService.translate("contract.label.centerCode")}}</label>
            </span>
        </div>
        <div class="col-3">
            <span class="p-float-label">
                <input class="w-full" (keyup.enter)="onSearch()" pInputText id="paymentName" [(ngModel)]="searchInfo.paymentName" />
                <label htmlFor="paymentName">{{tranService.translate("contract.label.paymentName")}}</label>
            </span>
        </div>
        <div class="col-3">
            <span class="p-float-label">
                <input class="w-full" (keyup.enter)="onSearch()" pInputText id="contactPhone" [(ngModel)]="searchInfo.contactPhone" />
                <label htmlFor="contactPhone">{{tranService.translate("contract.label.contactPhone")}}</label>
            </span>
        </div>
        <div class="col-3">
            <span class="p-float-label">
                <input class="w-full" (keyup.enter)="onSearch()" pInputText id="contractor" [(ngModel)]="searchInfo.contractor" />
                <label htmlFor="contractor">{{tranService.translate("contract.label.contractor")}}</label>
            </span>
        </div>
        <div class="col-3">
            <span class="p-float-label">
                <input class="w-full" (keyup.enter)="onSearch()" pInputText id="contractCode" [(ngModel)]="searchInfo.contractCode" />
                <label htmlFor="contractCode">{{tranService.translate("contract.label.contractCode")}}</label>
            </span>
        </div>
        <div class="col-3">
            <span class="p-float-label">
                <input class="w-full" (keyup.enter)="onSearch()" pInputText id="customerCode" [(ngModel)]="searchInfo.customerCode" />
                <label htmlFor="customerCode">{{tranService.translate("contract.label.customerCode")}}</label>
            </span>
        </div>
        <div class="col-3">
            <span class="p-float-label">
                <input class="w-full" (keyup.enter)="onSearch()" pInputText id="customerName" [(ngModel)]="searchInfo.customerName" />
                <label htmlFor="customerName">{{tranService.translate("contract.label.customerName")}}</label>
            </span>
        </div>
        <div class="col-3 pb-0">
            <p-button icon="pi pi-search"
                        styleClass="p-button-rounded p-button-secondary p-button-text button-search"
                        (click)="onSearch()"
            ></p-button>
        </div>
    </div>
</p-panel>
<!-- <div>{{selectItems.length}}</div> -->
<table-vnpt
    [tableId]="'tableListContract'"
    [fieldId]="'id'"
    [(selectItems)]="selectItems"
    [columns]="columns"
    [dataSet]="dataSet"
    [options]="optionTable"
    [loadData]="search.bind(this)"
    [pageNumber]="pageNumber"
    [pageSize]="pageSize"
    [sort]="sort"
    [params]="searchInfo"
    [labelTable]="this.tranService.translate('contract.label.title')"
></table-vnpt>

<p-dialog [header]="simlistheader" [(visible)]="isShowSimList" [modal]="true" [style]="{ width: '90vw' }" [draggable]="false" [resizable]="false">
    <table-vnpt
        [tableId]="'tableSimListInPageContract'"
        [fieldId]="'msisdn'"
        [(selectItems)]="selectItemsSimList"
        [columns]="columsSimList"
        [dataSet]="dataSetSimList"
        [options]="optionTableSimList"
        [loadData]="searchSimList.bind(this)"
        [pageNumber]="pageNumberSimList"
        [pageSize]="pageSizeSimList"
        [sort]="sortSimList"
    ></table-vnpt>
</p-dialog>
