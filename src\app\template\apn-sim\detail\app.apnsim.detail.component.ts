
import {AfterContentChecked, Component, Inject, OnInit} from "@angular/core";
import {MenuItem} from "primeng/api";
import {TranslateService} from "../../../service/comon/translate.service";
import {CONSTANTS} from "../../../service/comon/constants";
import {UtilService} from "../../../service/comon/util.service";
import {ActivatedRoute} from "@angular/router";
import {SimService} from "../../../service/sim/SimService";
import {ApnSimService} from "../../../service/apn/ApnSimService";
@Component({
    selector: "app-apn-sim-list",
    templateUrl: './app.apnsim.detail.conponent.html',
})
export class AppApnSimDetailComponent implements OnInit, AfterContentChecked{
    items: MenuItem[];
    apnId  = this.route.snapshot.paramMap.get("apnId");
    detailApnSim: {
        vpnChannelName: string|null,
        pdpcp: string|null,
        epsProfileId: string|null,
        msisdn: string|null,
        ipType: string|null,
        ip: string|null,
        apnId: string|null,
        apnStatus: string|null,
        deviceImei: string|null,
        customerName: string|null,
        contractCode: Date|string|null,
        contractDate: Date|string|null,
        status: string|null,
        note: string|null,
    }
    fieldsToDisplay = [
        // 'imsi',
        'vpnChannelName',
        'pdpcp',
        'epsProfileId',
        'msisdn',
        'ipType',
        'ip',
        'apnId',
        // 'apnStatus',
        'deviceImei',
        'customerName',
        'contractCode',
        'contractDate',
        'status',
        'note',
    ];
    home: MenuItem;
    constructor(@Inject(TranslateService) public tranService: TranslateService,
                @Inject(UtilService) private utilService: UtilService,
                @Inject(ApnSimService) private apnSimService: ApnSimService,
                private route: ActivatedRoute) {
    }
    ngOnInit(): void {
        let me = this;
        this.home = { icon: 'pi pi-home', routerLink: '/' };
        this.items =[{label: this.tranService.translate("global.menu.apnsim"), routerLink: '/apnsim'},
            {label: this.tranService.translate("global.menu.apnsimlist"), routerLink: '/apnsim'},
            {label: this.tranService.translate("global.menu.apnsimdetail")}]
        this.getDetailApnSim();
    }
    goBack() {
        window.history.back();
    }
    getTitle(key: string): string {
        switch (key) {
            case 'imsi':
                return this.tranService.translate("sim.label.imsi");
            case 'vpnChannelName':
                return this.tranService.translate("sim.label.vpnchannelname");
            case 'pdpcp':
                return this.tranService.translate("sim.label.pdpcp");
            case 'epsProfileId':
                return this.tranService.translate("sim.label.epsprofileid");
            case 'msisdn':
                return this.tranService.translate("sim.label.sothuebao");
            case 'ipType':
                return this.tranService.translate("sim.label.iptype");
            case 'ip':
                return this.tranService.translate("sim.label.ip");
            case 'apnId':
                return this.tranService.translate("sim.label.maapn");
            case 'apnStatus':
                return this.tranService.translate("account.label.status");
            case 'deviceImei':
                return this.tranService.translate("sim.label.imeiDevice");
            case 'customerName':
                return this.tranService.translate("sim.label.khachhang");
            case 'contractCode':
                return this.tranService.translate("sim.label.mahopdong");
            case 'contractDate':
                return this.tranService.translate("sim.label.ngaylamhopdong");
            case 'status':
                return this.tranService.translate("sim.label.trangthaisim");
            case 'note':
                return this.tranService.translate("sim.label.note");
            default:
                return key;
        }
    }

    getContent(key: string): any {
        switch (key) {
            case 'status':
            {
                let value = Number(this.detailApnSim[key]);
                if(value == 0){
                    return this.tranService.translate("sim.status.inventory");
                }else if(value == CONSTANTS.SIM_STATUS.READY){
                    // return this.tranService.translate("sim.status.ready");
                    return this.tranService.translate("sim.status.activated");
                }else if(value == CONSTANTS.SIM_STATUS.ACTIVATED){
                    return this.tranService.translate("sim.status.activated");
                }else if(value == CONSTANTS.SIM_STATUS.DEACTIVATED){
                    return this.tranService.translate("sim.status.deactivated");
                }else if(value == CONSTANTS.SIM_STATUS.PURGED){
                    return this.tranService.translate("sim.status.purged");
                }else if(value == CONSTANTS.SIM_STATUS.INACTIVED){
                    return this.tranService.translate("sim.status.inactivated");
                }else if(value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY){
                    return this.tranService.translate("sim.status.processingChangePlan");
                }else if(value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY){
                    return this.tranService.translate("sim.status.processingRegisterPlan");
                }else if(value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY){
                    return this.tranService.translate("sim.status.waitingCancelPlan");
                }
                return "";
            }
            break;
            case 'ipType':
            {
                if (Number(this.detailApnSim[key]) == CONSTANTS.IP_TYPE.STATIC) {
                    return this.tranService.translate("sim.label.staticIp");
                } else if(Number(this.detailApnSim[key]) == CONSTANTS.IP_TYPE.DYNAMIC) {
                    return this.tranService.translate("sim.label.dynamicIp");
                } else {
                    return this.detailApnSim[key];
                }
            }
            break;
            case 'contractDate':
                return this.utilService.convertDateToString(new Date(this.detailApnSim[key]));
            default:
                return this.detailApnSim[key];
        }
    }
    getDetailApnSim() {
        this.apnSimService.detail(this.apnId, (response)=>{
            this.detailApnSim = response;
        })
    }
    ngAfterContentChecked(): void {
    }

}
