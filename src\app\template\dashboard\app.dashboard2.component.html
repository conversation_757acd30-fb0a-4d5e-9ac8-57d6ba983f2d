<style>
    :host ::ng-deep {
        [pDraggable] {
            cursor: move;
            .button-scale-x {
                cursor: e-resize !important;
            }
            .button-scale-y {
                cursor: n-resize !important;
            }
            .button-scale-z {
                cursor: se-resize !important;
            }
        }
    }
</style>
<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">Dashboard</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
    <div>
        <p-toolbar styleClass="border-none bg-white" [style]="{padding: 0}">
            <div class="p-toolbar-group-start">

            </div>
            <div class="p-toolbar-group-center">
                <!-- <p-multiSelect *ngIf="modeView == objectModeView.UPDATE" class="custom-dropdown-right"
                                [overlayVisible]="false"
                                [style]="{maxWidth:'150px'}"
                                [options]="displayCharts"
                                [(ngModel)]="chartShows"
                                optionLabel="name"
                                filterLocale="vi"
                                (ngModelChange)="changeChartShow()"
                                (onFilter)="onFilterChartname($event)"
                ></p-multiSelect> -->
                <vnpt-select *ngIf="modeView == objectModeView.UPDATE"
                    [control]="chartComboboxController"
                    [(value)]="chartShows"
                    (onchange)="changeChartShow($event)"
                    [isAutoComplete]="false"
                    [isMultiChoice]="true"
                    [options]="displayCharts"
                    paramKey="name"
                    keyReturn="id"
                    displayPattern="${name}"
                    [lazyLoad]="false"
                    [isFilterLocal]="true"
                    typeValue="object"
                    [style]="{maxWidth: '250px', minWidth: '250px'}"
                    [stylePositionBoxSelect]="getBoxSelectStyle()"
                    styleClass="vnpt-select-dashboard"
                >
                </vnpt-select>
            </div>
            <div class="p-toolbar-group-end">
                <p-button *ngIf="modeView == objectModeView.UPDATE" styleClass="p-button-info" [label]="tranService.translate('global.button.save')" (click)="saveConfig()"></p-button>
                <p-button *ngIf="modeView == objectModeView.DETAIL" styleClass="p-button-info" [label]="tranService.translate('global.button.edit')" (click)="openEdit()"></p-button>
            </div>
        </p-toolbar>
    </div>
</div>

<div id="boxWrapper" class="relative mt-2" [style]="{
    minWidth: '100%',
    height: (heightBoxWrapper + 24)+'px',
    width: (widthBoxWrapper + 24) + 'px',
    padding: '12px',
    border: modeView == objectModeView.DETAIL ? 'none' : '1px dashed #777777',
    backgroundColor: modeView == objectModeView.DETAIL ? 'transparent' : '#CCCCCC',
    position: 'relative',
    boxSizing: 'border-box'
}" pDroppable (onDrop)="drop($event)">
    <!-- zIndex: chartPolicy.configPositionObject.zIndex,-->
    <div *ngFor="let chartPolicy of listChartPolicy;let i = index" pDraggable
        (onDragStart)="dragStart($event, chartPolicy)"
        (onDragEnd)="dragEnd($event)" (onDrag)="drag($event)"
        class="box-chart"
         [ngClass]="{ 'stacked-mode': isStackedView }"
         [style]="!isStackedView ? {
              position: 'absolute',
              top: chartPolicy.configPositionObject.top + 'px',
              left: chartPolicy.configPositionObject.left + 'px',
              border: modeView === objectModeView.DETAIL ? 'none' : '1px dashed gray'
            } : null"
        [id]="'chart'+chartPolicy.idChart"
    >
        <div *ngIf="chartPolicy.chartConfig && chartPolicy.status == 1" class="w-full" style="height: 100%;position: relative;">
            <dynamic-chart-vnpt
                [mode]="modeView"
                [chartConfig]="chartPolicy.chartConfig"
                [chartPolicy]="chartPolicy"
                [control]="listDynamicChartController[chartPolicy.chartConfig.id]"
                [width]="chartPolicy.configPositionObject.widthChart"
                [height]="chartPolicy.configPositionObject.heightChart"
                [handleOpenSetting]="handleOpenSetting.bind(this)"
                [isShowButtonExtra]="modeView == objectModeView.UPDATE"
            ></dynamic-chart-vnpt>
            <div *ngIf="modeView == objectModeView.UPDATE" [style.zIndex]="100 + chartPolicy.configPositionObject.zIndex + 1" class="button-scale-x" dragEffect="move"
                pDraggable (onDragStart)="dragXStart($event, chartPolicy)" (onDragEnd)="dragEnd($event)" (onDrag)="dragX($event)"
                style="width: 10px;height: 100%; position: absolute; top: 0;left: calc(100% - 5px);background-color: transparent;"></div>

            <div *ngIf="modeView == objectModeView.UPDATE" [style.zIndex]="100 + chartPolicy.configPositionObject.zIndex + 1" class="button-scale-y" pDraggable dragEffect="move"
            (onDragStart)="dragYStart($event, chartPolicy)" (onDragEnd)="dragEnd($event)" (onDrag)="dragY($event)"
             style="width: 100%;height: 10px; position: absolute; left: 0;top: calc(100% - 5px);background-color: transparent;"></div>

            <div *ngIf="modeView == objectModeView.UPDATE" [style.zIndex]="100 + chartPolicy.configPositionObject.zIndex + 2" class="button-scale-z" pDraggable dragEffect="move"
            (onDragStart)="dragXYStart($event, chartPolicy)" (onDragEnd)="dragEnd($event)" (onDrag)="dragXY($event)"
            style="width: 10px;height: 10px; position: absolute; top: calc(100% - 5px);left: calc(100% - 5px);background-color: transparent;">

            </div>
            <div *ngIf="modeView == objectModeView.UPDATE" class="button-scale-z" style="width: 10px;height: 10px; position: absolute; top: calc(100% - 10px);left: calc(100% - 10px);background-color: transparent;">
                <i class="pi pi-chevron-down" style="font-size: 10px;
                transform: rotate(-45deg);
                vertical-align: top;"></i>
            </div>
        </div>
    </div>
</div>














<p-dialog (onHide)="closeDialog()" [header]="tranService.translate('chart.label.thresholdConfig')" [(visible)]="isShowEditSetting" [modal]="true" [style]="{ width: '700px' }" [draggable]="false" [resizable]="false">
    <div class="w-full field grid p-0 m-0" *ngIf="chartPolicyForcus">
        <div class="w-full field grid" *ngFor="let threshold of chartPolicyForcus.configLevelArray;let i = index">
            <label htmlFor="threshold" class="col-fixed" style="width:150px;max-width: 150px;overflow: hidden; text-overflow: ellipsis;">
                {{tranService.translate("chart.label.threshold")}}&nbsp;{{i + 1}}
            </label>
            <div class="col flex flex-row flex-wrap justify-content-start align-items-center">
                <p-inputNumber
                    class="mr-2" disabled="true"
                    [(ngModel)]="chartPolicyForcus.configLevelArray[i].below"
                    inputId="minmax" mode="decimal" [disabled]="true"
                > </p-inputNumber>
                <p-inputNumber
                    class="mr-2"
                    [(ngModel)]="chartPolicyForcus.configLevelArray[i].above"
                    inputId="minmax" mode="decimal" [disabled]="i < (chartPolicyForcus.configLevelArray.length - 1) || (chartPolicyForcus.configLevelArray || []).length >= 10"
                    [min]="chartPolicyForcus.configLevelArray[i].below + 1"
                > </p-inputNumber>
                <p-button (click)="minusSetting()" styleClass="mr-2 p-button-info" icon="pi pi-minus" *ngIf="i != 0 && i == chartPolicyForcus.configLevelArray.length - 1"></p-button>
                <p-button (click)="createSetting()" icon="pi pi-plus" styleClass="p-button-info" *ngIf="(i == chartPolicyForcus.configLevelArray.length - 1) && (chartPolicyForcus.configLevelArray || []).length < 10" [disabled]="!chartPolicyForcus.configLevelArray[i].above || chartPolicyForcus.configLevelArray[i].above <= chartPolicyForcus.configLevelArray[i].below"></p-button>
            </div>
        </div>
    </div>
</p-dialog>
<p-dialog (onHide)="closeDialog()" [header]="tranService.translate('chart.label.thresholdConfig')" [(visible)]="isShowEditThresholdSetting" [modal]="true" [style]="{ width: '700px' }" [draggable]="false" [resizable]="false">
    <div class="w-full field grid p-0 m-0" *ngIf="chartPolicyForcus">
        <div class="w-full field grid" *ngFor="let threshold of chartPolicyForcus.configLevelArray; let i = index">
            <label htmlFor="threshold" class="col-fixed" style="width:150px; max-width: 150px; overflow: hidden; text-overflow: ellipsis;">
                {{tranService.translate("chart.label.threshold")}}
            </label>

            <div class="col flex flex-row flex-wrap justify-content-start align-items-center">

                <p-slider [(ngModel)]="sliderThreshold" [step]="5" class="col-8 mt-3"
                          [range]="true"
                          [min]="0"
                          [max]="100"
                ></p-slider>

                <lable>{{ sliderThreshold[0] + '% - ' + sliderThreshold[1] + '%'}}</lable>


                <p-button (onClick)="createSliderThreshold()" styleClass="p-button-info ml-3 " [label]="tranService.translate('global.button.add2')">
                </p-button>

            </div>
        </div>
    </div>
</p-dialog>

