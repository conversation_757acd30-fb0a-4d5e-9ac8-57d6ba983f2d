import {Component, Inject, Injector, OnInit} from "@angular/core";
import {MenuItem} from "primeng/api";
import {ComponentBase} from "../../../../../component.base";
import {ColumnInfo, OptionTable} from "../../../../common-module/table/table.component";
import {CONSTANTS} from "../../../../../service/comon/constants";
import {AccountService} from "../../../../../service/account/AccountService";
import {FormBuilder} from "@angular/forms";
import {SimTicketService} from "../../../../../service/ticket/SimTicketService";
import * as XLSX from 'xlsx';

@Component({
    selector: "list-sim-issued-ticket",
    templateUrl: './app.list.sim-issued.component.html'
})

export class ListSimIssuedComponent extends ComponentBase implements OnInit {
    items: MenuItem[];
    home: MenuItem
    searchInfo: {
        imsi: number | null,
        status: number | null,
        allocationDate: Date | null,
        activedDate: Date | null,
        ticketType: number | null,
    };
    columns: Array<ColumnInfo>;
    dataSet: {
        content: Array<any>,
        total: number
    };
    selectItems: Array<any>;
    optionTable: OptionTable;
    pageNumber: number;
    pageSize: number;
    sort: string;
    formSearchSimTicket: any;
    listProvince: Array<any>;
    listTicketType: Array<any>;
    listSimIssuedStatus: Array<any>;
    listEmail: Array<any>;
    userInfo: any
    userType: any

    constructor(
        @Inject(SimTicketService) private simTicketService: SimTicketService,
        @Inject(AccountService) private accountService: AccountService,
        private formBuilder: FormBuilder,
        private injector: Injector) {
        super(injector);
    }

    ngOnInit() {
        let me = this;
        this.userInfo = this.sessionService.userInfo;
        this.userType = CONSTANTS.USER_TYPE;
        this.listTicketType = [
            {
                label: this.tranService.translate('ticket.type.orderSim'),
                value: 2
            }
        ]
        this.listSimIssuedStatus = [
            {
                label: me.tranService.translate("ticket.label.notActivated"),
                value: CONSTANTS.SIM_TICKET_STATUS.NOT_ACTIVATED
            },
            // {
            //     label: me.tranService.translate("ticket.label.awaitingActivation"),
            //     value: CONSTANTS.SIM_TICKET_STATUS.AWAITING_ACTIVATION
            // },
            {
                label: me.tranService.translate("ticket.label.activated"),
                value: CONSTANTS.SIM_TICKET_STATUS.ACTIVATED
            },
        ]
        this.searchInfo = {
            imsi: null,
            status: null,
            activedDate: null,
            allocationDate: null,
            ticketType: CONSTANTS.REQUEST_TYPE.ORDER_SIM
        }
        this.formSearchSimTicket = this.formBuilder.group(this.searchInfo);
        this.columns = [
            {
                name: this.tranService.translate("ticket.label.imsi"),
                key: "imsi",
                size: "fit-content",
                align: "left",
                isShow: true,
                isSort: true
            }, {
                name: this.tranService.translate("ticket.label.allocationDate"),
                key: "allocationDate",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: true,
                funcConvertText(value) {
                    return me.utilService.convertDateToString(new Date(value))
                },
            }, {
                name: this.tranService.translate("ticket.label.activedDate"),
                key: "activedDate",
                size: "fit-content",
                align: "left",
                isShow: true,
                isSort: true,
                funcConvertText(value) {
                    if (value == null) return null;
                    return me.utilService.convertDateToString(new Date(value))
                },
            }, {
                name: this.tranService.translate("ticket.label.status"),
                key: "status",
                size: "fit-content",
                align: "left",
                isShow: true,
                isSort: true,
                funcGetClassname: (value) => {
                    if (value == CONSTANTS.SIM_TICKET_STATUS.NOT_ACTIVATED) {
                        return ['p-2', 'text-white', "bg-blue-400", "border-round", "inline-block"];
                    } else if (value == CONSTANTS.SIM_TICKET_STATUS.AWAITING_ACTIVATION) {
                        return ['p-2', 'text-white', "bg-orange-400", "border-round", "inline-block"];
                    } else if (value == CONSTANTS.SIM_TICKET_STATUS.ACTIVATED) {
                        return ['p-2', 'text-white', "bg-green-500", "border-round", "inline-block"];
                    }
                    return '';
                },
                funcConvertText: function (value) {
                    if (value == CONSTANTS.SIM_TICKET_STATUS.NOT_ACTIVATED) {
                        return me.tranService.translate("ticket.label.notActivated");
                    } else if (value == CONSTANTS.SIM_TICKET_STATUS.AWAITING_ACTIVATION) {
                        return me.tranService.translate("ticket.label.awaitingActivation");
                    } else if (value == CONSTANTS.SIM_TICKET_STATUS.ACTIVATED) {
                        return me.tranService.translate("ticket.label.activated");
                    }
                    return "";
                }
            }
        ];

        this.optionTable = {
            hasClearSelected: false,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
            // action: [
            //     {
            //         icon: "pi pi-window-maximize",
            //         tooltip: this.tranService.translate("global.button.edit"),
            //         func: function (id, item) {
            //             me.handleEditRequest(id, item)
            //         }
            //     }]
        }
        this.pageNumber = 0;
        this.pageSize = 10;
        this.sort = "createdDate,desc"
        this.dataSet = {
            content: [],
            total: 0
        }
        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
    }


    search(page, limit, sort, params) {
        let me = this;
        this.pageNumber = page;
        this.pageSize = limit;
        this.sort = sort;
        let dataParams = {
            page,
            size: limit,
            sort
        }
        Object.keys(this.searchInfo).forEach(key => {
            if (this.searchInfo[key] != null) {
                if (key == "allocationDate") {
                    dataParams["allocationDate"] = this.searchInfo.allocationDate.getTime();
                } else if (key == "activedDate") {
                    dataParams["activedDate"] = this.searchInfo.activedDate.getTime();
                } else {
                    dataParams[key] = this.searchInfo[key];
                }
            }
        })
        this.dataSet = {
            content: [],
            total: 0
        }
        me.messageCommonService.onload();
        this.simTicketService.search(dataParams, (response) => {
            me.dataSet = {
                content: response.content,
                total: response.totalElements
            }
            console.log(response);
        }, null, () => {
            me.messageCommonService.offload();
        })
    }


    onSubmitSearch() {
        this.pageNumber = 0;
        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
    }


    handleEditRequest(id, item) {
        let me = this
    }

    preventCharacter(event) {
        if (event.ctrlKey) {
            return;
        }
        if (event.keyCode == 8 || event.keyCode == 13 || event.keyCode == 37 || event.keyCode == 39) {
            return;
        }
        if (event.keyCode < 48 || event.keyCode > 57) {
            event.preventDefault();
        }
        // Chặn ký tự 'e', 'E' và dấu '+'
        if (event.keyCode == 69 || event.keyCode == 101 || event.keyCode == 107 || event.keyCode == 187) {
            event.preventDefault();
        }
    }

    onExport() {
        let me = this;
        let limitRow = 1000;
        if (me.dataSet.total == 0) {
            me.messageCommonService.warning(me.tranService.translate("ticket.message.empty"));
        } else if (me.dataSet.total  > limitRow) {
            me.messageCommonService.warning(me.tranService.translate("ticket.message.large", {limitRow: limitRow}));
        } else {
            this.export(0, limitRow, this.sort, this.searchInfo);
        }
    }

    export(page, limit, sort, params) {
        let me = this;

        this.pageNumber = page;
        this.pageSize = limit;
        this.sort = sort;
        let dataParams = {
            page,
            size: limit,
            sort
        }
        Object.keys(this.searchInfo).forEach(key => {
            if (this.searchInfo[key] != null) {
                if (key == "allocationDate") {
                    dataParams["allocationDate"] = this.searchInfo.allocationDate.getTime();
                } else if (key == "activedDate") {
                    dataParams["activedDate"] = this.searchInfo.activedDate.getTime();
                } else {
                    dataParams[key] = this.searchInfo[key];
                }
            }
        })
        me.messageCommonService.onload();
        this.simTicketService.search(dataParams, (response) => {
                const headers = [this.tranService.translate("global.text.stt"),
                    this.tranService.translate("ticket.label.imsi"),
                    this.tranService.translate("ticket.label.allocationDate"),
                    this.tranService.translate("ticket.label.activedDate"),
                    this.tranService.translate("ticket.label.status"),
                ];
                const fields = ["imsi", "allocationDate", "activedDate", "status"];
                const fileName = "list_imsi"
                this.exportToExcel(response.content, fields, headers, fileName);

        }, null, () => {
            me.messageCommonService.offload();
        })
    }

    exportToExcel(data: any[], fields: string[], headers: string[], fileName: string): void {
        let val = 1;
        let me = this;
        // Chọn chỉ mục của các trường bạn muốn lấy
        const filteredData = data.map(item => {
            const filteredItem: any = {
                stt: val++
            };
            fields.forEach(field => {
                if (field == 'allocationDate' || field == 'activedDate') {
                    filteredItem[field] = item[field] == null ? '' : me.utilService.convertDateToString(new Date(item[field]))
                } else if (field == 'status') {
                    if (item[field] == CONSTANTS.SIM_TICKET_STATUS.NOT_ACTIVATED) {
                        filteredItem[field] = me.tranService.translate("ticket.label.notActivated");
                    } else if (item[field] == CONSTANTS.SIM_TICKET_STATUS.AWAITING_ACTIVATION) {
                        filteredItem[field] = me.tranService.translate("ticket.label.awaitingActivation");
                    } else if (item[field] == CONSTANTS.SIM_TICKET_STATUS.ACTIVATED) {
                        filteredItem[field] = me.tranService.translate("ticket.label.activated");
                    }
                } else {
                    filteredItem[field] = item[field].toString();
                }
            });
            return filteredItem;
        });
        const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(filteredData);

        // Bôi đậm dòng đầu tiên
        const headerCells = ['A1', 'B1', 'C1'];
        headerCells.forEach(cell => {
            if (!ws[cell]) ws[cell] = {}; // Nếu ô chưa tồn tại, tạo một ô mới
            ws[cell].s = {
                font: {
                    bold: true
                }
            };
        });
        XLSX.utils.sheet_add_aoa(ws, [headers], {origin: 'A1'});
        const columnWidths = {A: {wch: 5}, B: {wch: 20}, C: {wch: 15}, D: {wch: 15}, E: {wch: 15}};
        ws['!cols'] = Object.keys(columnWidths).map(col => ({...{width: columnWidths[col].wch}, ...columnWidths[col]}))
        const wb: XLSX.WorkBook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'Active sim');
        XLSX.writeFile(wb, 'Danh sách SIM.xlsx');
    }
}
