<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{tranService.translate("groupSim.breadCrumb.group")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
<!--    <div class="col-5 flex flex-row justify-content-end align-items-center">-->
<!--        &lt;!&ndash; <a routerLink="/sims/createGroup">-->
<!--            <button pButton [label]="buttonAdd" ></button>-->
<!--        </a> &ndash;&gt;-->
<!--    </div>-->
</div>
<div class="col-14 py-3">
    <div class="card responsive-form" *ngIf="updateGroupForm" >
        <!-- <h5>Create Group</h5> -->
        <form
        action=""
        [formGroup]="updateGroupForm"
        (submit)="submitForm()">
            <div class="p-fluid p-formgrid grid grid-1">
                <!-- group scope -->
                <div class="flex justify-content-between col-12 md:col-12 py-0">
                    <label htmlFor="groupScope" class="col-fixed pl-0" style="min-width: 110px;">{{tranService.translate("groupSim.label.groupScope")}}</label>
                    <div class="col-11 md:col-11 pb-0">
                        <span *ngIf="groupScope == groupScopeObjects.GROUP_ADMIN">{{tranService.translate("groupSim.scope.admin")}}</span>
                        <span *ngIf="groupScope == groupScopeObjects.GROUP_PROVINCE">{{tranService.translate("groupSim.scope.province")}}</span>
                        <span *ngIf="groupScope == groupScopeObjects.GROUP_CUSTOMER">{{tranService.translate("groupSim.scope.customer")}}</span>
                    </div>
                </div>
                <!-- group key code -->
                <div class="flex justify-content-between col-12 md:col-12 py-0">
                    <label htmlFor="groupCode" class="my-auto" style="min-width: 110px;">{{tranService.translate("groupSim.label.groupKey")}}<span class="text-red-500">*</span></label>
                    <div class="col-11 md:col-11 pb-0">
                        <input pInputText id="groupKey" formControlName="groupKey" type="text" [placeholder]="placeHolderGroupKey"  (ngModelChange)="checkExistGroupKey()"/>
                    </div>
                </div>
                <div class="flex justify-content-between col-12 md:col-12 py-0">
                    <div class="my-auto" style="min-width: 110px;"></div>
                    <div class="col-11 md:col-11 py-0">
                        <div *ngIf="updateGroupForm.controls['groupKey']?.dirty && updateGroupForm.controls['groupKey'].hasError('required')" class="text-red-500">
                            {{tranService.translate("groupSim.error.requiredError")}}
                        </div>
                        <div *ngIf="updateGroupForm.controls['groupKey'].hasError('maxlength')" class="text-red-500">
                            {{tranService.translate("groupSim.error.lengthError_16")}}
                        </div>
                        <div *ngIf="updateGroupForm.controls['groupKey'].hasError('invalidCharacters')" class="text-red-500">
                            {{tranService.translate("groupSim.error.characterError_code")}}
                        </div>
                        <div *ngIf="isGroupKeyExists" class="text-red-500">
                            {{tranService.translate("groupSim.error.existedError")}}
                        </div>
                    </div>
                </div>
                <!-- groupname -->
                <div class="flex justify-content-between col-12 md:col-12 py-0">
                    <label htmlFor="groupName" class="my-auto" style="min-width: 110px;">{{tranService.translate("groupSim.label.groupName")}}<span class="text-red-500">*</span></label>
                    <div class="col-11 md:col-11 pb-0">
                        <input pInputText id="name" formControlName="name" type="text" [placeholder]="placeHolderGroupName"/>
                    </div>
                </div>
                <div class="flex justify-content-between col-12 md:col-12 py-0">
                    <div class="my-auto" style="min-width: 110px;"></div>
                    <div class="col-11 md:col-11 py-0">
                        <div *ngIf="updateGroupForm.controls['name']?.dirty && updateGroupForm.controls['name'].hasError('required')" class="text-red-500">
                            {{tranService.translate("groupSim.error.requiredError")}}
                        </div>
                        <div *ngIf="updateGroupForm.controls['name'].hasError('maxlength')" class="text-red-500">
                            {{tranService.translate("groupSim.error.lengthError_255")}}
                        </div>
                        <div *ngIf="updateGroupForm.controls['name'].hasError('invalidCharacters')" class="text-red-500">
                            {{tranService.translate("groupSim.error.characterError_name")}}
                        </div>
                    </div>
                </div>
                <!-- customer -->
                <div *ngIf="groupScope == groupScopeObjects.GROUP_CUSTOMER" class="w-full">
                    <div class="flex justify-content-between col-12 md:col-12 py-0 flex-row">
                        <label htmlFor="customerCode" class="my-auto" style="min-width: 110px;height: fit-content;">{{tranService.translate("groupSim.label.customer")}}<span class="text-red-500">*</span></label>
                        <div class="col-11 md:col-11 pb-0">
                            <span *ngIf="customerDetail">{{customerDetail.customerName}} - {{customerDetail.customerCode}}</span>&nbsp;
                        </div>
                    </div>
<!--                    <div class="flex justify-content-between col-12 md:col-12 py-0">-->
<!--                        <div class="my-auto" style="min-width: 110px;"></div>-->
<!--                        <div class="col-11 md:col-11 py-0">-->
<!--                            <div *ngIf="updateGroupForm.controls['customerCode']?.dirty && updateGroupForm.controls['customerCode'].hasError('required')" class="text-red-500">-->
<!--                                {{tranService.translate("groupSim.error.requiredError")}}-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->
                </div>
                <div *ngIf="groupScope == groupScopeObjects.GROUP_CUSTOMER" class="w-full">
                    <div class="flex justify-content-between col-12 md:col-12 py-0 flex-row">
                        <label htmlFor="customerCode" class="my-auto" style="min-width: 110px;height: fit-content;">{{tranService.translate("groupSim.label.contractCode")}}<span class="text-red-500">*</span></label>
                        <div class="col-11 md:col-11 pb-0">
                            <span *ngIf="contractCode">{{contractCode}}</span>&nbsp;
                        </div>
                    </div>
                </div>
                <!-- province -->
                <div *ngIf="groupScope == groupScopeObjects.GROUP_PROVINCE" class="w-full">
                    <div class="flex justify-content-between col-12 md:col-12 py-0">
                        <label htmlFor="customer" class="my-auto" style="min-width: 110px;">{{tranService.translate("account.label.province")}}<span class="text-red-500">*</span></label>
                        <div class="col-11 md:col-11 pb-0">
                            {{getDisplayProvince()}}
                        </div>
                    </div>
                    <div class="flex justify-content-between col-12 md:col-12 py-0">
                        <div class="my-auto" style="min-width: 110px;"></div>
                        <div class="col-11 md:col-11 py-0">
                            <div *ngIf="updateGroupForm.controls['provinceCode']?.dirty && updateGroupForm.controls['provinceCode'].hasError('required')" class="text-red-500">
                                {{tranService.translate("groupSim.error.requiredError")}}
                            </div>
                        </div>
                    </div>
                </div>
                <!-- description -->
                <div class="flex justify-content-between col-12 md:col-12 py-0">
                    <label htmlFor="description" class="py-3" style="min-width: 110px;">{{tranService.translate("groupSim.label.description")}}</label>
                    <div class="col-11 md:col-11 pb-0">
                        <textarea id="description" rows="5" cols="30" formControlName="description" [class.ng-dirty]="updateGroupForm.controls['description'].invalid " [placeholder]="placeHolderDescription" pInputText></textarea>
                    </div>
                </div>
                <div class="flex justify-content-between col-12 md:col-12 py-0">
                    <div class="my-auto" style="min-width: 110px;"></div>
                    <div class="col-11 md:col-11 pt-0">
                        <div *ngIf="updateGroupForm.controls['description'].hasError('maxlength')" class="text-red-500">
                            {{tranService.translate("groupSim.error.lengthError_255")}}
                        </div>
                    </div>
                </div>
            </div>
            <div class="flex justify-content-center col-12 md:col-12 py-0 gap-3">
                <a routerLink="/sims/group">
                    <button pButton pRipple type="button" [label]="labelBtnCancel" class="p-button-outlined p-button-secondary"></button>
                </a>
                <p-button styleClass="p-button-info" [label]="labelBtnSave" type="submit" [disabled]="updateGroupForm.invalid||isGroupKeyExists"></p-button>
            </div>
        </form>
    </div>
</div>
