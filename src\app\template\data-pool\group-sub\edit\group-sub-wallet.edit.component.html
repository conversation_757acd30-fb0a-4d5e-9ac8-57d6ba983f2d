<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{tranService.translate("groupSim.breadCrumb.group")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
</div>
<form
    action=""
    [formGroup]="editGroupForm"
    (submit)="submitForm()"
    (keydown.enter)="$event.preventDefault()"
    class="responsive-form">

    <div class="mt-3">
        <p-card>
            <div class="gap-4 mt-3 px-2 mb-0">
                <div class="flex flex-column gap-2 flex-1">
                    <label htmlFor="groupCode" class="my-auto" style="min-width: 110px;">{{tranService.translate("groupSim.label.groupKey")}}<span class="text-red-500">*</span></label>
                    <input
                        pInputText
                        id="groupKey"
                        formControlName="groupCode"
                        type="text"
                        [placeholder]="tranService.translate('groupSim.placeHolder.groupKey')"
                        [(ngModel)]="groupInfo.groupCode"
                        (blur)="onCodeBlur()"
                    />
                </div>
                <div class="flex flex-row gap-4 px-2 py-0 m-0">
                    <div *ngIf="editGroupForm.controls['groupCode']?.dirty && editGroupForm.controls['groupCode'].hasError('required')" class="text-red-500">
                        {{tranService.translate("groupSim.error.requiredError")}}
                    </div>
                    <div *ngIf="editGroupForm.controls.groupCode.errors?.['maxlength']" class="text-red-500">
                        {{tranService.translate("groupSim.error.lengthError_16")}}
                    </div>
                    <div *ngIf="editGroupForm.controls.groupCode.errors?.['pattern']" class="text-red-500">
                        {{tranService.translate("groupSim.error.characterError_code")}}
                    </div>
                    <div *ngIf="isExistGroupCode" class="text-red-500">
                        {{tranService.translate("datapool.error.existedGroupCode")}}
                    </div>
                </div>
                <div class="flex flex-column gap-2 flex-1 mt-3">
                    <label htmlFor="groupName" class="my-auto" style="min-width: 110px;">{{tranService.translate("groupSim.label.groupName")}}<span class="text-red-500">*</span></label>
                    <input
                        pInputText
                        id="groupName"
                        formControlName="groupName"
                        type="text"
                        class="w-full"
                        [placeholder]="tranService.translate('groupSim.placeHolder.groupName')"
                        [(ngModel)]="groupInfo.groupName"
                        (blur)="onNameBlur()"
                    />
                </div>
                <div class="flex flex-row gap-4 px-2 py-0 m-0">
                    <div *ngIf="editGroupForm.controls['groupName']?.dirty && editGroupForm.controls['groupName'].errors?.['required']" class="text-red-500">
                        {{tranService.translate("groupSim.error.requiredError")}}
                    </div>
                    <div *ngIf="editGroupForm.controls.groupName.errors?.['maxlength']" class="text-red-500">
                        {{tranService.translate("groupSim.error.lengthError_255")}}
                    </div>
                    <div *ngIf="editGroupForm.controls.groupName.errors?.['pattern']" class="text-red-500">
                        {{tranService.translate("groupSim.error.characterError_name")}}
                    </div>
                </div>
            </div>
            <div class="w-full mt-3 px-2">
                <div class="flex flex-column gap-2 flex-1">
                    <label htmlFor="description">{{tranService.translate("datapool.label.description")}}</label>
                    <textarea
                        class="w-full" style="resize: none;"
                        rows="5"
                        [autoResize]="false"
                        pInputTextarea id="description"
                        formControlName="description"
                        [placeholder]="tranService.translate('sim.text.inputDescription')"
                        [(ngModel)]="groupInfo.description"
                    ></textarea>
                </div>
            </div>
            <div class="w-full field grid px-2 m-0 py-0 mb-3">
                <div *ngIf="editGroupForm.get('description').invalid && editGroupForm.get('description').dirty">
                    <div *ngIf="editGroupForm.get('description').errors.maxlength" class="text-red-500" >{{tranService.translate("global.message.maxLength",{len:255})}}</div>
                </div>
            </div>
            <div class="flex justify-content-center col-12 md:col-12 py-0 gap-3 mt-4">
                <a routerLink="/data-pool/group/listGroupSub">
                    <button pButton pRipple type="button" [label]="tranService.translate('groupSim.label.buttonCancel')" class="p-button-outlined p-button-secondary"></button>
                </a>
                <p-button styleClass="p-button-info" [label]="tranService.translate('groupSim.label.buttonSave')" type="submit" [disabled]="editGroupForm.invalid || isExistGroupCode"></p-button>
            </div>
        </p-card>
    </div>

    <div class="mt-3">
        <p-card>
            <div class="flex justify-content-between responsive-container-2">
                <div class="flex flex-row justify-content-center gap-3">
                    <input style="min-width: 35vw"  type="text" pInputText [placeholder]="tranService.translate('sim.label.quickSearch')" (keydown.enter)="onQuickSearch()" [(ngModel)]="valueSearch" [ngModelOptions]="{standalone: true}" class="search-input-edit">
                    <p-button icon="pi pi-search"
                              styleClass="ml-3 p-button-rounded p-button-secondary p-button-text button-search"
                              type="button"
                              (click)="onQuickSearch()"
                    ></p-button>
                </div>
                <div class="flex flex-wrap justify-content-end gap-3 button-container">
                    <p-button
                        [disabled]="editGroupForm.invalid|| isExistGroupCode"
                        type="button"
                        styleClass="p-button-info"
                        [label]="tranService.translate('global.button.addSubToGroup')"
                        (onClick)="addSubToGroup()"
                    >
                    </p-button>
                    <button pButton [disabled]="editGroupForm.invalid || isExistGroupCode" type="button" [label]="tranService.translate('groupSim.label.addByFile')" (click)="addSubFile()" class="p-button-success"></button>
                    <p-button
                        [disabled]="editGroupForm.invalid || selectItems.length == 0"
                        type="button"
                        styleClass="p-button-secondary"
                        [label]="tranService.translate('global.button.deleteSubInGroup')"
                        (click)="showModalDeleteManySubInGroup()"
                    >
                    </p-button>
                </div>
            </div>
            <div class="mt-6">
                <table-vnpt
                    [tableId]="'tableSubInGroup'"
                    [fieldId]="'id'"
                    [(selectItems)]="selectItems"
                    [columns]="columns"
                    [dataSet]="dataSet"
                    [options]="optionTable"
                    [loadData]="search.bind(this)"
                    [pageNumber]="pageNumber"
                    [pageSize]="pageSize"
                    [sort]="sort"
                    [params]="searchInfo"
                    [labelTable]=""
                ></table-vnpt>
            </div>
        </p-card>
        <p-dialog [style]="{ width: '800px', overflowY :'scroll', maxHeight : '80%', height: '400px' }" [contentStyle]="{'overflow':'visible'}" class="w-full" [header]="tranService.translate('global.button.addSubToGroup')" [(visible)]="isShowDialogAddSub" [modal]="true" [draggable]="false" [resizable]="false">
            <div class="mt-5 flex flex-row gap-3 justify-content-between">
                <div class="flex flex-row gap-3 col-12">
                    <div class="col-5" style="max-width: calc(100% - 1px) !important;">
                        <vnpt-select
                            [(value)]="phoneReceiptSelect"
                            (onchange)="checkValidAdd()"
                            (onSelectItem)="addPhone(phoneReceiptSelect)"
                            [isAutoComplete]="true"
                            [isMultiChoice]="false"
                            paramKey="phoneReceipt"
                            keyReturn="phoneReceipt"
                            styleClass="w-full"
                            [lazyLoad]="true"
                            [placeholder]="tranService.translate('datapool.label.receiverPhone')"
                            displayPattern="${phoneReceipt}"
                            [showClear]="false"
                            [loadData]="getListShareInfoCbb.bind(this)"
                        ></vnpt-select>
                    </div>
                    <button [disabled]="isClickAdd || !isValidPhone" type="button" pButton [label]="tranService.translate('groupSim.label.buttonAddSim')" (click)="addPhoneNotInSelect(phoneReceiptSelect)"></button>
                </div>
            </div>
            <div class="mb-5 flex flex-row gap-3 justify-content-between text-red-500 px-1">
                <div *ngIf="!isValidPhone">
                    {{tranService.translate("datapool.message.digitError")}}
                </div>
            </div>
            <p-table [value]="groupInfo.listSub" [tableStyle]="{ 'min-width': '50rem' }">
                <ng-template pTemplate="header">
                    <tr>
                        <th>{{tranService.translate("datapool.label.phone")}}</th>
                        <th>{{tranService.translate('datapool.label.fullName')}}</th>
                        <th>{{tranService.translate('datapool.label.email')}}</th>
                        <th></th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-list let-index="rowIndex">
                    <tr>
                        <td>{{ list.phoneReceipt }}</td>
                        <td>
                            <input type="text" (input)="changeDataName($event, index)" pInputText [value]="list.name" [readonly]="userInfo.type == CONSTANTS.USER_TYPE.CUSTOMER && list.createdBy!= null &&  list.createdBy != userInfo.id">
                            <div class="text-red-500" *ngIf="list.name?.length > 50">
                                {{tranService.translate("global.message.maxLength",{len:50})}}
                            </div>
                            <div *ngIf="!utilService.checkValidCharacterVietnamese(list.name)" class="text-red-500">
                                {{tranService.translate("global.message.wrongFormatName",{len:150})}}
                            </div>
                        </td>
                        <td>
                            <input type="text" (input)="changeDataMail($event, index)" pInputText [value]="list.email" [readonly]="userInfo.type == CONSTANTS.USER_TYPE.CUSTOMER && list.createdBy!= null &&  list.createdBy != userInfo.id">
                            <div class="text-red-500" *ngIf="list.email?.length > 100">
                                {{tranService.translate("global.message.maxLength",{len:100})}}
                            </div>
                            <div class="text-red-500" *ngIf="isMailInvalid(list.email)">
                                {{tranService.translate("global.message.formatEmail")}}
                            </div>
                        </td>
                        <td><button type="button" pButton class="p-button-outlined" (click)="deleteItem(index, list.id, null)"><i class="pi pi-trash"></i></button></td>
                    </tr>
                </ng-template>
            </p-table>
            <div *ngIf="groupInfo.listSub.length == 0">
                <div class="flex justify-content-center align-items-center" style="height: 100px; min-height: 120px; text-align: center;">
                    <div class="box-item-empty">
                        <span class="pi pi-inbox" style="font-size: x-large;">&nbsp;</span>{{tranService.translate("global.text.nodata")}}
                    </div>
                </div>
            </div>
            <div class="flex justify-content-center col-12 md:col-12 py-0 gap-3 mt-4">
                <button pButton pRipple type="button" [label]="tranService.translate('groupSim.label.buttonCancel')" class="p-button-outlined p-button-secondary" (click)="cancelAddSub()"></button>
                <p-button styleClass="p-button-info" [disabled]="groupInfo.listSub.length == 0 || !isAllEmailsValid()" [label]="tranService.translate('groupSim.label.buttonSave')" (onClick)="saveAddSubToGroup()"></p-button>
            </div>
        </p-dialog>
    </div>
</form>

<p-dialog [header]="tranService.translate('datapool.button.editSub')" [(visible)]="isShowDialogEditSub" [style]="{width: '50vw'}" [draggable]="false" [resizable]="false" [modal]="true" styleClass="responsive-dialog-listShare">
    <form action="" [formGroup]="sharedEditGroup" (submit)="submitEditForm()" class="flex flex-column">
        <div class="px-4 flex flex-row flex-nowrap align-items-center">
            <label htmlFor="fullName"  style="min-width: 140px;">{{tranService.translate("datapool.label.fullName")}}</label>
            <input class="flex-1" formControlName="name" pInputText id="fullName" type="text" [placeholder]="tranService.translate('datapool.placeholder.fullName')">
        </div>
        <div class="px-4 py-0 flex flex-row flex-nowrap align-items-center">
            <label htmlFor="fullName"  style="min-width: 140px;"></label>
            <div class="text-red-500" *ngIf="sharedEditGroup.controls.name.errors?.['required'] && sharedEditGroup.controls.name.dirty">{{tranService.translate("global.message.required")}}</div>
            <div class="text-red-500" *ngIf="sharedEditGroup.controls.name.errors?.['maxlength'] && sharedEditGroup.controls.name.dirty">{{tranService.translate("global.message.maxLength",{len:50})}}</div>
<!--            <div class="text-red-500" *ngIf="( sharedEditGroup.controls.name.errors?.['pattern'] || addPhoneGroup.controls.email.errors?.['email'] ) && sharedEditGroup.controls.name.dirty">{{tranService.translate("global.message.formatCode")}}</div>-->
        </div>
        <div class="px-4 flex flex-row flex-nowrap align-items-center pt-3">
            <label htmlFor="phone"  style="min-width: 140px;">{{tranService.translate("datapool.label.phone")}}<span class="text-red-500">*</span></label>
            <input class="flex-1" formControlName="phone" pInputText id="phone" type="text" [placeholder]="tranService.translate('datapool.placeholder.phone')">
        </div>
        <div class="px-4 py-0 flex flex-row flex-nowrap align-items-center">
            <label htmlFor="fullName"  style="min-width: 140px;"></label>
            <div class="text-red-500" *ngIf="sharedEditGroup.controls.phone.errors?.['required'] && sharedEditGroup.controls.phone.dirty">{{tranService.translate("global.message.required")}}</div>
            <small class="text-red-500" *ngIf="sharedEditGroup.controls.phone.errors?.['pattern']">{{tranService.translate("global.message.invalidPhone")}}</small>
            <div class="text-red-500" *ngIf="sharedEditGroup.controls.phone.errors?.['duplicateItem'] && sharedEditGroup.controls.phone.dirty">{{tranService.translate("datapool.message.existedPhone")}}</div>
        </div>
        <div class="px-4 py-0 flex flex-row flex-nowrap align-items-center">
            <label htmlFor="fullName"  style="min-width: 140px;"></label>
            <div class="text-red-500" *ngIf="sharedEditGroup.controls.phone.errors?.['numericLength'] && sharedEditGroup.controls.phone.dirty">{{tranService.translate("datapool.message.digitError")}}</div>
        </div>
        <div class="px-4 flex flex-row flex-nowrap align-items-center pt-3">
            <label htmlFor="email"  style="min-width: 140px;">{{tranService.translate("datapool.label.email")}}</label>
            <input class="flex-1" formControlName="email" pInputText id="email" type="text" [placeholder]="tranService.translate('datapool.placeholder.email')">
        </div>
        <div class="px-4 pt-0 flex flex-row flex-nowrap align-items-center pb-3">
            <label htmlFor="fullName"  style="min-width: 140px;"></label>
            <div class="text-red-500" *ngIf="sharedEditGroup.controls.email.errors?.['pattern'] && sharedEditGroup.controls.email.dirty">{{tranService.translate("global.message.formatEmail")}}</div>
            <div class="text-red-500" *ngIf="sharedEditGroup.controls.email.errors?.['maxlength'] && sharedEditGroup.controls.email.dirty">{{tranService.translate("global.message.maxLength",{len:100})}}</div>
        </div>
        <div class="flex flex-row gap-2 justify-content-center">
            <button type="button" pButton class="p-button-secondary" [label]="tranService.translate('global.button.cancel')" (click)="closeForm()"></button>
            <button pButton class="" [disabled]="sharedEditGroup.invalid" [label]="tranService.translate('global.button.save')" ></button>
        </div>
    </form>
</p-dialog>

<style>
    .button-reset-file{
        width: fit-content;
        height: fit-content;
        top: 50%;
        position: absolute;
        right: 12px;
        z-index: 2;
        transform: translateY(-50%);
        line-height: 14px;
    }
</style>

<p-dialog [contentStyle]="{'overflow':'visible'}" class="w-full" [header]="tranService.translate('groupSim.label.addPhoneByFile')" [(visible)]="isShowDialogAddFile" [modal]="true" [style]="{ width: '800px', overflowY :'scroll', maxHeight : '80%' }"  [draggable]="false" [resizable]="false" (onHide)="reset()">
    <div class="w-full field grid">
        <div class="col-10 flex flex-column justify-content-start">
            <div class="w-full h-auto flex flex-row justify-content-start align-items-center">
                <div class="relative mr-2" [style]="{'width': (options.isShowButtonUpload?'80%':'100%')}">
                    <div class="h-full w-full absolute top-0 left-0 z-1 opacity-0">
                        <input
                            type="file"
                            [(ngModel)]="formObject.file"
                            class="h-full w-full"
                            [class]="options.disabled?'':'cursor-pointer'"
                            (change)="changeFile($event)"
                            [disabled]="options.disabled"
                        />
                    </div>
                    <div [class]="options.disabled?'bg-black-alpha-10':''"  class="w-full border-1 border-black-alpha-40 border-round border-dotted flex flex-row justify-content-center align-items-center" style="box-sizing: border-box;min-height: 42px;">
                        <div class="max-w-full overflow-hidden text-overflow-ellipsis p-2 pl-4 pr-4 white-space" style="box-sizing: border-box;">
                            {{fileObject?textDescription:tranService.translate("global.button.uploadFile")}}
                        </div>
                    </div>
                    <div *ngIf="fileObject != null && !options.disabled" class="cursor-pointer button-reset-file" (click)="resetFile()">
                        <i class="pi pi-times"></i>
                    </div>
                </div>
            </div>
            <div>
                <small class="text-red-500" *ngIf="invalid =='required'">{{tranService.translate("global.message.required")}}</small>
                <small class="text-red-500" *ngIf="invalid =='maxsize'">{{tranService.translate("global.message.maxsizeupload",{len:50})}}</small>
                <small class="text-red-500" *ngIf="invalid =='invalidtype'">{{options.messageErrorType ? options.messageErrorType : tranService.translate("global.message.invalidtypeupload")}}</small>
            </div>
        </div>
        <div class="col-2 flex flex-row justify-content-end align-items-center">
            <p-button icon="pi pi-download" [pTooltip]="tranService.translate('global.button.downloadTemp')" styleClass="p-button-outlined p-button-secondary" (click)="downloadTemplate()"></p-button>
        </div>
    </div>
    <!--    <div class="grid"><div class="col pt-0"><small class="text-red-500" *ngIf="isShowErrorUpload">{{messageErrorUpload}}</small></div></div>-->
    <div class="flex justify-content-center gap-3">
        <button [disabled]="invalid || fileObject == null || options.disabled" pButton type="button" [pTooltip]="tranService.translate('global.button.upFile')" (click)="upload()">{{tranService.translate("global.button.save")}}</button>
        <button pButton (click)="isShowDialogAddFile = false"  class="p-button-outlined p-button-secondary" type="button">{{tranService.translate("global.button.cancel")}}</button>
    </div>
</p-dialog>
