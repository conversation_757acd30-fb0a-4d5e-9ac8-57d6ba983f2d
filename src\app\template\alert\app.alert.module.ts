import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CheckboxModule } from 'primeng/checkbox';
import { AppAlertRoutingModule } from './app.alert-routing.module';
import { AppAlertListComponent } from './alert-setting/list/app.alert.list.component';
import {BreadcrumbModule} from "primeng/breadcrumb";
import {FieldsetModule} from "primeng/fieldset";
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {InputTextModule} from "primeng/inputtext";
import {ButtonModule} from "primeng/button";
import {CommonVnptModule} from "../common-module/common.module";
import {SplitButtonModule} from "primeng/splitbutton";
import {AutoCompleteModule} from "primeng/autocomplete";
import {CalendarModule} from "primeng/calendar";
import {DropdownModule} from "primeng/dropdown";
import {CardModule} from "primeng/card";
import {DialogModule} from "primeng/dialog";
import {InputTextareaModule} from "primeng/inputtextarea";
import {MultiSelectModule} from "primeng/multiselect";
import {AccountService} from "../../service/account/AccountService";
import { AppAlertCreateComponent } from './alert-setting/create/app.alert.create.component';
import {InputSwitchModule} from "primeng/inputswitch";
import {RadioButtonModule} from "primeng/radiobutton";
import {PanelModule} from "primeng/panel";
import { AppAlertDetailComponent } from './alert-setting/detail/app.alert.detail.component';
import { AppAlertEditComponent } from './alert-setting/edit/app.alert.edit.component';
import {AppGroupReceivingCreateComponent} from "./alert-receiving-group/create/app.group-receiving.create.component";
import { AppGroupReceivingDetailComponent } from './alert-receiving-group/detail/app.group-receiving.detail.component';
import { AppGroupReceivingEditComponent } from './alert-receiving-group/edit/app.group-receiving.edit.component';
import {
    AppAlertsAlertReceivingGroupComponent
} from "./alert-receiving-group/list/app.alerts.alert.receiving.group.component";
import {AppAlertsAlertHistoryComponent} from "../alert/alert-history/app.alerts.alert.history";
import {GroupSimService} from "../../service/group-sim/GroupSimService";
import {SimService} from "../../service/sim/SimService";
import {CustomerService} from "../../service/customer/CustomerService";
import { TrafficWalletService } from 'src/app/service/datapool/TrafficWalletService';
import { TooltipModule } from 'primeng/tooltip';
import {RatingPlanService} from "../../service/rating-plan/RatingPlanService";


@NgModule({
    imports: [
        AppAlertRoutingModule,
        CommonModule,
        BreadcrumbModule,
        FieldsetModule,
        FormsModule,
        ReactiveFormsModule,
        InputTextModule,
        ButtonModule,
        CommonVnptModule,
        SplitButtonModule,
        AutoCompleteModule,
        CalendarModule,
        DropdownModule,
        CardModule,
        DialogModule,
        InputTextareaModule,
        MultiSelectModule,
        InputSwitchModule,
        RadioButtonModule,
        PanelModule,
        CheckboxModule,
        TooltipModule
    ],
    declarations: [
        AppAlertListComponent,
        AppAlertCreateComponent,
        AppAlertDetailComponent,
        AppAlertEditComponent,
        AppGroupReceivingCreateComponent,
        AppGroupReceivingDetailComponent,
        AppGroupReceivingEditComponent,
        AppAlertsAlertReceivingGroupComponent,
        AppAlertsAlertHistoryComponent,
    ],
    providers: [
        AccountService,
        GroupSimService,
        SimService,
        CustomerService,
        TrafficWalletService,
        RatingPlanService
    ]
})
export class AppAlertModule { }

