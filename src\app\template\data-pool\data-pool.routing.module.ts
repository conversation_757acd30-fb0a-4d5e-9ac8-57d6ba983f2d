import {NgModule} from "@angular/core";
import {RouterModule} from "@angular/router";
import DataPage from "src/app/service/data.page";
import { ShareDataComponent } from "./share-mgmt/share-data/share-data.component";
import { ShareDataDetailComponent } from "./share-mgmt/share-data-detail/share-data-detail.component";
import {DataPoolListComponent} from "./trafficWallet/list/data-pool.list.component";
import { WalletConfigComponent } from "./wallet-config/wallet-config.component";
import { ListShareComponent } from "./share-mgmt/list-share/list-share.component";
import {HistoryWalletListComponent} from "./history-wallet/history-wallet.list.component";
import { DataPoolDetailComponent } from "./trafficWallet/detail/data-pool.detail.component";
import { ShareWalletComponent } from "./trafficWallet/share-wallet/share-wallet.component";
import {GroupSubWalletListComponent} from "./group-sub/list/group-sub-wallet.list.component";
import {CONSTANTS} from "../../service/comon/constants";
import {GroupSubWalletCreateComponent} from "./group-sub/create/group-sub-wallet.create.component";
import {GroupSubWalletEditComponent} from "./group-sub/edit/group-sub-wallet.edit.component";
import {AutoShareWalletDetailComponent} from "./auto-share-wallet/detail/auto-share-wallet.detail.component";
import {AutoShareGroupListComponent} from "./auto-share-group/list/auto-share-group.list.component";

@NgModule({
    imports: [
        RouterModule.forChild([
            { path: 'shareMgmt/listShare', component: ListShareComponent , data: new DataPage("global.menu.shareList", [CONSTANTS.PERMISSIONS.DATAPOOL.VIEW_SHARE]) },
            { path: 'shareMgmt/share', component: ShareDataComponent , data: new DataPage("datapool.label.shareData") },
            { path: 'shareMgmt/detail/:id', component: ShareDataDetailComponent , data: new DataPage("datapool.label.detailSharing") },
            { path: 'walletMgmt/share/:id', component: ShareWalletComponent , data: new DataPage("datapool.label.shareData")},
            { path: 'config', component: WalletConfigComponent , data: new DataPage("global.menu.walletConfig")},
            { path: 'walletMgmt/list', component: DataPoolListComponent , data: new DataPage("global.menu.walletList",[CONSTANTS.PERMISSIONS.DATAPOOL.VIEW_WALLET])},
            { path: 'walletMgmt/detail/:id', component: DataPoolDetailComponent , data: new DataPage("global.menu.shareList")},
            { path: 'history', component: HistoryWalletListComponent , data: new DataPage("global.menu.historyWallet",[CONSTANTS.PERMISSIONS.DATAPOOL.VIEW_HISTORY_WALLET])},
            { path: 'group/listGroupSub', component: GroupSubWalletListComponent , data: new DataPage("global.menu.listGroupSub", [CONSTANTS.PERMISSIONS.SHARE_GROUP.VIEW_LIST])},
            { path: 'group/create', component: GroupSubWalletCreateComponent, data: new DataPage("datapool.label.createGroupShare", [CONSTANTS.PERMISSIONS.SHARE_GROUP.CREATE])},
            { path: 'group/edit/:id', component: GroupSubWalletEditComponent, data: new DataPage("datapool.label.editGroupShare", [CONSTANTS.PERMISSIONS.SHARE_GROUP.EDIT])},
            { path: 'auto-share-group/detail/:id', component: AutoShareWalletDetailComponent, data: new DataPage("datapool.label.autoShareWalletDetail", [CONSTANTS.PERMISSIONS.AUTO_SHARE_GROUP.VIEW_DETAIL])},
            {path: 'auto-share-group/list', component: AutoShareGroupListComponent, data: new DataPage("datapool.label.autoShareGroup", [CONSTANTS.PERMISSIONS.AUTO_SHARE_GROUP.VIEW_LIST])},
        ]),
    ],
    exports: [RouterModule],
})

export class DataPoolRoutingModule {}
