import {AfterContentChecked, Component, Inject, Injector, OnInit} from "@angular/core";
import {MenuItem} from "primeng/api";
import {FormBuilder} from "@angular/forms";
import {ColumnInfo, OptionTable} from "../../../common-module/table/table.component";
import {ComponentBase} from "../../../../component.base";
import {CONSTANTS} from "../../../../service/comon/constants";
import {ReceivingGroupService} from "../../../../service/alert/ReceivingGroup";

@Component({
    selector: "app-alerts-alert-receiving-group",
    templateUrl: "./app.alerts.alert.receiving.group.component.html",
})
export class AppAlertsAlertReceivingGroupComponent extends ComponentBase implements OnInit, AfterContentChecked {
    items: MenuItem[];
    home: MenuItem;
    optionTable: OptionTable;
    columns: Array<ColumnInfo>;
    formSearchAlertReceivingGroup: any;
    searchInfoStandard: any;
    selectItems: Array<{ id: number, [key: string]: any }>;
    searchInfo: {
        name: string | null,
        description: string | null,
    }
    pageNumber: number;
    pageSize: number;
    // sort: string;
    dataSet: {
        content: Array<any>,
        total: number,
    };
    formReceivingGroup : any;
    receivingGroupInfo: {
        name: string|null,
        description: string|null,
        emails: Array<any>|null,
        smsList: Array<any>|null,
    };
    dataSetSms: {
        content: Array<any>,
        total: number
    };
    dataSetEmail: {
        content: Array<any>,
        total: number
    };
    rgId: number | string;
    isShowModalDetail: boolean = false;
    selectItemsSms: Array<any> = [];
    selectItemsEmail: Array<any> = [];
    columnsSms: Array<ColumnInfo>;
    columnsEmail: Array<ColumnInfo>;
    optionTableSms: OptionTable;
    optionTableEmail: OptionTable;
    constructor(@Inject(ReceivingGroupService) private alertService: ReceivingGroupService,
                @Inject(ReceivingGroupService) private receivingGroupService: ReceivingGroupService,
                private formBuilder: FormBuilder,
                injector: Injector) {
        super(injector);
    }

    ngOnInit(): void {
        let me = this;
        this.selectItems = []
        this.home = {icon: 'pi pi-home', routerLink: '/'};
        this.items = [{label: this.tranService.translate("global.menu.alerts"), routerLink: '/alerts'}, {label: this.tranService.translate("global.menu.alertreceivinggroup")}];
        this.searchInfoStandard = {
            group: null,
            description: null,
        }
        this.searchInfo = {
            name: null,
            description: null,
        }
        this.selectItemsSms = [];
        this.selectItemsEmail = [];
        this.columnsSms = [
            {
                name: this.tranService.translate("alert.receiving.sms"),
                key: "smsList",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
            },
        ];
        this.columnsEmail = [
            {
                name: this.tranService.translate("alert.receiving.emails"),
                key: "emails",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
            },
        ];
        this.dataSetEmail = {
            content: [],
            total: 0
        }
        this.dataSetSms = {
            content: [],
            total: 0
        }
        this.optionTableEmail = {
            hasClearSelected: false,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
        };
        this.optionTableSms = {
            hasClearSelected: true,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
        };
        this.receivingGroupInfo = {
            name: "nhom1",
            description: null,
            emails: [],
            smsList: [],
        };
        me.formReceivingGroup = me.formBuilder.group(me.receivingGroupInfo);
        me.formReceivingGroup.controls['name'].disable()
        me.formReceivingGroup.controls['description'].disable()
        this.optionTable = {
            hasClearSelected: false,
            hasShowChoose: true,
            hasShowIndex: true,
            hasShowToggleColumn: false,
            action: [
                {
                    icon: "pi pi-file-edit",
                    tooltip: this.tranService.translate("global.button.edit"),
                    func: function (id, item) {
                        me.router.navigate([`/alerts/receiving-group/edit/${id}`]);
                    },
                    funcAppear: function (id, item) {
                        return me.checkAuthen([CONSTANTS.PERMISSIONS.ALERT_RECEIVING_GROUP.UPDATE])
                    }
                },
                {
                    icon: "pi pi-trash",
                    tooltip: this.tranService.translate("global.button.delete"),
                    func: function (id, item) {
                        me.messageCommonService.confirm(
                            me.tranService.translate("global.message.titleConfirmDeleteAlertReceivingGroup"),
                            me.tranService.translate("global.message.confirmDeleteAlertReceivingGroup"),
                            {
                                ok: () => {
                                    me.messageCommonService.onload();
                                    me.alertService.deleleAlertReceivingGroup(id, (response) => {
                                        me.messageCommonService.onload();
                                        me.search(me.pageNumber, me.pageSize, me.searchInfo, () => {
                                            me.messageCommonService.success(me.tranService.translate("global.message.deleteSuccess"));
                                            for (let i = 0; i < me.selectItems.length; i++) {
                                                if (me.selectItems[i].id == id){
                                                    me.selectItems.splice(me.selectItems.indexOf(me.selectItems[i]), 1);
                                                }
                                            }
                                        });
                                    }, null, ()=>{
                                        me.messageCommonService.offload();
                                    })
                                },
                                cancel: () => {

                                }
                            }
                        )
                    },
                    funcAppear: function (id, item) {
                        return me.checkAuthen([CONSTANTS.PERMISSIONS.ALERT_RECEIVING_GROUP.DELETE])
                    }
                },

            ]
        }
        this.columns = [{
            name: this.tranService.translate("alert.label.alertreceivinggroup"),
            key: "name",
            size: "40%",
            align: "left",
            isShow: true,
            isSort: false,
            style: {
                cursor: "pointer",
             color: "var(--mainColorText)"
            },
            funcClick(id, item) {
                me.rgId = id;
                me.getDetail();
                me.searchSms();
                me.searchEmail();
                me.search(0, me.pageSize, me.searchInfo);
                me.isShowModalDetail = true;
            },
        }, {
            name: this.tranService.translate("alert.label.description"),
            key: "description",
            size: "40%",
            align: "left",
            isShow: true,
            isSort: false,
        },
        ]
        this.formSearchAlertReceivingGroup = this.formBuilder.group(this.searchInfo);
        this.pageNumber = 0;
        this.pageSize = 10;
        // this.sort = "name, desc";
        this.dataSet = {
            content: [],
            total: 0
        }
        this.search(this.pageNumber, this.pageSize, this.searchInfo);
        this.messageCommonService.onload();
    }

    onSubmitSearch() {
        this.pageNumber = 0;
        this.messageCommonService.onload();
        this.search(0, this.pageSize, this.searchInfo);
    }

    search(page, limit, params, callback?) {
        this.pageNumber = page;
        this.pageSize = limit;
        // this.sort = sort;
        let me = this;
        let dataParams = {
            page,
            size: limit,
            // sort
        }
        this.updateParams(dataParams);
        me.messageCommonService.onload();
        this.alertService.getListAlertReceivingGroup(dataParams, (response) => {
            me.dataSet = {
                content: response.content,
                total: response.totalElements
            }
            me.searchInfoStandard = {...me.searchInfo}
            if (callback) {
                callback(); // Gọi callback nếu được truyền vào
            }
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }

    updateParams(dataParams) {
        Object.keys(this.searchInfo).forEach(key => {
            if (this.searchInfo[key] != null) {
                dataParams[key] = this.searchInfo[key];
            }
        })
    }

    hasItemsSelected(): boolean {
        return this.selectItems && this.selectItems.length > 0;
    }

    removeMany() {
        let me = this;
        me.messageCommonService.confirm(
            me.tranService.translate("global.message.titleConfirmDeleteAlertReceivingGroup"),
            me.tranService.translate("global.message.confirmDeleteAlertReceivingGroup"),
            {
                ok: () => {
                    me.messageCommonService.onload();
                    me.alertService.deleleListAlertReceivingGroup(this.selectItems.map(item => item.id), (response) => {
                        me.search(this.pageNumber, this.pageSize, this.searchInfo, () =>{
                            me.messageCommonService.success(me.tranService.translate("global.message.deleteSuccess"));
                            me.selectItems = []
                        })
                    }, null, ()=>{
                        me.messageCommonService.offload();
                    })
                },
            }
        )
    }

    getDetail(){
        let me = this;
        me.messageCommonService.onload()
        me.receivingGroupService.getById(Number(me.rgId), (response)=>{
            me.receivingGroupInfo = response;
            me.receivingGroupInfo.emails = response.emails
            me.receivingGroupInfo.smsList = response.msisdns

            if (response.emails != null){
                for (let i = 0; i <response.emails.split(", ").length; i++) {
                    me.dataSetEmail.content.push({emails :response.emails.split(", ")[i]})
                }
            }

            if (response.msisdns != null){
                for (let i = 0; i <response.msisdns.split(", ").length; i++) {
                    me.dataSetSms.content.push({smsList :response.msisdns.split(", ")[i]})
                }
            }
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }

    searchSms(){
        let me = this
        me.dataSetSms = {
            content: [],
            total: 0
        }
    }

    searchEmail(){
        let me = this
        me.dataSetEmail = {
            content: [],
            total: 0
        }
    }

    ngAfterContentChecked(): void {
    }

    protected readonly CONSTANTS = CONSTANTS;
}
