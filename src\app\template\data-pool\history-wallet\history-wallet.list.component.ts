import {Component, Inject, Injector, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild} from "@angular/core";
import {FormBuilder} from "@angular/forms";
import {MenuItem} from "primeng/api";
import {ComponentBase} from "../../../component.base";
import {ColumnInfo, OptionTable, TableVnptComponent} from "../../common-module/table/table.component";
import {TrafficWalletService} from "../../../service/datapool/TrafficWalletService";
import {ReportDynamicFormControl} from "../../reporting/report-dynamic/components/report.dynamic.form.component";
import {CONSTANTS} from "../../../service/comon/constants";
import { DatePipe } from "@angular/common";
import * as XLSX from "xlsx-js-style";
import * as FileSaver from "file-saver";
import {trigger} from "@angular/animations";

@Component({
    selector: "traffic-wallet-list",
    templateUrl: './history-wallet.list.component.html'
})
export class HistoryWalletListComponent extends ComponentBase implements OnInit {
    constructor(injector: Injector, private formBuilder: FormBuilder,
                @Inject(TrafficWalletService) private walletService: TrafficWalletService,
            private datePipe: DatePipe) {
        super(injector);
    }
    columns: Array<ColumnInfo>;
    @ViewChild('phoneList') tablePhone!: TableVnptComponent;
    optionTable: OptionTable;
    dataSet: {
        content: Array<any>,
        total: number
    };
    pageNumber: number;
    pageSize: number;
    sort: string;
    searchInfo: {
        activeType: string | null,
        typeShare: string | null,
        fromDate: Date|null,
        toDate: Date|null,
        status: number | null,
    }
    isShowPhonelist:boolean;
    columnPhones: Array<ColumnInfo>;
    optionTablePhone: OptionTable;
    dataSetPhone: {
        content: Array<any>,
        total: number
    };
    dataSetPhone1: {
        content: Array<any>,
        total: number
    };
    pageNumberPhone: number = 0;
    pageSizePhone: number = 10;
    sortPhone: string = "";
    searchInfoPhone: any;
    styleTable: any;
    formSearch: any;
    activeType: any;
    selectItems: any;
    items: MenuItem[];
    home: MenuItem
    maxDateFrom: Date|number|string|null = new Date();
    minDateTo: Date|number|string|null = null;
    maxDateTo: Date|number|string|null = new Date();
    objectPermissions = CONSTANTS.PERMISSIONS;
    modeForm: number = CONSTANTS.MODE_VIEW.CREATE;
    idReport: number = null;
    reportDynamicFormControl: ReportDynamicFormControl = new ReportDynamicFormControl();
    showContent: boolean =  false
    contentHistory : string = ""
    fullDataSetPhone: any;
    statuses: Array<any> = [];
    transformDate(dateString: string): string {
        const date = new Date(dateString);
        return this.datePipe.transform(date, 'dd/MM/yyyy HH:mm:ss');
    }
    activeTypeOption = [{
        name: this.tranService.translate("datapool.activeType.share"),
        value: CONSTANTS.WALLET_ACITVE_TYPE.SHARE
    },{
        name:this.tranService.translate("datapool.activeType.accuracy"),
        value: CONSTANTS.WALLET_ACITVE_TYPE.ACCURACY
    },{
        name: this.tranService.translate("datapool.activeType.registerNonOTP"),
        value: CONSTANTS.WALLET_ACITVE_TYPE.REGISTER_NO_OTP
    },{
        name:this.tranService.translate("datapool.activeType.cancelRegisterNonOTP"),
        value: CONSTANTS.WALLET_ACITVE_TYPE.CANCEL_REGISTER_NO_OTP
    },]
    typeShareOption=[{
        name: this.tranService.translate("datapool.typeShare.manual"),
        value: 0
    }
        ,{
            name: this.tranService.translate("datapool.typeShare.auto"),
            value: 1
        }]

    ngOnInit(): void {
        let me = this;
        this.items = [{ label: this.tranService.translate("global.menu.trafficManagement"),},{ label: this.tranService.translate("global.menu.historyWallet")},];
        this.home = { icon: 'pi pi-home', routerLink: '/' };
        this.selectItems = [];
        this.searchInfo = {
            activeType: null,
            typeShare: null,
            fromDate: null,
            toDate:null,
            status: null,
        }
        this.formSearch = this.formBuilder.group(this.searchInfo);
        this.activeType = [
            {
                value: CONSTANTS.WALLET_ACITVE_TYPE.BUY,
                name: this.tranService.translate("datapool.activeType.buy")
            },
            {
                value: CONSTANTS.WALLET_ACITVE_TYPE.SHARE,
                name: this.tranService.translate("datapool.activeType.share")
            },
            {
                value: CONSTANTS.WALLET_ACITVE_TYPE.ACCURACY,
                name: this.tranService.translate("datapool.activeType.accuracy")
            }
        ]
        this.columns = [
            {
                name: this.tranService.translate("datapool.label.created"),
                key: "createdDate",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
                funcConvertText(value){
                    if(value == null) return "";
                    return me.transformDate(value);
                }
            },
            {
                name: this.tranService.translate("datapool.label.activity"),
                key: "activeType",
                size: "250px",
                align: "left",
                isShow: true,
                isSort: false,
                funcGetClassname: (value) => {
                    if (value == CONSTANTS.WALLET_ACITVE_TYPE.BUY) {
                        return ['p-2', 'text-green-600', "bg-green-100", "border-round", "inline-block"];
                    } else if (value == CONSTANTS.WALLET_ACITVE_TYPE.SHARE) {
                        return ['p-2', 'text-yellow-600',"bg-yellow-100", "border-round", "inline-block"];
                    } else if (value == CONSTANTS.WALLET_ACITVE_TYPE.ACCURACY){
                        return ['p-2', 'text-cyan-600',"bg-cyan-100", "border-round", "inline-block"];
                    }else if (value == CONSTANTS.WALLET_ACITVE_TYPE.REGISTER_NO_OTP){
                        return ['p-2', 'text-teal-600',"bg-teal-100", "border-round", "inline-block"]
                    }else if (value == CONSTANTS.WALLET_ACITVE_TYPE.CANCEL_REGISTER_NO_OTP){
                        return ['p-2', 'text-red-600',"bg-red-100", "border-round", "inline-block"]
                    }
                    return [];
                },
                funcConvertText: (value) => {
                    if (value == CONSTANTS.WALLET_ACITVE_TYPE.BUY) {
                        return me.tranService.translate("datapool.activeType.buy");
                    } else if(value == CONSTANTS.WALLET_ACITVE_TYPE.SHARE) {
                        return me.tranService.translate("datapool.activeType.share");
                    }else if(value == CONSTANTS.WALLET_ACITVE_TYPE.ACCURACY) {
                        return me.tranService.translate("datapool.activeType.accuracy");
                    }else if (value == CONSTANTS.WALLET_ACITVE_TYPE.REGISTER_NO_OTP){
                        return this.tranService.translate("datapool.activeType.registerNonOTP")
                    }else if (value == CONSTANTS.WALLET_ACITVE_TYPE.CANCEL_REGISTER_NO_OTP){
                        return this.tranService.translate("datapool.activeType.cancelRegisterNonOTP")
                    }
                    return "";
                },
                style: {
                    color: "white"
                },
            },
            {
                name: this.tranService.translate("datapool.label.walletCodeShare"),
                key: "walletCode",
                size: "fit-content",
                align: "left",
                isShow: true,
                isSort: false
            },
            // {
            //     name: this.tranService.translate("datapool.label.content"),
            //     key: "content",
            //     size: "250px",
            //     align: "left",
            //     isShow: true,
            //     isSort: false,
            //     style: {
            //         display: 'inline-block',
            //         maxWidth: '600px',
            //         overflow: 'hidden',
            //         textOverflow: 'ellipsis'
            //     },
            //     funcClick(id, item) {
            //         me.contentHistory = item.content;
            //         me.showContent = true;
            //     }
            // },
            // {
            //     name: this.tranService.translate("datapool.label.phoneFull"),
            //     key: "phoneList",
            //     size: "250px",
            //     align: "left",
            //     isShow: true,
            //     isSort: false,
            //     style: {
            //         color: 'var(--blue-400)',
            //         textDecoration: 'underline',
            //         cursor: 'pointer'
            //     },
            //     funcClick(id, item) {
            //         me.showPhoneList(id);
            //     },
            //     funcConvertText(item){
            //         return me.tranService.translate("datapool.label.viewPhoneList")
            //     }
            // },
            {
                name: this.tranService.translate("datapool.label.status"),
                key: "status",
                size: "100px",
                align: "left",
                isShow: true,
                isSort: false,
                funcConvertText(value, item) {
                    if (value == CONSTANTS.ACTIVITY_HISTORY.STATUS.FAILED) {
                        return me.tranService.translate("datapool.activityHistoryStatus.fail");
                    } else if (value == CONSTANTS.ACTIVITY_HISTORY.STATUS.SUCCESSFUL) {
                        return me.tranService.translate("datapool.activityHistoryStatus.success");
                    } else if (value == CONSTANTS.ACTIVITY_HISTORY.STATUS.PROCESSING) {
                        return me.tranService.translate("datapool.activityHistoryStatus.processing");
                    } else if (value == CONSTANTS.ACTIVITY_HISTORY.STATUS.COMPLETED) {
                        return me.tranService.translate("datapool.activityHistoryStatus.completed");
                    }
                    return ""
                }
            },
            {
                name: this.tranService.translate("datapool.label.msisdnShareSucess"),
                key: "dataResp",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
                style: {
                    color: 'var(--blue-400)',
                    textDecoration: 'underline',
                    cursor: 'pointer'
                },
                funcClick(id, item) {
                    me.showPhoneList(item.dataResp, CONSTANTS.ACTIVITY_HISTORY.PHONE_SHARED_STATUS.SUCCESS);
                },
                funcConvertText(value, item){
                    if (item.activeType != CONSTANTS.ACTIVITY_HISTORY.TYPE.SHARED_TRAFFIC) return "";
                    if (me.countPhoneList(item.dataResp, CONSTANTS.ACTIVITY_HISTORY.PHONE_SHARED_STATUS.SUCCESS) > 0) {
                        return me.tranService.translate("datapool.label.viewPhoneList")
                    }
                    return ""
                },
            },
            {
                name: this.tranService.translate("datapool.label.msisdnShareFail"),
                key: "dataResp",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
                style: {
                    color: 'var(--blue-400)',
                    textDecoration: 'underline',
                    cursor: 'pointer'
                },
                funcClick(id, item) {
                    me.showPhoneList(item.dataResp, CONSTANTS.ACTIVITY_HISTORY.PHONE_SHARED_STATUS.FAILED);
                },
                funcConvertText(value, item){
                    if (item.activeType != CONSTANTS.ACTIVITY_HISTORY.TYPE.SHARED_TRAFFIC) return "";
                    if (me.countPhoneList(item.dataResp, CONSTANTS.ACTIVITY_HISTORY.PHONE_SHARED_STATUS.FAILED) > 0) {
                        return me.tranService.translate("datapool.label.viewPhoneList")
                    }
                    return ""
                }
            },
            {
                name: this.tranService.translate("datapool.label.typeShare"),
                key: "typeShare",
                size: "100px",
                align: "left",
                isShow: true,
                isSort: false,
                funcConvertText: (value) => {
                    if (value == 0) {
                        return me.tranService.translate("datapool.typeShare.manual");
                    } else if(value == 1) {
                        return me.tranService.translate("datapool.typeShare.auto");
                    }
                    return "";
                }
            },
            {
                name: this.tranService.translate("datapool.label.operator"),
                key: "createdName",
                size: "70%",
                align: "left",
                isShow: true,
                isSort: false
            },

        ];
        this.optionTable = {
            hasClearSelected: true,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
        }
        this.statuses = [
            {
              value: CONSTANTS.ACTIVITY_HISTORY.STATUS.FAILED,
              name: me.tranService.translate("datapool.activityHistoryStatus.fail"),
            },
            {
                value: CONSTANTS.ACTIVITY_HISTORY.STATUS.SUCCESSFUL,
                name: me.tranService.translate("datapool.activityHistoryStatus.success"),
            },
            {
                value: CONSTANTS.ACTIVITY_HISTORY.STATUS.PROCESSING,
                name: me.tranService.translate("datapool.activityHistoryStatus.processing"),
            },
            {
                value: CONSTANTS.ACTIVITY_HISTORY.STATUS.COMPLETED,
                name: me.tranService.translate("datapool.activityHistoryStatus.completed"),
            },
        ]
        this.pageNumber = 0;
        this.pageSize= 10;
        this.sort = "createdDate,desc";
        this.dataSet ={
            content: [],
            total: 0
        }
            //

        this.columnPhones = [
            {
                name: this.tranService.translate("datapool.label.phoneFull"),
                key: "phone",
                size: "250px",
                align: "left",
                isShow: true,
                isSort: false
            },
            {
                name: this.tranService.translate("datapool.label.description"),
                key: "error",
                size: "70%",
                align: "left",
                isShow: true,
                isSort: false,
                funcConvertText(value, item){
                    if(item.status == 0){
                        return "Lỗi do thuê bao không hợp lệ";
                    }
                    return value;
                }
            },

        ];
        this.optionTablePhone = {
            hasClearSelected: true,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
            paginator: true
        }
        this.pageNumberPhone = 0;
        this.pageSizePhone= 10;
        this.sortPhone = "createdDate,desc";
        this.dataSetPhone ={
            content: [],
            total: 0
        }
        this.dataSetPhone1 ={
            content: [],
            total: 0
        }
        this.styleTable={
            'margin': '0 !important',
            'padding-top': '10px !important'
        }
        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
        this.pageNumberPhone = 0;
        this.pageSizePhone= 10;
        this.sortPhone = "";
    }

    onSubmitSearch(){
        let me = this;
        me.pageNumber = 0;
        me.search(0, this.pageSize, this.sort, this.searchInfo);
    }
    onChangeDateFrom(value){
        if(value){
            this.minDateTo = value;
        }else{
            this.minDateTo = null
        }
    }

    onChangeDateTo(value){
        if(value){
            this.maxDateFrom = value;
        }else{
            this.maxDateFrom = new Date();
        }
    }
    search(page, limit, sort, params){
        let me = this;
        this.pageNumber = page;
        this.pageSize = limit;

        let dataParams = {
            page,
            size: limit,
            sort
        }
        Object.keys(this.searchInfo).forEach(key => {
            if(this.searchInfo[key] != null) {
                if (key == "fromDate") {
                    dataParams["fromDate"] = this.searchInfo.fromDate.getTime();
                } else if (key == "toDate") {
                    dataParams["toDate"] = this.searchInfo.toDate.getTime();
                } else {
                    dataParams[key] = this.searchInfo[key];
                }
            }
        })
        me.messageCommonService.onload();
        me.walletService.searchActivityHistory(dataParams, (response)=>{
            me.dataSet = {
                content: response.content,
                total: response.totalElements
            }
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }

    // showPhoneList(id){
    //     this.messageCommonService.onload();
    //     this.walletService.getListPhoneShare(id, (response) => {
    //         if(response.data){
    //             this.dataSetPhone.content = JSON.parse(response.data);
    //             this.dataSetPhone.total = this.dataSetPhone.content.length;
    //             this.dataSetPhone.content = this.dataSetPhone.content.map(item => {
    //                 if (item.status === 1 && item.error === "") {
    //                     return { ...item, error: this.tranService.translate("datapool.message.shareOK") };
    //                 }
    //                 return item;
    //             });
    //             this.pageNumberPhone = 0;
    //             this.pageSizePhone= 10;
    //             this.sortPhone = "";
    //             this.fullDataSetPhone = JSON.parse(JSON.stringify(this.dataSetPhone));
    //         }
    //     }, ()=>{}, ()=>{
    //         this.messageCommonService.offload();
    //     })
    //     this.isShowPhonelist = true;
    // }
    showPhoneList(listShare: any, status: number) {
        let me = this;
        if (typeof listShare === 'string') {
            try {
                listShare = JSON.parse(listShare);
            } catch (e) {
                listShare = [];
            }
        }

        if (Array.isArray(listShare)) {
            const filteredList = listShare
                .filter(item => item.status === status)
                .map(el => {
                    if (el.error === "")
                        return {
                            ...el, error: this.tranService.translate("datapool.message.shareOK")
                        };
                    else return el;
                });
            me.dataSetPhone.content = filteredList;
            me.dataSetPhone.total = filteredList.length;
            me.dataSetPhone1.content = filteredList;
            me.dataSetPhone1.total = filteredList.length;
            me.fullDataSetPhone = JSON.parse(JSON.stringify(me.dataSetPhone));
            me.pageNumberPhone = 0;
            me.pageSizePhone= 10;
            me.sortPhone = "";
            me.isShowPhonelist = true;
        } else {
            me.dataSetPhone.content = []
            me.dataSetPhone.total = 0
            me.fullDataSetPhone = [];
            this.isShowPhonelist = true;
        }
    }
    countPhoneList(listShare: any, status: number) {
        let me = this;
        if (listShare == null || listShare == undefined) return 0;
        if (typeof listShare === 'string') {
            try {
                listShare = JSON.parse(listShare);
            } catch (e) {
                listShare = [];
            }
        }
        if (Array.isArray(listShare)) {
            const filteredList = listShare
                .filter(item => item.status == status);
            return filteredList.length;
        }
        return 0;
    }


    searchPhone(){}

    downloadPhoneList() {
        this.exportToExcel(this.dataSetPhone1.content);
    }

    pagingDataPhone(pageNumber, pageSize){
        const startIndex = pageNumber * pageSize;
        const endIndex = startIndex + pageSize;
        this.dataSetPhone.content = this.fullDataSetPhone.content.slice(startIndex, endIndex);
        this.dataSetPhone = {...this.dataSetPhone}
    }

    onHidePhoneList(){
        this.pageNumberPhone = 0;
        this.pageSizePhone= 10;
        this.sortPhone = "";
        this.tablePhone.resetPageNumber();
        this.dataSetPhone.content = [];
    }

    exportToExcel(data) {
        // Chuẩn bị dữ liệu và tiêu đề cột
        const header = ['STT', 'SĐT', 'Mô tả'];
        const excelData = data.map((item, index) => [index+1, item.phone, item.error]);
        console.log(data);

        // Tạo sheet và thêm tiêu đề
        const ws: XLSX.WorkSheet = XLSX.utils.aoa_to_sheet([header, ...excelData]);

        ws['!cols'] = [{wch: 5}, {wch :21}, {wch:30}]

        // Bôi đậm tiêu đề
        const headerCells = ['A1', 'B1', 'C1']; // Các ô tiêu đề trong sheet (tương ứng với cột A, B, C)
        headerCells.forEach(cell => {
            if (ws[cell]) {
                ws[cell].s = {
                    font: {
                        bold: true, // Bôi đậm chữ
                    },
                    alignment: {
                        horizontal: 'center', // Căn giữa theo chiều ngang
                        vertical: 'center', // Căn giữa theo chiều dọc
                    },
                };
            }
        });

        // Căn giữa cho các ô dữ liệu
        const rowCount = data.length;
        for (let row = 2; row <= rowCount + 1; row++) {
            for (let col = 0; col < header.length; col++) {
                const cellRef = XLSX.utils.encode_cell({ r: row - 1, c: col });
                if (ws[cellRef]) {
                    ws[cellRef].s = {
                        alignment: {
                            horizontal: 'center', // Căn giữa theo chiều ngang
                            vertical: 'center', // Căn giữa theo chiều dọc
                        },
                    };
                }
            }
        }

        // Tạo workbook và xuất file
        const wb: XLSX.WorkBook = {
            Sheets: { 'Danh sách SĐT chia sẻ': ws },
            SheetNames: ['Danh sách SĐT chia sẻ'],
        };

        const excelBuffer: any = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
        this.saveAsExcelFile(excelBuffer, 'Danh_sach_chia_se_');
    }

    private saveAsExcelFile(buffer: any, fileName: string): void {
        const data: Blob = new Blob([buffer], { type: EXCEL_TYPE });
        FileSaver.saveAs(data, fileName + '_' + formatDateToDDMMYYYYHHMMSS(new Date().getTime()) + EXCEL_EXTENSION);
    }

    protected readonly CONSTANTS = CONSTANTS;
}



const EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
const EXCEL_EXTENSION = '.xlsx';

function formatDateToDDMMYYYYHHMMSS(timestamp: number): string {
    const date = new Date(timestamp);

    const dd = String(date.getDate()).padStart(2, '0');
    const MM = String(date.getMonth() + 1).padStart(2, '0'); // Tháng bắt đầu từ 0
    const yyyy = date.getFullYear();
    const HH = String(date.getHours()).padStart(2, '0');
    const mm = String(date.getMinutes()).padStart(2, '0');
    const ss = String(date.getSeconds()).padStart(2, '0');

    return `${dd}${MM}${yyyy}${HH}${mm}${ss}`;
}
