<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{tranService.translate("global.menu.alertreceivinggroup")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
    <div class="col-5 flex flex-row justify-content-end align-items-center">
        <button pButton class="p-button-info mr-2"
                  icon="" [routerLink]="['/alerts/receiving-group/create']"
                  routerLinkActive="router-link-active"
                  *ngIf="checkAuthen([CONSTANTS.PERMISSIONS.ALERT_RECEIVING_GROUP.CREATE])">{{tranService.translate('global.button.create')}}</button>

        <button pButton class="mr-2 p-button-secondary p-button-outlined" (click)="removeMany()" *ngIf="checkAuthen([CONSTANTS.PERMISSIONS.ALERT_RECEIVING_GROUP.DELETE])"
                [disabled]="!hasItemsSelected()">{{this.tranService.translate("global.button.delete")}}</button>
    </div>
</div>

<form [formGroup]="formSearchAlertReceivingGroup" (ngSubmit)="onSubmitSearch()" class="pb-2 pt-3 vnpt-field-set">
    <p-panel [toggleable]="true" [header]="tranService.translate('global.text.filter')">
        <div class="grid search-grid-3">
            <!-- Tên nhóm nhận cảnh cáo -->
            <div class="col-4">
                <span class="p-float-label">
                    <input class="w-full" pInputText id="groupName" [(ngModel)]="searchInfo.name"
                           formControlName="name"/>
                    <label htmlFor="name">{{tranService.translate("alert.label.alertreceivinggroup")}}</label>
                </span>
            </div>
            <!-- Mô tả-->
            <div class="col-4">
                <span class="p-float-label">
                    <input pInputText class="w-full" pInputText id="description" [(ngModel)]="searchInfo.description"
                           formControlName="description"/>
                    <label htmlFor="description">{{tranService.translate("alert.label.description")}}</label>
                </span>
            </div>
            <div class="col-4">
                <div class="w-5">
                    <p-button icon="pi pi-search"
                              styleClass="p-button-rounded p-button-secondary p-button-text button-search"
                              type="submit"></p-button>
                </div>
            </div>
        </div>
    </p-panel>
</form>

<div class="flex justify-content-center dialog-vnpt">
    <p-dialog [header]="tranService.translate('global.button.view')" [(visible)]="isShowModalDetail" [modal]="true" [style]="{ width: '980px' }" [draggable]="false" [resizable]="false">
        <form action="" [formGroup]="formReceivingGroup" *ngIf="isShowModalDetail">
            <div class="pt-0 shadow-2 border-round-md m-1 flex p-fluid p-formgrid grid">
                <div class="col-8">
                    <div class="w-full field grid ">
                        <!--  name -->
                        <label htmlFor="name" class="col-fixed alert-group-label" style="width:250px">{{tranService.translate("alert.receiving.name")}}<span class="text-red-500">*</span></label>
                        <div class="col">
                            <input class="w-full input-fit"
                                   pInputText id="name"
                                   [(ngModel)]="receivingGroupInfo.name"
                                   formControlName="name"
                                   [required]="true"
                                   [maxLength]="50"
                                   pattern="^[a-zA-Z0-9\-_]*$"
                                   [placeholder]="tranService.translate('alert.text.inputNameReceiving')"
                            />
                            <!-- error name -->
                            <label htmlFor="name" class="col-fixed alert-group-label" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500" *ngIf="formReceivingGroup.controls.name.dirty && formReceivingGroup.controls.name.errors?.required">{{tranService.translate("global.message.required")}}</small>
                                <small class="text-red-500" *ngIf="formReceivingGroup.controls.name.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:50})}}</small>
                                <small class="text-red-500" *ngIf="formReceivingGroup.controls.name.errors?.pattern">{{tranService.translate("global.message.formatCode")}}</small>
                                <!--                        <small class="text-red-500" *ngIf="isUsernameExisted">{{tranService.translate("global.message.exists",{type: tranService.translate("account.label.username")})}}</small>-->
                            </div>
                        </div>
                    </div>
                    <div class="w-full field grid">
                        <!--  description -->
                        <label for="description" class="col-fixed alert-group-label" style="width:250px">{{tranService.translate("alert.receiving.description")}}</label>
                        <div class="col">
                            <input class="w-full input-fit"
                                   pInputText id="description"
                                   [(ngModel)]="receivingGroupInfo.description"
                                   formControlName="description"
                                   [maxLength]="50"
                                   [placeholder]="tranService.translate('alert.text.inputDescription')"
                            />
                        </div>
                    </div>
                </div>
            </div>
            <h4 class="ml-2"></h4>
            <div class="pt-0 shadow-2 border-round-md m-1 flex p-fluid p-formgrid grid">
                <div class="flex-1">
                    <div class="field  px-4 pt-4  flex-row ">
                        <!-- email -->
                        <div class="field  px-4 pt-4  flex-row ">
                            <table-vnpt
                                [fieldId]="'id'"
                                [(selectItems)]="selectItemsEmail"
                                [columns]="columnsEmail"
                                [dataSet]="dataSetEmail"
                                [options]="optionTableEmail"
                                [loadData]="searchEmail.bind(this)"
                                [scrollHeight]="'200px'"
                            ></table-vnpt>
                        </div>
                    </div>
                </div>
                <div class="flex-1">
                    <div class="field  px-4 pt-4  flex-row ">
                        <!-- sms -->
                        <div class="field  px-4 pt-4  flex-row ">
                            <table-vnpt
                                [fieldId]="'id'"
                                [(selectItems)]="selectItemsSms"
                                [columns]="columnsSms"
                                [dataSet]="dataSetSms"
                                [options]="optionTableSms"
                                [loadData]="searchSms.bind(this)"
                                [scrollHeight]="'200px'"
                            ></table-vnpt>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </p-dialog>
</div>

<table-vnpt
    [fieldId]="'id'"
    [(selectItems)]="selectItems"
    [columns]="columns"
    [dataSet]="dataSet"
    [options]="optionTable"
    [loadData]="search.bind(this)"
    [pageNumber]="pageNumber"
    [pageSize]="pageSize"
    [params]="searchInfo"
    [labelTable]="this.tranService.translate('global.menu.alertreceivinggroup')"
></table-vnpt>
