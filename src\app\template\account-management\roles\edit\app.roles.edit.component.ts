import {AfterContentChecked, Component, Injector, OnInit} from '@angular/core';
import {RolesService} from "../../../../service/account/RolesService";
import {FormBuilder} from "@angular/forms";
import {MenuItem, TreeNode} from "primeng/api";
import {CONSTANTS} from "../../../../service/comon/constants";
import {ComponentBase} from "../../../../component.base";

@Component({
  selector: 'app-app.roles.edit',
  templateUrl: './app.roles.edit.component.html',
})
export class AppRolesEditComponent extends ComponentBase implements OnInit, AfterContentChecked{
    constructor(
                public rolesService: RolesService,
                private formBuilder: FormBuilder,
                private injector: Injector) {
        super(injector);
    }
    items: Array<MenuItem>;
    home: MenuItem;
    type: number;
    formRole: any;
    roleInfo: {
        name: string| null,
        type: number|null,
        status: number|null,
        description: string|null
        roles: Array<any>,
        permissionIds: Array<any>,
    };
    isRoleNameExisted: boolean = false;
    userTypes: Array<any>;
    statusRoles: Array<any>;
    dataSet: {
        content: TreeNode[],
        total: number
    };
    dataSetOld: any;
    roleId: number;
    rolesSubmit: {
        name: string| null,
        type: number|null,
        status: number|null,
        description: string| null,
        permissionIds: Array<any>,
    }
    permissionIds: Array<any>;

    ngAfterContentChecked(): void {
    }

    ngOnInit(): void {
        if (!this.checkAuthen([CONSTANTS.PERMISSIONS.ROLE.UPDATE])) {window.location.hash = "/access";}
        this.checkUserType()
        let me = this;
        this.type = this.sessionService.userInfo.type;
        this.home = { icon: 'pi pi-home', routerLink: '/' };
        this.items = [
            { label: this.tranService.translate("global.menu.accountmgmt") },
            { label: this.tranService.translate("global.menu.listroles"), routerLink:"/roles" },
            { label: this.tranService.translate("global.menu.editroles") }
        ];
        this.roleInfo = {
            name: null,
            type: null,
            status: null,
            roles: null,
            description: null,
            permissionIds: null,
        }
        this.formRole = this.formBuilder.group(this.roleInfo);
        let fullTypeAccount = [
            {name: this.tranService.translate("roles.type.admin"),value:CONSTANTS.ROLE_TYPE.ADMIN, accepts:[CONSTANTS.ROLE_TYPE.ADMIN]},
            {name: this.tranService.translate("roles.type.all"),value:CONSTANTS.ROLE_TYPE.ALL, accepts:[CONSTANTS.ROLE_TYPE.ADMIN]},
            {name: this.tranService.translate("roles.type.customer"),value:CONSTANTS.ROLE_TYPE.CUSTOMER,accepts:[CONSTANTS.ROLE_TYPE.ADMIN, CONSTANTS.ROLE_TYPE.CUSTOMER, CONSTANTS.ROLE_TYPE.PROVINCE, CONSTANTS.ROLE_TYPE.TELLER]},
            {name: this.tranService.translate("roles.type.province"),value:CONSTANTS.ROLE_TYPE.PROVINCE,accepts:[CONSTANTS.ROLE_TYPE.ADMIN]},
            {name: this.tranService.translate("roles.type.teller"),value:CONSTANTS.ROLE_TYPE.TELLER,accepts:[CONSTANTS.ROLE_TYPE.ADMIN,CONSTANTS.ROLE_TYPE.PROVINCE]},
            {name: this.tranService.translate("roles.type.agency"),value:CONSTANTS.ROLE_TYPE.AGENCY,accepts:[CONSTANTS.ROLE_TYPE.ADMIN, CONSTANTS.ROLE_TYPE.PROVINCE, CONSTANTS.ROLE_TYPE.TELLER, CONSTANTS.ROLE_TYPE.AGENCY]},
        ]
        this.userTypes = fullTypeAccount.filter(el => el.accepts.includes(this.type));
        let fullStatusRoles = [
            {name: this.tranService.translate("roles.status.active"),value:CONSTANTS.ROlES_STATUS.ACTIVE},
            {name: this.tranService.translate("roles.status.inactive"),value:CONSTANTS.ROlES_STATUS.INACTIVE},
        ]
        this.statusRoles = fullStatusRoles;
        this.dataSet = {
            content: [{
                label: this.tranService.translate("global.text.all"),
                key: "all",
                children: [],
                data: null
            }],
            total: 0
        }
        this.dataSetOld = {};
        this.roleId = parseInt(this.route.snapshot.paramMap.get("id"));
        this.getDetailAccount();
    }
    getDetailAccount(){
        let me = this;
        this.messageCommonService.onload();
        this.rolesService.getById(this.roleId, (response)=>{
            this.roleInfo.name = response.name
            this.roleInfo.type = response.type
            this.roleInfo.status = response.status
            this.roleInfo.permissionIds = response.permissionIds
            this.getTreeRoles()
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }
    onSubmitEdit(){
        let me = this;
        let permissionIds = this.roleInfo.roles.filter(el => el.data != null)
            .map(el => el.data.id);
        this.rolesSubmit = {
            name: this.roleInfo.name,
            type: this.roleInfo.type,
            status: this.roleInfo.status,
            description: this.roleInfo.description,
            permissionIds: permissionIds,
        }
        this.rolesService.editRole(this.roleId, this.rolesSubmit,  (response)=>{
            // me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);
            me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"));
            this.router.navigate(['/roles'])
        },(error)=>{
            me.messageCommonService.error(me.tranService.translate("global.message.saveError"))
        })
    }
    closeForm(){
        this.router.navigate(['/roles'])
    }
    checkInvalidCreate(){
        return this.formRole.invalid || this.roleInfo.roles == null || this.roleInfo.roles.length == 0;
    }

    getTreeRoles() {
        let me = this
        this.rolesService.getTreeRoles((response)=>{
            response.forEach(el => {
                el.partialSelected = true;
                if (el.label == "RptContent"){
                    el.label = me.tranService.translate(`permission.RptContent.RptContent`)
                    el.children.forEach(item => {
                        item.label = item.data.description != null ? item.data.description : el.data.permissionKey
                    })
                }else {
                    el.label = me.tranService.translate(`permission.${el.key}.${el.key}`)
                    if(el.children){
                        el.children.forEach(item => {
                            item.label = me.tranService.translate(`permission.${el.key}.${item.key}`, null, item.data.description)
                        })
                    }
                }
            });
            me.dataSet = {
                content: [{
                    label: this.tranService.translate("global.text.all"),
                    key: "all",
                    children: response,
                    data: null,
                    expanded: true,
                    partialSelected: true
                }],
                total: 0
            }
            me.roleInfo.roles = [];
            let totalOfTotal = 0;
            me.dataSet.content[0].children.forEach(el => {
                if(el.children != null){
                    let total = 0;
                    el.children.forEach(item => {
                        if(this.roleInfo.permissionIds.includes(item.data.id)){
                            me.roleInfo.roles.push(item);
                            total ++;
                        }
                    });
                    if(total != 0 && total == el.children.length){
                        me.roleInfo.roles.push(el);
                        el.partialSelected = false;
                        totalOfTotal ++;
                    }else if(total == 0){
                        el.partialSelected = false;
                    }
                }
            })
            if(totalOfTotal != 0 && totalOfTotal == me.dataSet.content[0].children.length){
                let element = me.dataSet.content[0];
                element.partialSelected = false;
                me.roleInfo.roles.push(element);
            }
        })
    }
    nameChanged(event){
        this.isRoleNameExisted = false;
        if(/^[a-zA-Z0-9-_]$/.test(event.key)){
            let me = this
            this.debounceService.set("name",me.rolesService.checkName.bind(me.rolesService),{query:me.roleInfo.name},(response)=>{
                if (response == 1){
                    me.isRoleNameExisted = true
                }
                else {
                    me.isRoleNameExisted = false
                }
            })
        }else{
            event.preventDefault();
        }
    }

    checkUserType(){
        // let userType = this.sessionService.userInfo.type
        // if (userType == CONSTANTS.USER_TYPE.CUSTOMER){
        //     window.location.hash = "/access";
        // }
    }
}
