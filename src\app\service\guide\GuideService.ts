import { Inject, Injectable } from "@angular/core";
import { HttpService } from "../comon/http.service";

@Injectable({ providedIn: 'root'})
export class GuideService{
    private prefixApi: string;
    constructor(@Inject(HttpService) private httpService:HttpService) {
        this.prefixApi = "/docs";
    }

    public getProjectInfo(params:{[key: string]: string}, callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/projectInfo`, {},params , callback, errorCallback, finallyCallback);
    }

    public getListPage(params:{[key: string]: string}, callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/search`, {} ,params , callback, errorCallback, finallyCallback);
    }

    public getPageInfo(path: string, lang: string, keyProject: string, callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/pageInfo`, {}, {path, lang, keyProject}, callback, errorCallback, finallyCallback);
    }
}
