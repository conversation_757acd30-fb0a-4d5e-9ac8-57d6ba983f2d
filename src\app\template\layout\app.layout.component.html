<div class="layout-wrapper" [ngClass]="containerClass">
    <app-topbar></app-topbar>
    <div style="background-color: #021c34;" [class]="layoutService.typeMenu == 'big'?['layout-sidebar']:['layout-sidebar','layout-sidebar-small']">
        <app-sidebar></app-sidebar>
    </div>
    <div [class]="layoutService.typeMenu == 'big'?['layout-main-container']:['layout-main-container','layout-main-container-small']">
        <div class="layout-main">
            <router-outlet></router-outlet>
        </div>
        <app-footer></app-footer>
    </div>
    <div class="layout-mask"></div>
</div>
