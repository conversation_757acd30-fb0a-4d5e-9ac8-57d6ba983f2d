import { Inject, Injectable } from "@angular/core";
import { HttpService } from "../comon/http.service";

@Injectable()
export class DeviceService{
    private prefixApi: string;
    constructor(@Inject(HttpService) private httpService:HttpService) {
        this.prefixApi = "/device";
    }
    public detailDevice(msisdn: number, callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/${msisdn}`,{}, {}, callback, errorCallback, finallyCallback);
    }

    public getById(msisdn: number, callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/${msisdn}`,{}, {}, callback, errorCallback, finallyCallback);
    }

    public getByKey(key: string, value: string, callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/getByKey`,{}, {key, value}, callback, errorCallback, finallyCallback);
    }

    public createDevice(body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.post(`${this.prefixApi}`, {},body,{}, callback, errorCallback, finallyCallback);
    }

    public updateDevice(msisdn,body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.put(`${this.prefixApi}/${msisdn}`, {},body,{}, callback, errorCallback, finallyCallback);
    }
    public getListSubscription(msi: number,callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
    this.httpService.get(this.prefixApi+"/simIsvalid",{},{msi}, callback,errorCallBack,finallyCallback);
}
    public uploadRegisterByFile(objectFile, callback:Function){
        this.httpService.uploadFile(`${this.prefixApi}/import-device`, objectFile,{}, {}, callback);
    }
    public downloadTemplate(){
        this.httpService.downloadLocal(`/assets/data/device.xlsx`, "device.xlsx");
    }
    public search(params: {
        [key: string]: any
    }, callback?: Function, errorCallBack?: Function, finallyCallback?: Function) {
        this.httpService.get(`${this.prefixApi}/search`, {}, params, callback, errorCallBack, finallyCallback);
    }
    public deleleDevice(msisdn: number, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.delete(`${this.prefixApi}/${msisdn}`, {}, {}, callback, errorCallBack, finallyCallback);
    }
    public checkMsisdnAndDevice(msisdn: number,callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(this.prefixApi+"/checkExit/" +msisdn,{},{}, callback,errorCallBack,finallyCallback);
    }
    public checkExistsImeiDevice(imei: string,callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(this.prefixApi+"/checkImei/" +imei,{},{}, callback,errorCallBack,finallyCallback);
    }
    public demo(params, callback){

    }

    public getLocation(msisdn: number, callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(`/media/getLocation`,{}, {msisdn}, callback, errorCallback, finallyCallback);
    }

    public findCellId(params: {
        [key: string]: any
    }, callback?:Function, errorCallback?:Function, finallyCallback?:Function){
        this.httpService.get(this.prefixApi+"/getLatLng",{}, params, callback, errorCallback, finallyCallback);
    }

    public findAddress(lat, lon, callback?:Function, errorCallback?:Function, finallyCallback?:Function){
        this.httpService.findAddress(lat, lon, callback, errorCallback, finallyCallback);
    }
}
