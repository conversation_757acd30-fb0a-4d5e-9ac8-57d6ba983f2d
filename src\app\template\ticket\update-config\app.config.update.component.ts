import {Component, Inject, Injector, OnInit} from "@angular/core";
import {TicketService} from "src/app/service/ticket/TicketService";
import {ComponentBase} from "src/app/component.base";
import {AccountService} from "../../../service/account/AccountService";
import {FormBuilder} from "@angular/forms";
import {ComboLazyControl} from "../../common-module/combobox-lazyload/combobox.lazyload";
import {MenuItem} from "primeng/api";

@Component({
  selector: "ticket-config-list",
  templateUrl: './app.config.update.component.html'
})
export class UpdateTicketConfigComponent extends ComponentBase implements OnInit {
  items: Array<MenuItem>;
  home: MenuItem;
  ticketConfig: {
    provinceName: string | null,
    provinceCode: string | null,
    emailInfos: Array<any>
  };
  paramSearchCustomerProvince: { provinceCode: string } = {provinceCode: ""};
  controlComboSelect: ComboLazyControl = new ComboLazyControl();
  formTicketConfig: any

  constructor(
      @Inject(TicketService) private ticketService: TicketService,
      @Inject(AccountService) private accountService: AccountService,
      private formBuilder: FormBuilder,
      private injector: Injector) {
    super(injector);
  }

  ngOnInit() {
    let provinceCode = this.route.snapshot.paramMap.get("provinceCode");
    this.items = [
      { label: this.tranService.translate("ticket.menu.config") },
      { label: this.tranService.translate("ticket.menu.requestConfig"), routerLink:"/ticket/list-config" },
      { label: this.tranService.translate("global.button.edit") }
    ];
    console.log(this.route.snapshot.paramMap.get("provinceCode"))
    this.paramSearchCustomerProvince = {provinceCode: provinceCode}
    this.ticketConfig = {
      provinceName: null,
      provinceCode: null,
      emailInfos: []
    };

    let me = this;
    me.formTicketConfig = this.formBuilder.group(this.ticketConfig);
    me.messageCommonService.onload();
    this.ticketService.getDetailTicketConfig(provinceCode, (resp) => {
      me.ticketConfig.provinceName = resp.provinceName
      me.ticketConfig.provinceCode = resp.provinceCode
      me.ticketConfig.emailInfos = resp.emailInfos.map(e => ({
        id: e.userId,
        email: e.email
      }));
      console.log(me.ticketConfig)
    }, null, ()=>{
      me.messageCommonService.offload();
    })
  }

  onSubmitUpdate() {
    let me = this;
    this.ticketConfig.emailInfos = this.ticketConfig.emailInfos.map(e => ({
      userId: e.id,
      email: e.email
    }));
    // console.log(this.ticketConfig.emailInfos;
    this.ticketService.updateTicketConfig(this.ticketConfig, () => {
      me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"));
      // me.router.navigate(['/ticket/detailTicketConfig/' + me.ticketConfig.provinceCode])
      location.reload();
    }, null, ()=>{
      me.messageCommonService.offload();
    })
  }

  closeForm() {
    this.router.navigate(['/ticket/list-config'])
  }


}
