import { AfterContentChecked, Component, Injector, OnInit } from "@angular/core";
import { FormBuilder } from "@angular/forms";
import { MenuItem } from "primeng/api";
import { ComponentBase } from "src/app/component.base";
import { AccountService } from "src/app/service/account/AccountService";
import { CONSTANTS } from "src/app/service/comon/constants";
import { CustomerService } from "src/app/service/customer/CustomerService";
import { ComboLazyControl } from "../../common-module/combobox-lazyload/combobox.lazyload";
import {ColumnInfo, OptionTable} from "../../common-module/table/table.component";
import {ContractService} from "../../../service/contract/ContractService";

@Component({
    selector: "app-account-edit",
    templateUrl: './app.account.edit.component.html'
})
export class AppAccountEditComponent extends ComponentBase implements OnInit, AfterContentChecked{
    constructor(public accountService: AccountService,
                private customerService: CustomerService,
                private contractService: ContractService,
                private formBuilder: FormBuilder,
                injector: Injector) {
        super(injector);
    }
    items: Array<MenuItem>;
    home: MenuItem;
    //Danh sách customer khi lấy detail, dùng so sách với kết quả cuối cùng
    listCustomerOld: Array<any>;
    accountInfo: {
        accountName: string| null,
        fullName: string|null,
        email: string|null,
        phone: string|null,
        userType: number| null,
        province: any,
        roles: Array<any>,
        description: string|null,
        manager: any,
        //Danh sách customer TRƯỚC khi chọn, bỏ chọn
        customers: Array<any>,
        roleLst?: Array<any>,
        customerAccounts : any
        isRootCustomer : boolean | null
        //Danh sách contract khi lấy detail, dùng so sách với kết quả cuối cùng
        contracts: Array<any>
    };
    formAccount: any;
    statusAccounts: Array<any>;
    listRole: Array<any>;
    listProvince: Array<any>;
    userType: number;
    optionUserType: any;
    isUsernameExisted: boolean = false;
    isEmailExisted: boolean = false;
    isPhoneExisted: boolean = false;
    oldUserType: number | null = null;
    accountResponse: any;
    paramSearchCustomerProvince: {provinceCode: string} = {provinceCode: ""};
    paramSearchManager :{type: number, provinceCode: string} = {type: 3, provinceCode: ""};
    paramSearchCustomerAccount : {managerId: number, provinceCode: string} = {managerId: null, provinceCode: ""};
    controlComboSelect: ComboLazyControl = new ComboLazyControl();
    controlComboSelectRole: ComboLazyControl = new ComboLazyControl();
    controlComboSelectManager : ComboLazyControl = new ComboLazyControl();
    controlComboSelectCustomerAccount : ComboLazyControl = new ComboLazyControl();
    paramSearchRoleActive: {type: number, accountRootId: number};
    paramQuickSearchCustomer: {
        keyword: string|null,
        provinceCode: string|null,
        accountRootId: number| null,
        managerId: null | null,
        accountCustomerId: number | null,
    }
    columnInfoCustomer: Array<ColumnInfo>;
    optionTableCustomer: OptionTable;
    //Lưu lại list customer SAU khi chọn, bỏ chọn
    selectItemCustomer: Array<any>
    //sẽ lưu lại list contract SAU chọn, bỏ chọn
    selectItemContract: Array<any>
    //lưu lại contract bỏ chọn
    deselectedContracts: Set<string>
    dataSetCustomer: {
        content: Array<any>,
        total: number,
    }
    paginationCustomer: {
        page: number|null,
        size: number|null,
        sortBy: string|null,
    }
    customSelectAllCustomer = false;
    customSelectAllContract = false;
    loadingCustomer: boolean = false;
    loadingContract: boolean = false;

    paramQuickSearchContract: {
        keyword: string|null,
        customerIds: Array<{ id: number }>|null,
        accountRootId: number | null
    }
    columnInfoContract: Array<ColumnInfo>;
    optionTableContract: OptionTable;
    dataSetContract: {
        content: Array<any>,
        total: number,
    }
    paginationContract: {
        page: number|null,
        size: number|null,
        sortBy: string|null,
    }
    accountId: number;

    isShowSecretKey = true
    listModule = []
    //sẽ lưu lại list api sau khi đã chọn
    selectItemGrantApi: Array<any> = []
    paginationGrantApi: {
        page: number|null,
        size: number|null,
        sortBy: string|null,
    }
    columnInfoGrantApi: Array<ColumnInfo>;

    dataSetGrantApi: {
        content: Array<any>,
        total: number,
    }
    optionTableGrantApi: OptionTable;

    paramsSearchGrantApi = {api : null, module : null}

    genGrantApi = {clientId: null, secretKey: null}

    activeTabIndex = 0;

    statusGrantApi : any = null;

    userInfo = this.sessionService.userInfo;

    accountCurrentDetail : any = {}

    isChangeSecretKey : boolean = false;

    ngOnInit(): void {
        if (!this.checkAuthen([CONSTANTS.PERMISSIONS.ACCOUNT.UPDATE])) {window.location.hash = "/access";}
        this.userType = this.sessionService.userInfo.type;
        this.accountId = this.sessionService.userInfo.id;
        this.optionUserType = CONSTANTS.USER_TYPE;
        this.items = [
            { label: this.tranService.translate("global.menu.accountmgmt") },
            { label: this.tranService.translate("global.menu.listaccount"), routerLink:"/accounts" },
            { label: this.tranService.translate("global.button.edit") }
        ];
        this.home = { icon: 'pi pi-home', routerLink: '/' };

        let fullTypeAccount = [
            {name: this.tranService.translate("account.usertype.admin"),value:CONSTANTS.USER_TYPE.ADMIN, accepts:[CONSTANTS.USER_TYPE.ADMIN]},
            // {name: this.tranService.translate("account.usertype.customer"),value:CONSTANTS.USER_TYPE.CUSTOMER,accepts:[CONSTANTS.USER_TYPE.ADMIN,CONSTANTS.USER_TYPE.PROVINCE,CONSTANTS.USER_TYPE.DISTRICT, CONSTANTS.USER_TYPE.AGENCY, CONSTANTS.USER_TYPE.CUSTOMER]},
            {name: this.tranService.translate("account.usertype.customer"),value:CONSTANTS.USER_TYPE.CUSTOMER,accepts:[CONSTANTS.USER_TYPE.ADMIN,CONSTANTS.USER_TYPE.PROVINCE,CONSTANTS.USER_TYPE.DISTRICT, CONSTANTS.USER_TYPE.CUSTOMER]},
            {name: this.tranService.translate("account.usertype.province"),value:CONSTANTS.USER_TYPE.PROVINCE,accepts:[CONSTANTS.USER_TYPE.ADMIN]},
            {name: this.tranService.translate("account.usertype.district"),value:CONSTANTS.USER_TYPE.DISTRICT,accepts:[CONSTANTS.USER_TYPE.ADMIN,CONSTANTS.USER_TYPE.PROVINCE]},
            // {name: this.tranService.translate("account.usertype.agency"),value:CONSTANTS.USER_TYPE.AGENCY,accepts:[CONSTANTS.USER_TYPE.ADMIN,CONSTANTS.USER_TYPE.PROVINCE,CONSTANTS.USER_TYPE.DISTRICT]},
        ]
        this.statusAccounts = fullTypeAccount.filter(el => el.accepts.includes(this.userType));
        this.accountInfo = {
            accountName: null,
            fullName: null,
            email: null,
            phone: null,
            userType: this.statusAccounts[0].value,
            province: null,
            roles: null,
            description: null,
            manager: null,
            customers: null,
            customerAccounts : null,
            isRootCustomer : null,
            contracts: null,
        }
        this.paramQuickSearchCustomer = {
            keyword: null,
            accountRootId: null,
            provinceCode: this.accountInfo.province,
            accountCustomerId: null,
            managerId: null,
        }
        this.columnInfoCustomer = [
            {
                name: this.tranService.translate("customer.label.customerCode"),
                key: "code",
                size: "30%",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("customer.label.customerName"),
                key: "name",
                size: "50%",
                align: "left",
                isShow: true,
                isSort: false,
            },
        ]
        this.dataSetCustomer = {
            content: [],
            total: 0,
        }
        this.paginationCustomer = {
            page: 0,
            size: 10,
            sortBy: "name,asc;id,asc",
        }
        this.paginationGrantApi = {
            page: 0,
            size: 10,
            sortBy: "id,desc",
        }
        this.optionTableCustomer = {
            hasClearSelected: false,
            hasShowChoose: true,
            hasShowIndex: true,
            hasShowToggleColumn: false,
        }
        this.selectItemCustomer = []

        this.paramQuickSearchContract = {
            keyword: null,
            customerIds: [],
            accountRootId: -1,
        }
        this.columnInfoContract = [
            {
                name: this.tranService.translate("customer.label.customerCode"),
                key: "customerCode",
                size: "30%",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("customer.label.customerName"),
                key: "customerName",
                size: "50%",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("contract.label.contractCode"),
                key: "contractCode",
                size: "50%",
                align: "left",
                isShow: true,
                isSort: false,
            },
        ]

        this.columnInfoGrantApi = [
            {
                name: "API",
                key: "name",
                size: "30%",
                align: "left",
                isShow: true,
                isSort: true,
            },
            {
                name: "Module",
                key: "module",
                size: "50%",
                align: "left",
                isShow: true,
                isSort: true,
            }
        ]
        this.dataSetContract = {
            content: [],
            total: 0,
        }
        this.dataSetGrantApi = {
            content: [],
            total: 0,
        }
        this.paginationContract = {
            page: 0,
            size: 10,
            sortBy: "customerName,asc;id,asc",
        }
        this.optionTableGrantApi = {
            hasClearSelected: false,
            hasShowChoose: true,
            hasShowIndex: true,
            hasShowToggleColumn: false,
        }
        this.optionTableContract = {
            hasClearSelected: false,
            hasShowChoose: true,
            hasShowIndex: true,
            hasShowToggleColumn: false,
        }
        this.selectItemContract = []
        this.deselectedContracts = new Set<string>();
        this.getListProvince();
    }

    ngAfterContentChecked(): void {
        if(this.accountInfo.userType != this.oldUserType && this.formAccount){
            this.oldUserType = this.accountInfo.userType;
            this.formAccount.get("province").reset();
            this.formAccount.get("customers").reset();
        }
    }

    checkExistAccount(type){
        let email = null;
        let username = null;
        if(type == "accountName"){
            this.isUsernameExisted = false;
            username = this.accountInfo.accountName;
            if(username == this.accountResponse.username) return;
        }else if(type == "email"){
            this.isEmailExisted = false;
            email = this.accountInfo.email;
            if(email == this.accountResponse.email) return;
        }

        let me = this;

        this.debounceService.set(type, this.accountService.checkAccount.bind(this.accountService), email, username,(response)=>{
            if(response >= 1){
                if(type == "accountName"){
                    me.isUsernameExisted = true;
                }else{
                    me.isEmailExisted = true;
                }
            }
        })
    }

    onSubmitCreate(){
        let me = this;
        if(this.accountInfo.userType == CONSTANTS.USER_TYPE.CUSTOMER && this.accountInfo.manager == null) {
            this.controlComboSelectManager.dirty = true
            this.activeTabIndex = 0;
            me.messageCommonService.warning(me.tranService.translate('account.message.managerRequired'))
            return;
        }
        if(me.accountInfo.userType == CONSTANTS.USER_TYPE.CUSTOMER && me.selectItemCustomer.length == 0) {
            me.messageCommonService.warning(me.tranService.translate('account.message.customerRequired'))
            return;
        }
        let listOldCustomer = this.listCustomerOld
        let listOldContract = (this.accountInfo.contracts|| []).map(contract => contract.id)
        let customerAdds = ((this.selectItemCustomer|| []).map(customer => customer.id)).filter(el => !listOldCustomer.includes(el)).map(el => {
            return {
                customerId: el,
                type: 1
            }
        });
        let customerDelete = listOldCustomer.filter(el => !((this.selectItemCustomer|| []).map(customer => customer.id)).includes(el)).map(el => {
            return {
                customerId: el,
                type: -1
            }
        });

        let contractAdds = ((this.selectItemContract|| []).map(contract => contract.id)).filter(el => !listOldContract.includes(el)).map(el => {
            return {
                contractId: el,
                type: 1
            }
        });
        let contractDelete = listOldContract.filter(el => !((this.selectItemContract|| []).map(contract => contract.id)).includes(el)).map(el => {
            return {
                contractId: el,
                type: -1
            }
        });

        let customerUpdate = [...customerAdds, ...customerDelete];
        let contractUpdate = [...contractAdds,...contractDelete];
        if(this.accountInfo.userType != CONSTANTS.USER_TYPE.CUSTOMER) {
            this.statusGrantApi = null
            this.selectItemGrantApi = []
        }
        let dataBody = {
            username: this.accountInfo.accountName,
            fullName: this.accountInfo.fullName,
            description: this.accountInfo.description,
            email: this.accountInfo.email,
            phone: this.accountInfo.phone,
            type: this.accountInfo.userType,
            provinceCode: this.accountInfo.province,
            // roleLst: (this.accountInfo.roles|| []).map(el => el.id),
            roleLst: this.accountInfo.roleLst,
            customerLst: customerUpdate,
            // customerIdLst: (this.accountInfo.customers || [])
            idManager: this.accountInfo.manager || null,
            idUserManageList: (this.accountInfo.customerAccounts || []),
            contractLst: contractUpdate,
            statusApi: this.statusGrantApi,
            listApiId: (this.selectItemGrantApi || []).map(el=>el.id),
            secretId : this.genGrantApi.secretKey,
            isChangeSecretKey : this.isChangeSecretKey
        }
        if(dataBody.phone != null){
            if(dataBody.phone.startsWith('0')){
                dataBody.phone = "84"+dataBody.phone.substring(1, dataBody.phone.length);
            }else if(dataBody.phone.length == 9 || dataBody.phone.length == 10){
                dataBody.phone = "84"+dataBody.phone;
            }
        }
        this.messageCommonService.onload();
        this.accountService.updateAccount(this.accountResponse.id, dataBody, (response)=>{
            me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"));
            me.router.navigate(['/accounts/']);
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }

    closeForm(){
        this.router.navigate(['/accounts'])
    }

    getListAppIdSelected(){
        let me = this;
        this.accountService.viewProfile( (response)=>{
            me.accountCurrentDetail = response;
            if(this.accountInfo.userType == CONSTANTS.USER_TYPE.CUSTOMER) {
                if(this.genGrantApi.secretKey != null) {
                    me.searchGrantApi(me.paginationGrantApi.page, me.paginationGrantApi.size, me.paginationGrantApi.sortBy, me.paramsSearchGrantApi);
                }
            }
        },null, ()=>{
            me.messageCommonService.offload();
        })
    }

    getDetail(updateList: boolean = true){
        let me = this;
        let accountid = this.route.snapshot.paramMap.get("id");
        me.messageCommonService.onload()
        this.accountService.getById(parseInt(accountid), (response)=>{
            me.accountResponse = response;
            me.listCustomerOld = (response.customers|| []).map(customer => customer.customerId)
            me.accountInfo.accountName = response.username;
            me.accountInfo.fullName = response.fullName;
            me.accountInfo.email = response.email;
            me.accountInfo.description = response.description;
            me.accountInfo.phone = response.phone;
            me.accountInfo.province = response.provinceCode;
            me.accountInfo.userType = response.type;
            me.accountInfo.isRootCustomer = response.isRootCustomer;
            me.accountInfo.customers = (response.customers|| []).map(customer => ({
                id: customer.customerId,
                name: customer.customerName,
                code: customer.customerCode,
            }))
            // console.log(response.customers)
            me.selectItemCustomer = (response.customers|| []).map(customer => ({
                id: customer.customerId,
                name: customer.customerName,
                code: customer.customerCode,
            }))
            me.accountInfo.contracts = [...(response.contracts || [])];
            me.selectItemContract = [...(response.contracts || [])];
            me.paramQuickSearchCustomer.provinceCode = me.accountInfo.province;
            // Nếu tài khoản khách hàng sửa khách hàng cấp dưới, truyền id tk kh cấp trên để load customer theo tk kh cấp trên
            if (me.userType == CONSTANTS.USER_TYPE.CUSTOMER) {
                me.paramQuickSearchCustomer.accountRootId = me.accountId;
            } else { // nếu là tk cấp trên sửa
                me.paramQuickSearchCustomer.managerId = me.accountResponse?.manager?.id ? me.accountResponse.manager.id : -1;
                if (me.accountResponse.rootAccount) { //nếu có tài khoản con có root thì load danh sách theo root
                    me.paramQuickSearchCustomer.accountRootId = me.accountResponse.parentId
                    me.paramQuickSearchContract.accountRootId = me.accountResponse.parentId
                } else { //nếu k có load thì theo gdv và tk đang xem
                    me.paramQuickSearchCustomer.accountCustomerId = me.accountResponse.id;
                }

            }
            // console.log(me.selectItemContract)
            me.paramSearchRoleActive = {
                type: me.accountInfo.userType,
                accountRootId: me.accountInfo.userType == CONSTANTS.USER_TYPE.CUSTOMER && !me.accountInfo.isRootCustomer ? me.accountResponse.rootAccount.id : -1
            }
            me.formAccount = me.formBuilder.group(me.accountInfo);
            me.formAccount.controls.accountName.disable();
            me.formAccount.controls.userType.disable();
            if(me.accountInfo.userType ==  CONSTANTS.USER_TYPE.DISTRICT) {
                me.formAccount.controls.province.disable();
            }
            me.paramSearchCustomerProvince = {provinceCode: me.accountInfo.province}
            // me.paramSearchCustomerAccount = {managerId : response.id ,provinceCode: me.accountInfo.province}
            // me.paramSearchManager = {type: 3, provinceCode: me.accountInfo.province}
            if(response.type == CONSTANTS.USER_TYPE.CUSTOMER){
                me.formAccount.controls.province.disable();
            }
            me.accountInfo.roleLst = (response.roles || []).map(el => el.roleId)
            me.statusGrantApi = response.statusApi
            me.selectItemGrantApi = (response.listApiId || []).map(el=> ({id: el}))
            me.genGrantApi.secretKey = response.secretId
            me.genGrantApi.clientId = response.username
            this.getListAppIdSelected()
            if(updateList){
                me.getListCustomer(false);
            }
        }, null, ()=>{
            // me.messageCommonService.offload();
        })
    }

    getListRole(data?, callback?){
        let me = this;
        console.log(data)
        if(this.accountInfo.userType == this.paramSearchRoleActive.type){
            this.accountService.getListRole(data, (response)=> {
                if(callback){
                    let dataResponse = {
                        content: (response || []),
                        totalPages: 1,
                        totalElements: (response || []).length
                    }
                    callback(dataResponse);
                }
            })
        }else{
            this.paramSearchRoleActive = {
                type: this.accountInfo.userType,
                accountRootId: -1,
            }
        }
    }

    getListCustomer(isClear:boolean = true, name:string=""){
        if(this.accountInfo.userType == this.optionUserType.CUSTOMER) {
            if(isClear){
                this.accountInfo.customers = null;
            }else{
                if(this.accountResponse.customers != null && this.accountResponse.customers.length > 0){
                    // this.accountInfo.customers = this.accountResponse.customers.map(el => el.customerId);
                }else{
                    this.accountInfo.customers = null;
                }
                // chon GDV
                if(this.accountResponse.manager != null){
                    this.accountInfo.manager = this.accountResponse.manager.id;
                }else{
                    this.accountInfo.manager = null;
                }
            }
            let me = this;
            this.paramSearchCustomerProvince = {provinceCode: this.accountInfo.province}
            this.paramSearchManager = {type: 3, provinceCode: this.accountInfo.province}
            if(this.accountInfo.userType == CONSTANTS.USER_TYPE.CUSTOMER){
                this.paramSearchCustomerProvince["parentId"] = this.accountResponse["id"]
            }
        }
        // khi tao tai khoan GDV phai chon nhung tai khoan kh ma no' quan ly
        if(this.accountInfo.userType == this.optionUserType.DISTRICT) {
            if(isClear){
                this.accountInfo.manager = null;
            }else {
                // chon KH
                if(this.accountResponse.userManages != null && this.accountResponse.userManages.length > 0){
                    this.accountInfo.customerAccounts = this.accountResponse.userManages.filter(el=> el.isRootCustomer == true).map(el => el.id);
                }else{
                    this.accountInfo.customerAccounts = null;
                }
            }
            this.paramSearchCustomerAccount = {managerId : this.accountResponse.id ,provinceCode: this.accountInfo.province}
        }
    }

    getListProvince(){
        let me = this;
        this.accountService.getListProvince((response)=>{
            me.listProvince = response.map(el => {
                return {
                    id: el.code,
                    name: `${el.name} (${el.code})`
                }
            });
            me.getDetail();
        })
    }

    loadCustomerAccount(params, callback) {
        return this.accountService.getCustomerAccount(params, callback)
    }
    onSearchCustomer(back?) {
        let me = this;
        if(back) {
            me.paginationCustomer.page = 0;
        }
            me.searchCustomer(me.paginationCustomer.page, me.paginationCustomer.size, me.paginationCustomer.sortBy, me.paramQuickSearchCustomer);
    }
    searchCustomer(page, limit, sort, params){
        let me = this;
        this.paginationCustomer.page = page;
        this.paginationCustomer.size = limit;
        this.paginationCustomer.sortBy = sort;
        let dataParams = {
            page,
            size: limit,
            sort
        }
        Object.keys(this.paramQuickSearchCustomer).forEach(key => {
            if(this.paramQuickSearchCustomer[key] != null){
                dataParams[key] = this.paramQuickSearchCustomer[key];
            }
        })
        me.messageCommonService.onload();
        // console.log(dataParams)
        // console.log("quickSearchCustomer")
        this.customerService.quickSearchCustomer(dataParams, this.paramQuickSearchCustomer,(response)=>{
            me.dataSetCustomer = {
                content: response.content,
                total: response.totalElements
            }
            if(this.selectItemCustomer.length==response.totalElements && response.totalElements != 0){
                this.customSelectAllCustomer = true
            }
        }, null, ()=>{
            me.messageCommonService.offload();
        })
        // console.log(this.selectItemCustomer)
    }

    onSearchContract(back?) {
        let me = this;
        if(back) {
            me.paginationContract.page = 0;
        }
            me.paramQuickSearchContract.customerIds = (me.selectItemCustomer|| []).map(customer => customer.id),
            me.searchContract(me.paginationContract.page, me.paginationContract.size, me.paginationContract.sortBy, me.paramQuickSearchContract);
    }
    searchContract(page, limit, sort, params){
        let me = this;
        this.paginationContract.page = page;
        this.paginationContract.size = limit;
        this.paginationContract.sortBy = sort;
        let dataParams = {
            page,
            size: limit,
            sort
        }
        // Object.keys(this.paramQuickSearchContract).forEach(key => {
        //     if(this.paramQuickSearchContract[key] != null){
        //         dataParams[key] = this.paramQuickSearchContract[key];
        //     }
        // })
        me.messageCommonService.onload();
        this.contractService.quickSearchContract(dataParams, this.paramQuickSearchContract,(response)=>{
            me.dataSetContract = {
                content: response.content,
                total: response.totalElements
            }
            if(this.selectItemContract.length==response.totalElements && response.totalElements != 0){
                this.customSelectAllContract = true
            }
            // console.log(response)
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }

    onTabChange(event) {
        const tabName = event.originalEvent.target.innerText;
        let me = this;
        if (event && tabName.includes(this.tranService.translate('account.text.grantApi'))) {
            //có giá trị như thế nào thì hiển thì nguyên như vậy
            // if(!this.statusGrantApi && !this.genGrantApi.secretKey) {
            //     this.statusGrantApi = 1;
            // }
        } else if (event && tabName.includes(this.tranService.translate('account.text.addContract'))) {
            me.onSearchContract()
        } else if (event && tabName.includes(this.tranService.translate('account.text.addCustomer'))) {
            me.onSearchCustomer()
        }
    }

    generateToken(n) {
        var chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        var token = '';
        for(var i = 0; i < n; i++) {
            token += chars[Math.floor(Math.random() * chars.length)];
        }
        return token;
    }

    checkSelectItemChangeCustomer(event: any[]) {
        // console.log(this.selectItemCustomer)
        let me = this;
        if(this.selectItemCustomer.length==this.dataSetCustomer.total){
            this.customSelectAllCustomer = true
        }else{
            this.customSelectAllCustomer = false
        }
        const currentCustomerIds = new Set((event|| []).map(customer => customer.id));
        const previousCustomerIds = new Set((this.accountInfo.customers || []).map(customer => customer.id));
        // console.log(this.accountInfo.customers)
        const addedCustomers = (event|| []).filter(customer => !previousCustomerIds.has(customer.id));

        const removedCustomers = (this.accountInfo.customers || []).filter(customer => !currentCustomerIds.has(customer.id));
        this.fetchContractsByCustomerId((addedCustomers|| []).map(customer => customer.id))

        removedCustomers.forEach(customer => {
            this.selectItemContract = (this.selectItemContract|| []).filter(contract => contract.customerCode != customer.code) || [];
        });

        this.accountInfo.customers = event;

    }

    checkSelectItemChangeContract(event: any[]){
        if(this.selectItemContract.length==this.dataSetContract.total){
            this.customSelectAllContract = true
        }else{
            this.customSelectAllContract = false
        }
    }
    fetchContractsByCustomerId(customerIds: number[]) {

        let me = this;
        this.messageCommonService.onload()
        this.paginationContract.page = 0;
        let dataParams = {
            page: '0',
            size: '10000',
            sort: this.paginationContract.sortBy
        }
        this.contractService.quickSearchContract(dataParams,
            {
                keyword: null,
                provinceCode: this.accountInfo.province,
                customerIds: customerIds,
            }, (res) => {
                if (res.totalElements > 0) {
                    const newContracts = (res.content || []).filter(contract => !this.deselectedContracts.has(contract.contractCode) &&
                        !this.selectItemContract.some(existingContract => existingContract.contractCode === contract.contractCode));
                    this.selectItemContract.push(...newContracts);
                }
            }, null, () => {
                me.messageCommonService.offload();
            })
    }

    checkShowTabAddCustomerAndContract() {
        let me = this
        if (me.accountInfo.userType != CONSTANTS.USER_TYPE.CUSTOMER)  return false;
        // if (me.formAccount.invalid || me.isEmailExisted || me.isPhoneExisted || me.isUsernameExisted ) {
        //     return false;
        // }
        // if (this.accountInfo.province == null || this.accountInfo.manager == null) {
        //     return false
        // }
        return  true
    }

    onChangeSelectAllItemsCustomer(){
        // console.log(this.selectItemCustomer);
        let me = this;
        let params = {
            page: "0",
            size: "********",
            sort: "name,asc;id,asc",
        }
        Object.keys(this.paramQuickSearchCustomer).forEach(key => {
            if(this.paramQuickSearchCustomer[key] != null){
                params[key] = this.paramQuickSearchCustomer[key];
            }
        })
        this.loadingCustomer = true
        this.customerService.quickSearchCustomer(params,this.paramQuickSearchCustomer,(response)=>{
            if(this.selectItemCustomer.length == response.totalElements){
                this.selectItemCustomer = [];
                this.customSelectAllCustomer = false
                return;
            }
            this.selectItemCustomer = response.content
            this.customSelectAllCustomer = true
        },null,()=>{ this.loadingCustomer = false });
    }

    onChangeSelectAllItemsContract(){
        // console.log(this.selectItemCustomer);
        let me = this;
        let params = {
            page: "0",
            size: "********",
            sort: "customerName,asc;id,asc",
        }
        this.loadingContract = true
        this.contractService.quickSearchContract(params, this.paramQuickSearchContract,(response)=>{
            if(this.selectItemContract.length == response.totalElements){
                this.selectItemContract = [];
                this.customSelectAllContract = false
                return;
            }
            this.selectItemContract = response.content
            this.customSelectAllContract = true
        }, null, ()=>{
            this.loadingContract = false;
        })
    }

    onChangeTeller() {
        let me = this;
        if (me.accountInfo.manager) {
            me.paramQuickSearchCustomer.managerId = me.accountInfo.manager;
        } else {
            me.paramSearchCustomerAccount.managerId = -1;
        }
        me.paginationCustomer.page = 0;
        me.paginationContract.page = 0;
    }

    searchGrantApi(page, limit, sort, params){
        let me = this;
        this.paginationGrantApi.page = page;
        this.paginationGrantApi.size = limit;
        this.paginationGrantApi.sortBy = sort;
        let dataParams = {
            page,
            size: limit,
            sort
        }
        Object.keys(this.paramsSearchGrantApi).forEach(key => {
            if(this.paramsSearchGrantApi[key] != null){
                dataParams[key] = this.paramsSearchGrantApi[key];
            }
        })
        me.messageCommonService.onload();
        if(this.accountResponse.parentId) {
            dataParams['userCustomerParent'] = this.accountResponse.parentId
            this.accountService.getListAPI2(dataParams,(response)=>{
                me.dataSetGrantApi = {
                    content: response.content,
                    total: response.totalElements
                }
            }, null, ()=>{
                me.messageCommonService.offload();
            })
            let copyParam = {...dataParams};
            copyParam.size = *********;
            this.accountService.getListAPI2(copyParam,(response)=>{
                me.listModule = [...new Set(response.content.map(el=>el.module))]
                me.listModule = me.listModule.map(el=>({
                    name : el,
                    value : el
                }))
            }, null, ()=>{
                me.messageCommonService.offload();
            })
        }else {
            this.accountService.searchGrantApi(dataParams,(response)=>{
                me.dataSetGrantApi = {
                    content: response.content,
                    total: response.totalElements
                }
            }, null, ()=>{
                me.messageCommonService.offload();
            })
            let copyParam = {...dataParams};
            copyParam.size = *********;
            this.accountService.searchGrantApi(copyParam,(response)=>{
                me.listModule = [...new Set(response.content.map(el=>el.module))]
                me.listModule = me.listModule.map(el=>({
                    name : el,
                    value : el
                }))
            }, null, ()=>{
                me.messageCommonService.offload();
            })
        }
    }

    genToken(){
        this.isChangeSecretKey = true;
        let me = this;
        if(this.genGrantApi.secretKey) {
            this.genGrantApi.secretKey = this.generateToken(20);
        }else {
            this.genGrantApi.secretKey = this.generateToken(20);
            me.onSearchGrantApi()
        }
    }

    onSearchGrantApi(back?) {
        let me = this;
        if(back) {
            me.paginationGrantApi.page = 0;
        }
        me.searchGrantApi(me.paginationGrantApi.page, me.paginationGrantApi.size, me.paginationGrantApi.sortBy, me.paramsSearchGrantApi);
    }

    protected readonly CONSTANTS = CONSTANTS;
}
