import {Component, Inject,Injector, OnInit, HostListener} from '@angular/core';
import {TranslateService} from "../../../../service/comon/translate.service";
import {AccountService} from "../../../../service/account/AccountService";
import {MessageCommonService} from "../../../../service/comon/message-common.service";
import {FormBuilder} from "@angular/forms";
import {UtilService} from "../../../../service/comon/util.service";
import {ActivatedRoute, Router} from "@angular/router";
import {MenuItem} from "primeng/api";
import {ColumnInfo, OptionTable} from "../../../common-module/table/table.component";
import {CONSTANTS} from "../../../../service/comon/constants";
import {AlertService} from "../../../../service/alert/AlertService";
import {ComponentBase} from "../../../../component.base";
import {CustomerService} from "../../../../service/customer/CustomerService";
import {GroupSimService} from "../../../../service/group-sim/GroupSimService";
import {ComboLazyControl} from "../../../common-module/combobox-lazyload/combobox.lazyload";
import {TrafficWalletService} from "../../../../service/datapool/TrafficWalletService";

@Component({
  selector: 'app-app.alert.list',
  templateUrl: './app.alert.list.component.html',
})
export class AppAlertListComponent extends ComponentBase implements OnInit{
    constructor(
                @Inject(AccountService) private accountService: AccountService,
                @Inject(CustomerService) private customerService: CustomerService,
                @Inject(GroupSimService) private groupSimService: GroupSimService,
                @Inject(TrafficWalletService) private trafficWalletService: TrafficWalletService,
                private formBuilder: FormBuilder,
                @Inject(AlertService) private alertService: AlertService,
                private injector: Injector
    ) {
        super(injector);
    }
    statusAlert: Array<any>;
    statusSim: Array<any>;
    items: MenuItem[];
    home: MenuItem;
    formSearchAlert: any
    searchInfo: {
        name: string | null,
        ruleCategory:  number | null,
        eventType: number | null,
        actionType: string | null,
        status: number | null,
        severity: number | null,
    }
    pageNumber: number;
    pageSize: number;
    sort: string;
    dataSet: {
        content: Array<any>,
        total: number
    };
    formAlertDetail: any;
    repeat: boolean;
    comboSelectCustomerControl: ComboLazyControl = new ComboLazyControl();
    comboSelectSubControl: ComboLazyControl = new ComboLazyControl();
    comboSelectGroupSubControl: ComboLazyControl = new ComboLazyControl();
    controlComboSelectWallet: ComboLazyControl = new ComboLazyControl();
    isPlanExisted: boolean = false;
    alertInfo: {
        name: string|null,
        customerId: any,
        statusSim: number|null,
        subscriptionNumber: string|null,
        groupId: string|null,
        interval: number|null,
        count: number|null,
        unit: number|null,
        value: string|null,
        description: string|null,
        severity: string|null,
        listAlertReceivingGroupId: Array<any>|null,
        url: string|null,
        emailList: string|null,
        emailSubject: string|null,
        emailContent: string|null,
        smsList: string|null
        smsContent: string|null,
        ruleCategory: number | null,
        eventType: number | null,
        appliedPlan: Array<any>,
        actionType:number|null,
        walletName: string|null,
        notifyInterval : number | null,
        notifyRepeat: number | null;
        typeAlert: Array<any> | null;
        sendTypeEmail: boolean;
        sendTypeSMS: boolean;
        status : number | null;
        groupName : string | null
        customerName : string | null
        customerCode : string | null
        walletSubCode: string| null
        createdBy: number | null
    };
    appliedPlanOptions: Array<any>;
    isAlertNameExisted: boolean = false;
    statusAlertForDetail: any;
    alertId: number | string;
    statusTemp : any;
    selectItems: Array<any>;
    columns: Array<ColumnInfo>;
    optionTable: OptionTable;
    severityOptions: Array<any>;
    ruleOptions: Array<any>;
    eventOptions: Array<any>;
    actionOptions: Array<any>;
    eventOptionManagement: any;
    eventOptionMonitoring: any;
    customerNameOptions: Array<{ name: any, value: any, id: any }>;
    listGroupByCustomer: Array<any>;
    isShowModalDetail: boolean = false;
    unitWalletOptions = [
        {label: "%", value: 1},
        {label: "MB", value: 2},
        {label: "SMS", value: 3},
    ]
    walletOptions: Array<any>
    paramSearchGroupSim = {};
    alertResponse : any;
    userInfo: any = {};
    isShowConfimChangeStatus: boolean;

    isMobileView: boolean = false;

    @HostListener('window:resize', [])
    onResize() {
          this.checkIfMobile();
        }

    checkIfMobile() {
      this.isMobileView = window.innerWidth <= 440;
    }

    // Dynamically get a style for vnpt-select on alert
    getBoxSelectStyle(): {[key: string]: any} {
    if (this.isMobileView) {
        return {
            left: 'unset',
            right: '3.5vw',
            top: '64vw',
            display: 'flex',
            'flex-wrap': 'wrap',
            width: '60vw',
        };
    } else {
        return {
            left: 'unset',
            right: '21.5vw',
            top: '16.5vw',
            width: '18vw',
            };
        }
    }
    ngOnInit(): void {
        let me = this;
        this.items = [{ label: this.tranService.translate("global.menu.alertSettings") }, { label: this.tranService.translate("global.menu.alertList") }];
        this.home = { icon: 'pi pi-home', routerLink: '/' };
        this.searchInfo = {
            name: null,
            ruleCategory: null,
            eventType: null,
            actionType : null,
            status: null,
            severity: null
        }
        this.isShowConfimChangeStatus = false;
        this.formSearchAlert = this.formBuilder.group(this.searchInfo);
        this.selectItems = [];
        this.pageNumber = 0;
        this.pageSize = 10;
        this.sort = "id,desc";
        this.userInfo = this.sessionService.userInfo;
        console.log(this.userInfo)
        this.statusAlertForDetail = CONSTANTS.ALERT_STATUS;
        this.alertInfo = {
            name: null,
            customerId: null,
            statusSim: null,
            subscriptionNumber: null,
            groupId: null,
            interval: null,
            count: null,
            unit: null,
            value: null,
            description: null,
            severity: null,
            listAlertReceivingGroupId: [],
            url: null,
            emailList: null,
            emailSubject: null,
            emailContent: null,
            smsList: null,
            smsContent: null,
            ruleCategory : 1,
            eventType :  null,
            appliedPlan: null,
            actionType:0,
            walletName:null,
            notifyInterval:null,
            notifyRepeat: null,
            typeAlert: null,
            sendTypeEmail: true,
            sendTypeSMS: null,
            status : null,
            groupName : null,
            customerName : null,
            customerCode : null,
            walletSubCode: null,
            createdBy: null,
        }

        this.formAlertDetail = this.formBuilder.group(this.alertInfo);
        this.formAlertDetail.controls['name'].disable()
        this.formAlertDetail.controls['severity'].disable()
        this.formAlertDetail.controls['statusSim'].disable()
        this.formAlertDetail.controls['description'].disable()
        this.formAlertDetail.controls['customerId'].disable()
        this.formAlertDetail.controls['groupId'].disable()
        this.formAlertDetail.controls['subscriptionNumber'].disable()
        this.formAlertDetail.controls['unit'].disable()
        this.formAlertDetail.controls['count'].disable()
        this.formAlertDetail.controls['interval'].disable()
        this.formAlertDetail.controls['value'].disable()
        this.formAlertDetail.controls['listAlertReceivingGroupId'].disable()
        this.formAlertDetail.controls['url'].disable()
        this.formAlertDetail.controls['emailList'].disable()
        this.formAlertDetail.controls['emailSubject'].disable()
        this.formAlertDetail.controls['emailContent'].disable()
        this.formAlertDetail.controls['smsList'].disable()
        this.formAlertDetail.controls['smsContent'].disable()
        this.formAlertDetail.controls['ruleCategory'].disable()
        this.formAlertDetail.controls['eventType'].disable()
        this.formAlertDetail.controls['appliedPlan'].disable()
        this.formAlertDetail.controls['actionType'].disable()
        this.formAlertDetail.controls['notifyInterval'].disable()
        this.formAlertDetail.controls['notifyRepeat'].disable()
        this.formAlertDetail.controls['typeAlert'].disable();
        this.formAlertDetail.controls['sendTypeEmail'].disable();
        this.formAlertDetail.controls['sendTypeSMS'].disable();
        this.formAlertDetail.controls["listAlertReceivingGroupId"].disable();

        this.statusAlert = [
            {name: this.tranService.translate("alert.status.active"),value:CONSTANTS.ALERT_STATUS.ACTIVE},
            {name: this.tranService.translate("alert.status.inactive"),value:CONSTANTS.ALERT_STATUS.INACTIVE},
        ]
        this.statusSim = [
            {name: this.tranService.translate("alert.statusSim.outPlan"),value:CONSTANTS.ALERT_STATUS_SIM.OUT_PLAN},
            {name: this.tranService.translate("alert.statusSim.outLine"),value:CONSTANTS.ALERT_STATUS_SIM.OUT_LINE},
            {name: this.tranService.translate("alert.statusSim.disconnected"),value:CONSTANTS.ALERT_STATUS_SIM.DISCONNECTED},
            {name: this.tranService.translate("alert.statusSim.newConnection"),value:CONSTANTS.ALERT_STATUS_SIM.NEW_CONNECTION},
        ]
        this.severityOptions = [
            {name: this.tranService.translate("alert.severity.critical"), value: CONSTANTS.ALERT_SEVERITY.CRITICAL},
            {name: this.tranService.translate("alert.severity.major"), value: CONSTANTS.ALERT_SEVERITY.MAJOR},
            {name: this.tranService.translate("alert.severity.minor"), value: CONSTANTS.ALERT_SEVERITY.MINOR},
            {name: this.tranService.translate("alert.severity.info"), value: CONSTANTS.ALERT_SEVERITY.INFO}
        ]

        this.ruleOptions = [
            {name:this.tranService.translate("alert.ruleCategory.management"), value: CONSTANTS.ALERT_RULE_CATEGORY.MANAGEMENT},
            {name:this.tranService.translate("alert.ruleCategory.monitoring"), value: CONSTANTS.ALERT_RULE_CATEGORY.MONITORING}
        ]

        this.eventOptions = [
            {name:me.tranService.translate("alert.eventType.exceededPakage"), value:CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_PACKAGE},
            {name:me.tranService.translate("alert.eventType.exceededValue"), value:CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_VALUE},
            // {name:me.tranService.translate("alert.eventType.sessionEnd"), value:CONSTANTS.ALERT_EVENT_TYPE.SESSION_END},
            // {name:me.tranService.translate("alert.eventType.sessionStart"), value:CONSTANTS.ALERT_EVENT_TYPE.SESSION_START},
            {name:me.tranService.translate("alert.eventType.smsExceededPakage"), value:CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_PACKAGE},
            {name:me.tranService.translate("alert.eventType.smsExceededValue"), value:CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_VALUE},
            {name:me.tranService.translate("alert.eventType.owLock"), value:CONSTANTS.ALERT_EVENT_TYPE.ONE_WAY_LOCK},
            {name:me.tranService.translate("alert.eventType.twLock"), value:CONSTANTS.ALERT_EVENT_TYPE.TWO_WAY_LOCK},
            // {name:me.tranService.translate("alert.eventType.noConection"), value:CONSTANTS.ALERT_EVENT_TYPE.NO_CONECTION},
            // {name:me.tranService.translate("alert.eventType.simExp"), value:CONSTANTS.ALERT_EVENT_TYPE.SIM_EXP},
            {name:me.tranService.translate("alert.eventType.dataWalletExp") , value:CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP},
            {name:me.tranService.translate("alert.eventType.owtwlock") , value:CONSTANTS.ALERT_EVENT_TYPE.ONE_WAY_TWO_WAY_LOCK},
            {name:me.tranService.translate("alert.eventType.walletThreshold") , value:CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD}

        ]
        this.eventOptionManagement = this.eventOptions.filter(item =>
            item.value == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_PACKAGE ||
            item.value == CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_PACKAGE||
            item.value == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_VALUE ||
            item.value == CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_VALUE ||
            item.value == CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD ||
            item.value == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP )

        this.eventOptionMonitoring = this.eventOptions.filter(item =>
            item.value == CONSTANTS.ALERT_EVENT_TYPE.ONE_WAY_LOCK ||
            item.value == CONSTANTS.ALERT_EVENT_TYPE.TWO_WAY_LOCK ||
            item.value == CONSTANTS.ALERT_EVENT_TYPE.ONE_WAY_TWO_WAY_LOCK);


        this.actionOptions = [
            {name:this.tranService.translate("alert.actionType.alert"), value:CONSTANTS.ALERT_ACTION_TYPE.ALERT}
            // ,
            // {name:this.tranService.translate("alert.actionType.api"), value:CONSTANTS.ALERT_ACTION_TYPE.API}
        ]

        this.columns = [
            {
                name: this.tranService.translate("alert.label.name"),
                key: "name",
                size: "400px",
                align: "left",
                isShow: true,
                isSort: false,
                style:{
                    cursor: "pointer",
                    color: "var(--mainColorText)",
                    display: 'inline-block',
                    maxWidth: '400px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                },
                isShowTooltip: true,
                funcClick(id, item) {
                    me.alertId = id;
                    me.getDetail();
                    me.isShowModalDetail = true;
                },
            },
            {
                name: this.tranService.translate("alert.label.rule"),
                key: "ruleCategory",
                size: "250px",
                align: "left",
                funcConvertText(value) {
                    if(value == CONSTANTS.ALERT_RULE_CATEGORY.MONITORING){
                        return me.tranService.translate("alert.ruleCategory.monitoring");
                    }else if(value == CONSTANTS.ALERT_RULE_CATEGORY.MANAGEMENT){
                        return me.tranService.translate("alert.ruleCategory.management");
                    }else{
                        return "";
                    }
                },
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("alert.label.event"),
                key: "eventType",
                size: "300px",
                align: "left",
                funcConvertText(value) {
                    if(value == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_PACKAGE){
                        return me.tranService.translate("alert.eventType.exceededPakage");
                    }else if(value == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_VALUE){
                        return me.tranService.translate("alert.eventType.exceededValue");
                    }else if(value == CONSTANTS.ALERT_EVENT_TYPE.SESSION_END){
                        return me.tranService.translate("alert.eventType.sessionEnd");
                    }else if(value == CONSTANTS.ALERT_EVENT_TYPE.SESSION_START){
                        return me.tranService.translate("alert.eventType.sessionStart");
                    }else if(value == CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_PACKAGE){
                        return me.tranService.translate("alert.eventType.smsExceededPakage");
                    }else if(value == CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_VALUE){
                        return me.tranService.translate("alert.eventType.smsExceededValue");
                    }else if(value == CONSTANTS.ALERT_EVENT_TYPE.ONE_WAY_LOCK){
                        return me.tranService.translate("alert.eventType.owLock");
                    }else if(value == CONSTANTS.ALERT_EVENT_TYPE.TWO_WAY_LOCK){
                        return me.tranService.translate("alert.eventType.twLock");
                    }else if(value == CONSTANTS.ALERT_EVENT_TYPE.NO_CONECTION){
                        return me.tranService.translate("alert.eventType.noConection");
                    }else if(value == CONSTANTS.ALERT_EVENT_TYPE.SIM_EXP){
                        return me.tranService.translate("alert.eventType.simExp");
                    }else if(value == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP){
                        return me.tranService.translate("alert.eventType.dataWalletExp");
                    }else if(value == CONSTANTS.ALERT_EVENT_TYPE.ONE_WAY_TWO_WAY_LOCK){
                        return me.tranService.translate("alert.eventType.owtwlock");
                    }else if (value == CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD){
                        return me.tranService.translate("alert.eventType.walletThreshold");
                    }else{
                        return "";
                    }
                },
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("alert.label.action"),
                key: "actionType",
                size: "200px",
                align: "left",
                isShow: true,
                isSort: false,
                funcConvertText(value) {
                    if(value == CONSTANTS.ALERT_ACTION_TYPE.ALERT){
                        return me.tranService.translate("alert.actionType.alert");
                    }else if(value == CONSTANTS.ALERT_ACTION_TYPE.API){
                        return me.tranService.translate("alert.actionType.api");
                    }else{
                        return "";
                    }
                },
            },
            {
                name: this.tranService.translate("alert.label.level"),
                key: "severity",
                size: "200px",
                align: "left",
                isShow: true,
                isSort: false,
                funcConvertText(value) {
                    if(value == CONSTANTS.ALERT_SEVERITY.CRITICAL){
                        return me.tranService.translate("alert.severity.critical");
                    }else if(value == CONSTANTS.ALERT_SEVERITY.MAJOR){
                        return me.tranService.translate("alert.severity.major");
                    }else if(value == CONSTANTS.ALERT_SEVERITY.MINOR){
                        return me.tranService.translate("alert.severity.minor");
                    }else if(value == CONSTANTS.ALERT_SEVERITY.INFO){
                        return me.tranService.translate("alert.severity.info");
                    }else{
                        return "";
                    }
                },
            },
            {
                name: this.tranService.translate("alert.label.status"),
                key: "status",
                size: "180px",
                align: "left",
                isShow: true,
                isSort: false,
                funcConvertText(value) {
                    if(value == CONSTANTS.ALERT_STATUS.ACTIVE){
                        return me.tranService.translate("alert.status.active");
                    }else if(value == CONSTANTS.ALERT_STATUS.INACTIVE){
                        return me.tranService.translate("alert.status.inactive");
                    }else{
                        return "";
                    }
                },
                funcGetClassname: (value) => {
                    if(value == CONSTANTS.ALERT_STATUS.ACTIVE){
                        return ['p-2', "text-green-800", "bg-green-100", "border-round","inline-block"];
                    }else if(value == CONSTANTS.ALERT_STATUS.INACTIVE){
                        return ['p-2', 'text-red-700', "bg-red-100","border-round","inline-block"];
                    }
                    return [];
                },
                style:{
                    color:"white"
                }
            }
        ]

        this.optionTable = {
            hasClearSelected: true,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
            action: [
                {
                    icon: "pi pi-pencil",
                    tooltip: this.tranService.translate('global.button.edit'),
                    func: function (id, item) {
                        if (item.eventType == CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD) {
                            me.router.navigate([`/alerts/wallet-threshold/edit/${id}`]);
                        } else if (item.eventType == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP) {
                            me.router.navigate([`/alerts/wallet-expiry/edit/${id}`]);
                        } else {
                            me.router.navigate([`/alerts/edit/${id}`]);
                        }
                    },
                    funcAppear: function (id, item) {
                        if (item.eventType == CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD)
                            return item.status == CONSTANTS.ALERT_STATUS.INACTIVE && me.checkAuthen([CONSTANTS.PERMISSIONS.ALERT.UPDATE_WALLET_THRESHOLD]) && item.createdBy == me.userInfo.id
                        else if (item.eventType == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP)
                            return item.status == CONSTANTS.ALERT_STATUS.INACTIVE && me.checkAuthen([CONSTANTS.PERMISSIONS.ALERT.UPDATE_WALLET_EXPIRY]) && item.createdBy == me.userInfo.id
                        else
                        return me.checkAuthen([CONSTANTS.PERMISSIONS.ALERT.UPDATE]) && item.status == CONSTANTS.ALERT_STATUS.INACTIVE
                    }
                },
                {
                    icon: "pi pi-trash",
                    tooltip: this.tranService.translate("global.button.delete"),
                    func: function (id, item) {
                        me.messageCommonService.confirm(
                            me.tranService.translate("global.message.titleConfirmDeleteAlert"),
                            me.tranService.translate("global.message.confirmDeleteAlert"),
                            {
                                ok:()=>{
                                    me.alertService.deleteById(parseInt(id),(response)=>{
                                        me.messageCommonService.success(me.tranService.translate("global.message.deleteSuccess"));
                                        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);
                                    })
                                },
                                cancel: ()=>{

                                }
                            }
                        )
                    },
                    funcAppear: function (id, item) {
                        if (item.eventType == CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD)
                            return me.checkAuthen([CONSTANTS.PERMISSIONS.ALERT.DELETE]) && item.status == CONSTANTS.ALERT_STATUS.INACTIVE && me.checkAuthen([CONSTANTS.PERMISSIONS.ALERT.UPDATE_WALLET_THRESHOLD]) && item.createdBy == me.userInfo.id
                        else if (item.eventType == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP)
                            return me.checkAuthen([CONSTANTS.PERMISSIONS.ALERT.DELETE]) && item.status == CONSTANTS.ALERT_STATUS.INACTIVE && me.checkAuthen([CONSTANTS.PERMISSIONS.ALERT.UPDATE_WALLET_EXPIRY]) && item.createdBy == me.userInfo.id
                        else
                        return me.checkAuthen([CONSTANTS.PERMISSIONS.ALERT.DELETE]) && item.status == CONSTANTS.ALERT_STATUS.INACTIVE
                    }
                },

            ]
        }
        this.customerNameOptions = []
        this.listGroupByCustomer = []
        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
        this.checkIfMobile();
    }

    onSubmitSearch(){
        this.pageNumber = 0;
        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
    }

    search(page, limit, sort, params){
        let me = this;
        this.pageNumber = page;
        this.pageSize = limit;
        this.sort = sort;
        let dataParams = {
            page,
            size: limit,
            sort
        }
        Object.keys(this.searchInfo).forEach(key => {
            if(this.searchInfo[key] != null){
                dataParams[key] = this.searchInfo[key];
            }
        })
        me.messageCommonService.onload();
        this.alertService.search(dataParams, (response) => {
            me.dataSet = {
                content: response.content,
                total: response.totalElements
            }
            // me.searchInfoStandard = {...me.searchInfo}
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }

    protected readonly CONSTANTS = CONSTANTS;

    getOptionEventType() {
        if(this.searchInfo.ruleCategory == CONSTANTS.ALERT_RULE_CATEGORY.MANAGEMENT) {
            return this.eventOptionManagement;
        }else if(this.searchInfo.ruleCategory == CONSTANTS.ALERT_RULE_CATEGORY.MONITORING) {
            return this.eventOptionMonitoring;
        }
        return this.eventOptions;
    };

    getDetail(){
        let me = this;
        me.messageCommonService.onload()
        this.alertService.getById(Number(me.alertId), (response)=>{
            me.alertResponse = {...response};
            me.alertInfo = response;
            me.alertInfo.name = response.name;
            me.alertInfo.customerId = {id: response.customerId};
            // me.alertInfo.customerCode = response.customerCode;
            me.alertInfo.subscriptionNumber = response.subscriptionNumber;
            me.alertInfo.description = response.description;
            me.alertInfo.groupId = response.groupId;
            me.alertInfo.listAlertReceivingGroupId = response.listAlertReceivingGroup;
            me.alertInfo.emailList = response.emailList;
            me.alertInfo.emailSubject = response.emailSubject;
            me.alertInfo.emailContent = response.emailContent;
            me.alertInfo.smsList = response.smsList;
            me.alertInfo.smsContent = response.smsContent;
            me.alertInfo.url = response.url;
            me.alertInfo.interval = response.interval;
            me.alertInfo.count = response.count;
            me.alertInfo.unit = response.unit;
            me.alertInfo.value = response.eventType == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP ? response.value / 24 : response.value,
                me.alertInfo.severity = response.severity;
            me.alertInfo.actionType = response.actionType;
            me.alertInfo.ruleCategory = response.ruleCategory;
            me.alertInfo.eventType = response.eventType;
            me.alertInfo.appliedPlan = response.dataPackCode;
            me.alertInfo.status = response.status;
            me.alertInfo.createdBy = response.createdBy;
            me.statusTemp = response.status;
            me.alertInfo.notifyInterval = response.notifyInterval / 24;
            if(response.notifyRepeat == 1){
                this.repeat = true
            }else if (response.notifyRepeat == 0){
                this.repeat = false
            }
            me.getListRatingPlan();
            me.restoreTypeAlert(response);
            me.alertInfo.notifyRepeat = response.notifyRepeat
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    };

    // onChangeStatus(event) {
    //     let me = this;
    //     setTimeout(function(){
    //         if(event.checked == CONSTANTS.ALERT_STATUS.ACTIVE) {
    //             me.alertInfo.status = CONSTANTS.ALERT_STATUS.INACTIVE;
    //         }else {
    //             me.alertInfo.status = CONSTANTS.ALERT_STATUS.ACTIVE;
    //         }
    //         me.changeStatus(event.checked)
    //     })
    // };

    restoreTypeAlert(response: any): any {
        this.alertInfo.typeAlert = []
        if (response.listAlertReceivingGroupId != null && response.listAlertReceivingGroupId.length > 0) {
            this.alertInfo.typeAlert.push("Group")
        }
        if (response.emailList != null) {
            this.alertInfo.typeAlert.push("Email")
        }
        if (response.smsList != null) {
            this.alertInfo.typeAlert.push("SMS")
        }
    }

    // changeStatus(value){
    //     let me = this;
    //
    //     me.messageCommonService.confirm(
    //         me.tranService.translate("global.message.titleConfirmChangeStatusAlert"),
    //         me.tranService.translate("global.message.confirmChangeStatusAlert"),
    //         {
    //             ok:()=>{
    //                 let dataBody = {
    //                     id : me.alertId,
    //                     status: value
    //                 }
    //                 me.alertService.changeStatus(dataBody,(response)=>{
    //                     me.messageCommonService.success(me.tranService.translate("global.message.changeStatusSuccess"));
    //                     me.alertInfo.status = value;
    //                     me.statusTemp = value;
    //                     me.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
    //                 })
    //             },
    //             cancel: ()=>{
    //
    //             }
    //         }
    //     )
    // };

    getListRatingPlan() {
        let me = this;
        console.log(me.alertInfo.eventType)
        if (me.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP) {
            me.trafficWalletService.searchPakageCode({}, (response) => {
                me.appliedPlanOptions = (response || []).map(el => ({code: el}))
                if(me.alertResponse.dataPackCode != null && me.alertResponse.dataPackCode.length > 0) {
                    me.appliedPlanOptions.push(...me.alertResponse.dataPackCode.map(el=> ({code : el})))
                }
            })
        } else if (me.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD) {
            console.log(me.alertInfo.walletSubCode)
            me.trafficWalletService.search({getToView: "1", subCodeView: me.alertInfo.walletSubCode}, (response) => {
                me.walletOptions = (response.content || [])
                console.log(response.content)
                console.log(me.walletOptions)
            })
        }
    }
    checkChangeStatus() {
        let me = this;
        if (me.checkAuthen([CONSTANTS.PERMISSIONS.ALERT.CHANGE_STATUS])) {
            if (me.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP || me.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD) {
                return me.alertInfo.createdBy == me.userInfo.id ? true : false;
            } else {
                return true;
            }
        } else {
            return false;
        }
    }
    onRuleCategoryChange() {
        this.searchInfo.eventType = null;
    }
    showConfimChangeStatus() {
        let me = this;
        me.isShowConfimChangeStatus = true;
    }

    changeStatus() {
        let me = this;
        let value = me.alertInfo.status;
        let dataBody = {
            id: me.alertId,
            status: value
        }
        // console.log("status " + me.alertInfo.status)
        // console.log("val " + value)
        me.messageCommonService.onload();
        me.alertService.changeStatus(dataBody, (response) => {
            me.messageCommonService.offload();
            me.isShowConfimChangeStatus = false;
            me.messageCommonService.success(me.tranService.translate("global.message.changeStatusSuccess"));
            me.alertInfo.status = value;
            me.statusTemp = value;
            me.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
        })
    }
    revertStatus() {
        let me = this;
        me.isShowConfimChangeStatus = false;
        me.alertInfo.status = me.statusTemp
        // console.log("status " + me.alertInfo.status);
    }
}
