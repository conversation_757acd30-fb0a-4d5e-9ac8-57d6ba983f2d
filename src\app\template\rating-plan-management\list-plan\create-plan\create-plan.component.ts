import {On<PERSON><PERSON>roy, Injector} from '@angular/core';
import {Component, Inject} from '@angular/core';
import {
    AbstractControl,
    AsyncValidatorFn, FormBuilder,
    FormControl,
    FormGroup,
    ValidationErrors,
    ValidatorFn,
    Validators
} from '@angular/forms';
import {MenuItem} from 'primeng/api';
import {Observable, Subscription, debounceTime, map, switchMap, take} from 'rxjs';
import {ComponentBase} from 'src/app/component.base';
import {RatingPlanService} from 'src/app/service/rating-plan/RatingPlanService';
import {AccountService} from "../../../../service/account/AccountService";
import {CONSTANTS} from 'src/app/service/comon/constants';
import {ComboLazyControl} from "../../../common-module/combobox-lazyload/combobox.lazyload";
import {ColumnInfo, OptionTable} from "../../../common-module/table/table.component";
import {da, el} from "@fullcalendar/core/internal-common";

interface DropDownValue {
    id: string;
    name: string;
}

@Component({
    selector: 'app-create-plan',
    templateUrl: './create-plan.component.html',
})
export class CreatePlanComponent extends ComponentBase implements OnDestroy {
    home: MenuItem;
    items: MenuItem[];
    isPlanCodeExisted: boolean = true;
    isPlanNameExisted: boolean = true;

    customerTypes: DropDownValue[] | undefined;
    ratingScopes: DropDownValue[] | undefined;
    provinces: any[] | undefined;
    isPlanCodeValid: boolean = false;
    isPlaneNameValid: boolean = false;
    isDispatchCodeValid: boolean = false;
    isCustomerTypeValid: boolean = false;
    isSubscriptionFeeValid: boolean = false;
    isSubscriptionTypeValid: boolean = false; // Đã set default
    isPlanScopeValid: boolean = false;
    isProvinceCodeValid: boolean = false;
    isPlanCycleValid: boolean = false;
    isDurationValid: boolean = false;
    isDescriptionValid: boolean = false;
    isFreeDataValid: boolean = false;
    isLimitInsideSMSFreeValid: boolean = false;
    isLimitOutsideSMSFreeValid: boolean = false;
    isFeePerUnitNumberatorValid: boolean = false;
    isFeePerUnitDenominatorValid: boolean = false;
    isSqueezeSpeedNumberatorValid: boolean = false;
    isSqueezeSpeedDenominatorValid: boolean = false;
    isFeePerInsideSMSValid: boolean = false;
    isFeePerOutsideSMSValid: boolean = false;
    isMaxFeeValid: boolean = false;
    isDataMaxValid: boolean = false;
    isShowDialogAddCustomerAccount: boolean = false;
    searchInfoUser: {
        username: string | null,
        fullName: string | null,
        email: string | null,
        provinceCode: any | null,
    }
    listProvince: any[] | undefined;
    selectItemsUser: Array<{id: number, provinceCode: string, [key:string]:any}> = [];
    selectItemsUserOld: Array<{id: number, provinceCode: string, [key:string]:any}> = [{id: -1, provinceCode: ""}];
    pageNumberAssign: number;
    pageSizeAssign: number;
    sortAssign: string;
    formSearchUser: any;
    subPlanCode: Subscription;
    subPlaneName: Subscription;
    subDispatchCode: Subscription;
    subCustomerType: Subscription;
    subSubscriptionFee: Subscription;
    subSubscriptionType: Subscription;
    subPlanScope: Subscription;
    subProvinceCode: Subscription;
    subPlanCycle: Subscription;
    subDuration: Subscription;
    subDescription: Subscription;
    subFreeData: Subscription;
    subLimitInsideSMSFree: Subscription;
    subLimitOutsideSMSFree: Subscription;
    subFeePerUnitNumberator: Subscription;
    subFeePerUnitDenominator: Subscription;
    subSqueezeSpeedNumberator: Subscription;
    subSqueezeSpeedDenominator: Subscription;
    subFeePerInsideSMS: Subscription;
    subFeePerOutsideSMS: Subscription;
    subMaxFee: Subscription;
    subDataMax: Subscription;
    columnsInfoUser: Array<ColumnInfo>;
    dataSetAssignPlan: {
        content: Array<any>,
        total: number
    };
    optionTableAddCustomerAccount: OptionTable;
    sort: string;

    controlComboSelect: ComboLazyControl = new ComboLazyControl();
    customerCode: [];

    isProvince = false
    isCustomer = false

    cycles: DropDownValue[] | undefined;
    isFlexible: boolean = false;

    paidCategories: any[] = [
        {name: 'Trả trước', key: '1'},
        {name: 'Trả sau', key: '0'},
    ];

    selectedPaidCategory: any = null;

    placeHolderDescription: string = this.tranService.translate("groupSim.placeHolder.description");

    provinceInfo: string = "";
    userType = this.sessionService.userInfo.type;
    allUserType = CONSTANTS.USER_TYPE;

    constructor(@Inject(RatingPlanService) public ratingPlanService: RatingPlanService,
                @Inject(AccountService) private accountService: AccountService,
                private formBuilder: FormBuilder,
                injector: Injector) {
        super(injector);
    }

    placeHolder = {
        planCode: this.tranService.translate("ratingPlan.placeHolder.planCode"),
        planName: this.tranService.translate("ratingPlan.placeHolder.planeName"),
        dispatchCode: this.tranService.translate("ratingPlan.placeHolder.dispatchCode"),
        customerType: this.tranService.translate("ratingPlan.placeHolder.customerType"),
        description: this.tranService.translate("ratingPlan.placeHolder.description"),
        subscriptionFee: this.tranService.translate("ratingPlan.placeHolder.subscriptionFee"),
        subscriptionType: this.tranService.translate("ratingPlan.placeHolder.subscriptionType"),
        planScope: this.tranService.translate("ratingPlan.placeHolder.planScope"),
        provinceCode: this.tranService.translate("ratingPlan.placeHolder.provinceCode"),
        planCycle: this.tranService.translate("ratingPlan.placeHolder.planCycle"),
        duration: this.tranService.translate("ratingPlan.placeHolder.duration"),
        freeData: this.tranService.translate("ratingPlan.placeHolder.freeData"),
        insideSMSFree: this.tranService.translate("ratingPlan.placeHolder.insideSMSFree"),
        outsideSMSFree: this.tranService.translate("ratingPlan.placeHolder.outsideSMSFree"),
        feePerUnit: this.tranService.translate("ratingPlan.placeHolder.feePerUnit"),
        squeezedSpeed: this.tranService.translate("ratingPlan.placeHolder.squeezeSpeed"),
        feePerInsideSMS: this.tranService.translate("ratingPlan.placeHolder.feePerInsideSMS"),
        feePerOutsideSMS: this.tranService.translate("ratingPlan.placeHolder.feePerOutsideSMS"),
        maxFee: this.tranService.translate("ratingPlan.placeHolder.maxFee"),
        dataMax: this.tranService.translate("ratingPlan.placeHolder.dataMax"),
    }

    customCodeCharacterValidator(): ValidatorFn {
        return (control: AbstractControl): ValidationErrors | null => {
            const value = control.value;
            const isValid = /^[a-zA-Z0-9\-_]*$/.test(value);
            return isValid ? null : {'invalidCharacters': {value}};
        };
    }

    customNameCharacterValidator(): ValidatorFn {
        return (control: AbstractControl): ValidationErrors | null => {
            const value = control.value;
            if (value == '') {
                return null;
            }
            const isValid = /^[a-zA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠàáâãèéêìíòóôõùúăđĩũơỲỴÝỳỵỷỹƯứừữựụợ́̉̃À-ỹ0-9 _-]+$/.test(value);
            return isValid ? null : {'invalidCharacters': {value}};
        };
    }

    checkCodeExisted(query: {}): Observable<number> {
        return new Observable(observer => {
            this.ratingPlanService.checkingPlanCodeExisted(query, (response) => {
                observer.next(response);
                observer.complete();
            });
        });
    }

    checkNameExisted(query: {}): Observable<number> {
        return new Observable(observer => {
            this.ratingPlanService.checkingPlanNameExisted(query, (response) => {
                observer.next(response);
                observer.complete();
            });
        });
    }

    planCodeValidator(): AsyncValidatorFn {
        return (control: AbstractControl): Observable<ValidationErrors | null> => {
            return control.valueChanges.pipe(
                debounceTime(500),
                switchMap(value => this.checkCodeExisted({code: value})),
                take(1),
                map(result => {
                    // console.log('Map result:', result);
                    if (result === 0) {
                        this.isPlanCodeExisted = false;
                        return null;
                    } else {
                        this.isPlanCodeExisted = true;
                        return {'exited': true};
                    }
                }),
            );
        };
    }

    planNameValidator(): AsyncValidatorFn {
        return (control: AbstractControl): Observable<ValidationErrors | null> => {
            return control.valueChanges.pipe(
                debounceTime(500),
                switchMap(value => this.checkNameExisted({name: value})),
                take(1),
                map(result => {
                    // console.log('Map result:', result);
                    if (result === 0) {
                        this.isPlanNameExisted = false
                        return null;
                    } else {
                        this.isPlanNameExisted = true
                        return {'exited': true};
                    }
                }),
            );
        };
    }

    blockMinus(event: KeyboardEvent) {
        const invalidChars = ['-', '+', ',', '.', 'e', 'E', 'r', 'R']; // Danh sách các ký tự không cho phép
        if (invalidChars.includes(event.key)) {
            event.preventDefault();
        }
        // if (event.key === '-' || event.key ==='+' || event.key === ',' || event.key === '.') {
        //   event.preventDefault();
        // }
    }

    checkInputValue(event: InputEvent) {
        const input = event.target as HTMLInputElement;
        input.value = input.value.replace(/[^0-9]/g, ''); // Chỉ cho phép nhập số
    }


  createPlanForm = new FormGroup({
    status : new FormControl(2),
    code: new FormControl("", [Validators.required,Validators.maxLength(64), this.customCodeCharacterValidator()],[this.planCodeValidator()]),
    name: new FormControl("", [Validators.required, Validators.maxLength(255), this.customNameCharacterValidator()], [this.planNameValidator()]),
    dispatchCode: new FormControl("", [Validators.required, Validators.maxLength(64), this.customCodeCharacterValidator()]),
    customerType: new FormControl("", [Validators.required]),
    subscriptionFee: new FormControl(0, [Validators.required, Validators.max(9999999999), Validators.min(0) ]),
    paidType: new FormControl("", [Validators.required]),
    ratingScope: new FormControl("0", [Validators.required]),
    provinceCode: new FormControl({value: this.userType == CONSTANTS.USER_TYPE.ADMIN ? "": [this.sessionService.userInfo.provinceCode], disabled:!this.isProvince}, [Validators.required]),
    userIds: new FormControl(),
    cycleTimeUnit: new FormControl("", [Validators.required]),
    cycleInterval: new FormControl(0, [Validators.required, Validators.max(9999999999), Validators.min(0)]),
    reload: new FormControl(0),
    description: new FormControl("", [Validators.maxLength(255)]),//MS

    limitDataUsage: new FormControl(0, [Validators.required,Validators.max(9999999999), Validators.min(0)]),
    limitSmsInside: new FormControl(0, [Validators.max(9999999999), Validators.min(0)]),//insideSMSFree
    limitSmsOutside: new FormControl(0, [Validators.max(9999999999), Validators.min(0)]),//outsideSMSFree
    dataMax: new FormControl(0, [Validators.required,Validators.max(9999999999), Validators.min(0)]),

    flexible: new FormControl(0),
    feePerDataUnit: new FormControl({value: 0, disabled: !this.isFlexible}, [Validators.required, Validators.max(9999999999), Validators.min(0)]),
    dataRoundUnit: new FormControl({value: 0, disabled: !this.isFlexible}, [Validators.required, Validators.max(9999999999), Validators.min(0)]),
    downSpeed: new FormControl({value: 0, disabled: !this.isFlexible}, [Validators.max(9999999999), Validators.min(0)]),
    squeezedSpeed: new FormControl({value: 0, disabled: !this.isFlexible}, [ Validators.max(9999999999), Validators.min(0) ]),
    feeSmsInside: new FormControl({value: 0, disabled: !this.isFlexible}, [Validators.max(9999999999), Validators.min(0)]),
    feeSmsOutside: new FormControl({value: 0, disabled: !this.isFlexible}, [Validators.max(9999999999), Validators.min(0)]),
    maximumFee: new FormControl({value: 0, disabled: !this.isFlexible}, [Validators.max(9999999999), Validators.min(0)]),
      uploadSpeed : new FormControl({value:0, disabled: !this.isFlexible}),
  });

  onDropdownChange(event) {
    if(event.value==0){
      this.isProvince = false
      this.isCustomer = false
      this.createPlanForm.get('provinceCode').disable({ emitEvent: false });
      // this.createPlanForm.get('userIds').disable({ emitEvent: false });
    }else if (event.value == 1){
        // gói cước loại khách hàng
        this.isProvince = true
        this.isCustomer = false
        this.createPlanForm.get('provinceCode').enable({ emitEvent: false });
        // this.createPlanForm.get('userIds').enable({ emitEvent: false });
        this.changeProvince();
    }else if (event.value == 2){
        // gói cước loại tỉnh/thành phố
      this.isProvince = true
      this.isCustomer = false
      this.createPlanForm.get('provinceCode').enable({ emitEvent: false });
      // this.createPlanForm.get('userIds').disable({ emitEvent: false });
        this.changeProvince();
    }
  }

  onSwitchChange() {
      // console.log(this.createPlanForm);
    if (this.isFlexible) {
      this.createPlanForm.get('feePerDataUnit').enable({ emitEvent: false });
      this.createPlanForm.get('dataRoundUnit').enable({ emitEvent: false });
      this.createPlanForm.get('downSpeed').enable({ emitEvent: false });
      this.createPlanForm.get('squeezedSpeed').enable({ emitEvent: false });
      this.createPlanForm.get('feeSmsInside').enable({ emitEvent: false });
      this.createPlanForm.get('feeSmsOutside').enable({ emitEvent: false });
      this.createPlanForm.get('maximumFee').enable({ emitEvent: false });
    } else {
      this.createPlanForm.get('feePerDataUnit').disable({ emitEvent: false });
      this.createPlanForm.get('dataRoundUnit').disable({ emitEvent: false });
      this.createPlanForm.get('downSpeed').disable({ emitEvent: false });
      this.createPlanForm.get('squeezedSpeed').disable({ emitEvent: false });
      this.createPlanForm.get('feeSmsInside').disable({ emitEvent: false });
      this.createPlanForm.get('feeSmsOutside').disable({ emitEvent: false });
      this.createPlanForm.get('maximumFee').disable({ emitEvent: false });
    }
  }

  submitForm(){
    this.messageCommonService.onload();
    let me = this
    if (this.createPlanForm.valid) {
      let data = {...this.createPlanForm.value};
      if(data.reload){
        data.reload = 1
      }else{
        data.reload = 0;
      }
      if(data.flexible){
        data.flexible = 1
          data.uploadSpeed = data.downSpeed - data.squeezedSpeed
      }else{
        data.flexible = 0;
      }
      if (this.selectItemsUser.length > 0){
          let provinceSelected = me.createPlanForm.get("provinceCode").value;
          let currentSelected = me.selectItemsUser.filter((el) => provinceSelected.includes(el.provinceCode)).map(e => e.id);
          data.userIds = currentSelected;
      }
      this.ratingPlanService.createRatingPlan(data,(response)=>{
        // console.log(response)

        this.messageCommonService.success(this.tranService.translate('global.message.saveSuccess'))
        this.router.navigate(['/plans'])
      }, null, ()=>{
          me.messageCommonService.offload();
      })
    }
  }

    onSubmitSearchUser(){
        this.pageNumberAssign = 0;
        this.searchUser(this.pageNumberAssign, this.pageSizeAssign, this.sortAssign, this.searchInfoUser);
    }

  ngOnInit(){
      this.optionTableAddCustomerAccount = {
          hasClearSelected: false,
          hasShowIndex: true,
          hasShowChoose: true,
          hasShowToggleColumn: false,
      };
      this.dataSetAssignPlan = {
          content: [],
          total: 0,
      }
      this.searchInfoUser = {
          username: null,
          fullName: null,
          email: null,
          provinceCode: null
      }
      this.formSearchUser = this.formBuilder.group(this.searchInfoUser);
    this.items = [
      { label: this.tranService.translate("global.menu.ratingplanmgmt"), routerLink: '../'},
      { label: this.tranService.translate("global.menu.listplan"), routerLink: '../'},
      { label: this.tranService.translate("global.button.create") },
    ];
      this.columnsInfoUser = [
          {
              name: this.tranService.translate("ratingPlan.label.username"),
              key: "username",
              size: "150px",
              align: "left",
              isShow: true,
              isSort: false,
          },
          {
              name: this.tranService.translate("ratingPlan.label.fullName"),
              key: "fullName",
              size: "150px",
              align: "left",
              isShow: true,
              isSort: false,
          },
          {
              name: this.tranService.translate("ratingPlan.label.email"),
              key: "email",
              size: "150px",
              align: "left",
              isShow: true,
              isSort: false,
          },
          {
              name: this.tranService.translate("ratingPlan.label.province"),
              key: "provinceName",
              size: "150px",
              align: "left",
              isShow: true,
              isSort: false,
          },
      ]
        this.customerTypes = [
            {
                id: "1",
                name: this.tranService.translate("ratingPlan.customerType.personal"),
            },
            {
                id: "2",
                name: this.tranService.translate("ratingPlan.customerType.enterprise"),
            },
            {
                id: "0",
                name: this.tranService.translate("ratingPlan.customerType.agency"),
            },
            // ,
            // {
            //   id: "0",
            //   name: this.tranService.translate("ratingPlan.customerType.agency"),
            // },
        ];
        this.ratingScopes = [
            {
                id: "0",
                name: this.tranService.translate("ratingPlan.ratingScope.nativeWide"),
            },
            {
                id: "2",
                name: this.tranService.translate("ratingPlan.ratingScope.province"),
            },
            {
                id: "1",
                name: this.tranService.translate("ratingPlan.ratingScope.customer"),
            },
        ];

        this.cycles = [
            {
                id: "1",
                name: this.tranService.translate("ratingPlan.cycle.day")
            },
            {
                id: "3",
                name: this.tranService.translate("ratingPlan.cycle.month")
            },
        ]
        this.accountService.getListProvince((data) => {
            let me = this;
            this.provinces = data.map(el => {
                if (el.code == me.sessionService.userInfo.provinceCode) {
                    me.provinceInfo = `${el.name} - ${el.code}`;
                }
                return {
                    code: el.code,
                    name: `${el.name} - ${el.code}`
                }
            })
        })

        this.selectedPaidCategory = this.paidCategories[0].key

        this.subPlanCode = this.createPlanForm.get('code').statusChanges.subscribe(() => {
            const errors = this.createPlanForm.get('code').errors;
            if (errors) {
                this.isPlanCodeValid = true;
            } else {
                this.isPlanCodeValid = false;
            }
        });

        this.subPlaneName = this.createPlanForm.get('name').statusChanges.subscribe(() => {
            const errors = this.createPlanForm.get('name').errors;
            if (errors) {
                this.isPlaneNameValid = true;
            } else {
                this.isPlaneNameValid = false;
            }
        });

        this.subDispatchCode = this.createPlanForm.get('dispatchCode').statusChanges.subscribe(() => {
            const errors = this.createPlanForm.get('dispatchCode').errors;
            if (errors) {
                this.isDispatchCodeValid = true;
            } else {
                this.isDispatchCodeValid = false;
            }
        });

        this.subCustomerType = this.createPlanForm.get('customerType').statusChanges.subscribe(() => {
            const errors = this.createPlanForm.get('customerType').errors;
            if (errors) {
                this.isCustomerTypeValid = true;
            } else {
                this.isCustomerTypeValid = false;
            }
        });

        this.subSubscriptionFee = this.createPlanForm.get('subscriptionFee').statusChanges.subscribe(() => {
            const errors = this.createPlanForm.get('subscriptionFee').errors;
            if (errors) {
                this.isSubscriptionFeeValid = true;
            } else {
                this.isSubscriptionFeeValid = false;
            }
        });

        this.subSubscriptionType = this.createPlanForm.get('paidType').statusChanges.subscribe(() => {
            const errors = this.createPlanForm.get('paidType').errors;
            if (errors) {
                this.isSubscriptionTypeValid = true;
            } else {
                this.isSubscriptionTypeValid = false;
            }
        });

        this.subPlanScope = this.createPlanForm.get('ratingScope').statusChanges.subscribe(() => {
            const errors = this.createPlanForm.get('ratingScope').errors;
            if (errors) {
                this.isPlanScopeValid = true;
            } else {
                this.isPlanScopeValid = false;
            }
        });

        this.subProvinceCode = this.createPlanForm.get('provinceCode').statusChanges.subscribe(() => {
            const errors = this.createPlanForm.get('provinceCode').errors;
            if (errors) {
                this.isProvinceCodeValid = true;
            } else {
                this.isProvinceCodeValid = false;
            }
        });

        this.subPlanCycle = this.createPlanForm.get('cycleTimeUnit').statusChanges.subscribe(() => {
            const errors = this.createPlanForm.get('cycleTimeUnit').errors;
            if (errors) {
                this.isPlanCycleValid = true;
            } else {
                this.isPlanCycleValid = false;
            }
        });

        this.subDuration = this.createPlanForm.get('cycleInterval').statusChanges.subscribe(() => {
            const errors = this.createPlanForm.get('cycleInterval').errors;
            if (errors) {
                this.isDurationValid = true;
            } else {
                this.isDurationValid = false;
            }
        });

        this.subDescription = this.createPlanForm.get('description').statusChanges.subscribe(() => {
            const errors = this.createPlanForm.get('description').errors;
            if (errors) {
                this.isDescriptionValid = true;
            } else {
                this.isDescriptionValid = false;
            }
        });

        this.subFreeData = this.createPlanForm.get('limitDataUsage').statusChanges.subscribe(() => {
            const errors = this.createPlanForm.get('limitDataUsage').errors;
            if (errors) {
                this.isFreeDataValid = true;
            } else {
                this.isFreeDataValid = false;
            }
        });

        this.subLimitInsideSMSFree = this.createPlanForm.get('limitSmsInside').statusChanges.subscribe(() => {
            const errors = this.createPlanForm.get('limitSmsInside').errors;
            if (errors) {
                this.isLimitInsideSMSFreeValid = true;
            } else {
                this.isLimitInsideSMSFreeValid = false;
            }
        });

        this.subLimitOutsideSMSFree = this.createPlanForm.get('limitSmsOutside').statusChanges.subscribe(() => {
            const errors = this.createPlanForm.get('limitSmsOutside').errors;
            if (errors) {
                this.isLimitOutsideSMSFreeValid = true;
            } else {
                this.isLimitOutsideSMSFreeValid = false;
            }
        });

        this.subDataMax = this.createPlanForm.get('dataMax').statusChanges.subscribe(() => {
            const errors = this.createPlanForm.get('dataMax').errors;
            if (errors) {
                this.isDataMaxValid = true;
            } else {
                this.isDataMaxValid = false;
            }
        });

        this.subFeePerUnitNumberator = this.createPlanForm.get('feePerDataUnit').statusChanges.subscribe(() => {
            const errors = this.createPlanForm.get('feePerDataUnit').errors;
            if (errors) {
                this.isFeePerUnitNumberatorValid = true;
            } else {
                this.isFeePerUnitNumberatorValid = false;
            }
        });

        this.subFeePerUnitDenominator = this.createPlanForm.get('dataRoundUnit').statusChanges.subscribe(() => {
            const errors = this.createPlanForm.get('dataRoundUnit').errors;
            if (errors) {
                this.isFeePerUnitDenominatorValid = true;
            } else {
                this.isFeePerUnitDenominatorValid = false;
            }
        });

        this.subSqueezeSpeedNumberator = this.createPlanForm.get('downSpeed').statusChanges.subscribe(() => {
            const errors = this.createPlanForm.get('downSpeed').errors;
            if (errors) {
                this.isSqueezeSpeedNumberatorValid = true;
            } else {
                this.isSqueezeSpeedNumberatorValid = false;
            }
        });

        this.subSqueezeSpeedDenominator = this.createPlanForm.get('squeezedSpeed').statusChanges.subscribe(() => {
            const errors = this.createPlanForm.get('squeezedSpeed').errors;
            if (errors) {
                this.isSqueezeSpeedDenominatorValid = true;
            } else {
                this.isSqueezeSpeedDenominatorValid = false;
            }
        });

        this.subFeePerInsideSMS = this.createPlanForm.get('feeSmsInside').statusChanges.subscribe(() => {
            const errors = this.createPlanForm.get('feeSmsInside').errors;
            if (errors) {
                this.isFeePerInsideSMSValid = true;
            } else {
                this.isFeePerInsideSMSValid = false;
            }
        });

        this.subFeePerOutsideSMS = this.createPlanForm.get('feeSmsOutside').statusChanges.subscribe(() => {
            const errors = this.createPlanForm.get('feeSmsOutside').errors;
            if (errors) {
                this.isFeePerOutsideSMSValid = true;
            } else {
                this.isFeePerOutsideSMSValid = false;
            }
        });

        this.subMaxFee = this.createPlanForm.get('maximumFee').statusChanges.subscribe(() => {
            const errors = this.createPlanForm.get('maximumFee').errors;
            if (errors) {
                this.isMaxFeeValid = true;
            } else {
                this.isMaxFeeValid = false;
            }
        });

        this.home = {icon: 'pi pi-home', routerLink: '/'};
    }

    ngOnDestroy(): void {
        this.subPlanCode.unsubscribe();
        this.subPlaneName.unsubscribe();
        this.subDispatchCode.unsubscribe();
        this.subCustomerType.unsubscribe();
        this.subSubscriptionFee.unsubscribe();
        this.subSubscriptionType.unsubscribe();
        this.subPlanScope.unsubscribe();
        this.subProvinceCode.unsubscribe();
        this.subPlanCycle.unsubscribe();
        this.subDuration.unsubscribe();
        this.subDescription.unsubscribe();
        this.subFreeData.unsubscribe();
        this.subLimitInsideSMSFree.unsubscribe();
        this.subLimitOutsideSMSFree.unsubscribe();
        this.subFeePerUnitNumberator.unsubscribe();
        this.subFeePerUnitDenominator.unsubscribe();
        this.subSqueezeSpeedNumberator.unsubscribe();
        this.subSqueezeSpeedDenominator.unsubscribe();
        this.subFeePerInsideSMS.unsubscribe();
        this.subFeePerOutsideSMS.unsubscribe();
        this.subMaxFee.unsubscribe();
    }

    openDialogAddCustomerAccount() {
        let provincesSelected = this.createPlanForm.get('provinceCode').value;
        this.listProvince = this.provinces.filter((prov) =>
            provincesSelected.includes(prov.code)
        )
        // this.selectItemsUser = [];
        if (this.pageNumberAssign == null){
            this.pageNumberAssign = 0;
        }
        if (this.pageSizeAssign == null){
            this.pageSizeAssign = 10;
        }
        if (this.sortAssign == null){
            this.sortAssign = "id,desc";
        }
        this.searchInfoUser.provinceCode = this.listProvince.map(e => e.code);
        this.searchUser(this.pageNumberAssign, this.pageSizeAssign, this.sortAssign, this.searchInfoUser)
        this.isShowDialogAddCustomerAccount = true
    }


    searchUser(page, limit, sort, params){
      let  me = this;
        this.pageNumberAssign = page;
        this.pageSizeAssign = limit;
        if (sort == null || sort == undefined){
            this.sortAssign = "id,desc";
            sort = "id,desc";
        }else {
            this.sortAssign = sort;
        }
        let dataParams = {
            page,
            size: limit,
            sort,
            provinceCode: this.listProvince.map(e => e.code)
        }
        Object.keys(this.searchInfoUser).forEach(key => {
            if(this.searchInfoUser[key] != null){
                dataParams[key] = this.searchInfoUser[key];
            }
        })
        if (this.searchInfoUser.provinceCode == null){
            dataParams.provinceCode = this.listProvince.map(e => e.code);
        }
        this.selectItemsUserOld = [...this.selectItemsUser]
        this.ratingPlanService.getUserToAddAccount(dataParams, (response) => {
            me.dataSetAssignPlan = {
                    content: response.content,
                    total: response.totalElements
                }
            this.selectItemsUser = [...this.selectItemsUserOld]
            })

    }


    changeProvince() {
        let provinceSelected = this.createPlanForm.get("provinceCode").value
        let ratingScope = this.createPlanForm.get("ratingScope").value
        if (ratingScope == '1'){
            if (provinceSelected.length > 0){
                this.isCustomer = true;
            }else {
                this.isCustomer = false;
            }
        }
    }
}
