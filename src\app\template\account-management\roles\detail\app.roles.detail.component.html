<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{this.tranService.translate("global.menu.listroles")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
    <div class="col-5 flex flex-row justify-content-end align-items-center">
        <p-button styleClass="p-button-info" [label]="tranService.translate('global.button.edit')" icon="pi pi-pencil"
                  *ngIf="checkAuthen([CONSTANTS.PERMISSIONS.ROLE.UPDATE]) && (userType == CONSTANTS.USER_TYPE.ADMIN || roleInfo.type != CONSTANTS.ROLE_TYPE.ALL)"
                  (onClick)="editRole()" routerLinkActive="router-link-active" ></p-button>
    </div>
</div>
<p-card styleClass="mt-3">
    <div>
        <div class="flex flex-row justify-content-between">
            <div style="width: 49%;">
                <!-- username -->
                <div class="mt-1 ml-5 grid">
                    <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("roles.label.rolename")}}</span>
                    <span class="col">{{roleInfo.name}}</span>
                </div>
                <!-- loai tai khoan -->
                <div class="mt-1 ml-5 grid ">
                    <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("roles.label.usertype")}}</span>
                    <span class="col" >{{getType(roleInfo.type)}}</span>
                </div>
                <!-- trang thai -->
                <div class="mt-1 ml-5 grid ">
                    <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("roles.label.status")}}</span>
                    <span class="col" >{{getStatus(roleInfo.status)}}</span>
                </div>
            </div>
            <div style="width: 51%;">
                <label for="roles" class=" col-fixed inline-block mt-1 " style="width:180px;">{{tranService.translate("roles.label.rolelist")}}<span class="text-red-500">*</span></label>
                <div class="col">
                    <p-tree
                        disabled="true"
                        id="roles"
                        [value]="dataSet.content"
                        selectionMode="checkbox"
                        class="w-full md:w-30rem"
                        [(selection)]="roleInfo.roles"
                        [style]="{'max-height':'500px', 'overflow-y':'scroll'}"
                    ></p-tree>
                </div>
            </div>
        </div>
        <div class="flex flex-row justify-content-center align-items-center mt-6 mb-3">
            <p-button [label]="tranService.translate('global.button.cancel')" styleClass="p-button-secondary p-button-outlined mr-2" (click)="closeForm()"></p-button>
        </div>
    </div>
</p-card>
