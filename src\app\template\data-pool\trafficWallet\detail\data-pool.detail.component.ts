import {Component, Injector, OnInit} from "@angular/core";
import {ComponentBase} from "../../../../component.base";
import {MenuItem} from "primeng/api";
import {TrafficWalletService} from "../../../../service/datapool/TrafficWalletService";
import { ColumnInfo, OptionTable } from "src/app/template/common-module/table/table.component";
import { addDays } from "@fullcalendar/core/internal";
import {CONSTANTS} from "../../../../service/comon/constants";

@Component({
    selector: "data-pool-detail",
    templateUrl: './data-pool.detail.component.html'
})
export class DataPoolDetailComponent extends ComponentBase implements OnInit {
    optionTable: OptionTable;
    pageNumber: number;
    pageSize: number;
    sort: string;
    constructor(
        public trafficWalletService: TrafficWalletService,
        injector: Injector
    ) {
        super(injector);
    }
    searchInfoStandard:any;
    columns: Array<ColumnInfo>;
    dataSet: {
        content: Array<any>,
        total: number
    };
    items: Array<MenuItem>;
    home: MenuItem;
    walletDetail: any;
    subCodeId:string;
    listDetail:any;
    PERMISSIONS = CONSTANTS.PERMISSIONS;
    canView: boolean = false



    ngOnInit(): void {
        this.items = [
            { label: this.tranService.translate("global.menu.trafficManagement") },
            { label: this.tranService.translate("global.menu.walletList"), routerLink:"../../list" },
            { label: this.tranService.translate("global.button.view") }
        ];
        this.home = { icon: 'pi pi-home', routerLink: '/' };
        this.walletDetail ={
            payCode: 1,
            packageName: 2,
            phone: 3,
            tax: 4
        }

        this.subCodeId = this.route.snapshot.paramMap.get("id");
        this.getWalletDetail();

        this.columns = [{
            name: this.tranService.translate("datapool.label.walletCode"),
            key: "subCode",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: true
        },{
            name: this.tranService.translate("datapool.label.packageName"),
            key: "packageName",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: true
        },{
            name: this.tranService.translate("datapool.label.transactionCode"),
            key: "transactionCode",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: true
        },{
            name: this.tranService.translate("datapool.label.trafficType"),
            key: "trafficType",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: true
        },{
            name: this.tranService.translate("datapool.label.sharingDataNotType"),
            key: "trafficShare",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: true
        },{
            name: this.tranService.translate("datapool.label.shareDate"),
            key: "timeShare",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: true
        },{
            name: this.tranService.translate("datapool.label.usedDate"),
            key: "timeExpired",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: true
        }];

        this.optionTable = {
            hasClearSelected:false,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
        }
        this.pageNumber = 0;
        this.pageSize= 10;
        this.sort = "created_date,desc"
        this.dataSet ={
            content: [],
            total: 0
        }
    }

    getWalletDetail() {
        let me = this;
        let walletId = this.route.snapshot.paramMap.get("id");
        me.messageCommonService.onload();

        this.trafficWalletService.getById({subCode:this.subCodeId}, (res) => {
            this.walletDetail = res
            let startDate = this.getUnixTime(res.startDate);
            let endDate = this.getUnixTime(res.endDate);
            this.walletDetail.timeToUse = (endDate-startDate)/(60 * 60 * 24) +" ngày " +"["+this.getFormattedDateCrd(res.startDate)+"-"+this.getFormattedDateCrd(res.endDate)+"]"
            this.listDetail = res.listShared
            this.canView = true
            console.log(this.canView)
        },(error)=>{
            if(error.error.error.errorCode === "error.forbidden.view.detail"){
                this.messageCommonService.error("Bạn không có quyền truy cập thông tin này")
                this.router.navigate(["/data-pool/walletMgmt/list"])
            }
        },()=>{ this.messageCommonService.offload() })
    }

    getUnixTime(dateString: string): number {
        const date = new Date(dateString);
        return date.getTime() / 1000; // Chia cho 1000 để chuyển từ milliseconds sang seconds
    }

    parseDateTime(dateString) {
        // Split the date and time parts
        let [datePart, timePart] = dateString.split(' ');

        // Split the date part by '/'
        let dateParts = datePart.split('/');
        let day = parseInt(dateParts[0], 10);
        let month = parseInt(dateParts[1], 10) - 1; // Months are 0-based in JavaScript
        let year = parseInt(dateParts[2], 10);

        // Split the time part by ':'
        let timeParts = timePart.split(':');
        let hours = parseInt(timeParts[0], 10);
        let minutes = parseInt(timeParts[1], 10);
        let seconds = parseInt(timeParts[2], 10);

        // Create a new Date object
        return new Date(year, month, day, hours, minutes, seconds);
    }

    getFormattedDateCrd(dateString: string, addDate?: number): string {
        let date = new Date(dateString);

        if (addDate) {
            date.setUTCDate(date.getUTCDate() + addDate);
        }

        const day = date.getUTCDate().toString().padStart(2, '0'); // Lấy ngày và thêm số 0 nếu cần
        const month = (date.getUTCMonth() + 1).toString().padStart(2, '0'); // Lấy tháng (lưu ý tháng trong JavaScript bắt đầu từ 0)
        const year = date.getUTCFullYear();
        const hours = date.getUTCHours().toString().padStart(2, '0');
        const minutes = date.getUTCMinutes().toString().padStart(2, '0');
        const seconds = date.getUTCSeconds().toString().padStart(2, '0');

        return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
    }

    getFormattedDate(dateString: string, addDate?: number): string {
        let date = this.parseDateTime(dateString);

        if (addDate) {
            date.setDate(date.getDate() + addDate);
        }

        const day = date.getDate().toString().padStart(2, '0'); // Lấy ngày và thêm số 0 nếu cần
        const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Lấy tháng (lưu ý tháng trong JavaScript bắt đầu từ 0)
        const year = date.getFullYear();
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        const seconds = date.getSeconds().toString().padStart(2, '0');

        return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
    }

    goToShare() {
        this.router.navigate([`/data-pool/walletMgmt/share`, this.subCodeId]);

    }
    search(){}
}
