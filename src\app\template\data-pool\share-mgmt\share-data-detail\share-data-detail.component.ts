import {Component, Inject, Injector} from '@angular/core';
import {FormBuilder, FormControl, FormGroup} from '@angular/forms';
import {MenuItem, PrimeNGConfig} from 'primeng/api';
import {ComponentBase} from 'src/app/component.base';
import {CONSTANTS} from 'src/app/service/comon/constants';
import {SimService} from 'src/app/service/sim/SimService';
import {
    FilterInputType,
    SeperateFilterInfo,
    SeperateSearchInfo
} from 'src/app/template/common-module/search-filter-separate/search-filter-separate.component';
import {ColumnInfo, OptionTable} from 'src/app/template/common-module/table/table.component';
import {TrafficWalletService} from "../../../../service/datapool/TrafficWalletService";
import {ShareManagementService} from "../../../../service/datapool/ShareManagementService";

@Component({
  selector: 'app-share-data-detail',
  templateUrl: './share-data-detail.component.html',
  styleUrls: ['./share-data-detail.component.scss']
})
export class ShareDataDetailComponent extends ComponentBase{
  items: MenuItem[];
  home: MenuItem;
    searchInfo: {
        searchWalletCode: number | 0,
        searchPayCode: number | 0,
        value: string | " ",
        lstPackageCode: Array<any> | null,
        sharingDay:string,
        trafficType:string | " ",
        phoneReceipt:string | " "
    };
    searchInfoStandard:any;
    columns: Array<ColumnInfo>;
    dataSet: {
        content: Array<any>,
        total: number
    };
    dataStore: Array<any>;
    selectItems: Array<{imsi:number,msisdn: any,groupName:string|null,[key:string]:any}>;
    optionTable: OptionTable;
    pageNumber: number;
    pageSize: number;
    sort: string;
    itemExports: Array<MenuItem>;
    itemPushGroups: Array<MenuItem>;
    formSearchSim: any;
    formCreateGroupSim: any;
    dataCreateGroupSim: {
        name: string|null,
        groupKey: string|null,
        description: string|null
    };
    isExistsGroupKey: boolean|false;
    statuSims: Array<any>;
    listRatingPlan: Array<any>;
    listGroupSim: Array<any>;
    listCustomer: Array<any>;
    listGroupSimToSelect: Array<any>;
    maxDateFrom: Date|number|string|null = new Date();
    minDateTo: Date|number|string|null = null;
    maxDateTo: Date|number|string|null = new Date();
    isShowDialogCreateGroup:boolean = false;
    isShowDialogPushGroup:boolean = false;
    groupSimSelected: number|null;
    userType: number;
    provinceCode: any;
    provinceName: any;
    customerCode: any;
    customerName: any;
    groupScope: number;
    groupScopeObjects: any = CONSTANTS.GROUP_SCOPE;
    allPermissions = CONSTANTS.PERMISSIONS;
    paramSearchGroupSim: any = {};
    listProvince = [];
    visible = false;
    msisdn: string;
    isShowModalDeleteSim : boolean = false;
    searchList: Array<SeperateSearchInfo>;
    filterList: Array<SeperateFilterInfo>;
    subscriptionOption: Array<{name: string, value: string}>;
    phone:string;
    name:string;
    email:string;
    originSearch:any;
    formUpdatePerson = new FormGroup({
        fullName: new FormControl(),
        phone: new FormControl({value:"", disabled:true}),
        email: new FormControl()
    });
    isPrivilage:boolean = true
    // tranService: TranslateService;
    constructor(@Inject(SimService) private simService: SimService,
                private formBuilder: FormBuilder,
                @Inject(TrafficWalletService) private trafficWalletService: TrafficWalletService,
                @Inject(ShareManagementService) private shareService : ShareManagementService,
                private injector: Injector,
                private primengConfig: PrimeNGConfig) {
        super(injector);
    }
    ngOnInit(){

        let me = this;
        this.userType = this.sessionService.userInfo.type;
        this.selectItems = [];
        this.items = [{ label: this.tranService.translate(`global.menu.trafficManagement`) }, { label: this.tranService.translate("global.menu.shareList"), routerLink:["/data-pool/shareMgmt/listShare"] },{ label: this.tranService.translate('datapool.label.detailSharing') }];
        this.home = { icon: 'pi pi-home', routerLink: '/' };

        this.msisdn = this.route.snapshot.paramMap.get("id");

        this.searchInfo = {
            searchWalletCode: 0,
            searchPayCode: 0,
            value: " ",
            lstPackageCode: null,
            sharingDay: null,
            trafficType: " ",
            phoneReceipt: this.msisdn
        }

        this.searchList = [{
            name: "Mã ví",
            key: "searchWalletCode"
        },{
            name: "Mã thanh toán",
            key: "searchPayCode"
        }];
        this.subscriptionOption=[]

        this.messageCommonService.onload()
        this.trafficWalletService.getPackageCbbForUser(this.msisdn, (response)=>{
            response.map((data)=>{
                this.subscriptionOption.push({name: data.name,value: data.value})
            })
            this.subscriptionOption = this.subscriptionOption.filter((item, index, self) =>
                index === self.findIndex((t) => (
                    t.name === item.name && t.value === item.value
                ))
            );
            this.filterList = [{
                name: "Gói cước",
                key: "lstPackageCode",
                type: FilterInputType.multiselect,
                items:this.subscriptionOption,
                itemFilter: true
            },{
                name:"Loại lưu lượng",
                key: "trafficType",
                type: FilterInputType.dropdown,
                items:[{
                    name: "Gói Data",
                    value:"Gói Data"
                },{
                    name:"Gói SMS",
                    value:"Gói SMS"
                }],
                itemFilter: true
            },{
                name:"Ngày chia sẻ",
                key: "sharingDay",
                type: FilterInputType.calendar,
                unixTimeString: true
            }]
        },null, () => {
            this.messageCommonService.offload()
        })

        this.columns = [{
            name: this.tranService.translate("datapool.label.walletCode"),
            key: "subCode",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: true
        },{
            name: this.tranService.translate("datapool.label.packageName"),
            key: "packageName",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: true
        },{
            name: this.tranService.translate("datapool.label.transactionCode"),
            key: "transactionCode",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: true
        },{
            name: this.tranService.translate("datapool.label.trafficType"),
            key: "trafficType",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: true
        },{
            name: this.tranService.translate("datapool.label.sharingDataNotType"),
            key: "trafficShare",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: true
        },{
            name: this.tranService.translate("datapool.label.shareDate"),
            key: "timeShare",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: true
        },{
            name: this.tranService.translate("datapool.label.usedDate"),
            key: "timeExpired",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: true
        }];

        this.optionTable = {
            hasClearSelected:false,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
        }
        this.pageNumber = 0;
        this.pageSize= 10;
        this.sort = "created_date,desc"
        this.dataSet ={
            content: [],
            total: 0
        }

        this.messageCommonService.onload()
        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
        this.shareService.getByMsisdn(this.msisdn, (response) => {
            this.phone = response.phoneReceipt;
            this.name = response.name;
            this.email = response.email;
        },(error)=>{
            console.log(error.error.error.errorCode);
            if(error.error.error.errorCode="error.error.error.errorCode"){
                this.isPrivilage = false
                this.messageCommonService.error("Bạn không có quyền truy cập thông tin này")
                this.router.navigate(["/data-pool/shareMgmt/listShare"])
            }
        }, () => { this.messageCommonService.offload() })

        this.primengConfig.setTranslation({
            emptyFilterMessage:"Không tìm thấy kết quả",
            emptyMessage:"Không tìm thấy kết quả"
        });

        this.originSearch = this.searchInfo;

    }


    resetField(){
        this.formSearchSim.reset();
    }

    catchSearchDetail(event){
        this.searchInfo = this.originSearch
        this.searchInfo = {...this.searchInfo, ...event}
        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
    }

    onSubmitUpdatePerson(){

    }

    search(page, limit, sort, params){
        let me = this;
        this.pageNumber = page;
        this.pageSize = limit;
        this.sort = sort;
        let dataParams = {
            page,
            size: limit,
            sort
        }
        Object.keys(this.searchInfo).forEach(key => {
            dataParams[key] = this.searchInfo[key];
        })
        this.messageCommonService.onload()
        this.trafficWalletService.searchDetailShare(dataParams, (response) => {
            me.dataSet = {
                content: response.content,
                total: response.totalElements
            }
        }, null, () => {
            me.messageCommonService.offload();
        })
    }
}
