import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { AppGuideComponent } from './app.guide.component';
import {AppGuideIntegrationComponent} from "./integration/app.guide.integration.component";

@NgModule({
    imports: [RouterModule.forChild([
        { path: 'integration', component: AppGuideIntegrationComponent, children: [{ path: '**', component: AppGuideIntegrationComponent }] },
        { path: '**', component: AppGuideComponent },
    ])],
    exports: [RouterModule]
})
export class GuideRoutingModule { }
