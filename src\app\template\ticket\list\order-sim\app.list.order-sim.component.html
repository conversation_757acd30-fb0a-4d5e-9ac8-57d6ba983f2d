<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{ tranService.translate("ticket.menu.orderSim") }}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
    <div class="col-5 flex flex-row justify-content-end align-items-center">
        <p-button styleClass="p-button-info"
                  *ngIf="userInfo.type == userType.CUSTOMER && checkAuthen([CONSTANTS.PERMISSIONS.TICKET.CREATE])"
                  [label]="tranService.translate('global.button.create')"
                  (click)="showModalCreate()" icon="">
        </p-button>
    </div>
</div>

<form [formGroup]="formSearchTicket" (ngSubmit)="onSubmitSearch()" class="pt-3 pb-2 vnpt-field-set">
    <p-panel [toggleable]="true" [header]="tranService.translate('global.text.filter')">
        <div class="grid search-grid-3">
            <!-- ma tinh -->
            <div *ngIf="this.userInfo.type == this.userType.ADMIN" class="col-2">
                <span class="p-float-label">
                    <p-dropdown styleClass="w-full"
                                [showClear]="true" [filter]="true" filterBy="display"
                                id="provinceCode" [autoDisplayFirst]="false"
                                [(ngModel)]="searchInfo.provinceCode"
                                [required]="false"
                                formControlName="provinceCode"
                                [options]="listProvince"
                                optionLabel="display"
                                optionValue="code"
                    ></p-dropdown>
                    <label class="label-dropdown" htmlFor="provinceCode">{{ tranService.translate("account.label.province") }}</label>
                </span>
            </div>
            <div class="col-2">
                <span class="p-float-label">
                    <p-dropdown styleClass="w-full"
                                [showClear]="true" [filter]="true" filterBy="display"
                                id="status" [autoDisplayFirst]="false"
                                [(ngModel)]="searchInfo.status"
                                [required]="false"
                                formControlName="status"
                                [options]="listTicketStatus"
                                optionLabel="label"
                                optionValue="value"
                                [filter] = true
                                filterBy = "label"
                    ></p-dropdown>
                    <label class="label-dropdown" htmlFor="status">{{ tranService.translate("ticket.label.status") }}</label>
                </span>
            </div>
            <!-- email -->
            <div class="col-2">
                <span class="p-float-label">
                    <input class="w-full"
                           pInputText id="email"
                           [(ngModel)]="searchInfo.contactName"
                           formControlName="contactName"
                    />
                    <label htmlFor="email">{{ tranService.translate("ticket.label.fullName") }}</label>
                </span>
            </div>
            <!-- phone -->
            <div class="col-2">
                <span class="p-float-label">
                    <input class="w-full"
                           pInputText id="phone"
                           [(ngModel)]="searchInfo.contactPhone"
                           formControlName="contactPhone"
                           type="number"
                           (keydown)="preventCharacter($event)"
                           min = 0
                    />
                    <label htmlFor="phone">{{ tranService.translate("ticket.label.phone") }}</label>
                </span>
            </div>
            <div class="col-2">
                <span class="p-float-label">
                    <p-calendar styleClass="w-full"
                                id="dateFrom"
                                [(ngModel)]="searchInfo.dateFrom"
                                formControlName="dateFrom"
                                [showIcon]="true"
                                [showClear]="true"
                                dateFormat="dd/mm/yy"
                                [maxDate]="maxDateFrom"
                                (onSelect)="onChangeDateFrom(searchInfo.dateFrom)"
                                (onInput)="onChangeDateFrom(searchInfo.dateFrom)"
                    ></p-calendar>
                    <label class="label-calendar" htmlFor="dateFrom">{{ tranService.translate("ticket.label.dateFrom") }}</label>
                </span>
            </div>
            <div class="col-2">
                <span class="p-float-label">
                    <p-calendar styleClass="w-full"
                                id="dateTo"
                                [(ngModel)]="searchInfo.dateTo"
                                formControlName="dateTo"
                                [showIcon]="true"
                                [showClear]="true"
                                dateFormat="dd/mm/yy"
                                [minDate]="minDateTo"
                                [maxDate]="maxDateTo"
                                (onSelect)="onChangeDateTo(searchInfo.dateTo)"
                                (onInput)="onChangeDateTo(searchInfo.dateTo)"
                    />
                    <label class="label-calendar" htmlFor="dateTo">{{ tranService.translate("ticket.label.dateTo") }}</label>
                </span>
            </div>
            <div class="col-2 pb-0">
                <p-button icon="pi pi-search"
                          styleClass="p-button-rounded p-button-secondary p-button-text button-search"
                          type="submit"
                ></p-button>
            </div>
        </div>
    </p-panel>
</form>

<table-vnpt *ngIf="!changeTable"
    [tableId]="'tableTicketConfigList'"
    [fieldId]="'provinceCode'"
    [columns]="columns"
    [dataSet]="dataSet"
    [options]="optionTable"
    [pageNumber]="pageNumber"
    [loadData]="search.bind(this)"
    [pageSize]="pageSize"
    [sort]="sort"
    [params]="searchInfo"
    [labelTable]="tranService.translate('ticket.menu.requestList')"
></table-vnpt>
<table-vnpt *ngIf="changeTable"
    [tableId]="'tableTicketConfigList'"
    [fieldId]="'provinceCode'"
    [columns]="columns"
    [dataSet]="dataSet"
    [options]="optionTable"
    [pageNumber]="pageNumber"
    [loadData]="search.bind(this)"
    [pageSize]="pageSize"
    [sort]="sort"
    [params]="searchInfo"
    [labelTable]="tranService.translate('ticket.menu.requestList')"
></table-vnpt>
<!--    dialog tạo yêu cầu đặt sim-->
<div class="flex justify-content-center">
    <p-dialog [header]="tranService.translate('ticket.label.createRequest')" [(visible)]="isShowCreateRequest"
              [modal]="true" [style]="{ width: '700px' }" [draggable]="false" [resizable]="false">
        <form class="mt-3" [formGroup]="formTicketSim" (ngSubmit)="createOrderSim()">
            <div class="flex flex-row flex-wrap justify-content-between w-full">
                <!-- contactName -->
                <div class="w-full field grid">
                    <label htmlFor="contactName" class="col-fixed"
                           style="width:180px">{{ tranService.translate("ticket.label.customerName") }}<span
                        class="text-red-500">*</span></label>
                    <div class="col">
                        <input class="w-full"
                               pInputText id="contactName"
                               [(ngModel)]="ticket.contactName"
                               formControlName="contactName"
                               [required]="true"
                               [maxLength]="50"
                               pattern="^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]*$"
                               [placeholder]="tranService.translate('account.text.inputFullname')"
                               [readonly]="true"
                        />
                    </div>
                </div>
                <!-- error fullname -->
                <div class="w-full field grid text-error-field">
                    <label htmlFor="fullName" class="col-fixed" style="width:180px"></label>
                    <div class="col">
                        <small class="text-red-500"
                               *ngIf="formTicketSim.controls.contactName.dirty && formTicketSim.controls.contactName.errors?.required">{{ tranService.translate("global.message.required") }}</small>
                        <small class="text-red-500"
                               *ngIf="formTicketSim.controls.contactName.errors?.maxLength">{{ tranService.translate("global.message.maxLength", {len: 255}) }}</small>
                        <small class="text-red-500"
                               *ngIf="formTicketSim.controls.contactName.errors?.pattern">{{ tranService.translate("global.message.formatContainVN") }}</small>
                    </div>
                </div>

                <!-- email -->
                <div class="w-full field grid">
                    <label htmlFor="email" class="col-fixed"
                           style="width:180px">{{ tranService.translate("ticket.label.email") }}<span
                        class="text-red-500">*</span></label>
                    <div class="col">
                        <input class="w-full"
                               pInputText id="contactEmail"
                               [(ngModel)]="ticket.contactEmail"
                               formControlName="contactEmail"
                               [required]="true"
                               [maxLength]="50"
                               pattern="^[a-z0-9]+[a-z0-9\-\._]*[a-z0-9]+@([a-z0-9]+[a-z0-9\-\._]*[a-z0-9]+)+(\.[a-z]{2,})$"
                               [placeholder]="tranService.translate('account.text.inputEmail')"
                               [readonly]="true"
                        />
                    </div>
                </div>
                <!-- error contactEmail -->
                <div class="w-full field grid text-error-field">
                    <label htmlFor="contactEmail" class="col-fixed" style="width:180px"></label>
                    <div class="col">
                        <small class="text-red-500"
                               *ngIf="formTicketSim.controls.contactEmail.dirty && formTicketSim.controls.contactEmail.errors?.required">{{ tranService.translate("global.message.required") }}</small>
                        <small class="text-red-500"
                               *ngIf="formTicketSim.controls.contactEmail.errors?.maxLength">{{ tranService.translate("global.message.maxLength", {len: 255}) }}</small>
                        <small class="text-red-500"
                               *ngIf="formTicketSim.controls.contactEmail.errors?.pattern">{{ tranService.translate("global.message.invalidEmail") }}</small>
                        <!--                            <small class="text-red-500" *ngIf="isEmailExisted">{{tranService.translate("global.message.exists",{type: tranService.translate("account.label.email").toLowerCase()})}}</small> -->
                    </div>
                </div>
                <!-- phone -->
                <div class="w-full field grid">
                    <label htmlFor="phone" class="col-fixed"
                           style="width:180px">{{ tranService.translate("ticket.label.phone") }}<span
                        class="text-red-500">*</span></label>
                    <div class="col">
                        <input class="w-full"
                               pInputText id="contactPhone"
                               [(ngModel)]="ticket.contactPhone"
                               formControlName="contactPhone"
                               [required]="true"
                               (keydown)="preventCharacter($event)"
                               [maxLength]="12"
                               [placeholder]="tranService.translate('account.text.inputPhone')"
                               [readonly]="true"
                        />
                    </div>
                </div>
                <!-- error phone -->
                <div class="w-full field grid text-error-field">
                    <label htmlFor="phone" class="col-fixed" style="width:180px"></label>
                    <div class="col">
                        <small class="text-red-500"
                               *ngIf="formTicketSim.controls.contactPhone.dirty && formTicketSim.controls.contactPhone.errors?.required">{{ tranService.translate("global.message.required") }}</small>
                        <small class="text-red-500"
                               *ngIf="formTicketSim.controls.contactPhone.errors?.pattern">{{ tranService.translate("ticket.message.invalidPhone") }}</small>
                    </div>
                </div>
                <!-- quantity-->
                <div class="w-full field grid">
                    <label htmlFor="quantity" class="col-fixed"
                           style="width:180px;height: fit-content;">{{ tranService.translate("ticket.label.quantity") }}
                        <span class="text-red-500">*</span></label>
                    <div class="col">
                        <p-inputNumber class="w-full" style="resize: none; padding: 0;"
                                       [autoResize]="false"
                                       pInputTextarea id="quantity"
                                       [(ngModel)]="ticket.quantity"
                                       formControlName="quantity"
                                       [required]=true
                                       [placeholder]="tranService.translate('ticket.label.quantity')"
                                       (onInput)="checkQuantity($event)"
                        ></p-inputNumber>
                    </div>
                </div>
                <!-- error quantity -->
                <div class="w-full field grid text-error-field">
                    <label htmlFor="quantity" class="col-fixed" style="width:180px"></label>
                    <div class="col">
                        <small class="text-red-500"
                               *ngIf="formTicketSim.controls.quantity.dirty && formTicketSim.controls.quantity.errors?.required">{{ tranService.translate("global.message.required") }}</small>
                        <small class="text-red-500"
                               *ngIf="errorMaxQuantity">{{ tranService.translate("ticket.message.maxQuantity") }}</small>
                        <small class="text-red-500"
                               *ngIf="errorMinQuantity">{{ tranService.translate("ticket.message.minQuantity") }}</small>
                    </div>
                </div>
                <!--                 address-->
                <div class="w-full field grid">
                    <label class="col-fixed"
                           style="width:180px;height: fit-content;">{{ tranService.translate("ticket.label.deliveryAddress") }}
                        <span class="text-red-500">*</span></label>
                </div>
                <p-card class="w-full mb-3">
                    <div class="flex">

                        <vnpt-select
                            [(value)]="ticket.province"
                            [placeholder]="tranService.translate('ticket.label.province')"
                            objectKey="provinceAddress"
                            paramKey="name"
                            keyReturn="code"
                            displayPattern="${name}"
                            typeValue="object"
                            [isMultiChoice]="false"
                            [required]="true"
                            [disabled]="false"
                            (onchange)="onSelectProvince($event)"
                            class="flex-1 col-3">
                        </vnpt-select>
                        <vnpt-select class="flex-1 col-3"
                                     [(value)]="ticket.district"
                                     [placeholder]="tranService.translate('ticket.label.district')"
                                     objectKey="districtAddress"
                                     paramKey="name"
                                     keyReturn="code"
                                     displayPattern="${name}"
                                     typeValue="object"
                                     [isMultiChoice]="false"
                                     [required]="true"
                                     [disabled]="disableSelectDistrict"
                                     [paramDefault]="optionAddress"
                                     (onchange)="onSelectDistrict($event)"
                        >
                        </vnpt-select>

                        <vnpt-select class="flex-1 col-3"
                                     [(value)]="ticket.commune"
                                     [placeholder]="tranService.translate('ticket.label.commune')"
                                     objectKey="communeAddress"
                                     paramKey="name"
                                     keyReturn="code"
                                     displayPattern="${name}"
                                     typeValue="object"
                                     [isMultiChoice]="false"
                                     [required]="true"
                                     [disabled]="disableSelectCommune"
                                     [paramDefault]="optionAddress"
                                     (onchange)="onSelectCommune($event)"
                        >
                        </vnpt-select>

                    </div>
                    <div class="flex">
                        <div class="col">
                            <input class="w-full" style="resize: none;"
                                   rows="5"
                                   [autoResize]="false"
                                   pInputTextarea id="detailAddress"
                                   [(ngModel)]="ticket.detailAddress"
                                   formControlName="detailAddress"
                                   pattern="^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]*$"
                                   [required]="true"
                                   [maxlength]="255"
                                   [placeholder]="tranService.translate('ticket.label.detailAddress')"
                            />

                        </div>
                    </div>
                    <!-- error detailAddress -->
                    <div class="w-full field grid text-error-field">
                        <label htmlFor="detailAddress" class="col-fixed" style="width:180px"></label>
                        <div class="col">
                            <small class="text-red-500"
                                   *ngIf="formTicketSim.controls.detailAddress.errors?.maxLength">{{ tranService.translate("global.message.maxLength", {len: 255}) }}</small>
                            <small class="text-red-500"
                                   *ngIf="formTicketSim.controls.detailAddress.dirty && formTicketSim.controls.detailAddress.errors?.required">{{ tranService.translate("global.message.required") }}</small>
                            <small class="text-red-500"
                                   *ngIf="formTicketSim.controls.detailAddress.errors?.pattern">{{ tranService.translate("global.message.formatCode") }}</small>
                        </div>
                    </div>
                </p-card>
                <!-- content-->
                <div class="w-full field grid">
                    <label htmlFor="content" class="col-fixed"
                           style="width:180px;height: fit-content;">{{ tranService.translate("ticket.label.content") }}</label>
                    <div class="col">
                            <textarea class="w-full" style="resize: none;"
                                      rows="5"
                                      [autoResize]="false"
                                      pInputTextarea id="content"
                                      [(ngModel)]="ticket.content"
                                      formControlName="content"
                                      pattern="^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]*$"
                                      [maxlength]="255"
                                      (keydown)="onKeyDownContent($event)"
                                      [placeholder]="tranService.translate('ticket.label.content')"
                            ></textarea>
                    </div>
                </div>
                <!-- error content -->
                <div class="w-full field grid text-error-field">
                    <label htmlFor="content" class="col-fixed" style="width:180px"></label>
                    <div class="col">
                        <small class="text-red-500"
                               *ngIf="formTicketSim.controls.content.errors?.maxLength">{{ tranService.translate("global.message.maxLength", {len: 255}) }}</small>
                        <small class="text-red-500"
                               *ngIf="formTicketSim.controls.content.errors?.pattern">{{ tranService.translate("global.message.formatCode") }}</small>
                    </div>

                </div>

                <!-- note-->
                <div class="w-full field grid">
                    <label htmlFor="content" class="col-fixed"
                           style="width:180px;height: fit-content;">{{ tranService.translate("ticket.label.note") }}</label>
                    <div class="col">
                            <textarea class="w-full" style="resize: none;"
                                      rows="5"
                                      [autoResize]="false"
                                      pInputTextarea id="note"
                                      [(ngModel)]="ticket.note"
                                      formControlName="note"
                                      pattern="^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]*$"
                                      [maxlength]="255"
                                      (keydown)="onKeyDownNote($event)"
                                      [placeholder]="tranService.translate('ticket.label.note')"
                            ></textarea>
                    </div>
                </div>
                <!-- error note -->
                <div class="w-full field grid text-error-field">
                    <label htmlFor="note" class="col-fixed" style="width:180px"></label>
                    <div class="col">
                        <small class="text-red-500"
                               *ngIf="formTicketSim.controls.note.errors?.maxLength">{{ tranService.translate("global.message.maxLength", {len: 255}) }}</small>
                        <small class="text-red-500"
                               *ngIf="formTicketSim.controls.note.errors?.pattern">{{ tranService.translate("global.message.formatCode") }}</small>
                    </div>
                </div>
            </div>
            <div class="flex flex-row justify-content-center align-items-center mt-3">
                <p-button styleClass="mr-2 p-button-secondary" [label]="tranService.translate('global.button.cancel')"
                          (click)="isShowCreateRequest = false"></p-button>
                <p-button type="submit" styleClass="p-button-info" *ngIf="checkAuthen([CONSTANTS.PERMISSIONS.TICKET.CREATE])"
                          [disabled]="formTicketSim.invalid || typeRequest == 'create' && (errorMinQuantity || !selectedProvince || !selectedDistrict || !selectedCommune || errorMaxQuantity)"
                          [label]="tranService.translate('global.button.save')"></p-button>
            </div>
        </form>
    </p-dialog>
    <!--    dialog xử lý yêu cầu-->
    <p-dialog
        [header]="typeRequest == 'view' ? tranService.translate('ticket.label.viewOrderSim') :tranService.translate('ticket.label.updateOrderSim')"
        [(visible)]="isShowUpdateRequest"
        [modal]="true" [style]="{ width: '1000px' }" [draggable]="false" [resizable]="false">
        <form class="mt-3" [formGroup]="formUpdateOrderSim" (ngSubmit)="updateOrderSim()">
            <div class="flex dialog-ticket-sim-1">
                <div class="flex-1 col-11">
                    <div class="flex flex-row flex-wrap w-full">
                        <div class="w-full field grid dialog-grid-customer-2" *ngIf="this.userInfo.type == this.userType.ADMIN && typeRequest == 'view'">
                            <label htmlFor="contactName" class="col-fixed"
                                   style="width:180px">{{ tranService.translate("account.label.province") }}<span
                                class="text-red-500">*</span></label>
                            <div class="col">
                                <p-dropdown styleClass="w-full"
                                            [showClear]="false" [filter]="true" filterBy="display"
                                            id="provinceCode" [autoDisplayFirst]="false"
                                            [(ngModel)]="ticket.provinceCode"
                                            formControlName="provinceCode"
                                            [options]="listProvince"
                                            optionLabel="display"
                                            optionValue="code"
                                            [disabled]="true"
                                            [readonly]="true"
                                ></p-dropdown>
                            </div>
                        </div>
                        <!-- contactName -->
                        <div class="w-full field grid">
                            <label htmlFor="contactName" class="col-fixed"
                                   style="width:180px">{{ tranService.translate("ticket.label.customerName") }}<span
                                class="text-red-500">*</span></label>
                            <div class="col">
                                <input class="w-full"
                                       pInputText id="contactName"
                                       [(ngModel)]="ticket.contactName"
                                       formControlName="contactName"
                                       [required]="true"
                                       [maxLength]="50"
                                       pattern="^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]*$"
                                       [placeholder]="tranService.translate('account.text.inputFullname')"
                                       [readonly]="true"
                                />
                            </div>
                        </div>

                        <!-- phone-->
                        <div class="w-full field grid">
                            <label htmlFor="phone" class="col-fixed"
                                   style="width:180px">{{ tranService.translate("ticket.label.phone") }}<span
                                class="text-red-500">*</span></label>
                            <div class="col">
                                <input class="w-full"
                                       pInputText id="contactPhone"
                                       [(ngModel)]="ticket.contactPhone"
                                       formControlName="contactPhone"
                                       [required]="true"
                                       pattern="^((\+?[1-9][0-9])|0?)[1-9][0-9]{8,9}$"
                                       (keydown)="preventCharacter($event)"
                                       [placeholder]="tranService.translate('account.text.inputPhone')"
                                       [readonly]="true"
                                />
                            </div>
                        </div>

                        <!-- address-->
                        <div class="w-full field grid">
                            <label htmlFor="address" class="col-fixed"
                                   style="width:180px">{{ tranService.translate("ticket.label.deliveryAddress") }}<span
                                class="text-red-500">*</span></label>
                            <div class="col">
                                <textarea class="w-full"
                                          rows="2"
                                          pInputText id="address"
                                          [(ngModel)]="ticket.address"
                                          formControlName="address"
                                          [required]="true"
                                          [placeholder]="tranService.translate('ticket.label.address')"
                                          [readonly]="true"
                                ></textarea>
                            </div>
                        </div>

                        <div class="w-full field grid" [class]="isShowStatus ? '' : 'hidden'">
                            <label for="status" class="col-fixed"
                                   style="width:180px">{{ tranService.translate("ticket.label.status") }}</label>
                            <div class="col">
                                <p-dropdown styleClass="w-full"
                                            [showClear]="true"
                                            id="type" [autoDisplayFirst]="true"
                                            [(ngModel)]="ticket.status"
                                            [required]="isRequiredStatus"
                                            formControlName="status"
                                            [options]="ticket.statusOld !== null ? mapTicketStatus[ticket.statusOld] : listTicketStatus"
                                            optionLabel="label"
                                            optionValue="value"
                                            [placeholder]="tranService.translate('ticket.label.status')"
                                            [emptyMessage]="tranService.translate('global.text.nodata')"
                                            (onChange)="checkVisibleAndRequired()"
                                ></p-dropdown>
                            </div>
                        </div>

                        <div class="w-full field grid"
                             [class]="this.typeRequest == 'view' ? '' : 'hidden'">
                            <label for="statusOld" class="col-fixed"
                                   style="width:180px">{{ tranService.translate("ticket.label.status") }}<span
                                class="text-red-500">*</span></label>
                            <div class="col">
<!--                                <p-dropdown styleClass="w-full"-->
<!--                                            id="statusOld" [autoDisplayFirst]="true"-->
<!--                                            [(ngModel)]="ticket.statusOld"-->
<!--                                            [required]="true"-->
<!--                                            formControlName="statusOld"-->
<!--                                            [options]="listTicketStatus"-->
<!--                                            optionLabel="label"-->
<!--                                            optionValue="value"-->
<!--                                            [placeholder]="tranService.translate('ticket.label.status')"-->
<!--                                            [readonly]=true-->
<!--                                ></p-dropdown>-->
                                <span *ngIf="ticket.statusOld == CONSTANTS.REQUEST_STATUS.NEW" [class]="['p-2', 'text-white', 'bg-cyan-300', 'border-round', 'inline-block']">{{getValueStatus(ticket.statusOld)}}</span>
                                <span *ngIf="ticket.statusOld == CONSTANTS.REQUEST_STATUS.RECEIVED"  [class]="['p-2', 'text-white', 'bg-bluegray-500', 'border-round', 'inline-block']">{{getValueStatus(ticket.statusOld)}}</span>
                                <span *ngIf="ticket.statusOld == CONSTANTS.REQUEST_STATUS.IN_PROGRESS"  [class]="['p-2', 'text-white', 'bg-orange-400', 'border-round', 'inline-block']">{{getValueStatus(ticket.statusOld)}}</span>
                                <span *ngIf="ticket.statusOld == CONSTANTS.REQUEST_STATUS.REJECT"  [class]="['p-2', 'text-white', 'bg-red-500', 'border-round', 'inline-block']">{{getValueStatus(ticket.statusOld)}}</span>
                                <span *ngIf="ticket.statusOld == CONSTANTS.REQUEST_STATUS.DONE"  [class]="['p-2', 'text-white', 'bg-green-500', 'border-round', 'inline-block']">{{getValueStatus(ticket.statusOld)}}</span>
                            </div>
                        </div>
                        <!-- error trang thai yeu cau -->
                        <div class="w-full field grid text-error-field">
                            <label htmlFor="userType" class="col-fixed" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500"
                                       *ngIf="formTicketSim.controls.status.dirty && formTicketSim.controls.status.errors?.required">{{ tranService.translate("global.message.required") }}</small>
                            </div>
                        </div>
                        <!-- note admin-->
                        <div *ngIf="this.isShowNote"
                             class="w-full field grid">
                            <label htmlFor="cause" class="col-fixed"
                                   style="width:180px;height: fit-content;">{{ tranService.translate("ticket.label.processingNotes") }}
                                <small class="text-red-500" *ngIf="this.ticket.status != null">*</small></label>
                            <div class="col">
                            <textarea class="w-full" style="resize: none;"
                                      rows="2"
                                      [autoResize]="false"
                                      pInputTextarea id="cause"
                                      [(ngModel)]="ticket.cause"
                                      formControlName="cause"
                                      [maxlength]="255"
                                      [required]="this.isRequiredNote"
                                      [readonly]="this.typeRequest == 'view'"
                                      (input)="checkVisibleAndRequired()"
                                      (keydown)="onKeyDownCause($event)"
                                      [placeholder]="tranService.translate('ticket.label.processingNotes')"
                            ></textarea>
                            </div>
                        </div>
                        <!-- error note admin -->
                        <div class="w-full field grid text-error-field">
                            <label htmlFor="cause" class="col-fixed" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500"
                                       *ngIf="formTicketSim.controls.cause.errors?.maxLength">{{ tranService.translate("global.message.maxLength", {len: 255}) }}</small>
                                <small class="text-red-500"
                                       *ngIf="formTicketSim.controls.cause.dirty && formTicketSim.controls.cause.errors?.required">{{ tranService.translate("global.message.required") }}</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex-1 col-11">
                    <div class="flex flex-row flex-wrap w-full">
                        <div class="w-full field grid">
                            <label htmlFor="email" class="col-fixed"
                                   style="width:180px">{{ tranService.translate("ticket.label.email") }}<span
                                class="text-red-500">*</span></label>
                            <div class="col">
                                <input class="w-full"
                                       pInputText id="contactEmail"
                                       [(ngModel)]="ticket.contactEmail"
                                       formControlName="contactEmail"
                                       [required]="true"
                                       [maxLength]="50"
                                       pattern="^[a-z0-9]+[a-z0-9\-\._]*[a-z0-9]+@([a-z0-9]+[a-z0-9\-\._]*[a-z0-9]+)+(\.[a-z]{2,})$"
                                       [placeholder]="tranService.translate('account.text.inputEmail')"
                                       [readonly]="true"
                                />
                            </div>
                        </div>

                        <!-- quantity-->
                        <div class="w-full field grid">
                            <label htmlFor="content" class="col-fixed"
                                   style="width:180px;height: fit-content;">{{ tranService.translate("ticket.label.quantity") }}
                                <span class="text-red-500">*</span></label>
                            <div class="col">
                                <input class="w-full"
                                       pInputText id="quantity"
                                       [(ngModel)]="ticket.quantity"
                                       formControlName="quantity"
                                       (keydown)="preventCharacter($event)"
                                       [placeholder]="tranService.translate('ticket.label.quantity')"
                                       [readonly]="true"
                                />
                            </div>
                        </div>


                        <!-- content-->
                        <div class="w-full field grid">
                            <label htmlFor="content" class="col-fixed"
                                   style="width:180px;height: fit-content;">{{ tranService.translate("ticket.label.content") }}</label>
                            <div class="col">
                                <input class="w-full"
                                       pInputText id="content"
                                       [(ngModel)]="ticket.content"
                                       formControlName="content"
                                       (keydown)="preventCharacter($event)"
                                       [placeholder]="tranService.translate('ticket.label.content')"
                                       [readonly]="true"
                                />
                            </div>
                        </div>
                        <!-- note-->
                        <div class="w-full field grid">
                            <label htmlFor="content" class="col-fixed"
                                   style="width:180px;height: fit-content;">{{ tranService.translate("ticket.label.note") }}</label>
                            <div class="col">
                                <input class="w-full"
                                       pInputText id="note"
                                       [(ngModel)]="ticket.note"
                                       formControlName="note"
                                       (keydown)="preventCharacter($event)"
                                       [placeholder]="tranService.translate('ticket.label.note')"
                                       [readonly]="true"
                                />
                            </div>
                        </div>
                        <!-- chuyen xu ly-->
                        <div class="w-full field grid" [class]="isShowAssignee ? '' : 'hidden'">
                            <label for="assigneeId" class="col-fixed"
                                   style="width:180px">{{ tranService.translate("ticket.label.transferProcessing") }}</label>
                            <div class="col" style="max-width: calc(100% - 180px)">
                                <vnpt-select
                                    id="assigneeId"
                                    [(value)]="ticket.assigneeId"
                                    [placeholder]="tranService.translate('ticket.label.transferProcessing')"
                                    objectKey="account"
                                    paramKey="email"
                                    formControlName="assigneeId"
                                    [required]="isRequiredAssignee"
                                    keyReturn="id"
                                    displayPattern="${email}"
                                    (onchange)="checkVisibleAndRequired()"
                                    [isMultiChoice]="false"
                                    [disabled]="typeRequest == 'view'"
                                    [paramDefault]="{type : 3}"
                                ></vnpt-select>
                            </div>
                        </div>
                        <!-- error chuyen xu ly-->
                        <div class="w-full field grid text-error-field">
                            <label htmlFor="assigneeId" class="col-fixed" style="width:180px"></label>
                            <div class="col">
                                <small class="text-red-500"
                                       *ngIf="formTicketSim.controls.assigneeId.dirty && formTicketSim.controls.assigneeId.errors?.required">{{ tranService.translate("global.message.required") }}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex">
                <div class="flex-grow-1">
                    <div class=" col-fixed w-full pt-0">
                        <div *ngIf="isShowListNote">
                            <div class="w-full field grid">
                                <label class="col-fixed"
                                       style="height: fit-content;"><b>{{ tranService.translate("ticket.label.listNotes") }}</b>
                                </label>
                            </div>
                            <div class="p-grid p-justify-center">
                                <div>
                                    <p-table [value]="this.listNotes">
                                        <ng-template pTemplate="header">
                                            <tr>
                                                <th class="col-1">{{ tranService.translate('global.text.stt') }}</th>
                                                <th class="col-2">{{ tranService.translate('account.text.account') }}</th>
                                                <th class="col-2">{{ tranService.translate('global.button.changeStatus') }}</th>
                                                <th class="col-2">{{ tranService.translate('account.label.time') }}</th>
                                                <th>{{ tranService.translate('ticket.label.content') }}</th>
                                            </tr>
                                        </ng-template>
                                        <ng-template pTemplate="body" let-note let-i="rowIndex">
                                            <tr [formGroup]="mapForm[note.id]">
                                                <td class="col-1">{{ i + 1 }}</td>
                                                <td class="col-2">{{ note.userName }}</td>
                                                <td class="col-2">{{ getValueStatus(note.status) }}</td>
                                                <td class="col-2">{{ note.createdDate | date:"HH:mm:ss dd/MM/yyyy" }}</td>
                                                <td>
                                                    <input *ngIf="typeRequest == 'update'" class="w-full"
                                                           pInputText id="content"
                                                           [(ngModel)]="note.content"
                                                           formControlName="content"
                                                           [required]="true"
                                                           [maxLength]="255"
                                                           (keydown)="onKeyDownNoteContent($event, note)"
                                                    />
                                                    <span *ngIf="typeRequest == 'view'"
                                                          [pTooltip]="note.content">{{ getValueNote(note.content) }}</span>
                                                    <small class="text-red-500"
                                                           *ngIf="mapForm[note.id].controls.content.dirty && mapForm[note.id].controls.content.errors?.required">{{ tranService.translate("global.message.required") }}</small>
                                                    <small class="text-red-500"
                                                           *ngIf="mapForm[note.id].controls.content.errors?.maxLength">{{ tranService.translate("global.message.maxLength", {len: 255}) }}</small>
                                                </td>
                                            </tr>
                                        </ng-template>
                                    </p-table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-4 pt-0">
                    <div class="block w-full">
                        <div class="block ">
                            <div class="w-full field grid" [class]="isShowListImsi ? '' : 'hidden'">
                                <label class="col-fixed"
                                       style="width:180px;height: fit-content;"><b>{{ tranService.translate("ticket.label.listImsis") }}</b>
                                </label>
                            </div>
                            <div class="flex flex-row justify-content-between" *ngIf="isShowListImsi">
                                <div class="w-full">
                                    <p-table [value]="this.listImsis">
                                        <ng-template pTemplate="header">
                                            <tr>
                                                <th>{{ tranService.translate('global.text.stt') }}</th>
                                                <th>{{ tranService.translate('ticket.label.imsi') }}</th>
                                                <th *ngIf="typeRequest == 'update'">{{ tranService.translate('global.text.action') }}</th>
                                            </tr>
                                        </ng-template>
                                        <ng-template pTemplate="body" let-item let-i="rowIndex">
                                            <tr>
                                                <td>{{ i + 1 }}</td>
                                                <td>{{ item }}</td>
                                                <td *ngIf="typeRequest == 'update'">
                                                    <p-button icon="pi pi-trash" (click)="removeImsi(i)"
                                                              [hidden]="!isShowImsiInput"></p-button>
                                                </td>
                                            </tr>
                                        </ng-template>
                                    </p-table>
                                </div>
                            </div>
                            <div class="grid justify-center mt-1 px-auto" [class]="isShowImsiInput ? '' : 'hidden'">
                                <div class="col-11">
                                    <div class="p-inputGroup justify-content-center ">
                                        <button type="button" pButton icon="pi pi-plus" (click)="addImsi()"
                                                [disabled]="listImsis.length >= ticket.quantity || isShowMesMaxLenImsi || newImsi == '' || isShowMesExistImsi"></button>
                                        <input type="number"
                                               [ngModelOptions]="{standalone: true}" pInputText
                                               [placeholder]="tranService.translate('ticket.text.addImsi')"
                                               [(ngModel)]="newImsi"
                                               pattern="[0-9]*"
                                               maxlength="18"
                                               [disabled]="listImsis.length >= ticket.quantity"
                                               (input)="checkImsiInput()"
                                               (keydown)="preventCharacter($event)"
                                               min="0"
                                        />
                                    </div>
                                </div>
                                <div class="col-12 field grid text-error-field">
                                    <label htmlFor="imsi" class="col-fixed" style="width:180px"></label>
                                    <div class="col-12">
                                        <small class="text-red-500"
                                               *ngIf="getFirstErrorMessage()">{{ getFirstErrorMessage() }}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--                                        <div class="flex-1">-->
                    <!--                                            <div class="field  px-4 pt-4  flex-row ">-->
                    <!--                                                &lt;!&ndash; email &ndash;&gt;-->
                    <!--                                                <form [formGroup]="formMailInput">-->
                    <!--                                                    <div class="field grid px-4 pt-4 flex flex-row flex-nowrap">-->
                    <!--                                                        &lt;!&ndash; email &ndash;&gt;-->
                    <!--                                                        <label htmlFor="email" class="col-fixed" style="width:100px">{{tranService.translate("alert.receiving.emails")}}<span class="text-red-500"></span></label>-->
                    <!--                                                        <div class="col-8" >-->
                    <!--                                                            <input class="w-full"-->
                    <!--                                                                   pInputText id="email"-->
                    <!--                                                                   [(ngModel)]="newImsi"-->
                    <!--                                                                   [maxLength]="255"-->
                    <!--                                                                   [placeholder]="tranService.translate('ticket.label.imsi')"-->
                    <!--                                                                   pattern="^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$"-->
                    <!--                                                            />-->
                    <!--                                                        </div>-->
                    <!--                                                        <button pButton class="p-button-outlined" type="button" icon="pi pi-plus-circle add-icon-size" style="width: 41px; height: 36px" (click)="addImsi()" [disabled]="formMailInput.invalid"></button>-->
                    <!--                                                    </div>-->

                    <!--                                                </form>-->
                    <!--                                                <div class="field  px-4 pt-4  flex-row ">-->
                    <!--                                                    <table-vnpt-->
                    <!--                                                        [fieldId]="'id'"-->
                    <!--                                                        [columns]="columns"-->
                    <!--                                                        [dataSet]="dataSet"-->
                    <!--                                                        [options]="optionTable"-->
                    <!--                                                        [loadData]="search.bind(this)"-->
                    <!--                                                        [scrollHeight]="'200px'"-->
                    <!--                                                    ></table-vnpt>-->
                    <!--                                                </div>-->
                    <!--                                            </div>-->
                    <!--                                        </div>-->
                </div>

            </div>
            <div class="flex flex-row justify-content-center align-items-center mt-3" *ngIf="typeRequest == 'update'">
                <p-button styleClass="mr-2 p-button-secondary" [label]="tranService.translate('global.button.cancel')"
                          (click)="isShowUpdateRequest = false"></p-button>
                <p-button type="submit" styleClass="p-button-info" *ngIf="checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])"
                          [disabled]="formUpdateOrderSim.invalid || !isEnableButtonSave || isEmptyListImsi || (this.listNotes.length > 0 && !isFormValid())"
                          [hidden]="typeRequest == 'view'"
                          [label]="tranService.translate('global.button.save')"></p-button>
            </div>

        </form>
    </p-dialog>
</div>

<p-dialog class="dialog-vnpt" [header]="tranService.translate('ticket.label.historyOrder')" [modal]="true"
          [draggable]="false" [resizable]="false"
          [breakpoints]="{ '1199px': '75vw', '575px': '90vw' }" [(visible)]="isShowOrder" [style]="{ width: '50rem' }">
    <form [formGroup]="formSearchHistoryOrder" (ngSubmit)="onSubmitSearchHistory()" class="pt-3 pb-2 vnpt-field-set">
        <div class="grid search-grid-2  align-items-center">
            <div class="col-4">
                <span class="p-float-label">
                    <p-calendar styleClass="w-full"
                                id="dateFrom"
                                [(ngModel)]="searchHistoryOrder.fromDate"
                                formControlName="fromDate"
                                [showIcon]="true"
                                [showClear]="true"
                                dateFormat="dd/mm/yy"
                                [maxDate]="maxDateFrom"
                                (onSelect)="onChangeDateFrom(searchHistoryOrder.fromDate)"
                                (onInput)="onChangeDateFrom(searchHistoryOrder.fromDate)"
                    ></p-calendar>
                    <label htmlFor="dateFrom">{{ tranService.translate("ticket.label.dateFrom") }}</label>
                </span>
            </div>
            <div class="col-4 ml-2">
                <span class="p-float-label">
                    <p-calendar styleClass="w-full"
                                id="dateTo"
                                [(ngModel)]="searchHistoryOrder.toDate"
                                formControlName="toDate"
                                [showIcon]="true"
                                [showClear]="true"
                                dateFormat="dd/mm/yy"
                                [minDate]="minDateTo"
                                [maxDate]="maxDateTo"
                                (onSelect)="onChangeDateTo(searchHistoryOrder.toDate)"
                                (onInput)="onChangeDateTo(searchHistoryOrder.toDate)"
                    />
                    <label htmlFor="dateTo">{{ tranService.translate("ticket.label.dateTo") }}</label>
                </span>
            </div>
            <div class="col-2">
                <p-button icon="pi pi-search"
                          styleClass="p-button-rounded p-button-secondary p-button-text button-search"
                          type="submit"
                ></p-button>
            </div>
        </div>

    </form>

    <table-vnpt
        [columns]="columnsHistory"
        [dataSet]="dataSetHistory"
        [options]="optionTableHistory"
        [loadData]="searchHistory.bind(this)"
        [pageNumber]="0"
        [pageSize]="999999999999"
        [params]="searchHistoryOrder"
    ></table-vnpt>

</p-dialog>
