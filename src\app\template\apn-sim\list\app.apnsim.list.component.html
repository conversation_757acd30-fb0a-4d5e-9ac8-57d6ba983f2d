<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{tranService.translate("global.menu.apnsimlist")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
</div>
<form [formGroup]="formSearchSim" (ngSubmit)="onSubmitSearch()" class="pb-2 pt-3 vnpt-field-set">
    <p-panel [toggleable]="true" [header]="tranService.translate('global.text.filter')">
        <div class="grid search-grid-2">
            <!-- ma apn -->
            <div class="col-3">
                <span class="p-float-label">
                    <input class="w-full"
                           pInputText id="apnId"
                           [(ngModel)]="searchInfo.apnId"
                           formControlName="apnId"
                    />
                    <label htmlFor="apnId">{{tranService.translate("sim.label.maapn")}}</label>
                </span>
            </div>
            <!-- so thue bao -->
            <div class="col-3">
                <span class="p-float-label">
                    <input pInputText
                           class="w-full"
                           pInputText id="msisdn"
                           [(ngModel)]="searchInfo.msisdn"
                           formControlName="msisdn"
                    />
                    <label htmlFor="msisdn">{{tranService.translate("sim.label.sothuebao")}}</label>
                </span>
            </div>
            <div class="col-3 pb-0">
                <span class="p-float-label">
                    <p-calendar styleClass="w-full"
                                id="contractDateFrom"
                                [(ngModel)]="searchInfo.contractDateFrom"
                                formControlName="contractDateFrom"
                                [showIcon]="true"
                                [showClear]="true"
                                dateFormat="dd/mm/yy"
                                [maxDate]="maxDateFrom"
                                (onSelect)="onChangeDateFrom(searchInfo.contractDateFrom)"
                                (onInput)="onChangeDateFrom(searchInfo.contractDateFrom)"
                    ></p-calendar>
                    <label class="label-calendar" htmlFor="contractDateFrom">{{tranService.translate("sim.label.ngaylamhopdongtu")}}</label>
                </span>
            </div>
            <div class="col-3">
                <span class="p-float-label">
                    <p-dropdown styleClass="w-full" [showClear]="true"
                                id="status" [autoDisplayFirst]="false"
                                [(ngModel)]="searchInfo.status"
                                formControlName="status"
                                [options]="statuSims"
                                optionLabel="name"
                                optionValue="value"
                    ></p-dropdown>
                    <label for="status">{{tranService.translate("sim.label.trangthaisim")}}</label>
                </span>
            </div>
            <!-- khach hang -->
            <div class="col-3">
                <div class="relative">
                    <vnpt-select
                        class="w-full"
                        [(value)]="searchInfo.customer"
                        [placeholder]="tranService.translate('sim.label.khachhang')"
                        objectKey="dropdownListSim"
                        paramKey="name"
                        keyReturn="customerCode"
                        displayPattern="${customerName} - ${customerCode}"
                        typeValue="primitive"
                        [paramDefault]="{type: 'customer'}"
                        [isMultiChoice]="false"
                        [floatLabel]="true"
                        [stylePositionBoxSelect]="getBoxSelectStyle()"
                    ></vnpt-select>
                </div>
            </div>
            <!-- imei  -->
            <div class="col-3">
                <span class="p-float-label">
                    <input class="w-full"
                           pInputText id="deviceImei"
                           [(ngModel)]="searchInfo.deviceImei"
                           formControlName="deviceImei"
                    />
                    <label htmlFor="deviceImei">{{tranService.translate("sim.label.imeiDevice")}}</label>
                </span>
            </div>
            <!--            Ngày làm hợp đồng đến-->
            <div class="col-3 pb-0">
                <span class="p-float-label">
                    <p-calendar styleClass="w-full"
                                id="contractDateTo"
                                [(ngModel)]="searchInfo.contractDateTo"
                                formControlName="contractDateTo"
                                [showIcon]="true"
                                [showClear]="true"
                                dateFormat="dd/mm/yy"
                                [minDate]="minDateTo"
                                [maxDate]="maxDateTo"
                                (onSelect)="onChangeDateTo(searchInfo.contractDateTo)"
                                (onInput)="onChangeDateTo(searchInfo.contractDateTo)"
                    />
                    <label class="label-calendar" htmlFor="contractDateTo">{{tranService.translate("sim.label.ngaylamhopdongden")}}</label>
                </span>
            </div>
            <!--            button search-->
            <div class="col-3 pb-0">
                <p-button icon="pi pi-search"
                          styleClass="p-button-rounded p-button-secondary p-button-text button-search"
                          type="submit"
                ></p-button>
            </div>
        </div>
    </p-panel>
</form>
<div class="flex justify-content-center dialog-vnpt" *ngIf="isShowPopupDetailSim">
    <p-dialog [header]="tranService.translate('sim.text.detailSim')" [(visible)]="isShowPopupDetailSim" [modal]="true" [style]="{ width: '980px' }" [draggable]="false" [resizable]="false">
        <div class="grid grid-1 mt-1 h-auto" style="width: calc(100% + 16px);">
            <div class="col sim-detail pr-0">
                <p-card [header]="tranService.translate('sim.text.simInfo')">
                    <div class="flex flex-row justify-content-between custom-card">
                        <div class="w-6">
                            <div class="grid">
                                <span style="min-width: 150px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.sothuebao")}}</span>
                                <span class="col">{{detailSim.msisdn}}</span>
                            </div>
                            <div class="mt-1 grid">
                                <span style="min-width: 150px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.trangthaisim")}}</span>
                                <span class="w-auto ml-3" [class]="getClassStatus(detailSim.status)">{{getNameStatus(detailSim.status)}}</span>
                            </div>
                            <div class="mt-1 grid">
                                <span style="min-width: 150px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.imsi")}}</span>
                                <span class="col">{{detailSim.imsi}}</span>
                            </div>
                            <div class="mt-1 grid">
                                <span style="min-width: 150px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.imeiDevice")}}</span>
                                <span class="col">{{detailSim.imei}}</span>
                            </div>
                            <div class="mt-1 grid">
                                <span style="min-width: 150px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.maapn")}}</span>
                                <span class="col">{{detailSim.apnId}}</span>
                            </div>
                            <div class="mt-1 grid">
                                <span style="min-width: 150px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.trangthaiketnoi")}}</span>
                                <span *ngIf="detailSim.connectionStatus!==undefined && detailSim.connectionStatus!==null && detailSim.connectionStatus!=='0' " class="ml-3 p-2 text-green-800 bg-green-100 border-round inline-block">ON</span>
                                <span *ngIf="detailSim.connectionStatus==='0'" class="ml-3 p-2 text-50 surface-500 border-round inline-block">OFF</span>
                                <span *ngIf="detailSim.connectionStatus===undefined || detailSim.connectionStatus=== null " class="ml-3 p-2 text-50 surface-500 border-round inline-block">NOT FOUND</span>
                            </div>
                        </div>
                        <div class="w-6">
                            <div class="grid">
                                <span style="min-width: 150px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.startDate")}}</span>
                                <span class="col">{{detailSim.startDate | date:'dd/MM/yyyy'}}</span>
                            </div>
                            <div class="mt-1 grid">
                                <span style="min-width: 150px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.serviceType")}}</span>
                                <span class="w-auto ml-3">{{getServiceType(detailSim.serviceType)}}</span>
                            </div>
                        </div>
                    </div>
                </p-card>
                <p-card [header]="tranService.translate('sim.text.simStatusInfo')" styleClass="mt-3 sim-status">
                    <div class="grid">
                        <div class="col-4 text-center">
                            <p-toggleButton [(ngModel)]="detailStatusSim.statusData" [disabled]="true" onLabel="ON" offLabel="OFF"></p-toggleButton>
                            <div>{{tranService.translate("sim.status.service.data")}}</div>
                        </div>
                        <div class="col-4 text-center">
                            <p-toggleButton [(ngModel)]="detailStatusSim.statusReceiveCall" [disabled]="true" onLabel="ON" offLabel="OFF"></p-toggleButton>
                            <div>{{tranService.translate("sim.status.service.callReceived")}}</div>
                        </div>
                        <div class="col-4 text-center">
                            <p-toggleButton [(ngModel)]="detailStatusSim.statusSendCall" [disabled]="true" onLabel="ON" offLabel="OFF"></p-toggleButton>
                            <div>{{tranService.translate("sim.status.service.callSent")}}</div>
                        </div>
                    </div>
                    <div class="grid">
                        <div class="col-4 text-center">
                            <p-toggleButton [(ngModel)]="detailStatusSim.statusWorldCall" [disabled]="true" onLabel="ON" offLabel="OFF"></p-toggleButton>
                            <div>{{tranService.translate("sim.status.service.callWorld")}}</div>
                        </div>
                        <div class="col-4 text-center">
                            <p-toggleButton [(ngModel)]="detailStatusSim.statusReceiveSms" [disabled]="true" onLabel="ON" offLabel="OFF"></p-toggleButton>
                            <div>{{tranService.translate("sim.status.service.smsReceived")}}</div>
                        </div>
                        <div class="col-4 text-center">
                            <p-toggleButton [(ngModel)]="detailStatusSim.statusSendSms" [disabled]="true" onLabel="ON" offLabel="OFF"></p-toggleButton>
                            <div>{{tranService.translate("sim.status.service.smsSent")}}</div>
                        </div>
                    </div>
                </p-card>
                <!-- goi cuoc -->
                <p-card [header]="tranService.translate('sim.text.ratingPlanInfo')" styleClass="mt-3">
                    <div class="grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.tengoicuoc")}}</span>
                        <span class="col">{{detailSim.ratingPlanName}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.dataUseInMonth")}}</span>
                        <span class="col">{{this.utilService.bytesToMegabytes(detailRatingPlan.dataUseInMonth) | number }} {{detailRatingPlan.unit?detailRatingPlan.unit:"MB"}}</span>
                    </div>
                </p-card>
            </div>
            <div class="col sim-detail pr-0">
                <!-- hop dong -->
                <p-card [header]="tranService.translate('sim.text.contractInfo')">
                    <div class="grid mt-0">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.mahopdong")}}</span>
                        <span class="col">{{detailContract.contractCode}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.ngaylamhopdong")}}</span>
                        <span class="col">{{detailContract.contractDate | date:'dd/MM/yyyy'}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.nguoilamhopdong")}}</span>
                        <span class="col uppercase">{{detailContract.contractorInfo}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.matrungtam")}}</span>
                        <span class="col">{{detailContract.centerCode}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.dienthoailienhe")}}</span>
                        <span class="col">{{detailContract.contactPhone}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.diachilienhe")}}</span>
                        <span class="col">{{detailContract.contactAddress}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.paymentName")}}</span>
                        <span class="col uppercase">{{detailContract.paymentName}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.paymentAddress")}}</span>
                        <span class="col">{{detailContract.paymentAddress}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.routeCode")}}</span>
                        <span class="col">{{detailContract.routeCode}}</span>
                    </div>
                </p-card>
                <!-- customer -->
                <p-card [header]="tranService.translate('sim.text.customerInfo')" styleClass="mt-3">
                    <div class="grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.khachhang")}}</span>
                        <span class="col">{{detailCustomer.name}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.customerCode")}}</span>
                        <span class="col">{{detailCustomer.code}}</span>
                    </div>
                </p-card>
            </div>
        </div>
    </p-dialog>
</div>
<div class="flex justify-content-center dialog-vnpt" *ngIf="isShowPopupDetailAPNSim">
    <p-dialog [header]="tranService.translate('global.menu.apnsimdetail')" [(visible)]="isShowPopupDetailAPNSim" [modal]="true" [style]="{ width: '980px' }" [draggable]="false" [resizable]="false">
        <div class="border-round bg-white mt-3 pt-2">
            <div class = "grid mx-4 pt-3" *ngIf="detailApnSim">
                <div class = "col-4" *ngFor="let key of fieldsToDisplay">
                    <h5>{{ getTitle(key) }}</h5>
                    <p>{{ getContent(key) }}</p>
                </div>
            </div>
            <div class="text-center pb-4">
                <!--        <p-button styleClass="p-button-secondary p-button-outlined" (click)="goBack()">{{tranService.translate("global.button.back")}}</p-button>-->
            </div>
        </div>
    </p-dialog>
</div>
<!--table-->
<table-vnpt
    [tableId]="'listApnSim'"
    [fieldId]="'msisdn'"
    [(selectItems)]="selectItems"
    [columns]="columns"
    [dataSet]="dataSet"
    [options]="optionTable"
    [loadData]="search.bind(this)"
    [pageNumber]="pageNumber"
    [pageSize]="pageSize"
    [sort]="sort"
    [params]="searchInfo"
    [labelTable]="this.tranService.translate('global.menu.apnsimlist')"
></table-vnpt>
