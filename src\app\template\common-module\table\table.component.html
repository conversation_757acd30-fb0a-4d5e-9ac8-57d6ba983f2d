<div class="mt-2 table-vnpt relative p-4 bg-white border-round-lg" [style]="styleOutLineTable">
    <div class="flex flex-row justify-content-between mb-2" *ngIf="(!options.hasClearSelected && selectItems.length > 0)||labelTable||options.hasShowToggleColumn || isUseCustomSelectAll">
        <div class="flex flex-row gap-3 mb-1">
            <div *ngIf="labelTable" class="flex justify-content-center align-items-center text-lg font-bold">{{labelTable}}</div>
            <div *ngIf="(!options.hasClearSelected && selectItems.length > 0) || isUseCustomSelectAll" class="flex flex-row justify-content-center align-items-center text-base px-2">
                <p-checkbox *ngIf="isUseCustomSelectAll" [(ngModel)]="customSelectAll" [binary]="true" styleClass="mr-2" (click)="onChangeCustomSelectAll($event)" [pTooltip]="tooltipSelectAll"></p-checkbox>
                <span *ngIf="!options.hasClearSelected && selectItems.length > 0">{{transService.translate("global.text.itemselected")}} {{selectItems.length}}</span>
                <button *ngIf="selectItems.length > 0" pButton  [pTooltip]="clearSelected" tooltipPosition="bottom" class="ml-2" styleClass="" (click)="selectItems=[];modelSelected=[]"><i class=" pi pi-times "></i></button>
            </div>
        </div>
        <div class="flex flex-row align-items-center gap-2" *ngIf="options.hasShowToggleColumn">
            <div style="text-align: right;"  *ngIf="options.hasShowToggleColumn">
                <p-overlayPanel styleClass="p-0" #op>
                    <ng-template pTemplate="content">
                        <div class="max-h-20rem" style="overflow-y: scroll;">
                            <ol style="padding: 0;">
                                <li *ngFor="let item of columns">
                                    <p-checkbox class="mb-1" [(ngModel)]="columnShows" [value]="item.key" inputId="item.key" [label]="item.name" (ngModelChange)="columnShowChanged($event)"></p-checkbox>
                                </li>
                            </ol>
                        </div>
                    </ng-template>
                </p-overlayPanel>
                <button *ngIf="options.hasShowToggleColumn" pButton class="p-button-outlined" (click)="op.toggle($event)"><i class=" pi pi-filter "></i></button>
            </div>
            <!-- <button *ngIf="selectItems.length > 0" pButton  [pTooltip]="clearSelected" tooltipPosition="bottom" class="ml-2" styleClass="" (click)="selectItems=[];modelSelected=[]"><i class=" pi pi-times "></i></button> -->
        </div>


    </div>
    <p-table
        [value]="dataSet?.content"
        [paginator]="dataSet?.total > 0 && options.paginator"
        [rows]="pageSize"
        [first]="rowFirst"
        [showCurrentPageReport]="true"
        [tableStyle]="{ 'min-width': '100%' }"
        [currentPageReportTemplate]="transService.translate('global.text.templateTextPagination')"
        (onPage)="pageChange($event)"
        [rowsPerPageOptions]="rowsPerPageOptions"
        [styleClass]="'p-datatable-sm responsive-table'"
        [(selection)]="modelSelected"
        [totalRecords]="dataSet?.total"
        [lazy]="true"
        (onSort)="handleSort($event)" [customSort]="true"
        [sortField]="getSortField()"
        [sortOrder]="getSortOrder()"
        paginatorDropdownAppendTo="body"
        #dataTable
        (selectAllChange)="test($event)"
        (selectionChange)="handleSelectAllChange($event)"
        [resetPageOnSort]="false"
        [scrollHeight]="scrollHeight"
        [scrollable]="true"
        [reorderableColumns]="isRowDraggable"
    >
        <ng-template pTemplate="header">
            <tr>
                <th *ngIf="isRowDraggable"></th>
                <th [style.width.rem]="selectionWidth" class="flex flex-row align-items-center gap-3" *ngIf="options.hasShowChoose">
                    <p-tableHeaderCheckbox [disabled]="options.disabledCheckBox" (click)="onChangeSelectAll()"></p-tableHeaderCheckbox>
                    <div>{{tableSelectionText}}</div>
                </th>
                <th *ngIf="options.hasShowIndex" style="width: 100px;">{{transService.translate('global.text.stt')}}</th>
                <th *ngFor="let item of filterColumnShow(columns)" class="white-space-nowrap"
                    [style]="{'min-width': item.size, 'text-align': item.align, 'width': item.size}"
                    [pSortableColumn]="item.key"
                >{{ item.name }} <p-sortIcon [field]="item.key" *ngIf="item.isSort"></p-sortIcon></th>
                <th pFrozenColumn alignFrozen="right" *ngIf="options.action" style="text-align: center;" [ngStyle]="{'width': actionWidth ? actionWidth : null}" class="white-space-nowrap border-left-1">{{transService.translate("global.text.action")}}</th>
            </tr>
            <tr *ngIf="checkEmpty()">
                <td [attr.colspan]="getNumberColumnShow()" [style]="{'min-width': filterColumnShow(columns)[0].size}" class="box-table-nodata">
                    <span class="pi pi-inbox" style="font-size: x-large;">&nbsp;</span>{{transService.translate("global.text.nodata")}}
                </td>
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-data let-i="rowIndex">
            <tr [pReorderableRow]="isRowDraggable ? i : ''">
                <td *ngIf="isRowDraggable">
                    <span class="pi pi-sliders-h" pReorderableRowHandle></span>
                </td>
                <td *ngIf="options.hasShowChoose">
                    <p-tableCheckbox [value]="data" [disabled]="options.disabledCheckBox" (click)="onClickItemCheckbox()"></p-tableCheckbox>
                </td>
                <td *ngIf="options.hasShowIndex" style="width: 100px;">{{i + 1}}</td>
                <td *ngFor="let item of filterColumnShow(columns)" [style]="{'min-width': item.size,'width':item.size,'text-align':item.align}" [pTooltip]="item.isShowTooltip ? (item.funcCustomizeToolTip ? item.funcCustomizeToolTip(data[item.key], data) : (item.funcConvertText ? item.funcConvertText(data[item.key], data) : data[item.key])) : ''" class="table-column-vnpt"
                >
                    <!-- Editable input for specific columns -->
                    <div *ngIf="item.isEditable" style="text-wrap : auto">
                        <input pInputText *ngIf="item.isEditable && item.editInputType === 'string'" [(ngModel)]="data[item.key]" [style]="{'min-width': item.size}" (keydown)="filterInput($event, item.validate)"
                               (blur)="onBlurInput($event, isFormInvalid(data[item.key], item.validate), data, item.editInputType)"
                               [ngClass]="{'border-red-500': isFormInvalid(data[item.key], item.validate)}"
                               (ngModelChange)="onValueChange($event, data, item)"
                               class="form-control" type="text" required/>
                        <p-inputNumber *ngIf="item.isEditable && item.editInputType === 'number'"
                                mode="decimal"
                                locale="vi-VN"
                                [(ngModel)]="data[item.key]"
                                [style]="{'min-width': item.size}"
                                (keydown)="filterInput($event, item.validate)"
                                (onBlur)="onBlurInput($event, isFormInvalid(data[item.key], item.validate), data, item.editInputType)"
                                [ngClass]="{'border-red-500': isFormInvalid(data[item.key], item.validate)}"
                                (ngModelChange)="onValueChange($event, data, item)">
                        </p-inputNumber>
                        <div class="text-red-500" *ngIf="isFormInvalid(data[item.key], item.validate, 'pattern')">{{ item.validate.messageErrorPattern}}</div>
                        <div class="text-red-500" *ngIf="isFormInvalid(data[item.key], item.validate, 'required')">{{tranService.translate("global.message.required",{len:item.validate.maxLength})}}</div>
                        <div class="text-red-500" *ngIf="isFormInvalid(data[item.key], item.validate, 'maxLength')">{{tranService.translate("global.message.maxLength",{len:item.validate.maxLength})}}</div>
                        <div class="text-red-500" *ngIf="isFormInvalid(data[item.key], item.validate, 'minLength')">{{tranService.translate("global.message.minLength",{len:item.validate.minLength})}}</div>
                        <div class="text-red-500" *ngIf="isFormInvalid(data[item.key], item.validate, 'max')">{{tranService.translate("global.message.max",{value:formatNumber(item.validate.max)})}}</div>
                        <div class="text-red-500" *ngIf="isFormInvalid(data[item.key], item.validate, 'min')">{{tranService.translate("global.message.min",{value:formatNumber(item.validate.min)})}}</div>
                    </div>

                    <!-- Display static text when not editable -->
                    <ng-container *ngIf="!item.isEditable">
                <span *ngIf="!item.funcGetRouting" (click)="handleClickText(item, data)"
                      [style]="item.style"
                      [class]="item.funcGetClassname ? item.funcGetClassname(data[item.key]) : item.className">
                    {{ item.funcConvertText ? item.funcConvertText(data[item.key], data) : data[item.key] }}
                </span>
                        <a [routerLink]="item.funcGetRouting(data)"
                           routerLinkActive="router-link-active"
                           *ngIf="item.funcGetRouting"
                           [style]="item.style"
                           [class]="item.funcGetClassname ? item.funcGetClassname(data[item.key]) : item.className">
                            {{ item.funcConvertText ? item.funcConvertText(data[item.key], data) : data[item.key] }}
                        </a>
                    </ng-container>
                </td>
                <td pFrozenColumn alignFrozen="right" *ngIf="options.action" class="border-left-1" [ngStyle]="{'width': actionWidth ? actionWidth : null}">
                    <div class="flex flex-row grap-2 justify-content-center align-items-center">
                        <span *ngFor="let op of filterAction(options.action, data, data[fieldId])"
                            class="inline-block mr-1 cursor-pointer"
                            [class]="op.icon"
                            (click)="op.func(data[fieldId], data)"
                            [pTooltip]="op.tooltip"
                            tooltipPosition="left"
                        ></span>
                    </div>
                </td>
            </tr>
        </ng-template>
        <ng-template pTemplate="paginatorright">
            <div class="flex flex-row justify-content-start align-items-center grap-2 ml-3" *ngIf="options.hasShowJumpPage !== false">
                <div class="mr-2">{{transService.translate('global.text.page')}}</div>
                <p-inputNumber (keyup.enter)="jumpPage(pageC)" [(ngModel)]="pageCurrent" mode="decimal" [min]="1" [max]="getMaxPage()"> </p-inputNumber>
            </div>
        </ng-template>
    </p-table>
</div>
