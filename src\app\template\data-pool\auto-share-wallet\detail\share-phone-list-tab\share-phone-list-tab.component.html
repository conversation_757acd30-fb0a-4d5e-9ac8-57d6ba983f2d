<ng-template #sharePhoneListTemplateRef>
    <div style="box-shadow: 0px 10px 15px -3px rgba(0,0,0,0.1);">
        <form [formGroup]="form" (ngSubmit)="onSubmitSearch()" class="mt-2">
            <div class="flex align-items-center flex-wrap">
                <div class="col-3">
                <span class="p-float-label">
                    <input pInputText
                           class="w-full"
                           formControlName="phoneNumber"
                    />
                    <label htmlFor="msisdn">{{tranService.translate("account.label.phone")}}</label>
                </span>
                </div>
                <div class="col-3">
                <span class="p-float-label">
                    <input pInputText
                           class="w-full"
                           formControlName="fullName"
                    />
                    <label htmlFor="msisdn">{{tranService.translate("account.label.fullname")}}</label>
                </span>
                </div>
                <div class="col-3">
                <span class="p-float-label">
                    <p-multiSelect styleClass="w-full"
                                   [showClear]="true"
                                   id="chartSubType"
                                   formControlName="lstSubCode"
                                   [options]="walletCodeLst"
                                   optionLabel="name"
                                   optionValue="value"
                                   [placeholder]="''"
                                   [emptyFilterMessage]="tranService.translate('global.text.nodata')"
                    ></p-multiSelect>
                    <label htmlFor="msisdn">{{tranService.translate("datapool.label.walletCode")}}</label>
                </span>
                </div>
                <div class="col-3">
                    <p-dropdown styleClass="w-full" [showClear]="true"
                                id="status" [autoDisplayFirst]="false"
                                formControlName="status"
                                [options]="statusList"
                                optionLabel="name"
                                optionValue="value"
                                [placeholder]="tranService.translate('datapool.label.renewalStatus')"
                    ></p-dropdown>
                </div>
                <!--            fromDate-->
                <div class="col-3">
                <span class="p-float-label">
                    <p-calendar styleClass="w-full"
                                id="fromDate"
                                formControlName="fromDate"
                                [showIcon]="false"
                                [showClear]="true"
                                dateFormat="dd/mm/yy"
                    ></p-calendar>
                    <label htmlFor="fromDate">{{tranService.translate("datapool.button.dateFrom")}}</label>
                </span>
                </div>
                <!--            dateTo-->
                <div class="col-3">
                <span class="p-float-label">
                    <p-calendar styleClass="w-full"
                                id="toDate"
                                formControlName="toDate"
                                [showIcon]="false"
                                [showClear]="true"
                                dateFormat="dd/mm/yy"
                    ></p-calendar>
                    <label htmlFor="toDate">{{tranService.translate("datapool.button.dateTo")}}</label>
                </span>
                </div>
                <div class="col-3">
                    <p-button icon="pi pi-search"
                              styleClass="p-button-rounded p-button-secondary p-button-text button-search"
                              type="submit"
                    ></p-button>
                </div>
            </div>
        </form>
        <div class="flex flex-row justify-content-end align-items-center gap-2">
            <p-button styleClass="p-button-info" (click)="openModelAddShare()"
                      [label]="tranService.translate('datapool.button.addSharePhone')"></p-button>
            <p-button [disabled]="selectItems.length == 0" styleClass="p-button-secondary"
                      (click)="selectItems.length == 0? null : openModelDelete(1)"
                      [label]="tranService.translate('datapool.button.removeSharePhone')"></p-button>
        </div>
        <table-vnpt
                [fieldId]="'autoId'"
                [columns]="columns"
                [dataSet]="dataSet"
                [options]="optionTable"
                [(selectItems)]="selectItems"
                [pageNumber]="pageNumber"
                [loadData]="search.bind(this)"
                [pageSize]="pageSize"
                [sort]="sort"
                [params]="searchInfo"
                (blurInputEvent)="handleBlurInput($event)"
        >
        </table-vnpt>
    </div>
    <div class="flex justify-content-center dialog-vnpt" *ngIf="isShowModalAddSharePhone">
        <form [formGroup]="formAddShare" (ngSubmit)="addSharePhone()">
            <p-dialog [header]="tranService.translate('datapool.button.addSharePhone')"
                      [(visible)]="isShowModalAddSharePhone"
                      [modal]="true" [style]="{ width: '980px' }" [draggable]="false" [resizable]="false">
                <div class="flex flex-wrap">
                    <div class="col-6 mt-2">
                        <div class="flex">
                            <label style="min-width: 135px"
                                   htmlFor="walletName">{{tranService.translate("datapool.label.walletCode")}}<span
                                    class="text-red-500">*</span></label>
                            <p-dropdown class="w-full" [options]="dummyWallet" formControlName="walletCode"
                                        appendTo="body"
                                        optionLabel="subCode" optionValue="subCode"
                                        (onChange)="changeWalletCode($event)"
                                        [placeholder]="tranService.translate('datapool.label.selectWallet')"
                                        filter="true"
                                        [emptyFilterMessage]="tranService.translate('global.text.nodata')"
                                        id="walletName">
                            </p-dropdown>
                        </div>
                        <div class="mt-2" *ngIf="f1.walletCode.value && getTypeOfWallet() === 'DATA'">
                            <div class="flex w-full">
                                <label style="min-width: 135px"
                                       htmlFor="walletName">{{tranService.translate("datapool.label.sharingDataNotType") + ' (MB)'}}
                                    <span class="text-red-500">*</span></label>
                                <p-inputNumber class="w-full" formControlName="shareTraffic" locale="vi-VN"
                                               (keydown)="checkShareTraffic($event)"
                                               [placeholder]="tranService.translate('datapool.label.sharingDataNotType')"
                                > </p-inputNumber>
                            </div>
                            <div class="flex w-full">
                                <div style="min-width: 135px"></div>
                                <div class="">
                                    <small class="text-red-500"
                                           *ngIf="validateFormAddSharePhone('shareTraffic', 'required')">{{tranService.translate("global.message.required")}}</small>
                                </div>

                            </div>
                            <div class="flex w-full">
                                <div style="min-width: 135px"></div>
                                <div class="">
                                    <small class="text-red-500"
                                           *ngIf="validateFormAddSharePhone('shareTraffic', 'isInvalidDataUsage')">{{tranService.translate("datapool.message.invalidDataUsage")}}</small>
                                </div>
                            </div>
                            <div class="flex w-full">
                                <div style="min-width: 135px"></div>
                                <div class="">
                                    <div class="text-red-500" *ngIf="validateFormAddSharePhone('shareTraffic','max')">{{tranService.translate("global.message.max",{value:formatNumber(3000000)})}}</div>
                                </div>
                            </div>
                        </div>
                        <div class="mt-2" *ngIf="f1.walletCode.value && getTypeOfWallet() === 'SMS'">
                            <div class="flex w-full">
                                <label style="min-width: 135px"
                                       htmlFor="walletName">{{tranService.translate("datapool.label.sharingDataNotType") + ' (SMS)'}}
                                    <span class="text-red-500">*</span></label>
                                <p-inputNumber class="w-full" formControlName="shareTraffic" locale="vi-VN"
                                               (keydown)="checkShareTraffic($event)"
                                               [placeholder]="tranService.translate('datapool.label.sharingDataNotType')"
                                > </p-inputNumber>
                            </div>
                            <div class="flex w-full">
                                <div style="min-width: 135px"></div>
                                <div class="">
                                    <small class="text-red-500"
                                           *ngIf="validateFormAddSharePhone('shareTraffic', 'required')">{{tranService.translate("global.message.required")}}</small>
                                </div>
                            </div>
                            <div class="flex w-full">
                                <div style="min-width: 135px"></div>
                                <div class="">
                                    <small class="text-red-500"
                                           *ngIf="validateFormAddSharePhone('shareTraffic', 'isInvalidDataUsage')">{{tranService.translate("datapool.message.invalidSmsUsage")}}</small>
                                </div>
                            </div>
                            <div class="flex w-full">
                                <div style="min-width: 135px"></div>
                                <div class="">
                                    <div class="text-red-500" *ngIf="validateFormAddSharePhone('shareTraffic','max')">{{tranService.translate("global.message.max",{value:formatNumber(30000)})}}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="flex">
                            <div class="col-4">
                                <div class="field-checkbox">
                                    <p-radioButton name="typeShare" [value]="0" formControlName="typeShare"/>
                                    <label class="ml-2">Chia sẻ ngay</label>
                                </div>
                                <div class="field-checkbox mt-5">
                                    <p-radioButton name="typeShare" [value]="1" formControlName="typeShare"/>
                                    <label class="ml-2">Chia sẻ từ ngày</label>
                                </div>
                            </div>
                            <div class="col-8 mt-6">
                                <p-calendar styleClass="w-full"
                                            id="fromDate"
                                            [ngClass]="{'invalid-border': formAddShare.controls.typeShare.value == 1 && !formAddShare.controls.fromDate.value}"
                                            formControlName="fromDate"
                                            [showIcon]="true"
                                            [showClear]="true"
                                            dateFormat="dd/mm/yy"
                                            [disabled]=""
                                            [minDate]="minDate"
                                            [readonlyInput]="true"
                                ></p-calendar>
                                <small class="text-red-500"
                                       *ngIf="formAddShare.controls.typeShare.value == 1 && !formAddShare.controls.fromDate.value">{{tranService.translate("global.message.required")}}</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-12" style="border-top: 1px #d8d8d8 solid;">
                        <div class="flex gap-3">
                            <div>
                                <vnpt-select style="min-width: 250px;"
                                             [control]="controlPhone"
                                             [(value)]="phoneReceiptSelect"
                                             (onchange)="checkValidAdd()"
                                             [pattern]="regexPhone"
                                             (onSelectItem)="addPhone(phoneReceiptSelect, true)"
                                             [isAutoComplete]="true"
                                             [isMultiChoice]="false"
                                             paramKey="phoneReceipt"
                                             keyReturn="phoneReceipt"
                                             [lazyLoad]="true"
                                             [placeholder]="tranService.translate('datapool.label.receiverPhone')"
                                             displayPattern="${phoneReceipt}"
                                             [loadData]="getListShareInfoCbb.bind(this)"
                                ></vnpt-select>
                                <div class="text-red-500">
                                    <div *ngIf="!isValidPhone">
                                        {{tranService.translate("datapool.message.digitError")}}
                                    </div>
                                </div>
                            </div>
                            <div>
                                <button type="button" [disabled]="isClickCreate || !isValidPhone" pButton style="height: 36px"
                                        class="p-button-info"
                                        (click)="addPhone(phoneReceiptSelect)">{{tranService.translate('global.button.add2')}}</button>
                            </div>
                        </div>
                    </div>
                </div>
                <p-table [value]="shareList" [tableStyle]="{ 'min-width': '50rem'}" dataKey="phoneReceipt" [scrollable]="true" scrollHeight="400px">
                    <ng-template pTemplate="header">
                        <tr>
                            <th>{{tranService.translate("global.text.stt")}}</th>
                            <th>{{tranService.translate("account.label.phone")}}</th>
                            <th>{{tranService.translate('datapool.label.fullName')}}</th>
                            <th>{{tranService.translate('datapool.label.email')}}</th>
                            <th></th>
                        </tr>
                    </ng-template>
                    <ng-template pTemplate="body" let-list let-index="rowIndex">
                        <tr>
                            <td>{{index+1}}</td>
                            <td>{{ list.phoneReceipt }}</td>
                            <td>
                                <input class="w-full" type="text" (input)="changeDataName($event, index)" pInputText [value]="list.name">
                                <div class="text-red-500" *ngIf="list.name?.length >=50">
                                    {{tranService.translate("global.message.maxLength",{len:50})}}
                                </div>
                            </td>
                            <td>
                                <input class="w-full" type="text" (input)="changeDataMail($event, index)" pInputText [value]="list.email">
                                <div class="text-red-500" *ngIf="list.email?.length >=100">
                                    {{tranService.translate("global.message.maxLength",{len:100})}}
                                </div>
                                <div class="text-red-500" *ngIf="isMailInvalid(list.email)">
                                    {{tranService.translate("global.message.formatEmail")}}
                                </div>
                            </td>
                            <td><button type="button" pButton class="p-button-outlined" (click)="deleteItem(index)"><i class="pi pi-trash"></i></button></td>
                        </tr>
                    </ng-template>
                </p-table>
                <div class="flex flex-row justify-content-center align-items-center">
                    <p-button styleClass="mr-2 p-button-secondary p-button-outlined"
                              [label]="tranService.translate('global.button.cancel')"
                              (click)="isShowModalAddSharePhone = false"></p-button>
                    <p-button styleClass="p-button-info" [label]="tranService.translate('global.button.save')"
                              type="submit" [disabled]="formAddShare.invalid || shareList.length == 0"></p-button>
                </div>
            </p-dialog>
        </form>
    </div>
    <p-dialog [header]="tranService.translate('datapool.text.notify')" [(visible)]="isShowModalDeleteAutoShare"
              [modal]="true" [style]="{ width: '700px' }" [draggable]="false" [resizable]="false">
        <div class="flex flex-row justify-content-center align-items-center font-medium mb-1">
            <p style="font-size: 17px">{{tranService.translate("datapool.message.deleteAutoShare")}}</p>
        </div>
        <div class="flex flex-row justify-content-center align-items-center mb-1">
            <i class="text-red-500">({{tranService.translate("datapool.message.deleteWarnAutoShare")}})</i>
        </div>
        <div class="flex flex-row justify-content-center align-items-center" (click)="openNewTab()">
            <i class="cursor-pointer text-blue-500 underline">{{tranService.translate("datapool.text.learnMore")}}</i>
        </div>
        <div class="flex flex-row justify-content-center align-items-center mt-3">
            <p-button styleClass="mr-2 p-button-secondary" [label]="tranService.translate('global.button.cancel')"
                      (click)="isShowModalDeleteAutoShare = false"></p-button>
            <p-button styleClass="p-button-info" [label]="tranService.translate('global.button.yes')"
                      (click)="deleteSharePhone()"></p-button>
        </div>
    </p-dialog>

    <p-dialog class="dataError" [header]="tranService.translate('datapool.label.listShareError')" [(visible)]="isError" [modal]="true" [style]="{ width: '60vw' }" [draggable]="false" [resizable]="false">
        <p-button styleClass="mr-2 p-button-outlined" style="position: absolute;top: 30px;right: 45px;z-index: 5;font-size: 10px"
                  tooltipPosition="right"
                  [pTooltip]="tranService.translate('datapool.label.downloadErrorFile')"
                  icon="pi pi-download" (onClick)="downloadErrorFile()"></p-button>
        <table-vnpt
                [tableId]="'tbSubShareByGroup'"
                [fieldId]="'id'"
                [columns]="columnsError"
                [dataSet]="dataSetError"
                [options]="optionTableError"
                [pageNumber]="pageNumberError"
                [pageSize]="pageSizeError"
                [sort]="sortError"
                [params]="searchInfoError"
                [loadData]="pagingDataError.bind(this)"
                [labelTable]=""
        ></table-vnpt>
    </p-dialog>
</ng-template>

