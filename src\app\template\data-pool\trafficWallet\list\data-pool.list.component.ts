import {Component, Inject, Injector, <PERSON><PERSON><PERSON><PERSON>, OnInit} from "@angular/core";
import {ComponentBase} from "../../../../component.base";
import {FormBuilder, Validators} from "@angular/forms";
import {MenuItem, PrimeIcons, PrimeNGConfig} from "primeng/api";
import {ColumnInfo, OptionTable} from "../../../common-module/table/table.component";
import {TrafficWalletService} from "../../../../service/datapool/TrafficWalletService";
import {
    FilterInputType,
    Pagination,
    SearchType,
    SeperateFilterInfo,
    SeperateSearchInfo
} from "src/app/template/common-module/search-filter-separate/search-filter-separate.component";
import { CONSTANTS } from "src/app/service/comon/constants";
import {ShareManagementService} from "../../../../service/datapool/ShareManagementService";
import { digitValidator } from "src/app/template/common-module/validatorCustoms";

@Component({
    selector: "traffic-wallet-list",
    templateUrl: './data-pool.list.component.html'
})
export class DataPoolListComponent extends ComponentBase implements OnInit, OnDestroy {

    items: MenuItem[];
    home: MenuItem
    searchInfo: {
        lstPackageName: Array<string> | null,
        searchWalletCode: number | 0,
        searchPayCode: number | 0,
        value: string | "",
        searchName: number | 0,
        searchPhone:number | 0,
        lstTrafficType:string | ""
        //phuơng thức chia sẻ tự động, mặc định giá trị là -1
        autoType: number| -1
    };
    accuracyWallet: {
        accuracyType: number | null;
        payCode: string | null;
        subCode: string | null;
        tax: string | null;
        phoneNumber: number | null;
        otp:number|null;
        transId: number|null;
    }
    //body đăng ký/hủy chia sẻ tự động
    withoutOTP: {
        payCode: string | null;
        subCode: string | null;
        tax: string | null;
        phoneNumber: number | null;
        otp: number | null;
        sharingType: string | null;
        regisType: string | null;
        //new = true là ví vừa được tạo, false là đã tạo trước đó
        new: boolean
    }
    typeAutoShare: number | null;
    formAccuracyWallet: any;
    formSearchWallet: any;
    formWithoutOTP: any;
    isShowModalWalletAuthen: boolean;
    columns: Array<ColumnInfo>;
    dataSet: {
        content: Array<any>,
        total: number
    };
    countdown:number;
    interval: any;
    optionTable: OptionTable;
    pageNumber: number;
    pageSize: number;
    sort: string;
    pagination: Pagination;
    isSubmit: boolean = false;
    packageCbb: Array<{name:any,value:any}>;
    payCodeFilter: Array<{name:any,value:any}>;
    minutesText = this.tranService.translate("alert.label.minutes");
    originSearch:any;
    allPermissions = CONSTANTS.PERMISSIONS;
    isShowModalDetail: boolean = false;
    subCodeId: number | string;
    payCode: string | null;
    walletDetail: any;
    listDetail:any;
    canView: boolean = false;
    //hiển thị dialog đăng ký chọn mã ví hoặc mã thanh toán
    isShowRegisBySubOrPay: boolean
    //hiển thị dialog đăng ký theo mã thanh toán
    isShowRegisByPayCode: boolean
    //hiển thị dialog hủy đăng ký theo mã thanh toán
    isShowCancelByPayCode: boolean
    //hiển thị dialog hủy đăng ký theo mã ví
    isShowCancelBySubCode: boolean
    //dialog otp cho phần chia sẻ tự động
    isShowDiaglogOTPRegis: boolean

    columnsShareWallet: Array<ColumnInfo>;
    dataSetShareWallet: {
        content: Array<any>,
        total: number
    };
    optionTableShareWallet: OptionTable;
    pageNumberShareWallet: number;
    pageSizeShareWallet: number;
    sortShareWallet: string;
    // Lưu lại purchasedTraffic để chia phần trăm khi xem chi tiết
    purchasedTraffic: number;
    trafficType: string;
    isShowTableInDialogDetail: boolean;
    startCountdown() {
        if (this.interval) {
            clearInterval(this.interval); // Dừng interval cũ nếu có
        }

        this.interval = setInterval(() => {
            if (this.countdown > 0) {
                this.countdown--;
            } else {
                clearInterval(this.interval);
                this.interval = null; // Xóa tham chiếu sau khi hoàn tất
            }
        }, 1000);
    }

    resetTimer() {
        this.countdown = 60;
        clearInterval(this.interval);
        this.startCountdown();
        this.sendOTP()
    }
    //reset time cho otp chia sẻ tự động
    resetTimerForRegister() {
        this.countdown = 60;
        clearInterval(this.interval);
        this.startCountdown();
        this.sendOTPRegister()
    }

    extractDateString(dateTimeString: string): string {
        // Tách ngày, tháng, năm từ chuỗi ban đầu
        const [day, month, year] = dateTimeString.split(/[/\s:]+/);
        // Trả về chuỗi ngày ở định dạng dd/MM/yyyy
        return `${day}/${month}/${year}`;
    }

    openSubmit(){
        this.isSubmit = true
        this.countdown = 60
        this.startCountdown()
    }

    searchList: Array<SeperateSearchInfo>;
    filterList: Array<SeperateFilterInfo>;

    constructor(
        @Inject(TrafficWalletService) private trafficWalletService: TrafficWalletService,
        @Inject(ShareManagementService) private shareManagementService : ShareManagementService,
        private formBuilder: FormBuilder,
        private injector: Injector,
        private primengConfig: PrimeNGConfig,
    ) {
        super(injector);
    }

    catchSearchDetail(event){
        this.searchInfo = this.originSearch
        this.searchInfo = { ...this.searchInfo, ...event}
        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
    }

    async ngOnInit() {
        let me = this;

        this.items = [{ label: this.tranService.translate(`global.menu.trafficManagement`) }, { label: this.tranService.translate("global.menu.walletList") },];

        this.home = { icon: 'pi pi-home', routerLink: '/' };
        this.getPayCode("","PAY_CODE").then((resp)=> {
            me.getPackageCbb("", "WALLET");
        });
        this.isShowModalWalletAuthen = false;
        this.searchInfo = {
            lstPackageName: null,
            searchWalletCode: 0,
            searchPayCode: 0,
            value: "",
            searchName: 0,
            searchPhone: 0,
            lstTrafficType: null,
            autoType: -1,
        };

        this.walletDetail ={
            payCode: 1,
            packageName: 2,
            phone: 3,
            tax: 4
        };

        this.accuracyWallet = {
            accuracyType:0,
            payCode:null,
            subCode:null,
            tax:null,
            phoneNumber:null,
            otp:null,
            transId: 1,
        };
        this.withoutOTP = {
            payCode:  null,
            subCode: null,
            tax: null,
            phoneNumber: null,
            otp: null,
            sharingType: null,
            regisType: null,
            new: null,
        }
        this.columns = [
            {
                name: this.tranService.translate("datapool.label.walletCode"),
                key: "subCode",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
                style:{
                    cursor: "pointer",
             color: "var(--mainColorText)"
                },
                funcClick(id, item) {
                    me.subCodeId = item.subCode;
                    me.resetDialogDetail()
                    me.getWalletDetail();
                    me.purchasedTraffic = item.purchasedTraffic;
                    me.trafficType = item.trafficType;
                },
            },
            {
                name: this.tranService.translate("datapool.label.packageName"),
                key: "packageName",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false
            }, {
                name: this.tranService.translate("datapool.label.phone"),
                key: "phoneActive",
                size: "fit-content",
                align: "left",
                isShow: true,
                isSort: false
            }, {
                name: this.tranService.translate("datapool.label.payCode"),
                key: "payCode",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false
            }, {
                name: this.tranService.translate("datapool.label.trafficType"),
                key: "trafficType",
                size: "fit-content",
                align: "left",
                isShow: true,
                isSort: false
            }, {
                name: this.tranService.translate('datapool.label.remainData')+"/ "+ this.tranService.translate("datapool.label.purchasedData"),
                key: "remainData",
                size: "fit-content",
                align: "left",
                isShow: true,
                isSort: false,
                funcConvertText(value, item) {
                    let remain = me.utilService.convertNumberToString(item.totalRemainingTraffic);
                    let total =  me.utilService.convertNumberToString(item.purchasedTraffic);
                    if(item.trafficType == "Gói Data"){
                        return remain+"/ "+total +" MB";
                    }else if(item.trafficType == "Gói thoại"){
                        return remain+"/ "+total + me.minutesText;
                    } else if((item.trafficType || "").toUpperCase().includes("Gói SMS".toUpperCase())){
                        return remain+"/ "+total + " SMS";
                    }
                    return item.totalRemainingTraffic+"/ "+item.purchasedTraffic;
                },
            },
            {
                name: this.tranService.translate("datapool.label.usedTime"),
                key: "timeUsed",
                size: "fit-content",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("datapool.label.methodAutoShare"),
                key: "autoType",
                size: "fit-content",
                align: "left",
                isShow: true,
                isSort: false,
                funcConvertText(value) {
                    return me.getValueMethodAutoShare(value)
                }
            },
            {
                name: this.tranService.translate("datapool.label.accuracyDate"),
                key: "accuracyDate",
                size: "fit-content",
                align: "left",
                isShow: true,
                isSort: false,
                funcConvertText(value){
                    if(value == null) return "";
                    return me.extractDateString(value);
                }
            },
        ];
        this.payCode = null,
        this.optionTable = {
            hasClearSelected: false,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
            action: [
                {
                    icon: "pi pi-window-maximize",
                    tooltip: this.tranService.translate("datapool.button.share"),
                    func: function (id, item) {
                        me.router.navigate(['data-pool/walletMgmt/share',item.subCode])
                    },
                    funcAppear(id, item) {
                        return (item.canShare && me.checkAuthen([me.allPermissions.DATAPOOL.SHARE_WALLET]))
                    },
                },
                {
                    icon: PrimeIcons.LOCK,
                    tooltip: this.tranService.translate("datapool.placeholder.registerShare"),
                    func: function (id, item) {
                        me.showConfirmRegisterSubOrPay(item)
                    },
                    funcAppear(id, item) {
                        return (item.canShare && item.autoType == CONSTANTS.METHOD_AUTO_SHARE.NONE && me.checkAuthen([me.allPermissions.DATAPOOL.SHARE_WALLET]))
                    },
                },
                {
                    icon: PrimeIcons.LOCK_OPEN,
                    tooltip: this.tranService.translate("datapool.placeholder.cancelShare"),
                    func: function (id, item) {
                        if (item.autoType == CONSTANTS.METHOD_AUTO_SHARE.PAY_CODE) {
                            me.payCode = item.payCode
                            me.showConfirmCancelShareByPayCode(item)
                        } else if (item.autoType == CONSTANTS.METHOD_AUTO_SHARE.SUB_CODE) {
                            me.payCode = item.payCode
                            me.showConfirmCancelShareBySubCode(item)
                        }
                    },
                    funcAppear(id, item) {
                        return (item.canShare && (item.autoType == CONSTANTS.METHOD_AUTO_SHARE.PAY_CODE || item.autoType == CONSTANTS.METHOD_AUTO_SHARE.SUB_CODE) && me.checkAuthen([me.allPermissions.DATAPOOL.SHARE_WALLET]))
                    },
                },
                ],
        }
        this.pageNumber = 0;
        this.pageSize = 10;
        // this.sort = "accuracyDate,desc"
        this.sort = ""
        this.dataSet = {
            content: [],
            total: 0
        }
        this.columnsShareWallet = [{
            name: this.tranService.translate("datapool.label.phoneFull"),
            key: "phoneReceipt",
            size: "fit-content",
            align: "left",
            isShow: true,
            isSort: false
        },{
            name: this.tranService.translate("datapool.label.fullName"),
            key: "name",
            size: "fit-content",
            align: "left",
            isShow: true,
            isSort: false
        },{
            name: this.tranService.translate("datapool.label.email"),
            key: "email",
            size: "fit-content",
            align: "left",
            isShow: true,
            isSort: false
        },{
            name: this.tranService.translate("datapool.label.sharedTime"),
            key: "timeUpdate",
            size: "fit-content",
            align: "left",
            isShow: true,
            isSort: false,
            funcConvertText(value) {
                return me.getFormattedDate(value)
            }
        },{
            name: this.tranService.translate("datapool.label.usedDate"),
            key: "sharingDay",
            size: "fit-content",
            align: "left",
            isShow: true,
            isSort: false,
            funcConvertText(value, item) {
                return me.getFormattedDate(value, item.dayExprired)
            }
        },{
            name: this.tranService.translate("datapool.label.shared"),
            key: "trafficShare",
            size: "fit-content",
            align: "left",
            isShow: true,
            isSort: false,
            funcConvertText(value) {
                value = me.formatNumber(value)
                if ((me.trafficType || '').toUpperCase() == 'Gói Data'.toUpperCase()) return value + ' MB'
                else if ((me.trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase())) return value + ' SMS'
                else if ((me.trafficType || '').toUpperCase() == 'Gói thoại'.toUpperCase()) return value + me.tranService.translate('alert.label.minutes')
                else return value;
            }
        },{
            name: this.tranService.translate("datapool.label.percentage"),
            key: "trafficShare",
            size: "fit-content",
            align: "left",
            isShow: true,
            isSort: false,
            funcConvertText(value) {
                let result = 100.0 * value/me.purchasedTraffic;
                return Math.round(result * 100.0)/100.0 + " %"
            }
        },
        ]
        this.dataSetShareWallet = {
            content: [],
            total: 0
        }
        this.isShowTableInDialogDetail = false;
        this.pageNumberShareWallet = 0,
        this.pageSize = 10;
        this.sortShareWallet = '';
        this.optionTableShareWallet = {
            hasClearSelected: false,
            hasShowChoose: false,
            hasShowIndex: false,
            hasShowToggleColumn: false,
            action: null
        }

        this.formSearchWallet = this.formBuilder.group(this.searchInfo);
        this.formAccuracyWallet = this.formBuilder.group(this.accuracyWallet);
        this.formWithoutOTP = this.formBuilder.group(this.withoutOTP)
        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);

        this.primengConfig.setTranslation({
            emptyFilterMessage:"Không tìm thấy kết quả",
            emptyMessage:"Không tìm thấy kết quả"
        })
        this.originSearch = this.searchInfo
        this.isShowRegisBySubOrPay = false
        this.isShowRegisByPayCode = false
        this.isShowCancelByPayCode = false
        this.isShowCancelBySubCode = false
        this.typeAutoShare = 1;
        this.isShowDiaglogOTPRegis = false;
    }

    getPackageCbb(packageName, type){
        let me = this;
        let params = {
            packageName,
            type
        }
        this.messageCommonService.onload()
        this.trafficWalletService.getPackageCbb(params, (response) => {
            this.messageCommonService.offload()
            me.packageCbb = response.map(r => {
                return {
                    name: r.name,
                    value: r.value
                }
            });

            me.packageCbb = me.packageCbb.filter((item, index, self) =>
                index === self.findIndex((t) => (
                    t.name === item.name && t.value === item.value
                ))
            );

            this.searchList = [{
                name:this.tranService.translate("datapool.label.walletCode"),
                key:"searchWalletCode"
            },{
                name:this.tranService.translate("datapool.label.payCode"),
                key:"searchPayCode"
            },{
                name:this.tranService.translate("datapool.label.phone"),
                key:"searchPhone"
            }];

            this.filterList = [{
                name:this.tranService.translate("datapool.label.trafficType"),
                type: FilterInputType.multiselect,
                items:[{name:"Gói Data", value:"Gói Data"},{name:"Gói SMS ngoại mạng", value:"Gói SMS ngoại mạng"},{name:"Gói SMS VNP", value:"Gói SMS VNP"}],
                key:"lstTrafficType",
                itemFilter: true
            },{
                name:this.tranService.translate("datapool.label.packageName"),
                type: FilterInputType.multiselect,
                items: this.packageCbb,
                key:"lstPackageName",
                itemFilter:true
            },{
                name:this.tranService.translate("datapool.label.accuracyDate"),
                type: FilterInputType.rangeCalendar,
                key:["accuracyStartDate","accuracyEndDate"],
                unixTimeString: true
            },{
                name:this.tranService.translate("datapool.label.usedTime"),
                type: FilterInputType.rangeCalendar,
                key:["startDate","endDate"],
                unixTimeString: true
            },
                {
                    name:this.tranService.translate("datapool.label.methodAutoShare"),
                    type: FilterInputType.dropdown,
                    items:[{name: this.tranService.translate("datapool.methodAutoShare.subCode"), value: CONSTANTS.METHOD_AUTO_SHARE.SUB_CODE},
                        {name: this.tranService.translate("datapool.methodAutoShare.payCode"), value: CONSTANTS.METHOD_AUTO_SHARE.PAY_CODE},
                        {name: this.tranService.translate("datapool.methodAutoShare.none"), value: CONSTANTS.METHOD_AUTO_SHARE.NONE},
                        ],
                    key:"autoType",
                    itemFilter: true
                },
                {
                    name:this.tranService.translate("datapool.label.payCode"),
                    type: FilterInputType.multiselect,
                    items: this.payCodeFilter,
                    key:"lstPaymentCode",
                    itemFilter: true
                }
            ]
        },null, ()=>{
            this.messageCommonService.offload()
        })
    }

    getPayCode(packageName, type) : Promise<any> {
        let me = this;
        let params = {
            packageName,
            type
        }
        this.messageCommonService.onload()
        return new Promise((resolve,reject) => {
            this.trafficWalletService.getPackageCbb(params, (response) => {
                this.messageCommonService.offload()
                me.payCodeFilter = response.map(r => {
                    return {
                        name: r.name,
                        value: r.value
                    }
                });

                me.payCodeFilter = me.payCodeFilter.filter((item, index, self) =>
                    index === self.findIndex((t) => (
                        t.name === item.name && t.value === item.value
                    ))
                );
                resolve("");
            },null, ()=>{
                this.messageCommonService.offload()
            })
        });

    }

    search(page, limit, sort, params) {
        let me = this;
        this.pageNumber = page;
        this.pageSize = limit;
        // this.sort = sort;
        let dataParams = {
            page,
            size: limit,
            // sort
        }
        this.messageCommonService.onload()

        Object.keys(this.searchInfo).forEach(key => {
            dataParams[key] = this.searchInfo[key];
            if (key =='autoType' && this.searchInfo[key] == null) {
                dataParams[key] = -1
            }
        })

        this.trafficWalletService.searchWallet(dataParams, (response) => {
            me.dataSet = {
                content: response.content,
                total: response.totalElements
            }
        }, null, () => {
            me.messageCommonService.offload();
        })
    }
    searchShareWallet(page,limit, sort, params) {
        let me = this;
            this.pageNumberShareWallet = page,
            this.pageSizeShareWallet = limit,
            this.sortShareWallet = sort
        let dataParams = {
                page,
                size: limit,
                sort: sort,
                subCode: me.subCodeId.toString(),
        }
        me.messageCommonService.onload()
        this.trafficWalletService.getListShareWallet(dataParams, (response) => {
            me.dataSetShareWallet = {
                content: response.content,
                total: response.totalElements
            }
            me.isShowTableInDialogDetail = true;
        }, null, () => {
            me.messageCommonService.offload();
        })
    }

    onSubmitSearch() {
        this.pageNumber = 0;
        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
    }

    // send OTP
    sendOTP(){
        let body = {};
        let me = this;
            body = {
                phoneNumber: parseInt(String(this.accuracyWallet.phoneNumber)?.replace(/^0/,"84"))
            }
        this.messageCommonService.onload()
        this.shareManagementService.sendOTP(body, (res) => {
            // me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"));
            me.openSubmit();
            // me.isShowModalWalletAuthen = false;
            // me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo)
        }, null, () => {
            me.messageCommonService.offload()
        })
    }

    // Xac thuc va tao vi luu luong
    accuracyRequest() {

        Object.keys(this.formAccuracyWallet.controls).forEach(key => {
            const control = this.formAccuracyWallet.get(key);
            if (control.invalid) {
              console.log('Field:', key, 'is invalid. Errors:', control.errors);
            }
          });
        let me = this;
        let body = {};
        if (this.accuracyWallet.accuracyType == 0) {
            body = {
                accuracyType: this.accuracyWallet.accuracyType ,
                subCode: this.formAccuracyWallet.get("subCode").value,
                tax: this.accuracyWallet.tax,
                phoneNumber: this.accuracyWallet.phoneNumber,
                transId: this.accuracyWallet.transId,
                otp: this.formAccuracyWallet.get("otp").value
            }
        } else {
            body = {
                accuracyType: this.accuracyWallet.accuracyType,
                payCode: this.formAccuracyWallet.get("payCode").value,
                tax: this.accuracyWallet.tax,
                phoneNumber: this.accuracyWallet.phoneNumber,
                transId: this.accuracyWallet.transId,
                otp: this.formAccuracyWallet.get("otp").value
            }
        }

        me.withoutOTP = {
            payCode: this.formAccuracyWallet.get("payCode").value,
            subCode: this.formAccuracyWallet.get("subCode").value,
            tax: this.accuracyWallet.tax,
            phoneNumber: this.accuracyWallet.phoneNumber,
            otp: null,
            sharingType: '1',
            regisType: '1',
            new: true,
        }
        this.messageCommonService.onload();
        this.trafficWalletService.accuracyWallet(body, (res) => {
            if (res.error=="BSS-00000000"){
                me.messageCommonService.success(me.tranService.translate("global.message.accuracySuccess"));
                // tạo bằng mã ví
                if (this.accuracyWallet.accuracyType == 0) {
                    this.messageCommonService.onload();
                    me.trafficWalletService.getById({subCode: me.withoutOTP.subCode}, (detail) => {
                        if(detail.autoType !== 0) {
                            me.showConfirmRegisterSubOrPay(detail)
                        }
                    },(error)=>{
                        if(error.error.error.errorCode === "error.forbidden.view.detail"){
                            this.messageCommonService.error("Bạn không có quyền truy cập thông tin này")
                            this.router.navigate(["/data-pool/walletMgmt/list"])
                        }
                    },()=>{ this.messageCommonService.offload() })
                } else {
                    // tạo bằng mã thanh toán call api search sau đó tìm trong list có autoType nào = 0 hay chưa
                    // Loại auto (0: Mã thanh toán, 1: mã ví, 2: chưa đăng ký)
                    this.trafficWalletService.searchWallet({
                        "page": 0,
                        "size": 99999999,
                        "lstPackageName": null,
                        "searchWalletCode": 1,
                        "searchPayCode": 1,
                        "value": this.formAccuracyWallet.get("payCode").value,
                        "searchName": 0,
                        "searchPhone": 1,
                        "lstTrafficType": null,
                        "autoType": -1
                    }, (response) => {
                        if(me.dataSet?.content.find(el => el.autoType !== 0)){
                            me.isShowRegisByPayCode = true;
                        }
                    }, null, () => {
                        me.messageCommonService.offload();
                    })
                }
            } else if(res.error ==="err.subCode.exists") {
                me.messageCommonService.error("Ví lưu lượng đã được xác thực")
                return
            } else {
                me.messageCommonService.error(res.messageDetail);
                return
            }

            me.isShowModalWalletAuthen = false;
            // me.formAccuracyWallet.reset();
            this.isSubmit = false
            me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo)
        }, null, () => {
            me.messageCommonService.offload()
        })
    }

    showModalAccuracy() {
        this.isShowModalWalletAuthen = true;
        this.accuracyWallet = {
            accuracyType:0,
            payCode:null,
            subCode:null,
            tax:null,
            phoneNumber:null,
            otp:null,
            transId: 1,
        }
        this.formAccuracyWallet = this.formBuilder.group(this.accuracyWallet);
        this.formAccuracyWallet.get("subCode").setValidators([
            Validators.required,
            Validators.maxLength(64),
            Validators.pattern('^[a-zA-Z0-9\\- _\\u00C0-\\u024F\\u1E00-\\u1EFF]+$')
        ])
        this.formAccuracyWallet.get("payCode").addValidators([
            Validators.required,
            Validators.maxLength(64),
            Validators.pattern('^[a-zA-Z0-9\\- _\\u00C0-\\u024F\\u1E00-\\u1EFF]+$')
        ])
        this.formAccuracyWallet.get("tax").setValidators([
            Validators.required,
            Validators.maxLength(64),
            Validators.pattern('^[a-zA-Z0-9_-]+$')
        ])
        this.formAccuracyWallet.get("phoneNumber").setValidators([
            Validators.required,
            Validators.pattern(/^0[0-9]{9,10}$/)
        ])

        this.formAccuracyWallet.get("otp").setValidators([
            Validators.required,
            digitValidator(6),
        ])

        this.formAccuracyWallet.get("subCode").enable({emitEvent:false})
        this.formAccuracyWallet.get('payCode').disable({ emitEvent: false });
        this.formAccuracyWallet.controls.tax.enable();
        this.formAccuracyWallet.controls.phoneNumber.enable();
    }

    checkDisable(){
        if(this.formAccuracyWallet.get("accuracyType").value == 0){
            this.formAccuracyWallet.get("subCode").enable({emitEvent:false})
            this.formAccuracyWallet.get('payCode').disable({ emitEvent: false });
            return !(this.formAccuracyWallet.controls.subCode.valid &&
                   this.formAccuracyWallet.controls.tax.valid &&
                   this.formAccuracyWallet.controls.phoneNumber.valid)
        }else{
            this.formAccuracyWallet.get("payCode").enable({emitEvent:false})
            this.formAccuracyWallet.get('subCode').disable({ emitEvent: false });
            return !(this.formAccuracyWallet.controls.payCode.valid &&
                   this.formAccuracyWallet.controls.tax.valid &&
                   this.formAccuracyWallet.controls.phoneNumber.valid)
        }
        return this.formAccuracyWallet.invalid;
    }

    getWalletDetail() {
        let me = this;
        me.messageCommonService.onload();

        me.trafficWalletService.getById({subCode:me.subCodeId}, (res) => {
            me.walletDetail = res
            let startDate = me.getUnixTime(res.startDate);
            let endDate = me.getUnixTime(res.endDate);
            me.walletDetail.timeToUse = (endDate-startDate)/(60 * 60 * 24) +" ngày " +"["+me.getFormattedDateCrd(res.startDate)+"-"+me.getFormattedDateCrd(res.endDate)+"]"
            me.listDetail = res.listShared
            me.canView = true
            me.isShowModalDetail = true;
            me.searchShareWallet(0, 10, me.sortShareWallet, {subCode: me.subCodeId})
        },(error)=>{
            if(error.error.error.errorCode === "error.forbidden.view.detail"){
                this.messageCommonService.error("Bạn không có quyền truy cập thông tin này")
                this.router.navigate(["/data-pool/walletMgmt/list"])
            }
        },()=>{ this.messageCommonService.offload() })
    };

    getFormattedDate(dateString: string, addDate?: number): string {
        let me = this;
        let date = new Date(dateString);

        if (addDate) {
            date.setDate(date.getDate() + addDate);
        }

        const day = date.getDate().toString().padStart(2, '0'); // Lấy ngày và thêm số 0 nếu cần
        const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Lấy tháng (lưu ý tháng trong JavaScript bắt đầu từ 0)
        const year = date.getFullYear();
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        const seconds = date.getSeconds().toString().padStart(2, '0');

        return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
    };

    parseDateTime(dateString) {
        // Split the date and time parts
        let [datePart, timePart] = dateString.split(' ');

        // Split the date part by '/'
        let dateParts = datePart.split('/');
        let day = parseInt(dateParts[0], 10);
        let month = parseInt(dateParts[1], 10) - 1; // Months are 0-based in JavaScript
        let year = parseInt(dateParts[2], 10);

        // Split the time part by ':'
        let timeParts = timePart.split(':');
        let hours = parseInt(timeParts[0], 10);
        let minutes = parseInt(timeParts[1], 10);
        let seconds = parseInt(timeParts[2], 10);

        // Create a new Date object
        return new Date(year, month, day, hours, minutes, seconds);
    };

    getUnixTime(dateString: string): number {
        const date = new Date(dateString);
        return date.getTime() / 1000; // Chia cho 1000 để chuyển từ milliseconds sang seconds
    };

    getFormattedDateCrd(dateString: string, addDate?: number): string {
        let date = new Date(dateString);

        if (addDate) {
            date.setUTCDate(date.getUTCDate() + addDate);
        }

        const day = date.getUTCDate().toString().padStart(2, '0'); // Lấy ngày và thêm số 0 nếu cần
        const month = (date.getUTCMonth() + 1).toString().padStart(2, '0'); // Lấy tháng (lưu ý tháng trong JavaScript bắt đầu từ 0)
        const year = date.getUTCFullYear();
        const hours = date.getUTCHours().toString().padStart(2, '0');
        const minutes = date.getUTCMinutes().toString().padStart(2, '0');
        const seconds = date.getUTCSeconds().toString().padStart(2, '0');

        return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
    }

    onHideOtp(){
        this.formAccuracyWallet.controls.otp.reset()
    }

    ngOnDestroy(): void {
        clearInterval(this.interval)
    }

    showConfirmRegisterSubOrPay(item) {
        let me = this;
        me.isShowRegisBySubOrPay = true
        me.withoutOTP = {
            payCode: item.payCode,
            subCode: item.subCode,
            tax: item.tax,
            phoneNumber: item.phoneActive,
            otp: null,
            sharingType: '1',
            regisType: '1',
            new: false
        }
    }

    showConfirmCancelShareByPayCode(item) {
        let me = this;
        this.isShowCancelByPayCode = true
        me.withoutOTP = {
            payCode: item.payCode,
            subCode: item.autoType == CONSTANTS.METHOD_AUTO_SHARE.SUB_CODE ? item.subCode : null,
            tax: item.tax,
            phoneNumber: item.phoneActive,
            otp: null,
            sharingType: '1',
            regisType: '1',
            new: false,
        }
    }

    showConfirmCancelShareBySubCode(item) {
        let me = this;
        this.isShowCancelBySubCode = true
        me.withoutOTP = {
            payCode: item.payCode,
            subCode: item.autoType == CONSTANTS.METHOD_AUTO_SHARE.SUB_CODE ? item.subCode : null,
            tax: item.tax,
            phoneNumber: item.phoneActive,
            otp: null,
            sharingType: '1',
            regisType: '1',
            new: false,
        }
    }

    onHideRegisBySubOrPay() {
        let me = this;
        me.isShowRegisBySubOrPay = false
        me.typeAutoShare = 1
    }
    onHideRegisByPayCode() {
        let me = this;
        me.isShowRegisByPayCode = false
        me.typeAutoShare = 1
    }

    register() {
        let me = this;
        // thanh toán = mã ví mà chưa chọn cách đăng kí nào -> Hiển thị thông báo “Hãy chọn một cách đăng ký”
        // thanh toán = mã thanh toán -> ko cần chọn

        if (me.withoutOTP.new == true) { //nếu là ví mới thì check theo accuracyType
            if (me.accuracyWallet.accuracyType === 0 && me.typeAutoShare != null) {
                me.isShowRegisBySubOrPay = false;
                me.isShowRegisByPayCode = false
                me.isShowDiaglogOTPRegis = true;
                me.formAccuracyWallet.reset()
                me.sendOTPRegister()
            } else if (me.accuracyWallet.accuracyType === 1) {
                me.isShowRegisBySubOrPay = false;
                me.isShowRegisByPayCode = false
                me.isShowDiaglogOTPRegis = true;
                me.formAccuracyWallet.reset()
                me.sendOTPRegister()
            } else {
                me.messageCommonService.error(me.tranService.translate("datapool.text.chooseRegistrationMethod"));
            }
        } else if (me.withoutOTP.new == false) { // nếu là ví cũ thích check typeAutoShare đã chọn
            if(me.typeAutoShare != null) {
                me.isShowRegisBySubOrPay = false;
                me.isShowRegisByPayCode = false
                me.isShowDiaglogOTPRegis = true;
                me.sendOTPRegister()
            }else {
                me.messageCommonService.error(me.tranService.translate("datapool.text.chooseRegistrationMethod"));
            }
        }
    }

    cancel() {
    }

    hideDiaglogConfirmOTP() {
        let me = this;
        me.isShowDiaglogOTPRegis = false
    }

    // send OTP Register
    sendOTPRegister() {
        let body = {};
        let me = this;
        body = {
            phoneNumber: parseInt(String(this.withoutOTP.phoneNumber)?.replace(/^0/, "84"))
        }
        // me.openOTPRegister();
        this.messageCommonService.onload()
        this.shareManagementService.sendOTP(body, (res) => {
            me.openOTPRegister();
        }, null, () => {
            me.messageCommonService.offload()
        })
    }

    openOTPRegister() {
        this.countdown = 60
        this.startCountdown()
    }

    onRegister() {
        let me = this
        let body = {
            payCode: me.withoutOTP.payCode,
            subCode:  me.typeAutoShare && me.typeAutoShare == 1 ? me.withoutOTP.subCode : null,
            tax: me.withoutOTP.tax,
            phoneNumber: me.withoutOTP.phoneNumber,
            otp: this.formWithoutOTP.get("otp").value,
            sharingType: '1',
            regisType: '1',
        }
        this.messageCommonService.onload()
        this.trafficWalletService.registerWithoutOTP(body, (res) => {
            if (res && res.error) {
                me.messageCommonService.error(res.message)
            } else {
                me.messageCommonService.success(me.tranService.translate("datapool.message.registerSuccess"))
            }
            me.isShowDiaglogOTPRegis = false;
            me.formWithoutOTP.reset()
            me.onSubmitSearch()
        }, null, () => {
            me.messageCommonService.offload()
        })
    }

    onCancel() {
        let me = this
        let body = {
            payCode: me.withoutOTP.payCode,
            subCode: me.withoutOTP.subCode,
            tax: me.withoutOTP.tax,
            phoneNumber: me.withoutOTP.phoneNumber,
            otp: me.withoutOTP.otp,
            sharingType: '1',
            regisType: '2',
        }
        this.messageCommonService.onload()
        this.trafficWalletService.unRegisterWithoutOTP(body, (res) => {
            me.messageCommonService.success(me.tranService.translate("datapool.message.cancelSuccess"))
            me.isShowCancelBySubCode = false;
            me.isShowCancelByPayCode = false;
            me.onSubmitSearch()
        }, null, () => {
            me.messageCommonService.offload()
        })
    }
    getValueMethodAutoShare(value) {
        let me = this;
        if (value == CONSTANTS.METHOD_AUTO_SHARE.PAY_CODE) {
            return me.tranService.translate("datapool.methodAutoShare.payCode")
        } else if (value == CONSTANTS.METHOD_AUTO_SHARE.SUB_CODE) {
            return me.tranService.translate("datapool.methodAutoShare.subCode")
        } else {
            return me.tranService.translate("datapool.methodAutoShare.none")
        }
    }
    // openReadMore() {
    //     window.open(`/#/docs/autoShare`, '_blank');
    // }
    openReadMoreRegister() {
        window.open(`/#/docs/registerNonOTP`, '_blank');
    }
    openReadMoreUnRegister() {
        window.open(`/#/docs/unsubNonOTP`, '_blank');
    }

    resetDialogDetail() {
        let me = this;
        me.walletDetail ={
        };
        me.dataSetShareWallet = {
            content: [],
            total: 0,
        }
        me.isShowTableInDialogDetail = false
    }

    formatNumber(value: number): string {
        return new Intl.NumberFormat('vi-VN').format(value);
    }
}
