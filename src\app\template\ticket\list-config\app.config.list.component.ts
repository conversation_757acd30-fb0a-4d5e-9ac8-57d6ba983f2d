import {Component, Inject, Injector, OnInit} from "@angular/core";
import {MenuItem} from "primeng/api";
import {TicketService} from "src/app/service/ticket/TicketService";
import {ColumnInfo, OptionTable} from "../../common-module/table/table.component";
import {CONSTANTS} from "src/app/service/comon/constants";
import {ComponentBase} from "src/app/component.base";
import {AccountService} from "../../../service/account/AccountService";
import {FormBuilder} from "@angular/forms";
import {ComboLazyControl} from "../../common-module/combobox-lazyload/combobox.lazyload";
import {a, an} from "@fullcalendar/core/internal-common";

@Component({
  selector: "ticket-config-list",
  templateUrl: './app.config.list.component.html'
})
export class ListTicketConfigComponent extends ComponentBase implements OnInit {
  items: MenuItem[];
  home: MenuItem
  searchInfo: {
    provinceCode: string | null,
    email: string | null,
  } = {provinceCode: null, email: null};
  columns: Array<ColumnInfo>;
  dataSet: {
    content: Array<any>,
    total: number
  };
  selectItems: Array<any>;
  optionTable: OptionTable;
  pageNumber: number;
  pageSize: number;
  sort: string;
  formSearchTicketConfig: any;
  listProvince: Array<any>;
  ticketConfig: {
    provinceName: string | null,
    provinceCode: string | null,
    emailInfos: Array<any>
  };
  paramSearchCustomerProvince: { provinceCode: string } = {provinceCode: ""};
  controlComboSelect: ComboLazyControl = new ComboLazyControl();
  formTicketConfig: any
  isShowUpdateRequestConfig : boolean
  userInfo : any
  userType : any

  constructor(
      @Inject(TicketService) private ticketService: TicketService,
      @Inject(AccountService) private accountService: AccountService,
      private formBuilder: FormBuilder,
      private injector: Injector) {
    super(injector);
  }

  ngOnInit() {
    let me = this;
    this.isShowUpdateRequestConfig = false
    this.userInfo = this.sessionService.userInfo;
    this.userType = CONSTANTS.USER_TYPE;
    this.ticketConfig = {
      provinceName: null,
      provinceCode: null,
      emailInfos: []
    };

    this.columns = [{
      name: this.tranService.translate("ticket.label.config.provinceCode"),
      key: "provinceCode",
      size: "150px",
      align: "left",
      isShow: true,
      isSort: true
    }, {
      name: this.tranService.translate("ticket.label.config.provinceName"),
      key: "provinceName",
      size: "150px",
      align: "left",
      isShow: true,
      isSort: true
    },
      {
        name: this.tranService.translate("ticket.label.config.email"),
        key: "emails",
        size: "fit-content",
        align: "left",
        isShow: true,
        isSort: true,
        funcConvertText: function (value) {
          return value ? value.join(', ') : '';
        }
      }
    ];

    this.optionTable = {
      hasClearSelected: false,
      hasShowChoose: false,
      hasShowIndex: true,
      hasShowToggleColumn: false,
      action: [
        {
          icon: "pi pi-window-maximize",
          tooltip: this.tranService.translate("global.button.edit"),
          func: function (id, item) {
            me.isShowUpdateRequestConfig = true
            me.paramSearchCustomerProvince = {provinceCode: item.provinceCode}
            me.messageCommonService.onload();
            me.ticketService.getDetailTicketConfig(item.provinceCode, (resp) => {
              me.ticketConfig.provinceName = resp.provinceName
              me.ticketConfig.provinceCode = resp.provinceCode
              me.ticketConfig.emailInfos = resp.emailInfos.map(e => ({
                id: e.userId,
                email: e.email
              }));
            }, null, ()=>{
              me.messageCommonService.offload();
            })
          }
        }]
    }
    this.pageNumber = 0;
    this.pageSize = 10;
    this.sort = "provinceCode,asc"
    this.dataSet = {
      content: [],
      total: 0
    }
    this.formSearchTicketConfig = this.formBuilder.group(this.searchInfo);
    this.formTicketConfig = this.formBuilder.group(this.ticketConfig);
    this.getListProvince();
    this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
  }

  search(page, limit, sort, params) {
    let me = this;
    this.pageNumber = page;
    this.pageSize = limit;
    this.sort = sort;
    let dataParams = {
      page,
      size: limit,
      sort
    }
    Object.keys(this.searchInfo).forEach(key => {
      if(this.searchInfo[key] != null){
        dataParams[key] = this.searchInfo[key];
      }
    })
    this.dataSet = {
      content: [],
      total: 0
    }
    me.messageCommonService.onload();
    this.ticketService.searchTicketConfig(dataParams, (response) => {
      me.dataSet = {
        content: response.content,
        total: response.totalElements
      }
    }, null, () => {
      me.messageCommonService.offload();
    })
  }

  onSubmitSearch() {
    this.pageNumber = 0;
    this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
  }

  getListProvince() {
    this.accountService.getListProvince((response) => {
      this.listProvince = response.map(el => {
        return {
          ...el,
          display: `${el.code} - ${el.name}`
        }
      })
    })
  }

  onSubmitUpdate() {
    if(this.messageCommonService.isloading == true || this.isShowUpdateRequestConfig == false) return;
    let me = this;
    this.ticketConfig.emailInfos = this.ticketConfig.emailInfos.map(e => ({
      userId: e.id,
      email: e.email
    }));
    // console.log(this.ticketConfig.emailInfos;
    me.messageCommonService.onload();
    this.ticketService.updateTicketConfig(this.ticketConfig, () => {
      me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"));
      me.isShowUpdateRequestConfig = false;
      me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);
    }, null, ()=>{
      me.messageCommonService.offload();
    })
  }

  closeForm() {
    this.router.navigate(['/ticket/list-config'])
  }

  handleSearch(params, callback){
    this.ticketService.getListAssignee(params, (resp)=> {
      callback(resp)
    })
  }


}
