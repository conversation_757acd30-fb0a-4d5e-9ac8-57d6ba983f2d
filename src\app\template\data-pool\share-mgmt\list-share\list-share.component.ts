import {Component, Inject, Injector} from '@angular/core';
import {FormBuilder, FormControl, FormGroup, Validators} from '@angular/forms';
import {MenuItem, PrimeNGConfig} from 'primeng/api';
import {ComponentBase} from 'src/app/component.base';
import {
    FilterInputType,
    SeperateFilterInfo,
    SeperateSearchInfo
} from 'src/app/template/common-module/search-filter-separate/search-filter-separate.component';
import {ColumnInfo, OptionTable} from 'src/app/template/common-module/table/table.component';
import {ShareManagementService} from "../../../../service/datapool/ShareManagementService";
import {TrafficWalletService} from "../../../../service/datapool/TrafficWalletService";
import {CONSTANTS, isVinaphoneNumber} from "../../../../service/comon/constants";
import {OptionInputFile} from "../../../common-module/input-file/input.file.component";
import * as Excel from 'exceljs';
import * as FileSaver from 'file-saver';
import {saveAs} from 'file-saver';
import * as moment from 'moment';
import {checkExistedDynamicListArray, numericMaxLengthValidator} from 'src/app/template/common-module/validatorCustoms';

@Component({
    selector: 'app-list-share',
    templateUrl: './list-share.component.html',
    styleUrls: ['./list-share.component.scss']
})
export class ListShareComponent extends ComponentBase{
    items: MenuItem[];
    home: MenuItem;
    columns: Array<ColumnInfo>;
    columnsInDetailShare: Array<ColumnInfo>;
    optionTable: OptionTable;
    pageNumber: number;
    pageSize: number;
    sort: string;
    pageNumberDetail: number;
    pageSizeDetail: number;
    sortDetail: string;
    selectItems: Array<{imsi:number,msisdn: any,groupName:string|null,[key:string]:any}>;
    constructor(@Inject(ShareManagementService) private shareManagementService: ShareManagementService,
                @Inject(TrafficWalletService) private walletService: TrafficWalletService,
                private formBuilder: FormBuilder,
                private injector: Injector,
                private primengConfig: PrimeNGConfig) {
        super(injector);
    }
    dataSet: {
        content: Array<any>,
        total: number
    };
    dataSetShareDetail: {
        content: Array<any>,
        total: number
    };
    phoneReceipt: string;
    searchInfo: {
        searchName: number | null,
        searchPhone: number | null,
        value: string | null,
        lstPackageName: Array<any> | null,
        lstWalletName: Array<any> | null
        lstSubCode: Array<any> | null,
        lstPayCode: Array<any> | null
    };

    searchInfoDetail: {
        searchWalletCode: number | 0,
        searchPayCode: number | 0,
        value: string | " ",
        lstPackageCode: Array<any> | null,
        sharingDay:string,
        trafficType:string | " ",
        phoneReceipt:string | " "
    };

    addPhoneGroup = new FormGroup({
        name: new FormControl("",[Validators.maxLength(50), Validators.pattern('^[a-zA-Z0-9\\- _\\u00C0-\\u024F\\u1E00-\\u1EFF]+$')]),
        email: new FormControl("", [Validators.pattern(/^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9]+$/), Validators.maxLength(100)]),
        phone: new FormControl("", [Validators.required,numericMaxLengthValidator(12), Validators.pattern(/^0[0-9]{9,10}$/)], [checkExistedDynamicListArray(this.shareManagementService)])
    })

    sharedEditGroup = new FormGroup({
        shareId: new FormControl(),
        name: new FormControl("",[Validators.maxLength(50), Validators.pattern('^[a-zA-Z0-9\\- _\\u00C0-\\u024F\\u1E00-\\u1EFF]+$')]),
        phone: new FormControl({value:"", disabled: true}, [Validators.required]),
        email: new FormControl("", [Validators.pattern(/^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9]+$/), Validators.maxLength(100)])
    })

    searchList: Array<SeperateSearchInfo>;
    filterList: Array<SeperateFilterInfo>;
    searchListDetail: Array<SeperateSearchInfo>;
    filterListDetail: Array<SeperateFilterInfo>;
    optionTableDetail: OptionTable;
    serviceFunction: any;
    userInfo: any;
    visible:boolean;
    arrSubCode: Array<{name:any,value:any}>;
    arrPayCode: Array<{name:any,value:any}>;
    arrPackageCode: Array<{name:any,value:any}>;
    isShowDialogImportByFile: boolean = false;
    isShowErrorUpload: boolean = false;
    messageErrorUpload: string| null;
    optionInputFile: OptionInputFile;
    fileObject: any;
    visibleEdit: boolean;
    allPermissions = CONSTANTS.PERMISSIONS;
    phoneList: Array<any> =[];
    originSearch:any;
    originSearchDetail:any;
    isPrivilage:boolean = true;
    isShowModalDetail:boolean = false;
    phone:string;
    name:string;
    email:string;
    subscriptionOption: Array<{name: string, value: string}>;
    itemAddShare : MenuItem[]

    ngOnInit(){
        let me = this
        this.items = [{ label: this.tranService.translate(`global.menu.trafficManagement`) }, { label: this.tranService.translate("global.menu.shareList") },];
        this.itemAddShare = [
            {
                label: this.tranService.translate("datapool.button.addShare"),
                command: ()=>{
                    me.visible = true;
                },
            },
            {
                label: this.tranService.translate("global.button.import"),
                command: ()=>{
                    me.importByFile();
                }
            }
        ];
        this.searchInfo = {
            searchName:0,
            searchPhone:0,
            value:"",
            lstPackageName: null,
            lstWalletName: null,
            lstSubCode: null,
            lstPayCode: null

        };
        this.userInfo = this.sessionService.userInfo;
        this.getWalletCbb();
        this.home = { icon: 'pi pi-home', routerLink: '/' };
        this.selectItems = [];
        this.columns = [{
            name: this.tranService.translate("datapool.label.sharedPhone"),
            key: "phoneReceipt",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: false,
            style:{
                cursor: "pointer",
                color: "var(--mainColorText)"
            },
            funcClick(id, item) {
                me.subscriptionOption = [];
                me.phoneReceipt = item.phoneReceipt;
                me.shareManagementService.getByMsisdn(me.phoneReceipt, (response) => {
                    me.phone = response.phoneReceipt;
                    me.name = response.name;
                    me.email = response.email;
                },(error)=>{
                    console.log(error.error.error.errorCode);
                    if(error.error.error.errorCode="error.error.error.errorCode"){
                        me.isPrivilage = false
                        this.messageCommonService.error("Bạn không có quyền truy cập thông tin này")
                        this.router.navigate(["/data-pool/shareMgmt/listShare"])
                    }
                }, () => { this.messageCommonService.offload() });

                me.walletService.getPackageCbbForUser(item.phoneReceipt, (response)=>{
                    response.map((data)=>{
                        me.subscriptionOption.push({name: data.name,value: data.value})
                    })
                    me.subscriptionOption = me.subscriptionOption.filter((item, index, self) =>
                            index === self.findIndex((t) => (
                                t.name === item.name && t.value === item.value
                            ))
                    );
                    me.filterListDetail = [{
                        name: "Gói cước",
                        key: "lstPackageCode",
                        type: FilterInputType.multiselect,
                        items:me.subscriptionOption,
                        itemFilter: true
                    },{
                        name:"Loại lưu lượng",
                        key: "trafficType",
                        type: FilterInputType.dropdown,
                        items:[{name:"Gói Data", value:"Gói Data"},{name:"Gói SMS ngoại mạng", value:"Gói SMS ngoại mạng"},{name:"Gói SMS VNP", value:"Gói SMS VNP"}],
                        itemFilter: true
                    },{
                        name:"Ngày chia sẻ",
                        key: "sharingDay",
                        type: FilterInputType.calendar,
                        unixTimeString: true
                    }]
                },null, () => {
                    this.messageCommonService.offload()
                });
                me.columnsInDetailShare = [{
                    name: me.tranService.translate("datapool.label.walletCode"),
                    key: "subCode",
                    size: "150px",
                    align: "left",
                    isShow: true,
                    isSort: false
                },{
                    name: me.tranService.translate("datapool.label.packageName"),
                    key: "packageName",
                    size: "150px",
                    align: "left",
                    isShow: true,
                    isSort: false
                },{
                    name: me.tranService.translate("datapool.label.transactionCode"),
                    key: "transactionCode",
                    size: "150px",
                    align: "left",
                    isShow: true,
                    isSort: false
                },{
                    name: me.tranService.translate("datapool.label.trafficType"),
                    key: "trafficType",
                    size: "150px",
                    align: "left",
                    isShow: true,
                    isSort: false
                },{
                    name: me.tranService.translate("datapool.label.sharingDataNotType"),
                    key: "trafficShare",
                    size: "150px",
                    align: "left",
                    isShow: true,
                    isSort: false
                },{
                    name: me.tranService.translate("datapool.label.shareDate"),
                    key: "timeShare",
                    size: "150px",
                    align: "left",
                    isShow: true,
                    isSort: false
                },{
                    name: me.tranService.translate("datapool.label.usedDate"),
                    key: "timeExpired",
                    size: "150px",
                    align: "left",
                    isShow: true,
                    isSort: false
                }];

                me.optionTableDetail = {
                    hasClearSelected:false,
                    hasShowChoose: false,
                    hasShowIndex: true,
                    hasShowToggleColumn: false,
                }
                me.searchInfoDetail = {
                    searchWalletCode: 0,
                    searchPayCode: 0,
                    value: " ",
                    lstPackageCode: null,
                    sharingDay: null,
                    trafficType: " ",
                    phoneReceipt: me.phoneReceipt
                }
                me.searchListDetail = [{
                    name: "Mã ví",
                    key: "searchWalletCode"
                },{
                    name: "Mã thanh toán",
                    key: "searchPayCode"
                }];
                me.pageNumberDetail = 0;
                me.pageSizeDetail = 10;
                me.sortDetail = "id,desc"
                me.dataSetShareDetail ={
                    content: [],
                    total: 0
                }
                me.isShowModalDetail = true;
                me.originSearchDetail = me.searchInfoDetail;
                me.searchInDetail(me.pageNumberDetail, me.pageSizeDetail, me.sortDetail, me.searchInfoDetail);
            },
        },{
            name: this.tranService.translate("datapool.label.fullName"),
            key: "name",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: false
        },{
            name: this.tranService.translate("datapool.label.email"),
            key: "email",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: false,
            isShowTooltip: true,
            style: {
                display: 'inline-block',
                maxWidth: '250px',
                overflow: 'hidden',
                textOverflow: 'ellipsis'
            }
        },{
            name: this.tranService.translate("datapool.label.creator"),
            key: "emailCreator",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: false
        }];
        this.optionTable = {
            hasClearSelected:false,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
            action: [
                {
                    icon: "pi pi-pencil",
                    tooltip: this.tranService.translate("global.button.edit"),
                    func: function(id, item){
                        me.visibleEdit = true;
                        me.sharedEditGroup.get("shareId").setValue(item.id)
                        me.sharedEditGroup.get("name").setValue(item.name)
                        me.sharedEditGroup.get("phone").setValue(item.phoneReceipt)
                        me.sharedEditGroup.get("email").setValue(item.email)
                    },
                    funcAppear: function (id, item) {
                        // console.log(me.userInfo);
                       if (me.userInfo.type == CONSTANTS.USER_TYPE.CUSTOMER) {
                           if (item.createdBy == me.userInfo.id) {
                               return true
                           } else {
                               return false;
                           }
                       } else {
                           return true;
                       }
                    }
                },
                {
                    icon: "pi pi-trash",
                    tooltip: this.tranService.translate("global.button.delete"),
                    func: function(id, item){
                        me.messageCommonService.confirm(
                            me.tranService.translate("datapool.message.delete"),
                            me.tranService.translate("datapool.message.confirmDelete"),
                            {
                                ok:()=>{
                                    me.messageCommonService.onload();
                                    me.shareManagementService.deleteShared(item.id, (response) => {
                                        me.messageCommonService.success(me.tranService.translate("global.message.deleteSuccess"));
                                        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);
                                    }, null, ()=>{
                                        me.messageCommonService.offload();
                                    })
                                },
                                cancel: ()=>{
                                    // me.messageCommonService.error(me.tranService.translate("global.message.deleteFail"));
                                }
                            }
                        )
                    },
                    funcAppear: function (id, item) {
                        // console.log(me.userInfo);
                        if (me.userInfo.type == CONSTANTS.USER_TYPE.CUSTOMER) {
                            if (item.createdBy == me.userInfo.id) {
                                return true
                            } else {
                                return false;
                            }
                        } else {
                            return true;
                        }
                    }
                },
            ]
        }
        this.pageNumber = 0;
        this.pageSize= 10;
        this.sort = "createdDate,desc"
        this.dataSet ={
            content: [],
            total: 0
        }
        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
        this.optionInputFile = {
            type: ['xls','xlsx'],
            messageErrorType: this.tranService.translate("global.message.wrongFileExcel"),
            maxSize: 10,
            unit: "MB",
            required: true,
            isShowButtonUpload: true,
            actionUpload: this.uploadFile.bind(this),
            disabled: false
        }
        this.primengConfig.setTranslation({
            emptyFilterMessage:"Không tìm thấy kết quả",
            emptyMessage:"Không tìm thấy kết quả"
        })

        this.originSearch = this.searchInfo;
    }

    uploadFile(objectFile: any) {
        let me = this;
        if(objectFile.size >= 1048576){
            this.messageCommonService.error("Dung lượng file vượt quá dung lượng tối đa")
            return
        }
        me.messageCommonService.onload();
        this.shareManagementService.uploadFileShareInfo(objectFile, async (response) => {
            const dataError = [];
            const errorMessageCode = {
                '10': 'Tham số đầu vào không hợp lệ',
                '400': response => dataError.push(response?.headers?.get('cause')),
                '401': 'Kích thước file vượt quá giới hạn',
                '402': 'File tải lên thừa cột',
                '403': 'File tải lên thiếu cột',
                '404': 'File tải lên trùng cột',
                '405': 'Không thể lấy thông tin hàng từ file excel',
                '501': response => dataError.push(response?.headers?.get('Content-Disposition')),
                '430': 'Sai định dạng file mẫu'
            };

            if (response?.headers?.get('cause') === '0') {
                me.messageCommonService.success('Import người được chia sẻ thành công');
                me.isShowDialogImportByFile = false;
                me.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
            } else {
                me.isShowErrorUpload = true;
                const errorMessage = errorMessageCode[response?.headers?.get('cause')] || 'Lỗi không xác định';
                if (typeof errorMessage === 'function') {
                    errorMessage(response);
                    if (!response?.body) {
                        const fileName = response?.headers?.get('Content-Disposition');
                        const workbook = new Excel.Workbook();
                        const buf = await workbook.xlsx.writeBuffer();
                        const spliceFileName = fileName.substring(0, fileName.length - 5);
                        const exportFileName = ''.concat(spliceFileName, '_Danh sách lỗi_', moment().format('DDMMYYYYHHMMss'));
                        // download the processed file
                        await saveAs(new Blob([buf]), `${exportFileName}.xlsx`);
                    } else {
                        const dateMoment = moment().format('DDMMYYYYHHmmss');
                        const name = (objectFile.name || objectFile.fileName).split('.');
                        me.exportFile(response?.body, `${name[0]}_Danh sách lỗi_${dateMoment}`, '');
                        me.messageCommonService.error(me.tranService.translate('datapool.text.downloadErrorMessage'), null, 10000)
                    }
                } else {
                    me.messageCommonService.error(errorMessage);
                }
            }

        },null,()=>{
            this.messageCommonService.offload()
        })
    }

    exportFile = (bytes, fileName, fileType) => {

        const file = new Blob([bytes], {
            type: fileType || 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        });
        FileSaver.saveAs(file, fileName);
    };

    search(page, limit, sort, params){
        this.pageNumber = page;
        this.pageSize = limit;
        this.sort = sort;
        let me = this;
        let dataParams = {
            page,
            size: limit,
            sort
        }
        Object.keys(this.searchInfo).forEach(key => {
            dataParams[key] = this.searchInfo[key];
        })

        this.messageCommonService.onload()
        this.shareManagementService.search(dataParams, (response) => {
            me.dataSet = {
                content: response.content,
                total: response.totalElements
            }
        }, null, () => {
            me.messageCommonService.offload();
        })
    }

    catchSearchList(params){
        this.searchInfo = this.originSearch
        this.searchInfo = {...this.searchInfo, ...params};
        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
    }

    catchSearchDetail(params){
        this.searchInfoDetail = this.originSearchDetail
        this.searchInfoDetail = {...this.searchInfoDetail, ...params};
        this.searchInDetail(this.pageNumberDetail, this.pageSizeDetail, this.sortDetail, this.searchInfoDetail);
    }

    getWalletCbb() {
        let me = this;
        this.walletService.getWalletCbb((response) => {
            let mappedResponse = response.map(e => ({
                subCode: { name: e.subCode, value: e.subCode },
                payCode: { name: e.payCode, value: e.payCode },
                packageCode: { name: e.packageName, value: e.packageCode }
            }));

            me.arrSubCode = mappedResponse.map(e => e.subCode);
            me.arrSubCode = me.arrSubCode.filter((item, index, self) =>
                    index === self.findIndex((t) => (
                        t.name === item.name && t.value === item.value
                    ))
            );
            me.arrPayCode = mappedResponse.map(e => e.payCode);
            me.arrPayCode = me.arrPayCode.filter((item, index, self) =>
                    index === self.findIndex((t) => (
                        t.name === item.name && t.value === item.value
                    ))
            );
            me.arrPackageCode = mappedResponse.map(e => e.packageCode);
            me.arrPackageCode = me.arrPackageCode.filter((item, index, self) =>
                    index === self.findIndex((t) => (
                        t.name === item.name && t.value === item.value
                    ))
            );

            this.searchList = [{
                name: this.tranService.translate("datapool.label.fullName"),
                key: "searchName"
            }, {
                name: "Số điện thoại",
                key: "searchPhone"
            }];

            this.filterList = [{
                name: this.tranService.translate("datapool.label.walletCode"),
                key: "lstSubCode",
                type: FilterInputType.multiselect,
                items: this.arrSubCode,
                itemFilter: true
            },
                {
                    name: this.tranService.translate("datapool.label.payCode"),
                    key: "lstPayCode",
                    type: FilterInputType.multiselect,
                    items: this.arrPayCode,
                    itemFilter: true
                },
                {
                    name: this.tranService.translate("datapool.label.packageName"),
                    key: "lstPackageName",
                    type: FilterInputType.multiselect,
                    items: this.arrPackageCode,
                    itemFilter: true
                },
                {
                    name: this.tranService.translate("datapool.label.shareDate"),
                    key: "shareDate",
                    type: FilterInputType.calendar,
                    unixTimeString:true
                },
                {
                    name: this.tranService.translate("datapool.label.usedTime"),
                    key: ["startDate", "endDate"],
                    type: FilterInputType.rangeCalendar,
                    unixTimeString: true
                }
            ];
        },null, ()=>{
            this.messageCommonService.offload()
        });
    }

    importByFile() {
        let me = this;
        me.isShowDialogImportByFile = true;
        me.observableService.next(CONSTANTS.OBSERVABLE.KEY_INPUT_FILE_VNPT, {});
        me.isShowErrorUpload = false;
    }

    submitForm(){
        let me = this;
        this.messageCommonService.onload()
        this.shareManagementService.create(this.addPhoneGroup.value, (response)=>{
            if (response.id == -1) {
                    me.messageCommonService.error(me.tranService.translate("datapool.message.dublicateShareInfo"))
                    me.messageCommonService.offload()
                    return;
            } else {
                this.closeForm();
                this.messageCommonService.success(this.tranService.translate("global.message.saveSuccess"))
                this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo)
                this.sharedEditGroup.reset();
            }
        },null, ()=>{
            this.messageCommonService.offload()
        })
    }
    /**
     * bỏ check số vina
     */
    // checkPhoneAndSubmitForm() {
    //     let me = this;
    //     me.messageCommonService.onload()
    //     this.walletService.checkParticipant({phoneNumber: me.addPhoneGroup.value.phone},
    //         (response) => {
    //             if (response.error_code === "0" && (response.result === "02" || response.result === "11")) {
    //                 me.submitForm()
    //             } else if (response.error_code === "0" && response.result === "0") {
    //                 if (isVinaphoneNumber(me.addPhoneGroup.value.phone)) {
    //                     me.submitForm()
    //                 } else {
    //                     this.messageCommonService.error(this.tranService.translate("datapool.message.notValidPhone"))
    //                 }
    //             } else {
    //                 this.messageCommonService.error(this.tranService.translate("datapool.message.notValidPhone"))
    //             }
    //         },
    //         null, () => {
    //             this.messageCommonService.offload();
    //         })
    // }

    submitEditForm(){
        Object.keys(this.sharedEditGroup.controls).forEach(key => {
            const control = this.sharedEditGroup.get(key);
            if (control.invalid) {
                console.log('Field:', key, 'is invalid. Errors:', control.errors);
            }
        });
        this.messageCommonService.onload()
        this.shareManagementService.updateShared(this.sharedEditGroup.value, (response)=>{
            this.closeForm();
            this.messageCommonService.success(this.tranService.translate("global.message.saveSuccess"))
            this.search(this.pageNumber,this.pageSize,this.sort, this.searchInfo)
        },()=>{
            console.log("Error")
        },()=>{ this.messageCommonService.offload() })
    }

    clearFileCallback(){
        this.isShowErrorUpload = false;
    }

    downloadTemplate(){
        this.shareManagementService.downloadTemplate();
    }
    closeForm(){
        this.visible = false;
        this.visibleEdit = false;
        this.addPhoneGroup.reset();
    }
    onHideAdd(){
        this.addPhoneGroup.reset();
    }

    searchInDetail(page, limit, sort, params){
        let me = this;
        me.pageNumberDetail = page;
        me.pageSizeDetail = limit;
        me.sortDetail = sort;
        let dataParams = {
            ...params,
            page,
            size: limit,
            sort
        }
        Object.keys(me.searchInfoDetail).forEach(key => {
            dataParams[key] = me.searchInfoDetail[key];
        })
        this.messageCommonService.onload()
        this.walletService.searchDetailShare(dataParams, (response) => {
            me.dataSetShareDetail = {
                content: response.content,
                total: response.totalElements
            }
        }, null, () => {
            me.messageCommonService.offload();
        })
    }
}
