import {Component, Inject, Injector, <PERSON><PERSON><PERSON><PERSON>, ViewChild} from '@angular/core';
import {FormArray, FormControl, FormGroup, Validators} from '@angular/forms';
import {MenuItem} from 'primeng/api';
import {ComponentBase} from 'src/app/component.base';
import {ShareManagementService} from 'src/app/service/datapool/ShareManagementService';
import {ComboLazyControl} from 'src/app/template/common-module/combobox-lazyload/combobox.lazyload';
import {GroupInfo, PhoneInfo, ShareDetail, Wallet} from "../../data-pool.type-data";
import {TrafficWalletService} from "../../../../service/datapool/TrafficWalletService";
import {digitValidator} from 'src/app/template/common-module/validatorCustoms';
import {CONSTANTS, isVinaphoneNumber} from "../../../../service/comon/constants";
import {OptionInputFile} from "../../../common-module/input-file/input.file.component";
import {GroupSubWalletService} from "../../../../service/group-sub-wallet/GroupSubWalletService";
import {ColumnInfo, OptionTable} from "../../../common-module/table/table.component";
import * as XLSX from 'xlsx-js-style';
import * as FileSaver from 'file-saver';
import {convert84to0PhoneNumber} from "../../../common-module/utils/util";
import {Table} from "primeng/table";
import {ev} from "@fullcalendar/core/internal-common";

@Component({
    selector: 'app-share-data',
    templateUrl: './share-data.component.html',
    styleUrls: ['./share-data.component.scss']
})
export class ShareDataComponent extends ComponentBase implements OnDestroy {
  items: MenuItem[];
  home: MenuItem;
  dummyWallet:Wallet[];
  shareList: ShareDetail[];
  phoneList : PhoneInfo[] = [];
  groupShareList : GroupInfo[] = [];
  filteredPhoneList : PhoneInfo[];
  selectedWallet: Wallet;
  walletName: String;
  showShareList: boolean =false;
  isShowDefined:boolean = false;
  isSubmit: boolean = false;
  isError = false
  phoneReceiptSelect: any = "";
  phoneReceiptSelectControl = new ComboLazyControl();
  groupSelected:any;
  idGroupSelected: any = "";
  shareGroupChoose: any;
  FulldataSetError:any
  constructor( @Inject(ShareManagementService) private shareService: ShareManagementService,
               @Inject(TrafficWalletService) private walletService: TrafficWalletService,
               @Inject(GroupSubWalletService) private groupSubService: GroupSubWalletService,
              injector: Injector,
            ) {super(injector);}
  @ViewChild('normalShare') normalShareTable!: Table;
  walletControl: ComboLazyControl = new ComboLazyControl();
  paramSearchWallet = {};
  countdown:number;
  interval: any;
  trafficType: string;
  isClickCreate: boolean = true
  shareDataForm = new FormGroup({
    walletValue: new FormControl(null, [Validators.required]),
    dateShare: new FormControl(new Date),
    definedData: new FormControl(),
    selectedShareMethod: new FormControl(0),
    description: new FormControl(null, [Validators.maxLength(255)]),
    otp: new FormControl(null, [Validators.required, digitValidator(6)]),
    pageCurrent: new FormControl(1),
  });
  originRemain: number;
  remainDataTotal:number;
  isDefineValueError: boolean = false;
  totalData: number;
  isShowDialogImportByFile: boolean = false;
  isShareByGroup: boolean = false;
  isShowErrorUpload: boolean = false;
  optionInputFile: OptionInputFile;
  fileObject: any;
  isValidPhone: boolean = true;
  isValidDataFixed: boolean = false;
  messageErrorUpload: string | null;
  dataForLabel: string = "Data";
  selectItems: Array<{id:number,[key:string]:any}>;
  optionTable: OptionTable;
  pageNumber: number;
  pageSize: number;
  sort: string;
  dataSet: {
    content: Array<any>,
    total: number
  };
  searchInfo: {
    value?: string,
  };
  columns: Array<ColumnInfo>;
    optionTableError: OptionTable = {
        hasClearSelected: false,
        hasShowChoose: false,
        hasShowIndex: true,
        hasShowToggleColumn: false,
    };
    pageNumberError: number = 0;
    pageSizeError: number = 10;
    sortError: string = "";
    dataSetError: {
        content: Array<any>,
        total: number
    } = {
        content: [],
        total: 0
    };
    searchInfoError: {
        value?: string,
    } = {
        value: ""
    };
    columnsError: Array<ColumnInfo> = [
        {
            name: this.tranService.translate("datapool.label.phone"),
            key: "phoneReceipt",
            size: "fit-content",
            align: "left",
            isShow: true,
            isSort: false,
        },
        {
            name: this.tranService.translate("datapool.label.description"),
            key: "description",
            size: "fit-content",
            align: "left",
            isShow: true,
            isSort: false
        }];
  dataShareGroup: number = 0;
  dataShareGroupTotal: number;
  shareMethod: number;
  percentShareGroup: number;

  selectedTableData = [];
  isNonUseOTP = true
  groupSelectedValue: Array<any> = [];
  isAutoWallet = true

  openSubmit() {
    this.isSubmit = true
    this.countdown = 60
    this.startCountdown()
  }

  isMailInvalid(email:string){
    if (!email){
      return false
    }
    const pattern:RegExp = /^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$/
    return !pattern.test(email);
  }

  convertDateToString(value: Date): string {
    if (value == null) return "";

    const pad = (num: number) => num.toString().padStart(2, '0');

    const day = pad(value.getDate());
    const month = pad(value.getMonth() + 1); // Tháng bắt đầu từ 0 nên cần cộng 1
    const year = value.getFullYear();

    const hours = pad(value.getHours());
    const minutes = pad(value.getMinutes());
    const seconds = pad(value.getSeconds());

    return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
  }

  submitForm() {
    let me = this;
    this.groupSelected = null
    if (this.shareMethod == 1) {
        const dataBodyGroup = {
            subCode: this.selectedWallet.subCode,
            otp: this.shareDataForm.get('otp').value,
            reason: this.shareDataForm.get('description').value,
            sharingDay: this.convertDateToString(this.shareDataForm.get('dateShare').value),
            idGroup: Number(this.idGroupSelected),
            numOfShare: Number(this.dataShareGroup),
            transId: '',
            trafficType: "DATA",
            expireDate: '30 ngày',
            type: 0,
            autoPhones: this.groupSelectedValue.map(item => item.phoneReceipt)
        }

        me.messageCommonService.onload();

        this.shareService.shareTrafficByGroup(dataBodyGroup, (res) => {

            if (res.error_code === 'BSS-00000000') {
                let ok = 1
                let dataErrorTable = []
                let countToTal = 0
                res.data.forEach(element => {
                    if(element.status == 0 && element.error != ""){
                        // me.messageCommonService.error(`Thuê bao ${element.sdt} có lỗi (${element.error})`);
                        dataErrorTable.push({
                            phoneReceipt: convert84to0PhoneNumber(element.sdt),
                            // description: element.error
                            description: "Lỗi do thuê bao không hợp lệ"
                        })
                        ok = 0
                    }
                    countToTal++;
                });
                if(ok == 0){
                    this.isError = true
                    this.dataSetError = {
                        content: dataErrorTable,
                        total: dataErrorTable.length,
                    }
                    this.FulldataSetError = {...this.dataSetError}
                    me.messageCommonService.error(me.tranService.translate("datapool.message.shareNotifyFail", {success: countToTal - dataErrorTable.length, total : countToTal}), null, 10000);
                } else {
                me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"));
                this.router.navigate(['/data-pool/shareMgmt/listShare']);
                }
            }else if (res.error_code === 'Success') {
                me.messageCommonService.success(me.tranService.translate("datapool.message.shareNotifyBackground"),null, 15000);
                this.router.navigate(['/data-pool/shareMgmt/listShare']);
            } else {
                this.messageCommonService.error(res.message)
            }
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    } else {
        const convertShareInfo = this.shareList.map((e) => ({
            phone: String(e?.phoneReceipt),
            trafficType: "DATA",
            numOfShare: Number(e?.data),
            expireDate: '30 ngày',
            name: e?.name,
            email: e?.email,
            type: 0,
            isAuto: !!this.selectedTableData.find(item=> item.phoneReceipt=== e.phoneReceipt),
        }));

        const dataBody = {
            subCode: this.selectedWallet.subCode,
            otp: this.shareDataForm.get('otp').value,
            reason: this.shareDataForm.get('description').value,
            sharingDay: this.convertDateToString(this.shareDataForm.get('dateShare').value),
            shareInfos: convertShareInfo,
            transId: ''
        };

        console.log(convertShareInfo)

        me.messageCommonService.onload();

        this.shareService.shareTraffic(dataBody, (res) => {

            if (res.error_code === 'BSS-00000000') {
                let ok = 1
                let dataErrorTable = []
                res.data.forEach(element => {
                    if(element.status == 0 && element.error != ""){
                        // me.messageCommonService.error(`Thuê bao ${element.sdt} có lỗi (${element.error})`);
                        dataErrorTable.push({
                            phoneReceipt: convert84to0PhoneNumber(element.sdt),
                            // description: element.error
                            description: "Lỗi do thuê bao không hợp lệ"
                        })
                        ok = 0
                    }
                });
                if(ok == 0){
                    this.isError = true
                    this.dataSetError = {
                        content: dataErrorTable,
                        total: dataErrorTable.length,
                    }
                    this.FulldataSetError = {...this.dataSetError}
                    me.messageCommonService.error(me.tranService.translate("datapool.message.shareNotifyFail", {success: this.shareList.length - this.dataSetError.content.length, total : this.shareList.length}), null, 10000);
                }else {
                    me.messageCommonService.success(me.tranService.translate("datapool.message.shareNotifySuccess", {success: this.shareList.length, total : this.shareList.length}), null, 10000);
                    this.router.navigate(['/data-pool/shareMgmt/listShare']);
                }
            }else if (res.error_code === 'Success') {
                me.messageCommonService.success(me.tranService.translate("datapool.message.shareNotifyBackground"),null, 15000);
                this.router.navigate(['/data-pool/shareMgmt/listShare']);
            } else {
                this.messageCommonService.error(res.message)
            }
        }, null, ()=>{
            me.messageCommonService.offload();
        });
    }
    this.isNonUseOTP = true
  }

  ngOnInit() {
      let me = this;
      me.columns = [
          {
              name: this.tranService.translate("datapool.label.phone"),
              key: "phoneReceipt",
              size: "10%",
              align: "left",
              isShow: true,
              isSort: false,
          },
          {
              name: this.tranService.translate("datapool.label.fullName"),
              key: "name",
              size: "25%",
              align: "left",
              isShow: true,
              isSort: false
          },
          {
              name: this.tranService.translate("datapool.label.email"),
              key: "email",
              size: "25%",
              align: "left",
              isShow: true,
              isSort: false,
              isShowTooltip: true,
              style: {
                  display: 'inline-block',
                  maxWidth: '350px',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis'
              }
          },
          {
              name: me.trafficType == 'Gói Data'
                  ? me.tranService.translate("datapool.label.sharingData", {type: 'MB'})
                  : me.trafficType == 'Gói thoại'
                      ? me.tranService.translate("datapool.label.sharingData", {type: 'Phút'})
                      : (me.trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase())
                          ? me.tranService.translate("datapool.label.sharingData", {type: 'SMS'})
                          : me.tranService.translate("datapool.label.sharingDataNotType"),
              key: "sharingData",
              size: "25%",
              align: "left",
              isShow: true,
              isSort: false,
              funcConvertText(value) {
                  switch (me.trafficType) {
                      case 'Gói Data':
                          return `${me.dataShareGroup?.toString()} (MB)`;
                      case 'Gói thoại':
                          return `${me.dataShareGroup?.toString()} (Phút)`;
                      case 'Gói SMS':
                      case 'Gói SMS ngoại mạng':
                      case 'Gói SMS VNP':
                          return `${me.dataShareGroup?.toString()} (SMS)`;
                      default:
                          return '';
                  }
              },
          },
          {
              name: this.tranService.translate("datapool.label.percentage"),
              key: "percentDataGroup",
              size: "25%",
              align: "left",
              isShow: true,
              isSort: false,
              funcConvertText(value) {
                  return me.percentShareGroup?.toString() ? `${me.percentShareGroup?.toString()} %` : '';
              },
          },
      ]
      this.items = [{label: this.tranService.translate(`global.menu.trafficManagement`)}, {
          label: this.tranService.translate("global.menu.shareList"),
          routerLink: ["/data-pool/shareMgmt/listShare"]
      }, {label: this.tranService.translate("datapool.label.shareData")}];
      this.home = {icon: 'pi pi-home', routerLink: '/'};
      this.getWalletCbb();
      this.showShareList = false;
      this.phoneList = [];
      // this.changeWallet();
      this.observableService.subscribe(CONSTANTS.OBSERVABLE.UPDATE_SHARE_INFO, {
          next: this.loadData.bind(this)
      });
      this.shareMethod = 0;
      me.selectItems = [];
      me.optionTable = {
          hasClearSelected: false,
          hasShowChoose: true,
          hasShowIndex: true,
          hasShowToggleColumn: false,
          disabledCheckBox: false,
          paginator: true
      }
      me.searchInfo = {
          value: ""
      };
      me.pageNumber = 0;
      me.pageSize = 10;
      me.sort = "phoneReceipt,desc";
      me.dataSet = {
          content: [],
          total: 0
      }
  }

  loadData(data): void {
      if (data) {
          if(data.length > 1000){
              this.messageCommonService.error(this.tranService.translate("error.invalid.file.form.maxrow"))
              return
          }
          this.shareList = data;
          let oldSelectData = this.selectedTableData
          this.selectedTableData = []
          //data.isAuto.toLowerCase()==="có" || data.isAuto.toLowerCase()=="co"
          data.forEach((item, index) => {
              if(item.isAuto && (item.isAuto.localeCompare("Có", 'vi', { sensitivity: 'base' }) === 0 || item.isAuto.localeCompare("Co", 'vi', { sensitivity: 'base' }) === 0)){

                  this.selectedTableData.push(item)
              }
          })
          this.selectedTableData = [
              ...this.selectedTableData,
              ...oldSelectData
          ]
      }
      this.caculateRemain()
      if(!this.isAutoWallet){
          this.selectedTableData = []
          this.messageCommonService.warning("Ví chưa đăng ký chia sẻ không cần OTP nên các thuê bao đã tích chọn tự động sẽ bị bỏ tích");
      }
  }

  changeWallet() {
      let me = this;
      if (this.shareMethod == 1) {
          this.dataShareGroup = 0;
          this.percentShareGroup = 0;
      }
      if(this.shareList){
          this.shareList.forEach(item => {
              item.data = null;
              item.percent = null;
          });
      }else{
          this.shareList = [];
      }
    const walletId = this.shareDataForm.get('walletValue').value;
    const wallet = this.dummyWallet?.find(w => w.subCode === walletId);

    if (wallet) {
      this.selectedWallet = wallet;
      if(wallet.autoType != 2){
          this.isNonUseOTP = true
          this.isAutoWallet = true
          me.optionTable = {
              hasClearSelected: false,
              hasShowChoose: true,
              hasShowIndex: true,
              hasShowToggleColumn: false,
              disabledCheckBox: false,
              paginator: true
          }
      }else{
          this.isNonUseOTP = false
          this.isAutoWallet = false
          me.optionTable = {
              hasClearSelected: false,
              hasShowChoose: true,
              hasShowIndex: true,
              hasShowToggleColumn: false,
              disabledCheckBox: true,
              paginator: true
          }
      }
        this.groupSelectedValue = []
        this.selectedTableData = []
      this.walletName = this.selectedWallet.packageName;
    } else {
        console.log('Không tìm thấy ví có id =', walletId);
    }
    // this.getListShareInfoCbb();
    this.showShareList = true
    // typeOfTraffic
    this.trafficType = this.dummyWallet?.find(wallet => wallet.subCode === this.shareDataForm.get("walletValue").value)?.trafficType
    this.totalData = this.dummyWallet?.find(wallet => wallet.subCode === this.shareDataForm.get("walletValue").value)?.purchasedTraffic
    this.remainDataTotal = this.dummyWallet?.find(wallet => wallet.subCode === this.shareDataForm.get('walletValue').value)?.totalRemainingTraffic
    this.dataShareGroupTotal = this.dummyWallet?.find(wallet => wallet.subCode === this.shareDataForm.get('walletValue').value)?.totalRemainingTraffic
    this.originRemain = this.dummyWallet?.find(wallet => wallet.subCode === this.shareDataForm.get('walletValue').value)?.totalRemainingTraffic

    if((this.trafficType || "").toUpperCase().includes("Gói SMS".toUpperCase())){
        this.dataForLabel = "SMS"
    }else if (this.trafficType == "Gói Data"){
        this.dataForLabel = "MB"
    }


  }

    getListShareInfoCbb(params, callback) {
        return this.shareService.getListShareInfoCbb(params, (response)=>{
            this.phoneList = response.content;
            callback(response)
        });
    }

    getShareGroupInfo(params, callback){
        let me = this;
        this.messageCommonService.onload();
        this.groupSubService.search(params, (response) => {
            // console.log(response)
            me.groupShareList = response.content;
            let data = {
                content: response.content,
                totalPages: response.totalElements
            }
        },null,()=>{
            this.messageCommonService.offload()
        })
    }

  getWalletCbb() {
      let me = this;
      this.messageCommonService.onload()
      this.walletService.getWalletCbbShare((response) => {
          me.dummyWallet = response;
      }, null, () => {
          this.messageCommonService.offload()
      })
  }

  addPhone(data, onSelected?){
    let me = this;
      if(data === null || data === undefined || data === ""){
          return
      }
      if(this.shareList.length > 999){
          this.messageCommonService.error(this.tranService.translate("error.invalid.file.form.maxrow"))
          return
      }
    this.isClickCreate = false
    this.shareList = this.cleanArray(this.shareList)
    const value = this.phoneList.find(dta => dta.phoneReceipt === data)
    if(this.shareList.find(i => i?.phoneReceipt === data)){
      this.messageCommonService.error(this.tranService.translate("datapool.message.existed"))
    }else {
        /**
         * UAT 2.4 issue 31
         * Khi add số thuê bao được chia sẻ, nếu đã có trong danh sách thì bỏ qua check số đó là thuê bao vinaphone hay không, trong các trường hợp sau:
         * - Chia sẻ thường
         * - Nhóm chia sẻ tự động > thêm sdt chia sẻ tự động
         * - Thêm thuê bao vào nhóm
         * - icon chia sẻ ở Danh sách ví
         */
        this.addPhoneTable(value, data)
        /**
         * bỏ check số vina
         */
        // if (onSelected) {
        //     this.addPhoneTable(value, data)
        // } else {
        //     const phone = String(data)?.replace(/^0/,"84");
        //     this.messageCommonService.onload()
        //     this.walletService.checkParticipant({phoneNumber: phone},
        //         (response) => {
        //             if (response.error_code === "0" && (response.result === "02" || response.result === "11")) {
        //                 this.addPhoneTable(value, data)
        //             } else if (response.error_code === "0" && response.result === "0") {
        //                 if (isVinaphoneNumber(data)) {
        //                     this.addPhoneTable(value, data)
        //                 } else {
        //                     this.messageCommonService.error(this.tranService.translate("datapool.message.notValidPhone"))
        //                 }
        //             } else {
        //                 this.messageCommonService.error(this.tranService.translate("datapool.message.notValidPhone"))
        //             }
        //         },
        //         null, () => {
        //             this.messageCommonService.offload();
        //         })
        // }

    }
      this.phoneReceiptSelectControl.reload();
      this.phoneReceiptSelectControl.clearValue();
      this.phoneReceiptSelectControl.clearFilter();
      this.phoneReceiptSelectControl.reloadOption();
      this.phoneReceiptSelect = ""
  }

    addPhoneTable(value, data){
        let me = this;
        let dataValue;
        if((this.trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase())){
            dataValue = 10
        }else if(this.trafficType == "Gói Data"){
            dataValue = 100
        }else{
            dataValue = null
        }
        if (this.remainDataTotal < dataValue){
            return this.messageCommonService.error(this.tranService.translate("datapool.message.exceededData"))
        }
        if(value){
            if(value?.percent){
                value.percent=null;
            }
            value.data = dataValue;
            this.shareList.push(value)
            setTimeout(function(){
                me.phoneReceiptSelect = "";
            },100);
            this.isClickCreate = true
            this.shareList = [...this.shareList]
        }else{
            let pushData: ShareDetail = {
                phoneReceipt: data,
                name:"",
                email:"",
                data:dataValue
            }
            this.shareList.push(pushData)
            this.shareList = [...this.shareList]
        }
        this.caculateRemain()
    }

  checkValidAdd(){
    this.isClickCreate = true
    if(!this.phoneList.find(dta => dta.phoneReceipt === this.phoneReceiptSelect)){
      this.isClickCreate = false
    }else{
      this.isClickCreate = true
    }
    if(this.phoneReceiptSelect == ""|| this.phoneReceiptSelect == null || this.phoneReceiptSelect === undefined){
      this.isClickCreate = true
    }

    const regex = /^0[0-9]{9,10}$/;
    const inputValue = this.phoneReceiptSelect;
    this.isValidPhone = regex.test(inputValue);
  }

    checkValidData() {
        if ((this.shareList.length === 0 && this.shareMethod == 0) ||
            (this.dataShareGroupTotal === 0 && this.shareMethod == 1)) {
            return true;
        }

        if (this.shareMethod == 0 && this.shareList.length != 0) {
            for (let item of this.shareList) {
                if (!item.name) item.name = "";
                if (!item.email) item.email = "";

                if (!this.checkDataCondition(item) ||
                    !item.data ||
                    (this.trafficType === "Gói Data" && item.data < 100) ||
                    ((this.trafficType || "").toUpperCase().includes("Gói SMS".toUpperCase()) && item.data < 10) ||
                    item.name.length >= 50 ||
                    item.email.length >= 100 ||
                    this.isMailInvalid(item.email)) {
                    return true;
                }
            }
            return false;
        }

        if (this.shareMethod == 1 &&
            this.dataShareGroupTotal &&
            this.dataShareGroupTotal != 0 &&
            this.dataShareGroup > 0) {
            return false;
        }

        return true;
    }

  cleanArray(arr: any[]): any[] {
    return arr.filter(item => item !== null && item !== undefined && item !== "");
  }

  deleteItem(i){
    this.messageCommonService.confirm(this.tranService.translate("datapool.button.delete"),
    this.tranService.translate("datapool.message.confirmDelete"),{
      ok: ()=>{
        const a = this.shareList[i].data
        if(a){
          this.shareList[i].data = null
          this.shareList[i].percent = null
        }
        this.shareList = this.shareList.filter((item,index) => index != i);
        this.caculateRemain();
      }
    })
    // const a = this.shareList[i].data
    // if(a){
    //   this.shareList[i].data = null
    //   this.shareList[i].percent = null
    // }
    // this.shareList = this.shareList.filter((item,index) => index != i);
    // this.caculateRemain();
  }

  changeData(event, i) {
      const shareValue = event.target.value;
      this.shareList[i].data = shareValue;
      this.shareList[i].percent = Math.round((shareValue / this.originRemain) * 100 * 100) / 100;
      this.caculateRemain();
  }

  onKeyDown(event, i){
    const key = event.key;
    const isNumber = /^[0-9]$/.test(key);

    if (key === "-"){
        return false;
    }

    let total=0;
    this.shareList.forEach((item,index) => {
      let value
      if(!item.data||item.data == null|| item.data == undefined || i == index){
        value = 0
      }else{
        value = item.data
      }
      total = Number(total) + Number(value);
    });
    const shareValue = event.target.value;
    const nextValue = parseInt(shareValue + key, 10);
    let totalUsedValue = nextValue+total
    if(this.originRemain - totalUsedValue < 0){
      this.messageCommonService.error(this.tranService.translate("datapool.message.exceededData"))
      return false;
    }
    return true;
  }

  changeDataName(event, i){
    const shareValue = event.target.value
    this.shareList[i].name = shareValue
  }

  changeDataMail(event, i){
    const shareValue = event.target.value
    this.shareList[i].email = shareValue
  }

  shareData(){
    let remainInit = this.dummyWallet?.find(wallet => wallet.subCode === this.shareDataForm.get('walletValue').value)?.totalRemainingTraffic;
    let totalDataLocked = 0;
    let remainAfterLocked = remainInit - totalDataLocked;
    let totalDataGroup = this.dataSet.total;

    if (this.shareMethod == 1) {
        if (this.trafficType === 'Gói Data') {
            let trafficShare = Math.floor(remainAfterLocked / totalDataGroup / 100) * 100;
            if ((remainInit - trafficShare * totalDataGroup) < 0) {
                this.messageCommonService.error("Vượt quá dung lương chia sẻ");
            } else {
                this.dataShareGroup = Math.floor(remainAfterLocked / totalDataGroup / 100) * 100;
            }
        }
        if ((this.trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase())) {
            this.dataShareGroup = Math.floor(remainAfterLocked / totalDataGroup / 5) * 5;
        }
        this.dataShareGroupTotal = remainAfterLocked - (Number(this.dataShareGroup) * Number(totalDataGroup));
        this.percentShareGroup = Math.round((this.dataShareGroup / remainInit) * 100 * 100) / 100;
    } else {
        let lengthRemain = this.shareList.length
        this.shareList.filter(element => element.locked === true).forEach(element => {
            totalDataLocked = Number(totalDataLocked) + Number(element.data);
            lengthRemain = lengthRemain - 1;
        });
        let remainAfterLocked = remainInit - totalDataLocked
        let intPart = Math.floor(remainAfterLocked/lengthRemain);
        let remainderPart = remainAfterLocked % lengthRemain
        this.shareList.filter(element => element.locked === false || !element.locked).forEach(element => {
            element.data = intPart
            element.percent = Math.round((intPart / remainInit) * 100 * 100) / 100;
        });
        this.remainDataTotal = remainderPart
    }
  };

  caculateRemain() {
    let totalQuantity = 0;
    if (this.shareMethod == 1) {
        this.dataShareGroupTotal = this.originRemain - (Number(totalQuantity) + Number(this.dataShareGroup));
    } else {
        this.shareList.forEach(item => {
            let value
            if(!item.data||item.data == null|| item.data == undefined){
                value = 0
            }else{
                value = item.data
            }
            totalQuantity = Number(totalQuantity) + Number(value);
        });
        this.remainDataTotal = this.originRemain - totalQuantity
    }
  }

  defineData() {
      this.isShowDefined = true
  };

  definedDataChange() {
      if (this.shareMethod == 1) {
          this.dataShareGroup = this.shareDataForm.controls['definedData'].value;
          this.percentShareGroup = Math.round((this.shareDataForm.controls['definedData'].value / this.originRemain) * 100 * 100) / 100;
      } else {
          this.shareList.filter(e => e.locked == false || !e.locked).forEach(element => {
              element.data = this.shareDataForm.controls['definedData'].value;
              element.percent = Math.round((this.shareDataForm.controls['definedData'].value / this.originRemain) * 100 * 100) / 100;
          });
      }
      this.caculateRemain();
      this.isShowDefined = !this.isShowDefined
  }

  resetData(){
    this.messageCommonService.confirm(this.tranService.translate("datapool.button.revokeSharing"),
    this.tranService.translate("datapool.message.confirmRevoke"),{
      ok:() => {
        this.remainDataTotal = this.originRemain
          if (this.shareMethod == 0) {
              this.shareList.forEach(item => {
                  item.data = null;
                  item.percent = null;
              });
          } else {
              this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
              this.dataShareGroup = 0;
              this.percentShareGroup = 0;
          }
      }
    })
  };

  startCountdown() {
      this.interval = setInterval(() => {
          if (this.countdown > 0) {
              this.countdown--;
          } else {
              clearInterval(this.interval);
          }
      }, 1000);
  }

  resetTimer() {
      this.countdown = 60;
      clearInterval(this.interval);
      this.startCountdown();
      this.sendOTP();
    }

  onHideDefine(){
    this.shareDataForm.controls['definedData'].reset()
  }

  handleShareData(){
      if (this.isNonUseOTP){
          this.submitForm();
      }else{
          this.sendOTP()
      }
  }

    // send OTP
    sendOTP() {
        let me = this;
        let body = {
            phoneNumber: this.selectedWallet?.phoneActive?.replace(/^0/, '84')
        }
        me.messageCommonService.onload();
        this.shareService.sendOTP(body, (res) => {
          if (res.errorCode === 'BSS-00000000') {
              me.openSubmit();
          }
        }, null, () => {
            me.messageCommonService.offload()
        })
  }

  checkValidDefine(event){
    let lengthRemain = null;
    if (this.shareMethod == 0) {
        lengthRemain = this.shareList.length;
        this.shareList.filter(element => element.locked === true).forEach(element => {
            lengthRemain = lengthRemain - 1;
        });
    } else {
        lengthRemain = this.dataSet.total;
    }


    if((this.shareDataForm.controls['definedData'].value * lengthRemain)>this.originRemain){
      this.isDefineValueError = true
      return
    }else{
      this.isDefineValueError = false
    }
    if (this.trafficType === 'Gói Data') {
      if (this.shareDataForm.controls['definedData'].value % 100 !== 0) {
        this.isValidDataFixed = true;
      } else {
        this.isValidDataFixed = false;
      }
    }
      if((this.trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase())){
      if (this.shareDataForm.controls['definedData'].value % 5 !== 0) {
        this.isValidDataFixed = true;
      } else {
        this.isValidDataFixed = false;
      }
    }
  }

    onHideImport(){
      this.isShowDialogImportByFile = false
    }

    importByFile() {
        let me = this;
        me.isShowDialogImportByFile = true;
        me.observableService.next(CONSTANTS.OBSERVABLE.KEY_INPUT_FILE_VNPT, {});
        me.isShowErrorUpload = false;
    };

    checkDataCondition(item: any): boolean {
        if (this.trafficType === 'Gói Data') {
            return item.data % 100 === 0;
        }
        if ((this.trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase())) {
            return item.data % 5 === 0;
        }
        return true;
    }

    clearFileCallback() {
        this.isShowErrorUpload = false;
    }

    ngOnDestroy() {
        clearInterval(this.interval);
    }

    downloadTemplate() {
        this.shareService.downloadTemplateReceiveInfo();
    }

    onSelectGroup(group) {
        let me = this;
        me.idGroupSelected = group.id;
        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);
        me.dataShareGroupTotal = me.dummyWallet?.find(wallet => wallet.subCode === me.shareDataForm.get('walletValue').value)?.totalRemainingTraffic;
        me.dataShareGroup = 0;
        me.percentShareGroup = 0;
    }

    search(page, limit, sort, params){
        this.pageNumber = page;
        this.pageSize = limit;
        this.sort = sort;
        let me = this;
        let totalQuantity = 0;
        let dataParams = {
            ...params,
            page,
            size: limit,
            sort
        }
        me.messageCommonService.onload();
        this.groupSubService.searchInGroup(Number(me.idGroupSelected), dataParams, (response)=>{
            me.dataSet = {
                content: response.content,
                total: response.totalElements
            }
            // me.dataShareGroupTotal = response.totalElements;
            totalQuantity = Number(totalQuantity) + Number(me.dataShareGroup * response.totalElements);
            this.remainDataTotal = this.originRemain - totalQuantity;
            this.dataShareGroupTotal = this.originRemain - totalQuantity;
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }


    clearSelectedGroup(){
        this.groupSelectedValue = []
    }

    clearSelectedData(){
        this.selectedTableData = []
    }

    onClickSelection(){
        if(!this.isAutoWallet){
            this.messageCommonService.warning(this.tranService.translate("datapool.text.hasntAuto"));
        }
    }

    onHideError(){
        window.location.reload();
    }

    downloadErrorFile() {
        this.exportToExcel(this.FulldataSetError.content);
    }

    exportToExcel(data) {
        // Chuẩn bị dữ liệu và tiêu đề cột
        const header = ['STT', 'SĐT', 'Mô tả'];
        const excelData = data.map((item, index) => [index+1, item.phoneReceipt, item.description]);

        // Tạo sheet và thêm tiêu đề
        const ws: XLSX.WorkSheet = XLSX.utils.aoa_to_sheet([header, ...excelData]);

        ws['!cols'] = [{wch: 5}, {wch :21}, {wch:30}]

        // Bôi đậm tiêu đề
        const headerCells = ['A1', 'B1', 'C1']; // Các ô tiêu đề trong sheet (tương ứng với cột A, B, C)
        headerCells.forEach(cell => {
            if (ws[cell]) {
                ws[cell].s = {
                    font: {
                        bold: true, // Bôi đậm chữ
                    },
                    alignment: {
                        horizontal: 'center', // Căn giữa theo chiều ngang
                        vertical: 'center', // Căn giữa theo chiều dọc
                    },
                };
            }
        });

        // Căn giữa cho các ô dữ liệu
        const rowCount = data.length;
        for (let row = 2; row <= rowCount + 1; row++) {
            for (let col = 0; col < header.length; col++) {
                const cellRef = XLSX.utils.encode_cell({ r: row - 1, c: col });
                if (ws[cellRef]) {
                    ws[cellRef].s = {
                        alignment: {
                            horizontal: 'center', // Căn giữa theo chiều ngang
                            vertical: 'center', // Căn giữa theo chiều dọc
                        },
                    };
                }
            }
        }

        // Tạo workbook và xuất file
        const wb: XLSX.WorkBook = {
            Sheets: { 'Danh sách SĐT chia sẻ lỗi': ws },
            SheetNames: ['Danh sách SĐT chia sẻ lỗi'],
        };

        const excelBuffer: any = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
        this.saveAsExcelFile(excelBuffer, 'Danh_sach_loi_');
    }

    private saveAsExcelFile(buffer: any, fileName: string): void {
        const data: Blob = new Blob([buffer], { type: EXCEL_TYPE });
        FileSaver.saveAs(data, fileName + '_' + formatDateToDDMMYYYYHHMMSS(new Date().getTime()) + EXCEL_EXTENSION);
    }

    pagingDataError(pageNumber, pageSize){
        const startIndex = pageNumber * pageSize;
        const endIndex = startIndex + pageSize;
        this.dataSetError.content = this.FulldataSetError.content.slice(startIndex, endIndex);
        console.log(this.dataSetError)
        this.dataSetError = {...this.dataSetError}
    }

    pageChange(event) {
        this.normalShareTable.first = event.first;
        this.normalShareTable.rows = event.rows;
        this.shareDataForm.controls['pageCurrent'].setValue(event.first/event.rows + 1);
    }

    jumpPage() {
        let pageCurrent = this.shareDataForm.controls.pageCurrent.value;
        const totalPages = Math.ceil(this.shareList.length / this.normalShareTable.rows);
        if (pageCurrent < 1 || pageCurrent > totalPages) {
            return;
        }
        this.normalShareTable.first = (pageCurrent - 1) * this.normalShareTable.rows;
    }

    getMaxPage(){
        if(this.shareList.length % this.normalShareTable.rows == 0){
            return this.shareList.length/this.normalShareTable.rows;
        }else{
            return Math.ceil(this.shareList.length/this.normalShareTable.rows);
        }
    }

    formatNumber(value: number): string {
        return new Intl.NumberFormat('vi-VN').format(value);
    }
}

const EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
const EXCEL_EXTENSION = '.xlsx';

function formatDateToDDMMYYYYHHMMSS(timestamp: number): string {
    const date = new Date(timestamp);

    const dd = String(date.getDate()).padStart(2, '0');
    const MM = String(date.getMonth() + 1).padStart(2, '0'); // Tháng bắt đầu từ 0
    const yyyy = date.getFullYear();
    const HH = String(date.getHours()).padStart(2, '0');
    const mm = String(date.getMinutes()).padStart(2, '0');
    const ss = String(date.getSeconds()).padStart(2, '0');

    return `${dd}${MM}${yyyy}${HH}${mm}${ss}`;
}
