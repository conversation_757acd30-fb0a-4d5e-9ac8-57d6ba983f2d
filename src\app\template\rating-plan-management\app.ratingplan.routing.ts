import { NgModule } from "@angular/core";
import { RouterModule } from "@angular/router";
import { AppRatingPlanListComponent } from "./list-plan/app.ratingplan.list.component";
import { AppRegisterPlanListComponent } from "./list-register-plan/app.registerplan.list.component";
import { CreatePlanComponent } from "./list-plan/create-plan/create-plan.component";
import { UpdatePlanComponent } from "./list-plan/update-plan/update-plan.component";
import { AppRatingPlanDetailComponent } from "./detail-plan/app.ratingplan.detail.component";
import { AppHistoryRegisterplanListComponent } from "./list-history-register-plan/app.history.registerplan.list.component";
import { CONSTANTS } from "src/app/service/comon/constants";
import DataPage from "../../service/data.page";

@NgModule({
    imports: [
        RouterModule.forChild([
            {path: "", component: AppRatingPlanListComponent, data: new DataPage("global.menu.listplan", [CONSTANTS.PERMISSIONS.RATING_PLAN.VIEW_LIST])},
            {path: "registers", component: AppRegisterPlanListComponent, data: new DataPage("global.menu.registerplan", [CONSTANTS.PERMISSIONS.RATING_PLAN_SIM.REGISTER_PLAN])},
            // {path: "detail/:id", component: AppRatingPlanDetailComponent, data: new DataPage("global.menu.detailplan")},
            {path: "registers/history", component: AppHistoryRegisterplanListComponent, data: new DataPage("global.titlepage.historyRegisterPlan", [CONSTANTS.PERMISSIONS.RATING_PLAN_SIM.REGISTER_HISTORY])},
            {path: "create", component: CreatePlanComponent, data: new DataPage("global.titlepage.createRatingPlan", [CONSTANTS.PERMISSIONS.RATING_PLAN.CREATE])},
            {path: "update/:id", component: UpdatePlanComponent, data: new DataPage("global.titlepage.editRatingPlan", [CONSTANTS.PERMISSIONS.RATING_PLAN.UPDATE])}
        ])
    ],
    exports: [RouterModule]
})
export class AppRatingPlanRouting{}
