<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round" >
    <div class="">
        <div class="text-xl font-bold mb-1">{{tranService.translate("global.menu.apnsimlist")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
</div>
<div class="border-round bg-white mt-3 pt-2">
    <div class = "grid mx-4 pt-3" *ngIf="detailApnSim">
        <div class = "col-4" *ngFor="let key of fieldsToDisplay">
            <h5>{{ getTitle(key) }}</h5>
            <p>{{ getContent(key) }}</p>
        </div>
    </div>
    <div class="text-center pb-4">
<!--        <p-button styleClass="p-button-secondary p-button-outlined" (click)="goBack()">{{tranService.translate("global.button.back")}}</p-button>-->
    </div>
</div>

