import {Component, Inject, Injector, OnInit} from "@angular/core";
import {ComponentBase} from "../../../../component.base";
import {GroupSubWalletService} from "../../../../service/group-sub-wallet/GroupSubWalletService";
import {TrafficWalletService} from "../../../../service/datapool/TrafficWalletService";
import {ShareManagementService} from "../../../../service/datapool/ShareManagementService";
import {PhoneInGroup, ShareDetail} from "../../data-pool.type-data";
import {MenuItem} from "primeng/api";
import {FormBuilder, FormControl, FormGroup, Validators} from "@angular/forms";
import {ColumnInfo, OptionTable} from "../../../common-module/table/table.component";
import {checkExistedDynamicListArray, numericMaxLengthValidator} from "../../../common-module/validatorCustoms";
import {isVinaphoneNumber} from "../../../../service/comon/constants";
import {CONSTANTS} from "../../../../service/comon/constants";
import {OptionInputFile} from "../../../common-module/input-file/input.file.component";
import * as Excel from "exceljs";
import * as moment from "moment/moment";
import {saveAs} from "file-saver";
import * as FileSaver from "file-saver";

@Component({
    selector: 'app-edit-group-sub',
    templateUrl: './group-sub-wallet.edit.component.html',
})
export class GroupSubWalletEditComponent extends ComponentBase implements OnInit {

    editGroupForm = new FormGroup({
        groupCode: new FormControl("", [Validators.required,Validators.maxLength(16), Validators.pattern('^[A-Za-z0-9_-]+$')]),
        groupName: new FormControl("",[Validators.required, Validators.maxLength(255), Validators.pattern('^[a-zA-Z0-9\\- _\\u00C0-\\u024F\\u1E00-\\u1EFF]+$')]),
        description: new FormControl("", [Validators.maxLength(255)]),
    });
    items: MenuItem[];
    home: MenuItem;
    idGroup: string = this.route.snapshot.paramMap.get("id");
    groupInfo: {
        groupCode: string | null,
        groupName: string | null,
        description: string | null,
        listSub: ShareDetail[];
    };
    groupInfoAfterSave: {
        groupCode: string | null,
        groupName: string | null,
        description: string | null,
        listSub: ShareDetail[];
    };
    isShowDialogAddFile: boolean = false;
    isShowErrorUpload: boolean = false;
    fileObject: any;
    options: OptionInputFile;
    messageErrorUpload: string| null;
    listGroup: any = [];
    phoneReceiptSelect: string = "";
    isClickAdd: boolean = true;
    phoneList : PhoneInGroup[] = [];
    isValidPhone: boolean = true;
    isShowDialogAddSub: boolean = false;
    isShowDialogEditSub: boolean = false;
    isShowModalDeleteManySub: boolean = false;
    listSubAfterEdit: any[] = [];
    selectItems: Array<{id:number,[key:string]:any}>;
    optionTable: OptionTable;
    pageNumber: number;
    pageSize: number;
    sort: string;
    columns: Array<ColumnInfo>;
    dataSet: {
        content: Array<any>,
        total: number
    };
    searchInfo: {
        value?: string,
    };
    valueSearch: string;
    isExistGroupCode: boolean;
    userInfo: any = {};
    formInstance: any;
    formObject: {
        file: any
    }
    textDescription: string | null;
    invalid: "required" | "maxsize" | "invalidtype" | null;
    constructor(
        injector: Injector,
        private groupSubWalletService: GroupSubWalletService,
        private formBuilder: FormBuilder,
        @Inject(TrafficWalletService) private walletService: TrafficWalletService,
        @Inject(ShareManagementService) private shareService: ShareManagementService,
    ) {
        super(injector);
    }

    sharedEditGroup = new FormGroup({
        shareId: new FormControl(),
        name: new FormControl("",[Validators.maxLength(50)]),
        phone: new FormControl({value:"", disabled: true}, [Validators.required]),
        email: new FormControl("", [Validators.pattern(/^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9]+$/), Validators.maxLength(100)])
    })

    ngOnInit(): void {
        let me = this;
        this.isExistGroupCode = false;
        this.formObject = {
            file: null
        }
        this.formInstance = this.formBuilder.group(this.formObject);
        this.textDescription = this.tranService.translate("global.button.uploadFile");
        this.items = [{ label: this.tranService.translate("global.menu.trafficManagement") }, { label: this.tranService.translate("global.menu.listGroupSub"), routerLink: "/data-pool/group/listGroupSub" }, { label: this.tranService.translate("datapool.label.editGroupShare") }];
        this.home = { icon: 'pi pi-home', routerLink: '/' };
        this.options = {
            type: ['xls','xlsx'],
            messageErrorType: this.tranService.translate("global.message.wrongFileExcel"),
            maxSize: 10,
            unit: "MB",
            required: true,
            isShowButtonUpload: true,
            actionUpload: this.uploadFile.bind(this),
            disabled: false
        }
        me.groupInfo = {
            groupCode: "",
            groupName: "",
            description: "",
            listSub: []
        };
        this.userInfo = this.sessionService.userInfo;
        me.groupInfoAfterSave = {
            groupCode: "",
            groupName: "",
            description: "",
            listSub: []
        };
        me.columns = [
            {
                name: this.tranService.translate("datapool.label.phone"),
                key: "phoneReceipt",
                size: "10%",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("datapool.label.fullName"),
                key: "name",
                size: "25%",
                align: "left",
                isShow: true,
                isSort: false
            },
            {
                name: this.tranService.translate("datapool.label.email"),
                key: "email",
                size: "40%",
                align: "left",
                isShow: true,
                isSort: false,
                isShowTooltip: true,
                style: {
                    display: 'inline-block',
                    maxWidth: '450px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                }
            },
        ]
        me.selectItems = [];
        me.optionTable = {
            hasClearSelected: false,
            hasShowChoose: true,
            hasShowIndex: true,
            hasShowToggleColumn: false,
            action: [
                {
                    icon: "pi pi-pencil",
                    tooltip: this.tranService.translate("global.button.edit"),
                    func: function(id, item){
                        me.isShowDialogEditSub = true;
                        me.sharedEditGroup.get("shareId").setValue(item.id)
                        me.sharedEditGroup.get("name").setValue(item.name)
                        me.sharedEditGroup.get("phone").setValue(item.phoneReceipt)
                        me.sharedEditGroup.get("email").setValue(item.email)
                    },
                    funcAppear(id, item) {
                        if (me.userInfo.type == CONSTANTS.USER_TYPE.CUSTOMER && (item.createdBy == null || item.createdBy != me.userInfo.id)) {
                            return false
                        }
                        return true;
                    }
                },
                {
                    icon: "pi pi-trash",
                    tooltip: this.tranService.translate("global.button.delete"),
                    func: function(id, item){
                        me.deleteItem(0, id, item);
                    },
                }
            ]
        }
        me.searchInfo = {
            value: ""
        };
        me.pageNumber = 0;
        me.pageSize = 10;
        me.sort = "id,desc";
        me.dataSet = {
            content: [],
            total: 0
        }
        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);
        me.getDetail();
        me.getAllGroup();
    }

    onQuickSearch() {
        event.preventDefault();
        let me = this;
        if (me.valueSearch || me.valueSearch === "") {
            me.searchInfo = {
                value: me.valueSearch.trim()
            }
        }
        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);
    }

    search(page, limit, sort, params){
        this.pageNumber = page;
        this.pageSize = limit;
        this.sort = sort;
        let me = this;
        let dataParams = {
            ...params,
            page,
            size: limit,
            sort
        }
        me.messageCommonService.onload();
        this.groupSubWalletService.searchInGroup(Number(me.idGroup), dataParams, (response)=>{
            me.dataSet = {
                content: response.content,
                total: response.totalElements
            }
            me.groupInfo.listSub = response.content;
            // me.groupInfoAfterSave.listSub = response.content;
        }, null, ()=>{
            me.messageCommonService.offload();
        })
        let dataParamsAll = {
            page: 0,
            size: 3000,
            sort
        }
        this.groupSubWalletService.searchInGroup(Number(me.idGroup), dataParamsAll, (response)=>{
            me.groupInfoAfterSave.listSub = response.content;
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }

    // handle submit common
    handleFormSubmission(successCallback: () => void, errorCallback: (error: any) => void) {
        let me = this;
        let dataParams = {
            groupCode: me.editGroupForm.value.groupCode?.trim(),
            groupName: me.editGroupForm.value.groupName?.trim(),
            description: me.editGroupForm.value.description?.trim(),
            listSub: me.groupInfoAfterSave.listSub
        };
        this.messageCommonService.onload();
        this.groupSubWalletService.update(me.idGroup, dataParams,
            (response) => {
                successCallback(); // Call success callback specific to each function
            },
            (error) => {
                if (error.error.error.errorCode === "error.duplicate.value") {
                    me.messageCommonService.error(this.tranService.translate("datapool.error.existedGroupCode"));
                }
                errorCallback(error); // Call error callback specific to each function
            },
            () => {
                me.messageCommonService.offload();
            });
    }

    submitForm() {
        this.handleFormSubmission(
            () => {
                this.messageCommonService.success(this.tranService.translate("global.message.saveSuccess"));
                this.router.navigate(['/data-pool/group/listGroupSub']);
            },
            (error) => {
            }
        );
    }

    saveAddSubToGroup() {
        event.preventDefault();
        let me = this;
        me.groupInfo.listSub.forEach((item) => {

            const exists = me.groupInfoAfterSave.listSub.some(
                (existingItem) => existingItem.phoneReceipt === item.phoneReceipt
            );

            if (!exists) {
                me.groupInfoAfterSave.listSub.push(item);
            } else {
            }
        });
        this.handleFormSubmission(
            () => {
                this.isShowDialogAddSub = false;
                this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
            },
            (error) => {
            }
        );
    }

    addSubToGroup() {
        let me = this;
        me.isShowDialogAddSub = true;
        me.groupInfo.listSub = [];
        me.isClickAdd = true
        me.getListShareInfoCbb.call(this);
    }

    addSubFile() {
        let me = this;
        me.isShowDialogAddFile = true;
    }

    showModalDeleteManySubInGroup() {
        let me = this;
        if(me.selectItems.length === 0) return;
        // me.isShowModalDeleteManySub = true;
        this.messageCommonService.confirm(this.tranService.translate("datapool.button.deleteSub"),
            this.tranService.translate("datapool.message.deleteSub"),{
                ok: () => {
                    let ids = this.selectItems.map(e => e.id);
                    this.groupSubWalletService.deleteMany(ids, (response)=>{
                        me.isShowModalDeleteManySub = false;
                        me.messageCommonService.success(me.tranService.translate("global.message.deleteSuccess"));
                        me.selectItems = []
                    },null , () => {
                        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo)
                        // me.groupInfoAfterSave.listSub = me.groupInfo.listSub
                    })
                }
            })
    }

    // deleteManySubInGroup() {
    //     let me = this;
    //     let ids = this.selectItems.map(e => e.id);
    //     me.messageCommonService.onload();
    //     this.groupSubWalletService.deleteMany(ids, (response)=>{
    //         me.isShowModalDeleteManySub = false;
    //         me.messageCommonService.success(me.tranService.translate("global.message.deleteSuccess"));
    //         me.selectItems = []
    //     },null , () => {
    //         me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo)
    //     })
    // }

    cancelAddSub() {
        let me = this;
        me.isShowDialogAddSub = false;
        me.groupInfo.listSub = [];
        me.phoneReceiptSelect = "";
        me.getListShareInfoCbb.call(this);
    }

    initForm() {
        let me = this;
        me.editGroupForm = new FormGroup({
            groupCode: new FormControl(me.groupInfo.groupCode?.trim(), [Validators.required,Validators.maxLength(16), Validators.pattern('^[A-Za-z0-9_-]+$')]),
            groupName: new FormControl(me.groupInfo.groupName?.trim(),[Validators.required, Validators.maxLength(255), Validators.pattern('^[a-zA-Z0-9\\- _\\u00C0-\\u024F\\u1E00-\\u1EFF]+$')]),
            description: new FormControl(me.groupInfo.description?.trim(), [Validators.maxLength(255)]),
        });
    }

    getDetail () {
        let me = this;
        me.groupSubWalletService.getDetail(Number(me.idGroup), (response) => {
            me.groupInfo = {
                ...response
            };
            me.groupInfoAfterSave = {
                ...response
            };
            me.initForm();
        })
    }

    checkValidAdd(){
        this.isClickAdd = true
        if (!this.phoneList.find(dta => dta.phoneReceipt.toString() === this.phoneReceiptSelect)) {
            this.isClickAdd = false
        } else {
            this.isClickAdd = true
        }
        if(!this.phoneReceiptSelect){
            this.isClickAdd = true
        }

        const regex = /^0[0-9]{9,10}$/;
        const inputValue = this.phoneReceiptSelect;
        this.isValidPhone = regex.test(inputValue);
    }

    addPhoneTable (value, data) {
        let me = this;
        const listPhoneInRange = me.groupInfo.listSub.map(e => e.phoneReceipt);
        if(value){
            if (me.groupInfo.listSub.length >= CONSTANTS.SHARE_GROUP.LIMIT_ADD) {
                me.messageCommonService.error(me.tranService.translate("datapool.message.maximumSubAdd"));
            } else if (me.groupInfo.listSub.length < CONSTANTS.SHARE_GROUP.LIMIT_ADD && me.groupInfoAfterSave.listSub.length + me.groupInfo.listSub.length >= CONSTANTS.SHARE_GROUP.LIMIT) {
                me.messageCommonService.error(me.tranService.translate("datapool.message.maximumSubDisplay"));
            } else if (listPhoneInRange.includes(data)) {
                me.messageCommonService.error(me.tranService.translate("datapool.message.dublicateShareInfo"));
            } else {
                me.groupInfo.listSub.unshift({...value, idGroup: Number(me.idGroup)});
                // me.groupInfoAfterSave.listSub.push({...value, idGroup: Number(me.idGroup)});
            }
        } else {
            let pushData: ShareDetail = {
                idGroup: Number(me.idGroup),
                phoneReceipt: data,
                name: value?.name || "",
                email: value?.email || "",
            }
            if (me.groupInfo.listSub.length >= CONSTANTS.SHARE_GROUP.LIMIT_ADD) {
                me.messageCommonService.error(me.tranService.translate("datapool.message.maximumSubAdd"));
            } else if (me.groupInfo.listSub.length < CONSTANTS.SHARE_GROUP.LIMIT_ADD && me.groupInfoAfterSave.listSub.length + me.groupInfo.listSub.length >= CONSTANTS.SHARE_GROUP.LIMIT) {
                me.messageCommonService.error(me.tranService.translate("datapool.message.maximumSubDisplay"));
            } else if (listPhoneInRange.includes(data)) {
                me.messageCommonService.error(me.tranService.translate("datapool.message.dublicateShareInfo"));
            } else {
                let exists = me.groupInfo.listSub.some(item => item.phoneReceipt === data);
                if (!exists) {
                    me.groupInfo.listSub.unshift(pushData);
                    // me.groupInfoAfterSave.listSub.push(pushData);
                } else {
                    me.messageCommonService.warning(me.tranService.translate("datapool.message.dublicateShareInfo"))
                }

            }
        }
        me.isClickAdd = true
    }

    getAllGroup () {
        let me = this;
        me.messageCommonService.onload();
        me.groupSubWalletService.getAllGroup((response) => {
            me.listGroup = response;
        }, null, () => {
            me.messageCommonService.offload()
        })
    }

    addPhone(data){
        let me = this;
        console.log("data: " + data)
        if(!data){
            return;
        }
        me.isClickAdd = false
        const value = me.phoneList.find(dta => dta.phoneReceipt === data);
        const phone = String(data)?.replace(/^0/,"84");

        //check trước khi chạy các hàm khác
        let exists = me.groupInfo.listSub.some(item => item.phoneReceipt.toString() === phone);
        if (me.groupInfo.listSub.length >= CONSTANTS.SHARE_GROUP.LIMIT_ADD) {
            me.messageCommonService.error(me.tranService.translate("datapool.message.maximumSubAdd"));
            return;
        } else if (me.groupInfo.listSub.length < CONSTANTS.SHARE_GROUP.LIMIT_ADD && me.groupInfoAfterSave.listSub.length + me.groupInfo.listSub.length >= CONSTANTS.SHARE_GROUP.LIMIT) {
            me.messageCommonService.error(me.tranService.translate("datapool.message.maximumSubDisplay"));
            return;
        } else if (exists){
            console.log("vào check trc")
            me.messageCommonService.warning(me.tranService.translate("datapool.message.dublicateShareInfo"))
            return;
        }

        if (value?.idGroup) {
            me.messageCommonService.error(me.tranService.translate('datapool.message.duplicateSub', {data: data, groupName: value?.groupName}));
        } else {
            me.addPhoneTable(value, data);
            me.phoneReceiptSelect = "";
            /**
             * UAT 2.4 issue 31
             * Khi add số thuê bao được chia sẻ, nếu đã có trong danh sách thì bỏ qua check số đó là thuê bao vinaphone hay không, trong các trường hợp sau:
             * - Chia sẻ thường
             * - Nhóm chia sẻ tự động > thêm sdt chia sẻ tự động
             * - Thêm thuê bao vào nhóm
             * - icon chia sẻ ở Danh sách ví
             */
            // me.messageCommonService.onload()
            // me.walletService.checkParticipant({phoneNumber : phone},
            //     (response)=>{
            //         if (value?.idGroup) {
            //             me.messageCommonService.error(`Thuê bao đang thuộc nhóm "${value.groupName}"`);
            //         } else if(response.error_code === "0" && (response.result === "02" || response.result === "11") && !value?.idGroup){
            //             me.addPhoneTable(value, data);
            //             me.phoneReceiptSelect = "";
            //         } else if(response.error_code === "0" && response.result === "0" && !value?.idGroup){
            //             if(isVinaphoneNumber(data)){
            //                 me.addPhoneTable(value, data);
            //                 me.phoneReceiptSelect = "";
            //             }else{
            //                 me.messageCommonService.error(me.tranService.translate("datapool.message.notValidPhone"))
            //             }
            //         }else{
            //             me.messageCommonService.error(me.tranService.translate("datapool.message.notValidPhone"))
            //         }
            //     },
            //     null,()=>{
            //         me.messageCommonService.offload();
            //     })
        }
    }

    addPhoneNotInSelect(phone) {
        let me = this;

        if(!phone){
            return;
        }
        me.isClickAdd = false
        const value = me.phoneList.find(dta => dta.phoneReceipt === phone);
        me.groupSubWalletService.checkPhoneBelongGroup({phoneNumber: phone}, (response)=>{
            const phoneValid = String(phone)?.replace(/^0/,"84");
            //check trước khi chạy các hàm khác
            let exists = me.groupInfo.listSub.some(item => item.phoneReceipt.toString() === phoneValid);
            if (me.groupInfo.listSub.length >= CONSTANTS.SHARE_GROUP.LIMIT_ADD) {
                me.messageCommonService.error(me.tranService.translate("datapool.message.maximumSubAdd"));
                return;
            } else if (me.groupInfo.listSub.length < CONSTANTS.SHARE_GROUP.LIMIT_ADD && me.groupInfoAfterSave.listSub.length + me.groupInfo.listSub.length >= CONSTANTS.SHARE_GROUP.LIMIT) {
                me.messageCommonService.error(me.tranService.translate("datapool.message.maximumSubDisplay"));
                return;
            } else if (exists){
                console.log("vào check trc")
                me.messageCommonService.warning(me.tranService.translate("datapool.message.dublicateShareInfo"))
                return;
            }
            if (response) {
                me.messageCommonService.error(me.tranService.translate('Thuê bao này đã thuộc nhóm khác'));
            } else {
                if (value?.idGroup) {
                    me.messageCommonService.error(`Thuê bao đang thuộc nhóm "${value.groupName}"`);
                } else {
                    me.addPhoneTable(value, phone);
                }
                /**
                 * bỏ check số vina
                 */
                // me.messageCommonService.onload()
                // me.walletService.checkParticipant({phoneNumber : phoneValid},
                //     (response)=>{
                //         if (value?.idGroup) {
                //             me.messageCommonService.error(`Thuê bao đang thuộc nhóm "${value.groupName}"`);
                //         } else if(response.error_code === "0" && (response.result === "02" || response.result === "11") && !value?.idGroup){
                //             me.addPhoneTable(value, phone);
                //             me.phoneReceiptSelect = "";
                //         } else if(response.error_code === "0" && response.result === "0" && !value?.idGroup){
                //             if(isVinaphoneNumber(phone)){
                //                 me.addPhoneTable(value, phone);
                //                 me.phoneReceiptSelect = "";
                //             }else{
                //                 me.messageCommonService.error(me.tranService.translate("datapool.message.notValidPhone"))
                //             }
                //         }else{
                //             me.messageCommonService.error(me.tranService.translate("datapool.message.notValidPhone"))
                //         }
                //     },
                //     null,()=>{
                //         me.messageCommonService.offload();
                //     })
                // me.addPhoneTable(value, phone);
                me.phoneReceiptSelect = "";
            }
        },null ,null );
    }

    getListShareInfoCbb(params, callback) {
        return this.shareService.getListShareInfoCbb(params, (response)=>{
            this.phoneList = response.content;
            callback(response)
        });
    }

    changeDataName(event, i){
        const shareValue = event.target.value
        this.groupInfo.listSub[i].name = shareValue
    }

    changeDataMail(event, i){
        const shareValue = event.target.value
        this.groupInfo.listSub[i].email = shareValue
        this.isAllEmailsValid();
    }
    // Hàm kiểm tra xem tất cả email trong listSub có hợp lệ không
    isAllEmailsValid(): boolean {
        return this.groupInfo.listSub.every(item => !this.isMailInvalid(item.email));
    }

    deleteItem(i, idSub, dataSub){
        this.messageCommonService.confirm(this.tranService.translate("datapool.button.deleteSub"),
            this.tranService.translate("datapool.message.deleteSub"),{
                ok: () => {
                    const data = this.groupInfo.listSub[i]?.data
                    const phoneToDelete = this.groupInfo.listSub[i]?.phoneReceipt; // Lấy phoneReceipt để xóa
                    if(data){
                        this.groupInfo.listSub[i].data = null
                    }
                    if (idSub && (data || dataSub)) {
                        this.messageCommonService.onload();
                        this.groupSubWalletService.deleteSubInGroup(idSub, (response) => {
                            this.selectItems = this.selectItems.filter(e => e.id !== dataSub.id);
                            this.messageCommonService.success(this.tranService.translate("global.message.deleteSuccess"));
                            this.groupInfoAfterSave.listSub = this.groupInfoAfterSave.listSub.filter(item => item.phoneReceipt !== phoneToDelete)
                            this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo)
                        }, null, ()=>{
                            this.messageCommonService.offload();
                        })
                    } else {
                        this.groupInfo.listSub = this.groupInfo.listSub.filter((item,index) => index != i);
                        this.groupInfoAfterSave.listSub = this.groupInfoAfterSave.listSub.filter(item => item.phoneReceipt !== phoneToDelete);
                        this.messageCommonService.success(this.tranService.translate("global.message.deleteSuccess"));
                    }
                }
            })
    }

    isMailInvalid(email:string){
        if (!email){
            return false
        }
        // const pattern:RegExp = /^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$/
        const pattern: RegExp = /^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/;
        return !pattern.test(email);
    }

    submitEditForm(){
        Object.keys(this.sharedEditGroup.controls).forEach(key => {
            const control = this.sharedEditGroup.get(key);
            if (control.invalid) {
                console.log('Field:', key, 'is invalid. Errors:', control.errors);
            }
        });
        this.messageCommonService.onload()
        this.shareService.updateShared(this.sharedEditGroup.value, (response)=>{
            this.closeForm();
            this.messageCommonService.success(this.tranService.translate("global.message.saveSuccess"))
            this.search(this.pageNumber,this.pageSize,this.sort, this.searchInfo)
        },()=>{
            console.log("Error")
        },()=>{ this.messageCommonService.offload() })
    }

    closeForm(){
        this.isShowDialogEditSub = false;
    }
    onNameBlur() {
        let me = this;
        let formattedValue = this.editGroupForm.get('groupName').value;
        formattedValue = formattedValue.trim().replace(/\s+/g, ' ');
        this.editGroupForm.get('groupName').setValue(formattedValue);
    }
    onCodeBlur() {
        let me = this;
        let value = this.editGroupForm.get('groupCode').value;
        this.groupSubWalletService.checkExistGroupCode({groupCode: value, id: Number(me.idGroup) }, (res) => {
            if (res == true) {
                this.isExistGroupCode = true;
            } else {
                this.isExistGroupCode = false
            }
        })
    }

    clearFileCallback(){
        this.isShowErrorUpload = false;
    }

    uploadFile(objectFile: any) {
        let me = this;
        if(objectFile.size >= 1048576){
            this.messageCommonService.error("Dung lượng file vượt quá dung lượng tối đa")
            return
        }
        let dataParams = {
            id: me.idGroup,
            groupCode: me.editGroupForm.value.groupCode.trim(),
            groupName: me.editGroupForm.value.groupName.trim(),
            description: me.editGroupForm.value.description ? me.editGroupForm.value.description.trim() : "",
            listSub: me.groupInfoAfterSave.listSub
        };
        me.messageCommonService.onload();
        this.groupSubWalletService.uploadFile(objectFile, dataParams, async (response) => {
            const createdId = response.headers.get('CREATED-ID');
            const dataError = [];
            const errorMessageCode = {
                '10': 'Tham số đầu vào không hợp lệ',
                '400': response => dataError.push(response?.headers?.get('cause')),
                '401': 'Kích thước file vượt quá giới hạn',
                '402': 'File tải lên thừa cột',
                '403': 'File tải lên thiếu cột',
                '404': 'File tải lên trùng cột',
                '405': 'Không thể lấy thông tin hàng từ file excel',
                '501': response => dataError.push(response?.headers?.get('Content-Disposition')),
                '430': 'Sai định dạng file mẫu',
                '440': 'File vượt quá 3000 SĐT',
                '450': 'Tổng số điện thoại trong nhóm và file đã vượt quá giới hạn 3000 SĐT. Vui lòng kiểm tra lại dữ liệu!'
            };

            if(createdId){
                me.messageCommonService.success('Import người được chia sẻ thành công');
                me.isShowDialogAddFile = false;
                me.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
            }

            if (response?.headers?.get('cause') === '0') {

            } else {
                me.isShowErrorUpload = true;
                const errorMessage = errorMessageCode[response?.headers?.get('cause')] || 'Lỗi không xác định';
                if (typeof errorMessage === 'function') {
                    errorMessage(response);
                    if (!response?.body) {
                        const fileName = response?.headers?.get('Content-Disposition');
                        const workbook = new Excel.Workbook();
                        const buf = await workbook.xlsx.writeBuffer();
                        const spliceFileName = fileName.substring(0, fileName.length - 5);
                        const exportFileName = ''.concat(spliceFileName, '_Danh sách lỗi_', moment().format('DDMMYYYYHHMMss'));
                        // download the processed file
                        await saveAs(new Blob([buf]), `${exportFileName}.xlsx`);
                    } else {
                        const dateMoment = moment().format('DDMMYYYYHHmmss');
                        const name = (objectFile.name || objectFile.fileName).split('.');
                        me.exportFile(response?.body, `${name[0]}_Danh sách lỗi_${dateMoment}`, '');
                        me.messageCommonService.error(me.tranService.translate('datapool.text.downloadErrorMessage'), null, 10000)
                    }
                } else {
                    me.messageCommonService.error(errorMessage);
                }
            }

        },null,()=>{
            this.messageCommonService.offload()
        })
    }
    exportFile = (bytes, fileName, fileType) => {

        const file = new Blob([bytes], {
            type: fileType || 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        });
        FileSaver.saveAs(file, fileName);
    };

    downloadTemplate(){
        this.groupSubWalletService.downloadTemplate();
    }
    changeFile(event){
        let file = event.target.files[0];
        this.fileObject = file;
        if(this.fileObject == null){
            this.textDescription = this.tranService.translate("global.button.uploadFile");
            this.checkValid();
            return;
        }
        let filename = file.name;
        let filesize = Math.round(file.size/1024);
        let suffix = "KB";
        if(filesize/1024 > 2){
            filesize = Math.round(filesize/1024);
            suffix = "MB";
        }
        this.textDescription = `${filename} ${this.utilService.convertNumberToString(filesize)}(${suffix})`
        this.checkValid();
    }


    resetFile(){
        this.formObject.file = null;
        this.textDescription = this.tranService.translate("global.button.uploadFile");
        this.fileObject = null;
        this.checkValid();
    }

    checkValid(){
        this.invalid = null;
        if(this.fileObject){
            if(this.options.type){
                let extension = this.fileObject.name.substring(this.fileObject.name.lastIndexOf(".")+1, this.fileObject.name.length);
                if(!this.options.type.includes(extension)){
                    this.invalid = "invalidtype";
                }
            }
            if(this.options.maxSize && this.invalid == null){
                let comparesize = this.options.maxSize;
                if(this.options.unit == "KB"){
                    comparesize = comparesize * 1024;
                }else if(this.options.unit == "MB"){
                    comparesize = comparesize * 1024 * 1024;
                }else if(this.options.unit == "GB"){
                    comparesize = comparesize * 1024 * 1024 * 1024;
                }
                if(this.fileObject.size > comparesize){
                    this.invalid = "maxsize";
                }
            }
        }else{
            if(this.options.required){
                this.invalid = "required";
            }
        }
    }

    reset(){
        this.formObject.file = null;
        this.fileObject = null;
        this.invalid = null;
        this.options.disabled = false;
    }

    upload(){
        this.options.actionUpload(this.fileObject);
    }

    protected readonly CONSTANTS = CONSTANTS;
}
