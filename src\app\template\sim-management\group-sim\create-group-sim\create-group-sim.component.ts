import { Observable } from 'rxjs';
import { Component, Inject, Injector, OnInit } from '@angular/core';
import { AbstractControl, AsyncValidatorFn, FormControl, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { MenuItem } from 'primeng/api';
import { TranslateService } from 'src/app/service/comon/translate.service';
import { CustomerService } from 'src/app/service/customer/CustomerService';
import { GroupSimService } from 'src/app/service/group-sim/GroupSimService';
import { debounceTime, switchMap, map } from 'rxjs/operators';
import { take } from 'rxjs/operators';
import { MessageCommonService } from 'src/app/service/comon/message-common.service';
import { CONSTANTS } from 'src/app/service/comon/constants';
import { AccountService } from 'src/app/service/account/AccountService';
import { DebounceInputService } from 'src/app/service/comon/debounce.input.service';
import { ComponentBase } from 'src/app/component.base';
import { ComboLazyControl } from 'src/app/template/common-module/combobox-lazyload/combobox.lazyload';
import {ContractService} from "../../../../service/contract/ContractService";
import {el} from "@fullcalendar/core/internal-common";
interface Customer {
  id: string;
  name: string;
  code:string;
}

@Component({
  selector: 'app-create-group-sim',
  templateUrl: './create-group-sim.component.html',
  // styleUrls: ['./create-group-sim.component.scss']
})
export class CreateGroupSimComponent extends ComponentBase implements OnInit {
  userType: number;
  myProvince: any;
  listCustomer: Array<any>;
  customers: Customer[] | undefined;
  provinces: Array<any>;
  listContract: Array<any>;
  selectedCustomer: Customer | undefined;
  customerCode: any;
  customer: any;
  contractCode: any;
  userTypes = CONSTANTS.USER_TYPE;

  isDisableSave = false;

  labelBtnSave: string = this.tranService.translate("groupSim.label.buttonSave");
  labelBtnCancel: string = this.tranService.translate("groupSim.label.buttonCancel");
  labelPlaceholderCustomer: string = this.tranService.translate("groupSim.placeHolder.customer");
  labelPlaceholderContract: string = this.tranService.translate("groupSim.placeHolder.contractCode");
  placeHolderGroupKey:string = this.tranService.translate("groupSim.placeHolder.groupKey")
  placeHolderGroupName:string = this.tranService.translate("groupSim.placeHolder.groupName")
  placeHolderDescription: string = this.tranService.translate("groupSim.placeHolder.description")
  items: MenuItem[];
  home: MenuItem
  groupScope: number;
  groupScopeObjects: any;
  isGroupKeyExists: boolean = false;
  comboCustomerControl: ComboLazyControl = new ComboLazyControl();
  paramSearchCustomer = {};
  comboSelectContracCodeControl: ComboLazyControl = new ComboLazyControl();

    constructor(injector: Injector, private groupSimService: GroupSimService, private accountService: AccountService, private customerService: CustomerService){
    super(injector);
  }

  getValueLabel(option: Customer): string {
    return `${option.code} - ${option.name}`;
  }


  customCharacterValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const value = control.value;
      const isValid = /^[a-zA-Z0-9 \-_,\s\u00C0-\u1EF9]*$/.test(value);
      return isValid ? null : { 'invalidCharacters': { value } };
    };
  }

  customCodeCharacterValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const value = control.value;
      const isValid = /^[a-zA-Z0-9\-_]*$/.test(value);
      return isValid ? null : { 'invalidCharacters': { value } };
    };
  }

  checkExisted(query: {}): Observable<number> {
    return new Observable(observer => {
      this.groupSimService.groupkeyCheckExisted({},query, (response) => {
        observer.next(response);
        observer.complete();
      });
    });
  }



  /**
   * ^[a-zA-Z0-9 .\-_,\s\u00C0-\u1EF9]*$ cho biết những kí tự được phép
   *    \u00C0-\u1EF9 là range của Vietnamese'Unicode characters
   *    a-zA-Z0-9 cho phép kí tự chữ và số
   *    .\-_, cho phép _ và - còn \s cho phép blankspace
   */

  createGroupForm: any;

  submitForm(){
    let dataParams = this.createGroupForm.value;
    dataParams['scope'] = this.groupScope;
    dataParams["customerCode"] = this.customer?.customerCode;
    if(this.groupScope == CONSTANTS.GROUP_SCOPE.GROUP_CUSTOMER){
      dataParams["provinceCode"] = this.customer.provinceCode;
      if(this.userType == CONSTANTS.USER_TYPE.CUSTOMER) {
          dataParams["contractCode"] = this.contractCode.contractCode;
      }
    }
    let me = this;
    this.messageCommonService.onload()
    this.groupSimService.createSimGroup({},dataParams,{},(response)=>{
      me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"))
      this.router.navigate(['/sims/group']);
    }, null, ()=>{
      me.messageCommonService.offload();
    })
  }

  ngOnInit(){
    let me = this;
      me.listContract = []
      me.contractCode = ""
    this.userType = this.sessionService.userInfo.type;
    this.groupScope = parseInt(this.route.snapshot.queryParams["type"]);
    this.groupScopeObjects = CONSTANTS.GROUP_SCOPE;
    if(this.sessionService.userInfo.type != CONSTANTS.USER_TYPE.ADMIN){
        this.paramSearchCustomer = {
          provinceCode: this.sessionService.userInfo.provinceCode
        }
    }
    this.accountService.getListProvince((response)=>{
      this.provinces = response.map(el => {
        if(el.code == me.sessionService.userInfo.provinceCode){
          me.myProvince = el;
        }
        return {
          ...el,
          display: `${el.name} - ${el.code}`
        }
      })
    })
    this.items = [{ label: this.tranService.translate("global.menu.simmgmt") }, { label: this.tranService.translate("groupSim.breadCrumb.group"), routerLink: '/sims/group' }, { label: this.tranService.translate("groupSim.breadCrumb.create") }];
    this.home = { icon: 'pi pi-home', routerLink: '/' };

    this.createGroupForm = new FormGroup({
      groupKey: new FormControl("", [Validators.required,Validators.maxLength(16), this.customCodeCharacterValidator()]),
      name: new FormControl("", [Validators.required, Validators.maxLength(255), this.customCharacterValidator()]),
      description: new FormControl("", [Validators.maxLength(255)]),
      provinceCode: new FormControl(this.sessionService.userInfo.provinceCode, this.groupScope == CONSTANTS.GROUP_SCOPE.GROUP_PROVINCE ? [Validators.required] : []),
        // contractCode: new FormControl({value:"", disabled:true}, this.groupScope == CONSTANTS.GROUP_SCOPE.GROUP_CUSTOMER ? [Validators.required] : []),
      // customerCode: new FormControl("", this.groupScope == CONSTANTS.GROUP_SCOPE.GROUP_CUSTOMER ? [Validators.required] : [])
    });
    this.customerCode = null;
  }

  checkExistGroupKey(){
    this.isGroupKeyExists = false;
    let me = this;
    this.debounceService.set("groupKey", this.groupSimService.groupkeyCheckExisted.bind(this.groupSimService,{},{query: this.createGroupForm.value["groupKey"]},(response)=>{
      me.isGroupKeyExists = response == 1;
    }));
  }

    onChangeCustomerCode(event: any) {
      let me = this;
      me.listContract = []
        me.contractCode = null
      if (event != null){
          me.messageCommonService.onload();
          me.customerService.getContractByCustomer(event.id, (response) => {
              me.listContract = response;
              setTimeout(function(){
                  me.comboSelectContracCodeControl.reload();
              })
              me.messageCommonService.offload();
          })
      }
      //
      // if(this.customer){
      //     this.createGroupForm.get("contractCode").enable({emitEvent:false})
      // }else{
      //     this.createGroupForm.get("contractCode").setValue("")
      //     this.createGroupForm.get("contractCode").disable({emitEvent:false})
      // }
    }

}
