<style>
    /* .col-3{
        padding: 10px;
    } */
</style>

<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{this.tranService.translate("global.menu.apiLogs")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
</div>

<form *ngIf="checkAuthen([CONSTANTS.PERMISSIONS.ACCOUNT.SEARCH_LOG_API])" [formGroup]="formSearchAPI" (ngSubmit)="onSubmitSearch()" class="pt-3 pb-2 vnpt-field-set">
    <p-panel [toggleable]="true" [header]="tranService.translate('global.text.filter')">
        <div class="grid search-grid-3">
            <!-- client id -->
            <div class="col-3">
                <span class="p-float-label">
                    <vnpt-select
                        [control]="controlComboSelectCustomer"
                        class="w-full"
                        [(value)]="searchInfo.userName"
                        [placeholder]="tranService.translate('apiLog.label.clientID')"
                        objectKey="account"
                        paramKey="username"
                        keyReturn="username"
                        displayPattern="${username}"
                        typeValue="primitive"
                        [isMultiChoice]="false"
                        [paramDefault]="paramSearchCustomer"
                    ></vnpt-select>
                </span>
            </div>
            <!-- full name -->
            <div class="col-3">
                <span class="p-float-label">
                    <vnpt-select
                        [control]="controlComboSelectCustomer"
                        class="w-full"
                        [(value)]="searchInfo.fullName"
                        [placeholder]="tranService.translate('apiLog.label.fullName')"
                        objectKey="account"
                        paramKey="fullName"
                        keyReturn="fullName"
                        displayPattern="${fullName}"
                        typeValue="primitive"
                        [isMultiChoice]="false"
                        [paramDefault]="paramSearchCustomer"
                    ></vnpt-select>
                </span>
            </div>
            <!-- email -->
            <div class="col-3">
                <span class="p-float-label">
                    <vnpt-select
                        [control]="controlComboSelectCustomer"
                        class="w-full"
                        [(value)]="searchInfo.email"
                        [placeholder]="tranService.translate('apiLog.label.email')"
                        objectKey="account"
                        paramKey="email"
                        keyReturn="email"
                        displayPattern="${email}"
                        typeValue="primitive"
                        [isMultiChoice]="false"
                        [paramDefault]="paramSearchCustomer"
                    ></vnpt-select>
                </span>
            </div>
            <!-- danh sách module -->
            <div class="col-3">
                <span class="p-float-label">
                    <p-dropdown styleClass="w-full"
                                [showClear]="true" [filter]="true" filterBy="value"
                                [autoDisplayFirst]="false"
                                [(ngModel)]="searchInfo.moduleName"
                                formControlName="moduleName"
                                [options]="listModule"
                                optionLabel="value"
                                optionValue="value"
                                (onChange)="onChangeModule()"
                                [emptyFilterMessage]="tranService.translate('global.text.nodata')"
                    ></p-dropdown>
                    <label>{{tranService.translate("apiLog.label.module")}}</label>
                </span>
            </div>
            <!-- date from -->
            <div class="col-3">
                <span class="p-float-label">
                    <p-calendar
                        styleClass="w-full"
                        id="fromDate"
                        [(ngModel)]="searchInfo.fromDate"
                        formControlName="fromDate"
                        [showIcon]="true"
                        [showClear]="true"
                        [minDate]="minDate" [maxDate]="maxDate"
                        dateFormat="dd/mm/yy"
                    ></p-calendar>
                    <label class="label-calendar">{{tranService.translate("apiLog.label.fromDate")}}</label>
                </span>
            </div>
            <!-- to date-->
            <div class="col-3">
                <span class="p-float-label">
                    <p-calendar styleClass="w-full"
                                id="toDate"
                                [(ngModel)]="searchInfo.toDate"
                                formControlName="toDate"
                                [showIcon]="true"
                                [showClear]="true"
                                [minDate]="minDate" [maxDate]="maxDate"
                                dateFormat="dd/mm/yy"

                    />
                    <label class="label-calendar">{{tranService.translate("apiLog.label.toDate")}}</label>
                </span>
            </div>
            <!-- API  -->
            <div class="col-3">
                <span class="p-float-label">
                     <p-dropdown styleClass="w-full"
                                 [showClear]="true" [filter]="true" filterBy="name"
                                 id="status" [autoDisplayFirst]="false"
                                 [(ngModel)]="searchInfo.methodName"
                                 formControlName="methodName"
                                 [options]="listApi"
                                 optionLabel="name"
                                 optionValue="name"
                                 [emptyFilterMessage]="tranService.translate('account.label.status')"
                     ></p-dropdown>
                    <label htmlFor="status">{{tranService.translate("apiLog.label.api")}}</label>
                </span>
            </div>


            <div class="col-3 pb-0">
                <p-button icon="pi pi-search"
                          styleClass="p-button-rounded p-button-secondary p-button-text button-search"
                          type="submit"
                ></p-button>
            </div>
        </div>
    </p-panel>
</form>
<table-vnpt
    [fieldId]="'id'"
    [(selectItems)]="selectItems"
    [columns]="columns"
    [dataSet]="dataSet"
    [options]="optionTable"
    [loadData]="search.bind(this)"
    [pageNumber]="pageNumber"
    [pageSize]="pageSize"
    [sort]="sort"
    [params]="searchInfo"
    [labelTable]="this.tranService.translate('apiLog.label.list')"
></table-vnpt>

