import { Component, Inject, Injector, inject } from '@angular/core';
import { MenuItem } from 'primeng/api';
import { ColumnInfo, OptionTable } from '../../common-module/table/table.component';
import { CONSTANTS } from 'src/app/service/comon/constants';
import { ContractService } from 'src/app/service/contract/ContractService';
import { ComponentBase } from 'src/app/component.base';
import { SimService } from 'src/app/service/sim/SimService';

@Component({
  selector: 'app-contract-management',
  templateUrl: './contract-management.component.html',
//   styleUrls: ['./contract-management.component.scss']
})
export class ContractManagementComponent extends ComponentBase {
  buttonAdd: string =this.tranService.translate("groupSim.label.buttonAdd");
    items: MenuItem[];
    home: MenuItem
    searchInfo: {
        centerCode?:string,
        paymentName?: string,
        contactPhone?: string,
        contractor?: string,
        contractCode?:string,
        customerCode?:string,
        customerName?:string,
        loggable? : boolean | null
    };
    dataStore:any[]
    columns: Array<ColumnInfo>;
    dataSet: {
        content: Array<any>,
        total: number
    };
    columsSimList: Array<ColumnInfo>;
    dataSetSimList: {
        content: Array<any>;
        total: number;
    }
    // dataStore: Array<any>;
    selectItems: Array<any>;
    selectItemsSimList: Array<any>;
    optionTable: OptionTable;
    optionTableSimList: OptionTable
    pageNumber: number;
    pageSize: number;
    sort: string;
    pageNumberSimList: number;
    pageSizeSimList: number;
    sortSimList: string
    // tranService: TranslateService;
    constructor(@Inject(ContractService) private contractService:ContractService,
                @Inject(SimService) private simService:SimService, injector: Injector) {
        super(injector)
    }
    simlistheader = this.tranService.translate("contract.label.headerModal");
    isShowSimList:boolean=false
  ngOnInit(){
    let me = this;
        this.items = [{ label: this.tranService.translate(`global.menu.simmgmt`) }, { label: this.tranService.translate("contract.label.title") },];

        this.home = { icon: 'pi pi-home', routerLink: '/' };

        this.searchInfo = {};

        this.columns = [{
            name: this.tranService.translate("contract.label.contractCode"),
            key: "contractCode",
            size: "300px",
            align: "left",
            isShow: true,
            isSort: true,
            style:{
                cursor: "pointer",
                color: "var(--mainColorText)"
            },
            funcGetRouting(item) {
                return [`/sims`, {contractCode:item.contractCode, searchPanelCollapsRoute: 'false'}]
            },
            // funcClick(id, item) {
            //     me.openSimListModal(id, item);
            // },
        },
        {
            name: this.tranService.translate("contract.label.customerCode"),
            key: "customerCode",
            size: "175px",
            align: "left",
            isShow: true,
            isSort: true,
        },
        {
            name: this.tranService.translate("contract.label.customerName"),
            key: "customerName",
            size: "250px",
            align: "left",
            isShow: true,
            isSort: true
        },
        {
            name: this.tranService.translate("contract.label.contractor"),
            key: "contractor",
            size: "250px",
            align: "left",
            isShow: true,
            isSort: true,
            className: "white-space-normal"
        },
        {
            name: this.tranService.translate("contract.label.contractDate"),
            key: "contractDate",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: true
        },
        {
            name: this.tranService.translate("contract.label.centerCode"),
            key: "centerCode",
            size: "250px",
            align: "left",
            isShow: true,
            isSort: true,
            className: "white-space-normal"
        },
        {
            name: this.tranService.translate("contract.label.contactPhone"),
            key: "contactPhone",
            size: "150px",
            align: "left",
            isShow: false,
            isSort: true
        },{
            name: this.tranService.translate("contract.label.contactAddress"),
            key: "contactAddress",
            size: "150px",
            align: "left",
            isShow: false,
            isSort: true
        },{
            name: this.tranService.translate("contract.label.paymentName"),
            key: "paymentName",
            size: "150px",
            align: "left",
            isShow: false,
            isSort: true
        },{
            name: this.tranService.translate("contract.label.paymentAddress"),
            key: "paymentAddress",
            size: "150px",
            align: "left",
            isShow: false,
            isSort: true
        },{
            name: this.tranService.translate("contract.label.routeCode"),
            key: "routeCode",
            size: "150px",
            align: "left",
            isShow: false,
            isSort: true
        },{
            name: this.tranService.translate("contract.label.customerBirthday"),
            key: "birthday",
            size: "150px",
            align: "left",
            isShow: false,
            isSort: true,
            funcConvertText(value){
                return me.utilService.convertLongDateToString(value);
            }
        }];

        this.optionTable = {
            hasClearSelected:false,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: true,
        }
        this.pageNumber = 0;
        this.pageSize= 10;
        this.sort = "contractCode,asc"

        this.columsSimList = [{
            name: this.tranService.translate("sim.label.sothuebao"),
            key: "msisdn",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: false,
            style:{
                cursor: "pointer",
             color: "var(--mainColorText)"
            },
            funcGetRouting(item) {
                return [`/sims/detail/${item.msisdn}`]
            },
        },{
            name: this.tranService.translate("sim.label.imsi"),
            key: "imsi",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: false
        },
        // {
        //     name: this.tranService.translate("sim.label.iccid"),
        //     key: "iccid",
        //     size: "150px",
        //     align: "left",
        //     isShow: true,
        //     isSort: false
        // },
        {
            name: this.tranService.translate("sim.label.trangthaisim"),
            key: "status",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: false,
            funcGetClassname: (value) => {
                if(value == 0){
                    return ['p-1' , "border-round", "border-400", "text-color","inline-block"];
                }else if(value == CONSTANTS.SIM_STATUS.READY){
                    // return ['p-1', "bg-blue-600", "border-round","inline-block"];
                    return ['p-2', "text-green-800", "bg-green-100","border-round","inline-block"];
                }else if(value == CONSTANTS.SIM_STATUS.ACTIVATED){
                    return ['p-2', 'text-green-800', "bg-green-100","border-round","inline-block"];
                }else if(value == CONSTANTS.SIM_STATUS.INACTIVED){
                    return ['p-2', 'text-yellow-800', "bg-yellow-100", "border-round","inline-block"];
                }else if(value == CONSTANTS.SIM_STATUS.DEACTIVATED){
                    return ['p-2', 'text-indigo-600', "bg-indigo-100", "border-round","inline-block"];
                }else if(value == CONSTANTS.SIM_STATUS.PURGED){
                    return ['p-2', 'text-red-700', "bg-red-100", "border-round","inline-block"];
                }else if(value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY){
                    return ['p-2', 'text-cyan-800', "bg-cyan-100", "border-round","inline-block"];
                }else if(value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY){
                    return ['p-2', 'text-teal-800', "bg-teal-100", "border-round","inline-block"];
                }else if(value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY){
                    return ['p-2', 'text-orange-700', "bg-orange-100", "border-round","inline-block"];
                }
                return [];
            },
            funcConvertText: (value)=>{
                if(value == 0){
                    return me.tranService.translate("sim.status.inventory");
                }else if(value == CONSTANTS.SIM_STATUS.READY){
                    // return me.tranService.translate("sim.status.ready");
                    return me.tranService.translate("sim.status.activated");
                }else if(value == CONSTANTS.SIM_STATUS.ACTIVATED){
                    return me.tranService.translate("sim.status.activated");
                }else if(value == CONSTANTS.SIM_STATUS.DEACTIVATED){
                    return me.tranService.translate("sim.status.deactivated");
                }else if(value == CONSTANTS.SIM_STATUS.PURGED){
                    return me.tranService.translate("sim.status.purged");
                }else if(value == CONSTANTS.SIM_STATUS.INACTIVED){
                    return me.tranService.translate("sim.status.inactivated");
                }else if(value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY){
                    return this.tranService.translate("sim.status.processingChangePlan");
                }else if(value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY){
                    return this.tranService.translate("sim.status.processingRegisterPlan");
                }else if(value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY){
                    return this.tranService.translate("sim.status.waitingCancelPlan");
                }
                return "";
            },
            style:{
                color: "white"
            }
        },{
            name: this.tranService.translate("sim.label.dungluong"),
            key: "usagedData",
            size: "150px",
            align: "right",
            isShow: true,
            isSort: false,
            funcConvertText: function(value){
                return me.utilService.convertNumberToString(value);
            }
        },{
            name: this.tranService.translate("sim.label.tengoicuoc"),
            key: "ratingPlanName",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: false
        },{
            name: this.tranService.translate("sim.label.nhomsim"),
            key: "groupName",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: false
        },{
            name: this.tranService.translate("sim.label.maapn"),
            key: "apnCode",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: false
        },{
            name: this.tranService.translate("sim.label.khachhang"),
            key: "customerName",
            size: "150px",
            align: "left",
            isShow: false,
            isSort: false
        },{
            name: this.tranService.translate("sim.label.makhachhang"),
            key: "customerCode",
            size: "150px",
            align: "left",
            isShow: false,
            isSort: false
        },{
            name: this.tranService.translate("sim.label.mahopdong"),
            key: "contractCode",
            size: "150px",
            align: "left",
            isShow: false,
            isSort: false
        },{
            name: this.tranService.translate("sim.label.ngaylamhopdong"),
            key: "contractDate",
            size: "150px",
            align: "left",
            isShow: false,
            isSort: false,
            funcConvertText:(value)=>{
                return me.utilService.convertLongDateToString(value);
            }
        },{
            name: this.tranService.translate("sim.label.nguoilamhopdong"),
            key: "contractInfo",
            size: "150px",
            align: "left",
            isShow: false,
            isSort: false
        },{
            name: this.tranService.translate("sim.label.matrungtam"),
            key: "centerCode",
            size: "150px",
            align: "left",
            isShow: false,
            isSort: false
        },{
            name: this.tranService.translate("sim.label.dienthoailienhe"),
            key: "contactPhone",
            size: "150px",
            align: "left",
            isShow: false,
            isSort: false
        },
        {
            name: this.tranService.translate("sim.label.diachilienhe"),
            key: "contactAddress",
            size: "150px",
            align: "left",
            isShow: false,
            isSort: false
        },
        {
            name: this.tranService.translate("sim.label.paymentName"),
            key: "paymentName",
            size: "150px",
            align: "left",
            isShow: false,
            isSort: false
        },
        {
            name: this.tranService.translate("sim.label.paymentAddress"),
            key: "paymentAdress",
            size: "150px",
            align: "left",
            isShow: false,
            isSort: false
        },
        {
            name: this.tranService.translate("sim.label.routeCode"),
            key: "routeCode",
            size: "150px",
            align: "left",
            isShow: false,
            isSort: false
        },
        {
            name: this.tranService.translate("sim.label.customerBirth"),
            key: "birthDay",
            size: "150px",
            align: "left",
            isShow: false,
            isSort: false,
            funcConvertText: (value)=>{
                if(value == null) return "";
                return me.utilService.convertLongDateToString(value);
            }
        }];

        this.optionTableSimList = {
            hasClearSelected:false,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: true,
          }

          this.pageNumberSimList = 0;
          this.pageSizeSimList= 10;
          this.sortSimList = "customerCode,asc";
          this.selectItemsSimList = [];
          this.dataSetSimList ={
            content: [],
            total: 0,
          }

        this.dataStore = [];
        for(let i = 0;i<121;i++){
            this.dataStore.push({
                id:"i",
                customerCode:"Value Customer Code "+i,
                contractCode:"Value Contract Code "+i,
                customerName:"Value Customer Name "+i,
                contractor:"Value Contractor "+i,
                contractDate:"Value Contract Date "+i,
                centerCode: "Value Center Code "+i,
                contactPhone: "Value Contact Phone "+i,
                contactAddress: "Value Contact Address "+i,
                paymentName:"Value Payment Name "+i,
                paymentAddress: "Value Payment Address "+i,
                routeCode: "Value Route Code "+i,
                customerBirthday: "Value Customer Bỉthday "+i,
            });
        }

        // this.selectItems = [];
        // this.dataSet ={
        //     content: this.dataStore.slice(0, this.pageSize),
        //     total: this.dataStore.length
        // }

        this.selectItems = [];
        this.dataSet ={
            content: [],
            total: 0
        }

        me.messageCommonService.onload();
        me.contractService.searchContract({},(response)=>{
            this.dataSet.content=response.content.map((item:any)=>{
                // item.createdDate=this.convertToDDMMYYYY(item.createdDate)
                return item;
            });
            this.dataSet.total = response.totalElements;
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }

    onSearch(){
        // console.log(this.searchInfo);
        this.searchInfo.loggable = true;
        this.search(0,this.pageSize, this.sort, this.searchInfo)
    }

    openSimListModal(id, item){
        this.isShowSimList=true;
        this.simService.getSimByContractCode({contractCode:item.contractCode},(response)=>{
            this.dataSetSimList.content=response
            this.dataSetSimList.total=response.totalElements
        })
    }

    onDelete(id:string){}

    search(page, limit, sort,params){
        let me = this;
        this.pageNumber = page;
        this.pageSize = limit;
        this.sort = sort;
        let dataParam = {
            ...params,
            page,
            size:limit,
            sort
        }
        me.messageCommonService.onload();
        this.contractService.searchContract(dataParam,(response)=>{
            this.dataSet.content=response.content.map((item:any)=>{
                return item;
            });
            this.dataSet.total = response.totalElements;
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }

    searchSimList(page, limit, sort,params){
        let me = this;
        this.pageNumber = page;
        this.pageSize = limit;
        this.sort = sort;
        let dataParam = {
            ...params,
            page,
            size:limit,
            sort
        }
      }

}
