import { Observable } from 'rxjs';
import { Component, Inject, Injector } from '@angular/core';
import { AbstractControl, AsyncValidatorFn, FormControl, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { MenuItem } from 'primeng/api';
import { TranslateService } from 'src/app/service/comon/translate.service';
import { CustomerService } from 'src/app/service/customer/CustomerService';
import { GroupSimService } from 'src/app/service/group-sim/GroupSimService';
import { debounceTime, switchMap, map } from 'rxjs/operators';
import { take } from 'rxjs/operators';
import { MessageCommonService } from 'src/app/service/comon/message-common.service';
import { CONSTANTS } from 'src/app/service/comon/constants';
import { AccountService } from 'src/app/service/account/AccountService';
import { DebounceInputService } from 'src/app/service/comon/debounce.input.service';
import { ComponentBase } from 'src/app/component.base';

@Component({
  selector: 'app-update-group-sim',
  templateUrl: './update-group-sim.component.html',
  // styleUrls: ['./create-group-sim.component.scss']
})
export class UpdateGroupSimComponent extends ComponentBase {
  customerDetail: any;
  provinces: Array<any>;

  isDisableSave = false;

  labelBtnSave: string = this.tranService.translate("groupSim.label.buttonSave");
  labelBtnCancel: string = this.tranService.translate("groupSim.label.buttonCancel");
  labelPlaceholderCustomer: string = this.tranService.translate("groupSim.placeHolder.customer");
  placeHolderGroupKey:string = this.tranService.translate("groupSim.placeHolder.groupKey")
  placeHolderGroupName:string = this.tranService.translate("groupSim.placeHolder.groupName")
  placeHolderDescription: string = this.tranService.translate("groupSim.placeHolder.description")
  items: MenuItem[];
  home: MenuItem
  groupScope: number;
  groupScopeObjects: any = CONSTANTS.GROUP_SCOPE;
  isGroupKeyExists: boolean = false;
  groupDetail: any;
  contractCode: any;
  groupId: number;
  constructor(@Inject(GroupSimService) private groupSimService: GroupSimService,
              @Inject(CustomerService) private customerService: CustomerService,
              private accountService: AccountService, injector: Injector){super(injector);}

  customCharacterValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const value = control.value;
      const isValid = /^[a-zA-Z0-9 \-_,\s\u00C0-\u1EF9]*$/.test(value);
      return isValid ? null : { 'invalidCharacters': { value } };
    };
  }

  customCodeCharacterValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const value = control.value;
      const isValid = /^[a-zA-Z0-9\-_]*$/.test(value);
      return isValid ? null : { 'invalidCharacters': { value } };
    };
  }

  checkExisted(query: {}): Observable<number> {
    return new Observable(observer => {
      this.groupSimService.groupkeyCheckExisted({},query, (response) => {
        observer.next(response);
        observer.complete();
      });
    });
  }



  /**
   * ^[a-zA-Z0-9 .\-_,\s\u00C0-\u1EF9]*$ cho biết những kí tự được phép
   *    \u00C0-\u1EF9 là range của Vietnamese'Unicode characters
   *    a-zA-Z0-9 cho phép kí tự chữ và số
   *    .\-_, cho phép _ và - còn \s cho phép blankspace
   */

  updateGroupForm: any;

  submitForm(){
    if(this.isGroupKeyExists){
      return this.messageCommonService.error(this.tranService.translate("groupSim.error.existedError"))
    }
    let dataParams = {...this.groupDetail, ...this.updateGroupForm.value}
    let me = this;
    this.groupSimService.updateSimGroup(this.groupId+"",{}, dataParams,{},(response)=>{
      me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"))
      this.router.navigate(['/sims/group']);
    })
  }

  ngOnInit(){
    // let me = this;
    this.getGroupDetail();
    this.items = [{ label: this.tranService.translate("global.menu.simmgmt") }, { label: this.tranService.translate("groupSim.breadCrumb.group"), routerLink: '/sims/group' }, { label: this.tranService.translate("groupSim.breadCrumb.update") }];
    this.home = { icon: 'pi pi-home', routerLink: '/' };
  }

  checkExistGroupKey(){
    this.isGroupKeyExists = false;
    let me = this;
    if(this.updateGroupForm.value["groupKey"] == this.groupDetail.groupKey) return;
    this.debounceService.set("groupKey", this.groupSimService.groupkeyCheckExisted.bind(this.groupSimService,{},{query: this.updateGroupForm.value["groupKey"]},(response)=>{
      me.isGroupKeyExists = response == 1;
    }));
  }

  getGroupDetail(){
    let me = this;
    this.groupId = parseInt(this.route.snapshot.paramMap.get("idgroup"));
    this.messageCommonService.onload();
    this.groupSimService.getSimGroupById(this.groupId+"",{}, {}, (response)=>{
        me.groupDetail = response;
        me.groupScope = me.groupDetail.scope;
        me.groupScopeObjects = CONSTANTS.GROUP_SCOPE;
        me.contractCode = response.contractCode;
        if(me.groupScope == CONSTANTS.GROUP_SCOPE.GROUP_CUSTOMER){
          this.getCustomerDetail();
        }
        if(me.groupScope == CONSTANTS.GROUP_SCOPE.GROUP_PROVINCE){
          me.accountService.getListProvince((response)=>{
            this.provinces = response.map(el => {
              return {
                ...el,
                display: `${el.name} - ${el.code}`
              }
            })
          })
        }
        me.updateGroupForm = new FormGroup({
          groupKey: new FormControl(me.groupDetail.groupKey, [Validators.required,Validators.maxLength(16), this.customCodeCharacterValidator()]),
          name: new FormControl(me.groupDetail.name, [Validators.required, Validators.maxLength(255), this.customCharacterValidator()]),
          description: new FormControl(me.groupDetail.description, [Validators.maxLength(255)])
        });
    }, null, ()=>{
      me.messageCommonService.offload();
    })
  }

  getCustomerDetail(name:string = ""){
    let me = this;
    me.customerService.getByKey("customerCode", this.groupDetail.customerCode ,(response)=>{
      if(response){
        me.customerDetail = response[0];
      }
    })
  }

  getDisplayProvince(){
    if(this.groupDetail.provinceCode){
        let provinces = (this.provinces || []).filter(el => el.code == this.groupDetail.provinceCode);
        if(provinces.length > 0){
          let province = provinces[0];
          return province.display;
        }
    }
    return "";
  }
}
