import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { RouterModule } from "@angular/router";
import { TableModule } from 'primeng/table';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { ButtonModule } from 'primeng/button';
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { CheckboxModule } from 'primeng/checkbox';
import { InputNumberModule } from 'primeng/inputnumber';
import { TableVnptComponent } from "./table/table.component";
import { ChooseLanguageComponent } from "./choose-language/choose-language.component";
import {MessageCommonComponent} from "./message-common/message-common.component"
import { ConfirmDialogModule } from 'primeng/confirmdialog'
import { ToastModule } from 'primeng/toast';
import { BlockUIModule } from 'primeng/blockui';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { TooltipModule } from 'primeng/tooltip';
import { InputFileVnptComponent } from "./input-file/input.file.component";
import { VnptCombobox } from "./combobox-lazyload/combobox.lazyload";
import { InputTextModule } from "primeng/inputtext";
import { DropdownModule } from "primeng/dropdown";
import { SanitizePipe } from "src/app/pipe/sanitize.pipe";
import { TableInputComponent } from "./table/table.input.component";
import { CalendarModule } from 'primeng/calendar';
import { DialogModule } from "primeng/dialog";
import { CaptchaComponent } from "./captcha/captcha";
import { DynamicChartComponent } from "./charts/dynamic.chart.component";
import { ChartModule } from "primeng/chart";
import { AccountService } from "src/app/service/account/AccountService";
import { ProvinceService } from "src/app/service/account/ProvinceService";
import { ContractService } from "src/app/service/contract/ContractService";
import { CustomerService } from "src/app/service/customer/CustomerService";
import { DeviceService } from "src/app/service/device/DeviceService";
import { GroupSimService } from "src/app/service/group-sim/GroupSimService";
import { SimService } from "src/app/service/sim/SimService";
import { RatingPlanService } from "src/app/service/rating-plan/RatingPlanService";
import { AlertService } from "src/app/service/alert/AlertService";
import { SearchFilterSeparateComponent } from "./search-filter-separate/search-filter-separate.component";
import { CardModule } from "primeng/card";
import { MultiSelectModule } from "primeng/multiselect";
import {ProvinceAddressService} from "../../service/address/ProvinceAddressService";
import {DistrictAddressService} from "../../service/address/DistrictAddressService";
import {CommuneAddressService} from "../../service/address/CommuneAddressService";
import {UploadFileDialogComponent} from "./upload-file/upload-file-dialog.component";

@NgModule({
    imports: [
        CommonModule,
        TableModule,
        OverlayPanelModule,
        ButtonModule,
        FormsModule,
        ReactiveFormsModule,
        CheckboxModule,
        InputNumberModule,
        InputTextModule,
        DropdownModule,
        ConfirmDialogModule,
        ToastModule,
        BlockUIModule,
        ProgressSpinnerModule,
        RouterModule,
        TooltipModule,
        CalendarModule,
        DialogModule,
        ChartModule,
        CardModule,
        MultiSelectModule
    ],
    declarations:[
        TableVnptComponent,
        ChooseLanguageComponent,
        MessageCommonComponent,
        InputFileVnptComponent,
        VnptCombobox,
        SanitizePipe,
        TableInputComponent,
        CaptchaComponent,
        DynamicChartComponent,
        SearchFilterSeparateComponent,
        UploadFileDialogComponent
    ],
    exports:[
        TableVnptComponent,
        ChooseLanguageComponent,
        MessageCommonComponent,
        InputFileVnptComponent,
        VnptCombobox,
        SanitizePipe,
        TableInputComponent,
        CaptchaComponent,
        DynamicChartComponent,
        SearchFilterSeparateComponent,
        UploadFileDialogComponent
    ],
    providers:[
        AccountService, ProvinceService, ContractService, CustomerService, DeviceService, GroupSimService, SimService, RatingPlanService, AlertService, ProvinceAddressService, DistrictAddressService, CommuneAddressService
    ]
})
export class CommonVnptModule{}
