import { RouterModule } from '@angular/router';
import { NgModule } from '@angular/core';
import { AppApnSimListComponent} from "./list/app.apnsim.list.component";
import {AppApnSimDetailComponent} from "./detail/app.apnsim.detail.component";
import DataPage from "../../service/data.page";
import {CONSTANTS} from "../../service/comon/constants";

@NgModule({
    imports: [
        RouterModule.forChild([
            {path: "", component: AppApnSimListComponent, data: new DataPage("global.titlepage.listApnSim", [CONSTANTS.PERMISSIONS.APN_SIM.VIEW_LIST])},
            {path: "detail/:apnId", component: AppApnSimDetailComponent, data: new DataPage("global.titlepage.detailApnSim", [CONSTANTS.PERMISSIONS.APN_SIM.VIEW_DETAIL])}
        ])
    ],
    exports: [RouterModule]
})
export class AppApnSimRoutingModule {}
