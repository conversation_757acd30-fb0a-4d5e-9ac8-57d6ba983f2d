<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{tranService.translate("logs.menu.log")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
    <div class="col-5 flex flex-row justify-content-end align-items-center">

    </div>
</div>

<form [formGroup]="formSearch" (ngSubmit)="onSubmitSearch()" class="pt-3 pb-2 vnpt-field-set">
    <p-panel [toggleable]="true" [header]="tranService.translate('global.text.filter')">
        <div class="grid search-grid-3">
            <div class="col-3">
                <div class="relative">
                    <vnpt-select
                        class="w-full"
                        [(value)]="searchInfo.userName"
                        [placeholder]="tranService.translate('logs.label.viewAccount')"
                        paramKey="username"
                        keyReturn="username"
                        displayPattern="${username}"
                        typeValue="primitive"
                        [isMultiChoice]="false"
                        [floatLabel]="true"
                        [loadData]="loadAccount.bind(this)"
                    ></vnpt-select>
                </div>
            </div>
            <div class="col-3">
                <span class="p-float-label">
                    <p-dropdown styleClass="w-full"
                                [showClear]="true"
                                [autoDisplayFirst]="false"
                                [(ngModel)]="searchInfo.action"
                                formControlName="action"
                                [required]="false"
                                [options]="actionType"
                                optionLabel="label"
                                optionValue="value"
                    ></p-dropdown>
                    <label class="label-dropdown" htmlFor="status">{{tranService.translate("logs.label.actionType")}}</label>
                </span>
            </div>
            <div class="col-2 pb-0">
                <p-button icon="pi pi-search"
                          styleClass="p-button-rounded p-button-secondary p-button-text button-search"
                          type="submit"
                ></p-button>
            </div>
        </div>
    </p-panel>
</form>

<table-vnpt
    [tableId]="'logsList'"
    [fieldId]="'username'"
    [columns]="columns"
    [dataSet]="dataSet"
    [options]="optionTable"
    [pageNumber]="pageNumber"
    [loadData]="search.bind(this)"
    [pageSize]="pageSize"
    [sort]="sort"
    [params]="searchInfo"
></table-vnpt>

<!--    dialog-->
<div class="flex justify-content-center dialog-vnpt">
    <p-dialog [header]="tranService.translate('logs.label.affectedField')" [(visible)]="isShowModal" [modal]="true" [draggable]="false" [resizable]="false">
        <p-table [columns]="myColumns" [value]="myValues" [tableStyle]="{ 'min-width': '50rem' }">
            <ng-template pTemplate="header" let-columns>
                <tr>
                    <th *ngFor="let col of columns">
                        {{ col.header }}
                    </th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-rowData let-columns="columns">
                <tr>
                    <td *ngFor="let col of columns">
                        {{ rowData[col.field] }}
                    </td>
                </tr>
            </ng-template>
        </p-table>
    </p-dialog>
</div>
