<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{this.tranService.translate("global.menu.listaccount")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
    <div class="col-5 flex flex-row justify-content-end align-items-center">
        <p-button [label]="tranService.translate('global.button.edit')" styleClass="p-button-info mr-2" (click)="goToEdit()" *ngIf="checkAuthen([allPermissions.ACCOUNT.UPDATE])"></p-button>
        <p-button [label]="tranService.translate('global.button.delete')" styleClass="p-button-secondary p-button-outlined" (click)="deleteAccount()" *ngIf="checkAuthen([allPermissions.ACCOUNT.DELETE]) && !accountResponse?.isHasChild"></p-button>
    </div>
</div>

<p-card styleClass="mt-3">
    <div class="flex flex-row justify-content-between" *ngIf="accountResponse">
        <div style="width: 49%;">
            <!-- username -->
            <div class="w-full field grid">
                <label htmlFor="accountName" class="col-fixed" style="width:180px">{{tranService.translate("account.label.username")}}</label>
                <div class="col">
                    {{accountResponse.username}}
                </div>
            </div>
            <!-- status -->
            <div class="w-full field grid">
                <label htmlFor="fullName" class="col-fixed" style="width:180px">{{tranService.translate("account.label.status")}}</label>
                <div class="col">
                    {{getStringUserStatus(accountResponse.status)}}
                </div>
            </div>
            <!-- fullname -->
            <div class="w-full field grid">
                <label htmlFor="fullName" class="col-fixed" style="width:180px;height: fit-content;">{{tranService.translate("account.label.fullname")}}</label>
                <div class="col" style="width: calc(100% - 180px);overflow-wrap: break-word;">
                    {{accountResponse.fullName}}
                </div>
            </div>
            <!-- nhom quyen -->
            <div class="w-full field grid">
                <label htmlFor="roles" class="col-fixed" style="width:180px; height: fit-content;">{{tranService.translate("account.label.role")}}</label>
                <div class="col" style="max-width: calc(100% - 180px) !important;">
                    <!-- <div>{{getStringRoles()}}</div> -->
                    <div *ngFor="let item of accountResponse.roles">
                        {{ item.roleName}}
                    </div>
                </div>
            </div>
            <!-- description -->
            <div class="w-full field grid">
                <label htmlFor="description" class="col-fixed" style="width:180px">{{tranService.translate("account.label.description")}}</label>
                <div class="col">
                    {{accountResponse.description}}
                </div>
            </div>
        </div>
        <div style="width: 49%;">
            <!-- email -->
            <div class="w-full field grid">
                <label htmlFor="email" class="col-fixed" style="width:180px;height: fit-content;">{{tranService.translate("account.label.email")}}</label>
                <div class="col" style="width: calc(100% - 180px);overflow-wrap: break-word;">
                    {{accountResponse.email}}
                </div>
            </div>
            <!-- phone -->
            <div class="w-full field grid">
                <label htmlFor="phone" class="col-fixed" style="width:180px">{{tranService.translate("account.label.phone")}}</label>
                <div class="col">
                    {{accountResponse.phone}}
                </div>
            </div>
            <!-- loai tai khoan -->
            <div class="w-full field grid">
                <label for="userType" class="col-fixed" style="width:180px">{{tranService.translate("account.label.userType")}}</label>
                <div class="col">
                    <span>{{getStringUserType(accountResponse.type)}}</span>
                </div>
            </div>
            <!-- Tinh thanh pho -->
            <div class="w-full field grid" *ngIf="accountInfo.userType != optionUserType.ADMIN && accountInfo.userType != optionUserType.AGENCY">
                <label htmlFor="province" class="col-fixed" style="width:180px">{{tranService.translate("account.label.province")}}</label>
                <div class="col">
                    <span>{{accountResponse.provinceName}} ({{accountResponse.provinceCode}})</span>
                </div>
            </div>
            <!-- ten khach hang -->
            <div class="w-full field grid" *ngIf="accountInfo.userType == optionUserType.CUSTOMER">
                <label htmlFor="roles" class="col-fixed" style="width:180px;height: fit-content;">{{tranService.translate("account.label.customerName")}}</label>
                <div class="col" style="max-width: calc(100% - 180px) !important;">
                    <div *ngFor="let item of accountResponse.customers">
                        {{ item.customerName + ' - ' + item.customerCode}}
                    </div>
                </div>
            </div>
            <!-- GDV quan ly-->
            <div class="w-full field grid" [class]="accountInfo.userType == optionUserType.CUSTOMER && (userType == optionUserType.ADMIN || userType == optionUserType.PROVINCE) ? '' : 'hidden'">
                <label htmlFor="roles" class="col-fixed" style="width:180px">{{tranService.translate("account.label.managerName")}}</label>
                <div class="col" style="max-width: calc(100% - 180px) !important;">
                    {{ accountResponse?.manager?.username }}
                </div>
            </div>
            <!-- Danh sach tai khoan khach hang -->
            <div class="w-full field grid align-items-start" *ngIf="accountInfo.userType == optionUserType.DISTRICT">
                <label htmlFor="roles" class="col-fixed" style="width:180px">{{tranService.translate("account.label.customerAccount")}}</label>
                <div class="col" style="max-width: calc(100% - 180px) !important;">
                    <div *ngFor="let item of accountResponse?.userManages">
                        {{ item.username}}
                    </div>
                </div>
            </div>
        </div>
    </div>
</p-card>
