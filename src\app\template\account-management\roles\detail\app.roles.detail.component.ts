import {AfterContentChecked, Component, Injector, OnInit} from '@angular/core';
import {CONSTANTS} from "../../../../service/comon/constants";
import {MenuItem, TreeNode} from "primeng/api";
import {RolesService} from "../../../../service/account/RolesService";
import {ComponentBase} from "../../../../component.base";

@Component({
  selector: 'app-app.roles.detail',
  templateUrl: './app.roles.detail.component.html',
})
export class AppRolesDetailComponent extends ComponentBase implements OnInit, AfterContentChecked{
    constructor(public rolesService: RolesService,
                private injector: Injector) {
        super(injector);
    }
    items: Array<MenuItem>;
    home: MenuItem;
    type: number;
    roleId: number;
    dataSet: {
        content: TreeNode[],
        total: number
    };
    roleInfo: {
        name: string| null,
        type: number|null,
        status: number|null,
        description: string|null
        roles: Array<any>,
        permissionIds: Array<any>,
    };
    userType: number;


    ngAfterContentChecked(): void {

    }
    editRole(){
        this.router.navigate([`/roles/edit/${this.roleId}`]);
    }

    ngOnInit(): void {
        let me = this;
        this.type = CONSTANTS.USER_TYPE.ADMIN;
        this.home = { icon: 'pi pi-home', routerLink: '/' };
        this.userType = this.sessionService.userInfo.type;
        this.items = [
            { label: this.tranService.translate("global.menu.accountmgmt") },
            { label: this.tranService.translate("global.menu.listroles"), routerLink:"/roles" },
            { label: this.tranService.translate("global.menu.detailroles") }
        ];
        this.roleId = parseInt(this.route.snapshot.paramMap.get("id"));
        this.getDetailAccount();
        this.dataSet ={
            content: [],
            total: 0
        }
        this.dataSet = {
            content: [{
                label: this.tranService.translate("global.text.all"),
                key: "all",
                children: null,
                data: null
            }],
            total: 0
        }
        this.roleInfo = {
            name: null,
            type: null,
            status: null,
            roles: null,
            description: null,
            permissionIds: null,
        }
    }
    getDetailAccount(){
        let me = this;
        this.messageCommonService.onload();
        this.rolesService.getById(this.roleId, (response)=>{
            this.roleInfo.name = response.name
            this.roleInfo.type = response.type
            this.roleInfo.status = response.status
            this.roleInfo.permissionIds = response.permissionIds
            this.getTreeRoles()
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }

    closeForm(){
        this.router.navigate(['/roles'])
    }
    getClassStatus(value){
        if(value == 0){
            return ['p-1' , "border-round", "border-400", "text-color","inline-block"];
        }else if(value == 1){
            return ['p-1', "bg-blue-600", "border-round","inline-block"];
        }
        return ""
    }
    getStatus(value){
        if(value == CONSTANTS.ROlES_STATUS.INACTIVE){
            return this.tranService.translate("roles.status.inactive");
        }else if(value == CONSTANTS.ROlES_STATUS.ACTIVE){
            return this.tranService.translate("roles.status.active");
        }
        return "";
    }
    getType(value){
        if(value == CONSTANTS.ROLE_TYPE.ADMIN){
            return this.tranService.translate("roles.type.admin");
        }else if(value == CONSTANTS.ROLE_TYPE.CUSTOMER){
            return this.tranService.translate("roles.type.customer");
        }else if(value == CONSTANTS.ROLE_TYPE.PROVINCE){
            return this.tranService.translate("roles.type.province");
        }else if(value == CONSTANTS.ROLE_TYPE.TELLER){
            return this.tranService.translate("roles.type.teller");
        }else if(value == CONSTANTS.ROLE_TYPE.AGENCY){
            return this.tranService.translate("roles.type.agency");
        }else if(value == CONSTANTS.ROLE_TYPE.ALL){
            return this.tranService.translate("roles.type.all");
        }
        return "";
    }
    getTreeRoles(){
        let me = this;
        this.rolesService.getTreeRoles((response)=>{
            response.forEach(el => {
                el.label = me.tranService.translate(`permission.${el.key}.${el.key}`)
                el.partialSelected = true;
                if(el.children){
                    el.children.forEach(item => {
                        item.label = me.tranService.translate(`permission.${el.key}.${item.key}`, {}, item.data?.description)
                    })
                }
            });
            me.dataSet = {
                content: [{
                    label: this.tranService.translate("global.text.all"),
                    key: "all",
                    children: response,
                    data: null,
                    expanded: true,
                    partialSelected: true
                }],
                total: 0
            }
            // let permissionIds = [1, 2, 3, 4, 5];
            me.roleInfo.roles = [];
            let totalOfTotal = 0;
            me.dataSet.content[0].children.forEach(el => {
                if(el.children != null){
                    let total = 0;
                    el.children.forEach(item => {
                        if(this.roleInfo.permissionIds.includes(item.data.id)){
                            me.roleInfo.roles.push(item);
                            total ++;
                        }
                    });
                    if(total != 0 && total == el.children.length){
                        me.roleInfo.roles.push(el);
                        el.partialSelected = false;
                        totalOfTotal ++;
                    }else if(total == 0){
                        el.partialSelected = false;
                    }
                }
            })
            if(totalOfTotal != 0 && totalOfTotal == me.dataSet.content[0].children.length){
                let element = me.dataSet.content[0];
                element.partialSelected = false;
                me.roleInfo.roles.push(element);
            }
        })

    }

    protected readonly CONSTANTS = CONSTANTS;
}
