/* You can add global styles to this file, and also import other style files */
*{
    font-size: 14px;
}
:root {
    // --blue: #1e90ff;
    // --white: #ffffff;
    --mainColor: #08539C;
    --mainColorText: blue
}
$gutter: 12px; //for primeflex grid system
// $sm	Breakpoint of screens such as phones.	576px
// $md	Breakpoint of screens such as tablets.	768px
// $lg	Breakpoint of screens such as notebook monitors.	992px
// $xl	Breakpoint of smaller screens such as desktop monitors.	1200px
// $gutter	Padding of a grid column.	.5rem
// $fieldMargin	Spacing of a field. Can be vertical of horizontal depending on form layout.	.5rem
// $fieldLabelMargin	Spacing of a field label. Can be vertical of horizontal depending on form layout.	.5rem
// $helperTextMargin	Top spacing of a helper text.	.5rem
// $spacer	Base value to use in spacing utilities, view spacing documentation for details.	.5rem
@import "assets/layout/styles/layout/layout.scss";

/* PrimeNG */
@import "../node_modules/primeng/resources/primeng.min.css";
@import "../node_modules/primeflex/primeflex.scss";
@import "../node_modules/primeicons/primeicons.css";
@import "../node_modules/suneditor/dist/css/suneditor.min";

@import "assets/styles/flags/flags.css";
@import "assets/styles/badges.scss";
@import "assets/styles/code.scss";

@import "assets/styles/custome-primeng.css";

// .p-inputtext{
//     padding: 6px 12px;
// }

// .vnpt-field-set .p-fieldset .p-fieldset-content {
//     padding-bottom: 0px;
// }

/* width */
::-webkit-scrollbar {
    width: 5px;
    height: 5px;
}

/* Track */
::-webkit-scrollbar-track {
    background: transparent;
}

/* Handle */
::-webkit-scrollbar-thumb {
    background: #888;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
    background: #555;
}

.display-subElement {
    border: 1px solid #ccc;
    padding: 10px 15px;
    border-radius: 12px;
    background-color: #021C34;
    cursor: pointer; /* Prevent the click event from reaching underlying elements */
    .submenu-item{
        font-size: 16px;
        color: #959EAD;
        padding: 5px;
        border-radius: 5px;
    }
    .submenu-item:hover{
        background-color: #fff;
        color: #1616a7;
    }
    .active-route-subitem{
        color: #00e9e9;
        background-color: #032b4f;
    }
}

.active-route-small{
    .icon-small-menu{
        color: #fff;
        background-color: var(--mainColor);
    }
}

.active-route-big{
    color: white !important;
    // border-left: 1px solid white;
    .layout-menuitem-icon{
        color: white !important;
        background-color: #0a6fd0 !important;
    }
}

.icon-small-menu{
    color: #959EAD;
    font-size: 18px !important;
    background-color: #1c3349;
    display: flex;
    height: 40px;
    width: 40px;
    justify-content: center;
    align-items: center;
    border-radius: 4px;
    &:hover{
        color: #fff;
        background-color: var(--mainColor);
    }
    cursor: pointer;
}

.menu-big-list{
 overflow-y: auto;
 height: calc(100vh - (116px));
}
.menu-item-small{
    height: calc(100vh - (116px));
}

.menuItemExpanded{
    // .layout-menuitem-icon{
    //     padding: 10px;
    // }
    .active-route {
        .layout-menuitem-text{
            // font-weight: 500;
            // color: var(--primary-color);
            color: white !important;
        }
        // border-left: 1px solid white;
        .layout-menuitem-icon{
            color: white !important;
            background-color: #0a6fd0;
        }
    }
    ul{
        li{
            ul{
                .active-route{
                    border-left: 1px solid white;
                }
                li{
                    :hover{
                        background-color: white;
                        color: #1616a7!important;
                        .notification {
                            background-color: red;
                            color: white!important;
                        }
                    }
                }
            }
        }
    }
    .layout-menuitem-text{
        color: #959EAD;
        // font-size: 16px !important;
    }
    .layout-menuitem-icon{
        color: #959EAD;
        font-size: 16px !important;
        background-color: #1c3349;
        display: flex;
        height: 30px;
        width: 30px;
        justify-content: center;
        align-items: center;
        border-radius: 4px;
    }
    .layout-menuitem-icon::before{
        // padding: 40px;
    }
}

.menuItemCollaped{
    ul{
            // z-index: 1000;
            // width: 10000px;
    }
}

.font-footer-sbar{
    font-size: 12px;
}

.vnpt-field-set {
    .p-fieldset.p-fieldset-toggleable .p-fieldset-legend a {
        padding: 0.5rem;
    }
    // .p-fieldset .p-fieldset-legend{
    //     // margin-left: 92%;
    // }
    .p-panel .p-panel-header{
        background: #ffff;
    }
    .report-param-required-error {
        label  {
            margin-top: -1.15rem;
        }
    }
}

.header-cmp{
    background-color: var(--mainColor);
    color: white;
}

.menu-vnpt .p-tieredmenu{
    border: 1px solid transparent;
    width: 100%;
}

.table-column-vnpt{
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

.table-vnpt {
    .p-paginator-right-content {
        margin-left: 0 !important;
    }
}

.vnpt-toggle-column-empty{
    position: absolute;
    top: 0;
    left: 100%;
    z-index: 1;
    transform: translateX(-100%);
}

.vnpt-toggle-column-not-empty{
    position: absolute;
    top: 40px;
    left: 100%;
    z-index: 1;
    transform: translateX(-100%);
}

.p-menuitem-icon{
    font-size: x-large;
}

.box-table-nodata{
    height: 200px;
    background-color: white;
    text-align: center;
}

.vnpt .p-breadcrumb,.col-7,.col-5{
    border: none;
    padding: 0;
}


.vnpt {
    position: relative;
    .p-tieredmenu {
        width: fit-content;
        white-space: nowrap;
    }

    // .p-breadcrumb .p-menuitem-text{
    //     line-height: 1;
    //     font-size: 18px;
    //     font-weight: 500;
    //     text-transform: capitalize;
    // }

    .p-menuitem-icon {
        font-size: larger;
    }
}

.button-search{
    width: 36px !important;
    height: 36px !important;
    padding: 0 !important;
}

.button-toggle-column, .button-search{
    .pi{
        font-size: 24px;
    }
}

.p-autocomplete-dd .p-autocomplete-dropdown{

}


.dialog-push-group, .dialog-create-group, .dialog-vnpt{
    .p-dialog-mask {
        max-height: 100%;
        overflow: auto;
    }
    .p-dialog{
        position: absolute;
        top: 120px !important;
    }
    .p-dialog-content{
        overflow-y: unset;
//         max-height: calc(100vh - 200px);
    }
}

.text-error-field{
    margin-top: -15px !important;
}

.sim-detail{
    .col, .col-fixed{
        padding: 0;
        padding-left: 1rem;
    }
    .p-splitter{
        margin-top: -1rem;
        border: none;
    }
}

.hidden{
    visibility: hidden;
    height: 0px;
    padding: 0px;
    margin: 0px;
}

.add-icon-size {
    font-size: 1.5em !important;
}

span.p-inputnumber{
    width: 100%;
}

.custom-responsive-field {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 200px;
    .switch-group-wrapper {
        position: relative;
        top: -5px;
    }
}

// // hinh nen
// ::-webkit-scrollbar{
//     width: 10px;
//     height: 10px;
//     border-radius: 5px;
// }

// // phan trong duoi thanh tien trinh
// ::-webkit-scrollbar-track{
//     background-color: #CCCCCC;
//     border-radius: 5px;
// }
// //phan tu cuon
// ::-webkit-scrollbar-thumb{
//     background-color: #555555;
//     border-radius: 5px;
// }
// ::-webkit-scrollbar-corner{
//     visibility: hidden;
// }

// Large phone
@media(max-width: 430px) {
//     .marquee-div {
//         width: 90% !important;
//     }
    // Profile
    .responsive-form .hide-div {
        display: none;
    }

    // Dashboard
    #boxWrapper {
        width: 100vw !important;
        max-width: 100vw !important;
        overflow-x: auto !important;
        overflow-y: visible !important;
        height: 100vh !important;
        margin-left: -1.5rem;
    }

    .box-chart {
        width: 100% !important;
        max-width: 100% !important;
        height: 100vw !important;
    }

    .box-chart.stacked-mode {
        position: relative !important;
        top: unset !important;
        left: unset !important;
        width: 100% !important;
        max-width: 100% !important;
        border: none !important;
        margin-left: -1.5rem;
    }

    .vnpt-select-dashboard {
        min-width: 0 !important;
        max-width: 100% !important;
        width: 50vw !important;
    }

    .chart-grid{
        display: flex !important;
        flex-wrap: wrap !important;
        grid-template-columns: unset !important;
        gap: 8px;
    }

    .chart-grid > div {
        width: calc(50% - 4px);
    }

    .responsive-button-container{
        flex-direction: column !important;
        gap: 5px;
        .equal-button {
            width: 35vw;
            height: auto;
        }
        .center-button {
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            white-space: nowrap;
        }
    }

    .search-grid-2 {
        display: grid;
        grid-template-columns: 180px 180px !important;
        gap: 6px !important;

        .p-float-label > label {
            display: inline-block;
            width: 80%;
            max-width: 100%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            transition: width 0.1s ease;
        }

        .p-float-label:has(input:focus) > label {
            width: auto;
            overflow: visible;
            white-space: normal;
            text-overflow: unset;
        }
    }

    .search-grid-3 {
        display: grid;
        grid-template-columns: 30vw 30vw 30vw !important;
        gap: 6px !important;

        .p-float-label > label {
            display: inline-block;
            width: 80%;
            max-width: 100%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            transition: width 0.1s ease;
        }

        .p-float-label:has(input:focus) > label {
            width: 100%;
            overflow: visible;
            white-space: normal;
            text-overflow: unset;
        }

        .p-float-label > .label-dropdown, .label-calendar, .label-phone{
            display: inline-block;
            width: 50% !important;
            max-width: 100%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            transition: width 0.1s ease;
        }

//         vnpt-select .box-value-single {
//             color: red;
//             width: 100% !important;
//             max-width: 100%;
//             overflow: hidden;
//             white-space: nowrap;
//             text-overflow: ellipsis;
//             transition: width 0.1s ease;
//         }

        .p-float-label:has(input:focus) > .label-dropdown,
        .p-float-label:has(input:focus) > .label-calendar,
        .p-float-label:has(input:focus) > .label-phone {
            width: 100% !important;
            overflow: visible;
            white-space: normal;
            text-overflow: unset;
        }
    }

    .search-grid-4 {
        display: grid;
        grid-template-columns: 22.5vw 22.5vw 22.5vw 22.5vw !important;
        gap: 2px !important;

        .p-float-label > label {
            display: inline-block;
            width: 80%;
            max-width: 100%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            transition: width 0.1s ease;
        }

        .p-float-label:has(input:focus) > label {
            width: 100%;
            overflow: visible;
            white-space: normal;
            text-overflow: unset;
        }

        .p-float-label > .label-dropdown, .label-calendar, .label-phone {
            display: inline-block;
            width: 50% !important;
            max-width: 100%;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            transition: width 0.1s ease;
        }

        .p-float-label:has(input:focus) > .label-dropdown,
        .p-float-label:has(input:focus) > .label-calendar,
        .p-float-label:has(input:focus) > .label-phone{
            width: 100% !important;
            overflow: visible;
            white-space: normal;
            text-overflow: unset;
        }
    }

    .grid-1 {
        display: grid !important;
        grid-template-columns: 380px !important;
        gap: 2px !important;
    }


    .custom-card {
        flex-direction: column !important;
        .sim-status-block {
            margin-bottom: 20px;
        }
    }

    // Group sim
   .ml-card {
        margin-left: -1.5rem !important;
    }

   .p-formgrid {
        .col-11 > input, textarea, .vnpt-select{
            width: 55vw !important;
        }

        .alert-group-label {
            width: 42vw !important;
        }
   }

   .custom-dialog {
        width: 90vw !important;
   }

    .dialog-sim-list-grid-1 {
        grid-template-columns: 75vw !important;

        .col-9 {
            min-width: 0 !important;
            width: 40vw;
            overflow-wrap: break-word;
        }
    }
    // Customers
    .dialog-account-details {
        width: 90vw !important;
    }

    .dialog-customer-grid-1 {
        grid-template-columns: 81vw !important;
    }

    .dialog-customer-grid-1 :is(.custom-panel.custom-panel-1) {
        width: 80vw !important;
    }

    .dialog-customer-grid-2 {
        grid-template-columns: 40vw 40vw !important;
    }

    // Ticket
    .dialog-ticket-sim-1 {
        display: grid !important;
        grid-template-columns: 100vw !important;
        gap: 2px !important;

        .col {
            width: 50vw !important;
        }
    }

    .ticket-sim-card {
        width: 100vw !important;
    }

    .ticket-sim-card-1 {
        width: 96vw !important;
    }

    .dialog-config-list-1 {
        display: grid !important;
        grid-template-columns: 100vw !important;
        gap: 2px !important;

        .col {
            max-width: 100% !important;
            width: 30vw !important;
        }
    }

    // Devices
    .dialog-upload-device {
        width: 100vw !important;

        .input-div{
            max-width: 100% !important;
            width: 60vw !important;
        }
    }

    .upload-device-file {
        width: 20rem !important;
    }

    .devices-map {
        margin-left: -1% !important;
        width: 80vw !important;
    }

    // Alerts
    .group-alert-div {
        flex-wrap: wrap;
    }

    .alert-checkbox-email, .alert-checkbox-sms {
        width: 6vw !important;
    }

    .alert-error-email, .alert-error-sms {
        width: 30vw;
        text-wrap: wrap;
    }

    .alert-checkbox-email {
        margin-left: -1rem;
    }

    .alert-email-content {
        margin-left: -3rem;
    }

    .alert-checkbox-sms {
        margin-left: -6rem;
    }

    .alert-sms-content {
        margin-left: -4rem;
    }

    .alert-hide-div {
        display: none;
    }

    // Data Pool
    .responsive-dialog .p-dialog  {
        width: 100vw !important;
    }

    .wallet-detail-div {
        flex-direction: column !important;
    }

    // Plans
    .responsive-form-plans {
        .grid-col {
            width: 100% !important;
        }
        .responsive-div {
            flex-wrap: wrap !important;

            label {
                min-width: 0 !important;
                width: 100% !important;
            }
            input {
                width: 50vw;
            }
        }
    }

    .custom-rating-detail-limit, .custom-rating-detail {
        flex-direction: column;
    }

    // Account
    .account-create, .role-create, .profile-create {
        flex-direction: column !important;
    }

    .profile-create .wrap-div {
        width: 40vw !important;
        overflow-wrap: normal !important;
    }

    .role-create-div {
        width: 100% !important;
    }

    .role-create-div-1 {
        input{
            width: 35vw !important;
        }
    }

    .btn-section {
        width: 20% !important;
        margin-left: 30vw;
    }

    .api-input-section {
        margin-left: -8rem !important;
        flex-direction: column !important;
        input {
            width: 40vw !important;
        }
    }

    .api-input-section-edit {
        margin-left: -7rem;
        flex-direction: column !important;
        input {
            width: 40vw !important;
        }

        .btn-gen {
            marin-left: unset !important;
            position: relative;
            left: 7rem;
        }
    }

    .module-search{
        margin-left: -2rem !important;
    }
}

// Tablet
@media (max-width: 721px) {

    // Profile
    .responsive-form .hide-div {
        display: none;
    }

    .profile-create .wrap-div {
        width: 40vw !important;
        overflow-wrap: normal !important;
    }

    .profile-create .wrap-div-1 {
        width: 20vw !important;
    }

    #boxWrapper {
        width: 100vw !important;
        max-width: 100vw !important;
        overflow-x: auto !important;
        overflow-y: visible !important;
        height: 100vh !important;
        margin-left: -1.5rem;
    }

    .box-chart {
        width: 100% !important;
        max-width: 100% !important;
        height: 100vw !important;
    }

    .box-chart.stacked-mode {
        position: relative !important;
        top: unset !important;
        left: unset !important;
        width: 100% !important;
        max-width: 100% !important;
        margin-bottom: 24px;
        border: none !important;
    }

     .chart-grid{
         display: flex !important;
         flex-wrap: wrap !important;
         grid-template-columns: unset !important;
         gap: 8px;
     }

     .chart-grid > div {
         width: calc(50% - 4px);
     }

    .label-password {
        width: 380px !important;
    }

    .grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 8px;
    }

    .grid-1 {
        display: grid;
        grid-template-columns: 1fr;
        gap: 6px;
    }

    .grid-3 {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 4px;
    }

    .grid-4 {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr 1fr;
        gap: 1px;
    }

    .col-3, .col-2, .col-4, {
        width: 100%;
    }

    .vnpt, .p-panel, .responsive-form, .diagnose-div, .responsive-form-plans, .custom-tabMenu{
        margin-left: -1.5rem;
    }

    .vnpt-div {
//         margin-right: 2rem;
        width: 100% !important;
    }

    .vnpt-no-margin {
        margin-left: 0 !important;
    }

    .responsive-pcard, .responsive-pcard-sims{
        margin-left: -1.5rem !important;
    }

    .table-vnpt{
        margin-left: -1.5rem;
    }

    .sim-status .grid {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 4px;
    }

    .responsive-pcard-sims .grid {
        grid-template-columns: 10px 20px;
    }

    .responsive-pcard-sims label {
        width: 10vw !important;
        margin-right: 100px !important;
    }

    .w-6 .ml-3 {
        width: fit-content !important;
    }

    .dialog-vnpt .p-dialog {
        max-width: 95vw;
        max-height: 140vh;
        width: auto;
    }

    .custom-card {
        display: flex;
        flex-direction: row;
        justify-content: center;
    }

    // Sims
    .responsive-form .grid-1 .col-12 {
        width: 95%;
    }

    .dialog-sim-list-grid {
        grid-template-columns: 250px 400px ;

        .col-9 {
            min-width: 0 !important;
            width: 130px;
//             position: relative;
//             top: 15px;
            overflow-wrap: break-word;
        }
    }

    .dialog-sim-list-grid-1 {
        grid-template-columns: 75vw !important;

        .col-9 {
            min-width: 0 !important;
            width: 40vw;
            overflow-wrap: break-word;
        }
    }

    // Ticket
     .dialog-ticket-sim-1 {
        display: grid !important;
        grid-template-columns: 100vw !important;
        gap: 2px !important;

        .col {
            width: 50vw !important;
        }
    }

    .ticket-sim-card {
        width: 100vw !important;
    }

    .ticket-sim-card-1 {
        width: 96vw !important;
    }

    .dialog-config-list-1 {
        display: grid !important;
        grid-template-columns: 100vw !important;
        gap: 2px !important;

        .col {
            max-width: 100% !important;
            width: 30vw !important;
        }
    }


    // Rating plan
    .custom-rating-detail {
        .grid .col-fixed {
            min-width: 0 !important;
            max-width: 100% !important;
        }

        .grid .radioButton2 {
            padding-left: 10px !important;
        }
    }

    .header-grid {
        grid-template-columns: 170px 2fr;
    }

    .dialog-grid {
        grid-template-columns: 100px 450px;
    }

    .dialog-file {
        width: 90vw !important;
    }


    .responsive-form-plans {
        .grid-col {
            width: 49%;
        }
        .responsive-size-input {
            flex-wrap: wrap !important;
        }

        .custom-responsive-field {
            flex-direction: column !important;
            display: flex;
        }

        .radio-group-wrapper {
            flex-direction: row !important;
            flex-wrap: wrap;
            margin-bottom: 1rem;
        }

        .switch-group-wrapper {
             flex-direction: row !important;
             position: relative;
             left: -45px;

            p-inputSwitch {
                position: relative;
                left: -70px;
            }
        }

        .responsive-div {
            label {
                min-width: 0 !important;
                width: 180px;
            }
//             .feeSmsInside > input {
//                 width: 30vw;
//                 border: red dashed;
//             }
        }

        .responsive-div-error-2 {
            min-width: 0 !important;
            width: 5px;
            margin-right: -10px !important;
        }

        .responsive-error-2 {
            position: relative;
            left: -290px;
        }

        .responsive-div-error {
            min-width: 0 !important;
            width: 180px;
        }

        .responsive-error-1 {
            position: relative;
            top: -10px;
            margin-bottom: -50px;
        }


    }

    .date-filter {
        width: calc(100% + 16px) !important;
    }

    .date-filter-1 {
        width: calc(100% + 18px) !important;
    }

    // Alert
    .responsive-form, .dialog-vnpt {
        .input-fit {
            width: 35vw !important;
        }

        .input-fit-add {
            width: 40vw !important;
        }

        .input-full {
            width: 62vw !important;
        }

        .input-full-v2{
            width: 45vw !important;
        }

        .input-full-v3{
            width: 56vw !important;
        }

        .input-error {
            width: 70vw !important;
            margin-left: -14vw !important;
        }

        .input-add-error {
            width: 80vw !important;
            margin-left: -24vw !important;
        }

        .alert-select {
            width: 300px !important;
        }

        .alert-creation-div-parent{
            display: flex;
            flex-direction: column !important;
            gap: 5px;
        }

        .alert-error {
            padding: 0 !important;
            label {
                width: 0 !important;
            }
            div {
                margin-bottom: -40px !important;
            }
            .sms-error {
                margin-left: -20px !important;
            }
        }

        .alert-content-error {
            width: 200px !important;
            margin-bottom: -20px !important;
        }

        .alert-creation-div {
            display: flex;
            flex-direction: column !important;
            gap: 5px;

            textarea {
                 width: 30vw !important;
                 height: fit-content !important;
            }

            .sms-label {
                width: 200px !important;
            }
        }

        .alert-creation-div-content {
            display: flex;
            flex-direction: column !important;
            gap: 5px;

            textarea {
                 width: 30vw !important;
            }
        }

    }

    // Datapool Wallet
    .responsive-dialog .p-dialog{
        position: absolute;
        top: 250px !important;
        .mt-3 .grid {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 4px;
        }
    }

    .responsive-dialog-2 .p-dialog {
        position: absolute;
        top: 350px !important;
        width: fit-content !important;
    }

    .responsive-container {
        flex-direction: column !important;
        .equal-button {
            width: 45vw;
        }
        .equal-button-with-margin{
            width: 40vw;
            margin-bottom: 1vh  ;
        }
    }

    .responsive-dialog-listShare {
       width: 90vw !important;
       height: auto;
    }

    .responsive-container-2 {
        flex-direction: column !important;
        .search-input-edit {
            width: 50vw !important;
        }
        .button-container {
            margin: 2rem auto -2rem;
            justify-content: center !important;
        }
    }

    // Account
    .responsive-pcard .grid {
        grid-template-columns: 0.2fr 190px;
        label {
            margin-right: -3rem;
        }
        .col {
            max-width: 100% !important;
        }
    }

    .responsive-pcard .resp-search-bar input {
        width: 50vw !important;
    }

    .account-info-grid {
        grid-template-columns: 150px 400px;
        .col{
            overflow-wrap: normal !important;
        }
    }

    .role-info-grid {
        grid-template-columns: 150px 150px;

        .col{
            overflow-wrap: break-word !important;
        }
    }

    .dropdown-fit {
        width: 30vw !important;
    }


//     .roles-container {
//         label {
//             width: 140px !important;
//         }
//         input, .role-dropdown {
//             width: 25vw !important;
//         }
//     }

    // Customers
    .customer-details {
        width: 100vw !important;
        .card-details {
            width: fit-content;
        }

        .custom-panel {
            width: 45vw !important;
            .grid {
                 flex-wrap: wrap !important;
            }
        }
        .custom-panel-textarea {
            width: fit-content;
        }
    }

    .dialog-account-details {
        width: 90vw !important;
    }

    // Diagnose
    .diagnose-search {
        .required-select small {
            width: 150px ;
        }
    }

    .diagnose-div {
        .grid .col-fixed {
            min-width: 0 !important;
            max-width: 100% !important;
        }
    }

    // Reporting
    .dialog-report {
        width: 90vw !important;
    }

    .tab-report-grid {
        grid-template-columns: 20vw 60vw;
    }

    .tab-report-grid-query {
          display: grid;
          grid-template-columns: 20vw 60vw 10px;
          align-items: start;

          label {
            width: 180px !important;
          }

          .col {
            width: 100% !important;
            min-width: 0;
          }
    }

    .tab-report-grid-query-v2 {
          display: grid;
          grid-template-columns: 20vw 56vw 10px;
          align-items: start;

          label {
            width: 180px !important;
          }

          .col {
            width: 100% !important;
            min-width: 0;
          }
    }

    .tab-report-flex-email {
        display: flex;
        flex-direction: column;
        flex-wrap: wrap;
        align-items: flex-start;

        .col {
            position: relative;
            left: 20px;
            max-width: 100% !important;
            width: 100% !important;
        }
    }

    .table-input-div {
        margin-left: 10px;
        width: 99% !important;
    }

    .cycle-div {
        .cycle-div-select {
            min-width: 0 !important;
            width: 180px !important;
        }
    }

    .report-group-grid {
        grid-template-columns: 5vw 20vw;
    }

    .report-dynamic-button-div{
        width: fit-content !important;
    }

    .custom-tabMenu {
        width: 98vw !important;
    }
}
