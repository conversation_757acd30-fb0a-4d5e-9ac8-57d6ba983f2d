import {Component, Inject, Injector, OnInit} from '@angular/core';
import {MenuItem} from "primeng/api";
import {RatingPlanService} from "../../../service/rating-plan/RatingPlanService";
import {FormBuilder} from "@angular/forms";
import {CONSTANTS} from "../../../service/comon/constants";
import {ComponentBase} from "../../../component.base";
import {AccountService} from "../../../service/account/AccountService";

@Component({
  selector: 'app-app.ratingplan.detail',
  templateUrl: './app.ratingplan.detail.component.html',
})
export class AppRatingPlanDetailComponent extends ComponentBase implements OnInit {
    constructor(
                public ratingPlanService: RatingPlanService,
                private formBuilder: FormBuilder,
                private accountService: AccountService,
                private injector: Injector)
    {
        super(injector);
    }
    items: MenuItem[];
    home: MenuItem;
    checkedReload: boolean;
    checkedFlexible: boolean;
    planId: number;
    ratingPlanInfo: {
        id: number|null;
        code: string| null,
        name: string| null,
        status: number| null,
        dispatchCode: string| null,
        customerType: string| null,
        subscriptionFee: string| null,
        subscriptionType: string| null,
        ratingScope: number| null,
        cycleTimeUnit: string| null,
        cycleInterval: string| null,
        reload: string| null,
        flat: string| null,
        limitDataUsage: string| null,
        limitSmsOutside: string| null,
        limitSmsInside: string| null,
        flexible: string| null,
        feePerDataUnit: string| null,
        squeezedSpeed: string| null,
        feeSmsInside: string| null,
        feeSmsOutside: string| null,
        maximumFee: string| null,
        dataRoundUnit: string| null,
        downSpeed: string| null,
        provinceCode: Array<string>| null,
        description: string | null
    }
    myProvices: string| null;
    subscriptionTypes: any=[];
    response: any={};
    planStatuses: any = CONSTANTS.RATING_PLAN_STATUS;
    planScopes = CONSTANTS.RATING_PLAN_SCOPE;
    userInfo: any;
    userType: any = CONSTANTS.USER_TYPE;
    provinces: any[] | undefined;


    ngOnInit(): void {
        let me = this;
        this.items = [{ label: this.tranService.translate("global.menu.ratingplanmgmt") }, { label: this.tranService.translate("global.menu.listplan"), routerLink:"/plans"  }, { label: this.tranService.translate("global.menu.detailplan") }];
        this.home = { icon: 'pi pi-home', routerLink: '/' };
        this.planId = parseInt(this.route.snapshot.paramMap.get("id"));
        this.getDetailPLan();
        this.ratingPlanInfo = {
            id:  null,
            code:  null,
            name:  null,
            status:  null,
            dispatchCode:  null,
            customerType:  null,
            subscriptionFee:  null,
            subscriptionType:  null,
            ratingScope:  null,
            cycleTimeUnit:  null,
            cycleInterval:  null,
            reload:  null,
            flat:  null,
            limitDataUsage: null,
            limitSmsOutside: null,
            limitSmsInside: null,
            flexible: null,
            feePerDataUnit: null,
            squeezedSpeed: null,
            feeSmsInside: null,
            feeSmsOutside: null,
            maximumFee: null,
            dataRoundUnit: null,
            downSpeed: null,
            provinceCode: null,
            description: null
        }
        this.checkSubscriptionType();
        this.userInfo = this.sessionService.userInfo.type;
        // this.accountService.getListProvince((data)=>{
        //     this.provinces = data.map(el => {
        //         return {
        //             code: el.code,
        //             name: `${el.name} (${el.code})`
        //         }
        //     })
        //     // console.log(this.provinces)
        // })

    }

    getDetailPLan(){
        let me = this;
        this.messageCommonService.onload();
        this.ratingPlanService.getById(this.planId, (response)=>{
            // console.log(response)
            this.response = response

            this.ratingPlanInfo.id = response.id
            this.ratingPlanInfo.code = response.code
            this.ratingPlanInfo.name = response.name
            this.ratingPlanInfo.status = response.status
            this.ratingPlanInfo.dispatchCode = response.dispatchCode
            this.ratingPlanInfo.customerType = response.customerType
            this.ratingPlanInfo.subscriptionFee = response.subscriptionFee
            this.ratingPlanInfo.subscriptionType = response.paidType
            this.ratingPlanInfo.ratingScope = response.ratingScope
            this.ratingPlanInfo.cycleTimeUnit = response.cycleTimeUnit
            this.ratingPlanInfo.cycleInterval = response.cycleInterval
            this.ratingPlanInfo.reload = response.reload
            this.ratingPlanInfo.flat = response.flat
            this.ratingPlanInfo.limitDataUsage = response.limitDataUsage
            this.ratingPlanInfo.limitSmsOutside = response.limitSmsOutside
            this.ratingPlanInfo.limitSmsInside = response.limitSmsInside
            this.ratingPlanInfo.flexible = response.flexible
            this.ratingPlanInfo.feePerDataUnit = response.feePerDataUnit
            this.ratingPlanInfo.squeezedSpeed = response.squeezedSpeed
            this.ratingPlanInfo.feeSmsInside = response.feeSmsInside
            this.ratingPlanInfo.feeSmsOutside = response.feeSmsOutside
            this.ratingPlanInfo.maximumFee = response.maximumFee
            this.ratingPlanInfo.dataRoundUnit = response.dataRoundUnit
            this.ratingPlanInfo.downSpeed = response.downSpeed
            this.ratingPlanInfo.provinceCode = response.provinceCode
            this.ratingPlanInfo.description = response.description;

            me.getReload(this.ratingPlanInfo.reload)
            me.getFlexible(this.ratingPlanInfo.flexible)
            me.myProvices = ""

            me.accountService.getListProvince((data)=>{
                me.provinces = data.map(el => {
                    return {
                        code: el.code,
                        name: `${el.name}`
                    }
                })
                me.provinces.forEach(el => {
                    if(me.ratingPlanInfo.provinceCode.includes(el.code)){
                        me.myProvices += `${el.name}, `;

                    }
                })
                if(me.myProvices.length > 0){
                    me.myProvices = me.myProvices.substring(0, me.myProvices.length - 2);
                }
            })

        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }
    active(){
        let me = this
        me.messageCommonService.confirm(
            me.tranService.translate("global.message.confirmActivePlan"),
            me.tranService.translate("global.message.titleConfirmActivePlan"),
            {
                ok:()=>{
                    me.ratingPlanService.activePlan(me.planId,(response)=>{
                    })
                    me.messageCommonService.success(me.tranService.translate("global.message.activeSuccess"));
                    window.location.reload();
                },
                cancel: ()=>{
                    // me.messageCommonService.error(me.tranService.translate("global.message.deleteFail"));
                }
            }
        )
    }
    approve(){
        let me = this
        me.messageCommonService.confirm(
            me.tranService.translate("global.message.confirmApprovePlan"),
            me.tranService.translate("global.message.titleConfirmApprovePlan"),
            {
                ok:()=>{
                    me.ratingPlanService.activePlan(me.planId,(response)=>{
                        // me.router.navigate(['/plans']);
                    })
                    me.messageCommonService.success(me.tranService.translate("global.message.success"));
                    window.location.reload();
                },
                cancel: ()=>{
                    // me.messageCommonService.error(me.tranService.translate("global.message.deleteFail"));
                }
            }
        )
    }
    suspend(){
        let me = this
        me.messageCommonService.confirm(
            me.tranService.translate("global.message.confirmSuspendPlan"),
            me.tranService.translate("global.message.titleConfirmSuspendPlan"),
            {
                ok:()=>{
                    me.ratingPlanService.suspendPlan(me.planId,(response)=>{
                        // me.router.navigate(['/plans']);
                    })
                    me.messageCommonService.success(me.tranService.translate("global.message.success"));
                    window.location.reload();
                },
                cancel: ()=>{
                    // me.messageCommonService.error(me.tranService.translate("global.message.deleteFail"));
                }
            }
        )
    }
    getNameStatus(value){
        if(value == CONSTANTS.RATING_PLAN_STATUS.ACTIVATED){
            return this.tranService.translate("ratingPlan.status.activated");
        }else if(value == CONSTANTS.RATING_PLAN_STATUS.CREATE_NEW){
            return this.tranService.translate("ratingPlan.status.create");
        }else if(value == CONSTANTS.RATING_PLAN_STATUS.PENDING){
            return this.tranService.translate("ratingPlan.status.pending");
        }else if(value == CONSTANTS.RATING_PLAN_STATUS.DEACTIVATED){
            return this.tranService.translate("ratingPlan.status.deactivated");
        }
        return "";
    }
    getClassStatus(value){
        if(value == CONSTANTS.RATING_PLAN_STATUS.ACTIVATED){
            return ['p-2', "text-teal-800", "bg-teal-100","border-round","inline-block"];
        }else if(value == CONSTANTS.RATING_PLAN_STATUS.CREATE_NEW){
            return ['p-2', "text-primary-600", "bg-primary-100", "border-round","inline-block"];
        }else if(value == CONSTANTS.RATING_PLAN_STATUS.PENDING){
            return ['p-2', "text-red-700", "bg-red-100","border-round","inline-block"];
        }else if(value == CONSTANTS.RATING_PLAN_STATUS.DEACTIVATED){
            return ['p-2', "text-orange-700", "bg-orange-100", "border-round","inline-block"];
        }
        return [];
    }
    getNameCustomerType(value){
        if(value == CONSTANTS.CUSTOMER_TYPE.PERSONAL){
            return this.tranService.translate("ratingPlan.customerType.personal");
        }else if(value == CONSTANTS.CUSTOMER_TYPE.INTERPRISE){
            return this.tranService.translate("ratingPlan.customerType.enterprise");
        }else if(value == CONSTANTS.CUSTOMER_TYPE.AGENCY){
            return this.tranService.translate("ratingPlan.customerType.agency");
        }
        return "";
    }
    checkSubscriptionType(){
        this.subscriptionTypes = [{
            type: this.tranService.translate("ratingPlan.subscriptionType.post"),
            ip: CONSTANTS.SUBSCRIPTION_TYPE.POSTPAID,
        },
        {
            type: this.tranService.translate("ratingPlan.subscriptionType.pre"),
            ip: CONSTANTS.SUBSCRIPTION_TYPE.PREPAID,
        }]
    }
    getRatingScope(value){
        if(value == CONSTANTS.RATING_PLAN_SCOPE.NATION_WIDE){
            return this.tranService.translate("ratingPlan.ratingScope.nativeWide");
        }else if(value == CONSTANTS.RATING_PLAN_SCOPE.CUSTOMER){
            return this.tranService.translate("ratingPlan.ratingScope.customer");
        }
        return "";
    }
    getCycleTimeUnit(value){
        if(value == CONSTANTS.CYCLE_TIME_UNITS.DAY){
            return this.tranService.translate("ratingPlan.cycle.day");
        }else if(value == CONSTANTS.CYCLE_TIME_UNITS.MONTH){
            return this.tranService.translate("ratingPlan.cycle.month");
        }
        return "";
    }

    getReload(value){
        if(value == CONSTANTS.RELOAD.YES){
            return this.checkedReload = true;
        }else if(value == CONSTANTS.RELOAD.NO){
            return this.checkedReload = false
        }
        return "";
    }

    getFlexible(value){
        if(value == CONSTANTS.FLEXIBLE.YES){
            return this.checkedFlexible = true;
        }else if(value == CONSTANTS.FLEXIBLE.NO){
            return this.checkedFlexible = false
        }
        return "";
    }

    deletePlan(){
        let me = this;
        me.messageCommonService.confirm(
            me.tranService.translate("global.message.titleConfirmDeletePlan"),
            me.tranService.translate("global.message.confirmDeletePlan"),
            {
                ok:()=>{
                    me.ratingPlanService.deleteById(me.planId,(response)=>{
                        me.messageCommonService.success(me.tranService.translate("global.message.deleteSuccess"));
                        me.router.navigate(['/plans']);
                    })
                },
                cancel: ()=>{
                    // me.messageCommonService.error(me.tranService.translate("global.message.deleteFail"));
                }
            }
        )
    }
    onEdit(){
        this.router.navigate([`/plans/update/${this.planId}`]);
    }

    protected readonly CONSTANTS = CONSTANTS;
}
