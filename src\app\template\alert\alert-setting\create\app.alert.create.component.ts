import {AfterContentChecked, Component, Inject, Injector, OnInit} from '@angular/core';
import {AccountService} from "../../../../service/account/AccountService";
import {FormBuilder, FormGroup} from "@angular/forms";
import {MenuItem} from "primeng/api";
import {CONSTANTS} from "../../../../service/comon/constants";
import {ComponentBase} from "../../../../component.base";
import {CustomerService} from "../../../../service/customer/CustomerService";
import {GroupSimService} from "../../../../service/group-sim/GroupSimService";
import {AlertService} from "../../../../service/alert/AlertService";
import {SimService} from "../../../../service/sim/SimService";
import { ComboLazyControl } from 'src/app/template/common-module/combobox-lazyload/combobox.lazyload';
import { TrafficWalletService } from 'src/app/service/datapool/TrafficWalletService';
import {RatingPlanService} from "../../../../service/rating-plan/RatingPlanService";
import {HttpService} from "../../../../service/comon/http.service";


@Component({
  selector: 'app-app.alert.create',
  templateUrl: './app.alert.create.component.html',
})
export class AppAlertCreateComponent extends ComponentBase implements OnInit, AfterContentChecked{
    constructor(
                @Inject(AccountService) private accountService: AccountService,
                private formBuilder: FormBuilder,
                @Inject(CustomerService) private customerService: CustomerService,
                @Inject(GroupSimService) private groupSimService: GroupSimService,
                @Inject(TrafficWalletService) private trafficWalletService: TrafficWalletService,
                @Inject(AlertService) private alertService: AlertService,
                @Inject(SimService) private simService: SimService,
                @Inject(RatingPlanService) private ratingPlanService: RatingPlanService,
                @Inject(HttpService) private httpService: HttpService,
                private injector: Injector)
    {
        super(injector);
    }
    items: MenuItem[];
    home: MenuItem;
    formAlert: any;
    alertInfo: {
        name: string|null,
        customerId: any,
        contractCode: any,
        statusSim: number|null,
        subscriptionNumber: string|null,
        groupId: string|null,
        interval: number|null,
        count: number|null,
        unit: number|null,
        value: number|null,
        description: string|null,
        severity: string|null,
        listAlertReceivingGroupId: Array<any>|null,
        url: string|null,
        emailList: string|null,
        emailSubject: string|null,
        emailContent: string|null,
        smsList: string|null
        smsContent: string|null,
        ruleCategory: number | null,
        eventType: number | null,
        appliedPlan: Array<any>,
        actionType:number|null,
        walletName: string|null,
        notifyInterval : number | null,
        notifyRepeat: number | null;
        typeAlert: Array<any>;
        sendTypeEmail: boolean;
        sendTypeSMS: boolean;
        walletSubCode: string| null
    };
    userType: number;
    wallet:any;
    disableUnit: boolean;
    statusSimOptions: Array<any>;
    optionStatusSim: any;
    unitOptions: Array<any>;
    unitWalletOptions: Array<any>;
    severityOptions: Array<any>;
    customerNameOptions: Array<{ name: any, value: any, id: any }>;
    groupOptions: Array<any>;
    listGroupByCustomer: Array<any>;
    listSimByCustomer: Array<any>;
    subscriptionNumberOptions: Array<any>;
    groupReceivingOptions: Array<any>;
    isAlertNameExisted: boolean = false;
    isPlanExisted: boolean = false;
    statusOld: number;
    comboSelectCustomerControl: ComboLazyControl = new ComboLazyControl();
    comboSelectContracCodeControl: ComboLazyControl = new ComboLazyControl();
    comboSelectSubControl: ComboLazyControl = new ComboLazyControl();
    comboSelectGroupSubControl: ComboLazyControl = new ComboLazyControl();
    ruleOptions: Array<any>;
    eventOptions: Array<any>;
    eventOptionManagement: Array<any>;
    eventOptionMonitoring: Array<any>;
    actionOptions: Array<any>;
    paramSearchGroupSim = {};
    paramSearchContract = {};
    paramSearchSim = {};
    appliedPlanOptions: Array<any>;
    repeat: boolean = false;
    isDisableReceiveGroup : boolean = false;
    listAllField : Array<any>
    listEnableForGroup : Array<any>
    listEnableForEmail : Array<any>
    listEnableForSMS : Array<any>
    listEnable : Array<any>
    controlAlertReceiving : ComboLazyControl = new ComboLazyControl();
    controlComboSelectEventType : ComboLazyControl = new ComboLazyControl();
    userInfo: any;
    readonly CONSTANTS = CONSTANTS;
    controlComboSelectWallet: ComboLazyControl = new ComboLazyControl();
    paramSearchCustomer = {};
    ngOnInit(): void {
        let me = this;
        if(this.sessionService.userInfo.type != CONSTANTS.USER_TYPE.ADMIN){
            this.paramSearchCustomer = {
                provinceCode: this.sessionService.userInfo.provinceCode
            }
        }
        this.userType = this.sessionService.userInfo.type;
        this.items = [{ label: this.tranService.translate("global.menu.alertSettings") }, { label: this.tranService.translate("global.menu.alertList"), routerLink:"/alerts"  }, { label: this.tranService.translate("global.button.create") }];
        this.home = { icon: 'pi pi-home', routerLink: '/' };
        this.optionStatusSim = CONSTANTS.ALERT_STATUS_SIM;
        this.unitOptions = [
            {name: "KB", value: 1},
            {name: "MB", value: 2},
            {name: "GB", value: 3}
        ]
        this.unitWalletOptions = [
            {label: "%", value: 1},
        ]
        this.disableUnit = false;
        me.listAllField = ['receiveGroup', 'emailSubject','emailContent','smsContent','smsList','emailList']
        me.listEnableForGroup = ['receiveGroup', 'emailSubject','emailContent','smsContent']
        me.listEnableForEmail = ['emailSubject','emailContent','emailList']
        me.listEnableForSMS = ['smsList','smsContent']
        me.listEnable = []
        this.alertInfo = {
            name: null,
            customerId: null,
            contractCode: null,
            statusSim: null,
            subscriptionNumber: null,
            groupId: null,
            interval: null,
            count: null,
            unit: this.unitOptions[0].value,
            value: null,
            description: null,
            severity: null,
            listAlertReceivingGroupId: [],
            url: null,
            emailList: null,
            emailSubject: null,
            emailContent: null,
            smsList: null,
            smsContent: null,
            ruleCategory : 1,
            eventType :  null,
            appliedPlan: null,
            actionType:0,
            walletName:null,
            notifyInterval: 1,
            notifyRepeat: null,
            typeAlert: [],
            sendTypeEmail: true,
            sendTypeSMS: null,
            walletSubCode: null
        }
        this.wallet = null;
        this.formAlert = this.formBuilder.group(this.alertInfo);
        this.formAlert.get("url").disable()
        this.severityOptions = [
            {name: this.tranService.translate("alert.severity.critical"), value:"0"},
            {name: this.tranService.translate("alert.severity.major"), value:"1"},
            {name: this.tranService.translate("alert.severity.minor"), value:"2"},
            {name: this.tranService.translate("alert.severity.info"), value:"3"}
        ]
        this.customerNameOptions = []

        this.groupOptions = []
        this.listGroupByCustomer = []

        this.subscriptionNumberOptions = []
        this.listSimByCustomer = []
        this.ruleOptions = [];
        this.eventOptions = [];
        this.groupReceivingOptions = []
        this.getListReceivingGroup()
        this.userInfo = this.sessionService.userInfo;
        this.loadEventOptions();
        // this.eventOptions = [
        //     {name:me.tranService.translate("alert.eventType.exceededPakage"), value:CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_PACKAGE},
        //     {name:me.tranService.translate("alert.eventType.exceededValue"), value:CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_VALUE},
        //     // {name:me.tranService.translate("alert.eventType.sessionEnd"), value:CONSTANTS.ALERT_EVENT_TYPE.SESSION_END},
        //     // {name:me.tranService.translate("alert.eventType.sessionStart"), value:CONSTANTS.ALERT_EVENT_TYPE.SESSION_START},
        //     {name:me.tranService.translate("alert.eventType.smsExceededPakage"), value:CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_PACKAGE},
        //     {name:me.tranService.translate("alert.eventType.smsExceededValue"), value:CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_VALUE},
        //     {name:me.tranService.translate("alert.eventType.owLock"), value:CONSTANTS.ALERT_EVENT_TYPE.ONE_WAY_LOCK},
        //     {name:me.tranService.translate("alert.eventType.twLock"), value:CONSTANTS.ALERT_EVENT_TYPE.TWO_WAY_LOCK},
        //     // {name:me.tranService.translate("alert.eventType.noConection"), value:CONSTANTS.ALERT_EVENT_TYPE.NO_CONECTION},
        //     // {name:me.tranService.translate("alert.eventType.simExp"), value:CONSTANTS.ALERT_EVENT_TYPE.SIM_EXP},
        //     {name:me.tranService.translate("alert.eventType.dataWalletExp") , value:CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP},
        //     {name:me.tranService.translate("alert.eventType.owtwlock") , value:CONSTANTS.ALERT_EVENT_TYPE.ONE_WAY_TWO_WAY_LOCK}
        // ]
        // this.ruleOptions = [
        //     {name:this.tranService.translate("alert.ruleCategory.monitoring"), value: CONSTANTS.ALERT_RULE_CATEGORY.MONITORING},
        //     {name:this.tranService.translate("alert.ruleCategory.management"), value: CONSTANTS.ALERT_RULE_CATEGORY.MANAGEMENT}
        // ]

        if (this.checkAuthen([CONSTANTS.PERMISSIONS.ALERT.CREATE])) {
            this.ruleOptions.push({name:this.tranService.translate("alert.ruleCategory.monitoring"), value: CONSTANTS.ALERT_RULE_CATEGORY.MONITORING})
            this.ruleOptions.push({name:this.tranService.translate("alert.ruleCategory.management"), value: CONSTANTS.ALERT_RULE_CATEGORY.MANAGEMENT})
        } else if (CONSTANTS.PERMISSIONS.ALERT.CREATE_WALLET_THRESHOLD || CONSTANTS.PERMISSIONS.ALERT.CREATE_WALLET_EXPIRY) {
            this.ruleOptions.push({name:this.tranService.translate("alert.ruleCategory.management"), value: CONSTANTS.ALERT_RULE_CATEGORY.MANAGEMENT})
        }

        this.actionOptions = [
            {name:this.tranService.translate("alert.actionType.alert"), value:CONSTANTS.ALERT_ACTION_TYPE.ALERT}
            // ,
            // {name:this.tranService.translate("alert.actionType.api"), value:CONSTANTS.ALERT_ACTION_TYPE.API}
        ]

        // this.eventOptionManagement = this.eventOptions.filter(item =>
        //     item.value == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_PACKAGE ||
        //     item.value == CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_PACKAGE||
        //     item.value == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_VALUE ||
        //     item.value == CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_VALUE ||
        //     item.value == CONSTANTS.ALERT_EVENT_TYPE.SIM_EXP ||
        //     item.value == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP )
        //
        // this.eventOptionMonitoring = this.eventOptions.filter(item =>
        //     item.value == CONSTANTS.ALERT_EVENT_TYPE.ONE_WAY_LOCK ||
        //     item.value == CONSTANTS.ALERT_EVENT_TYPE.TWO_WAY_LOCK ||
        //     item.value == CONSTANTS.ALERT_EVENT_TYPE.ONE_WAY_TWO_WAY_LOCK ||
        //     item.value == CONSTANTS.ALERT_EVENT_TYPE.NO_CONECTION ||
        //     item.value == CONSTANTS.ALERT_EVENT_TYPE.SESSION_START ||
        //     item.value == CONSTANTS.ALERT_EVENT_TYPE.SESSION_END );

        this.eventOptionManagement = this.eventOptions.filter(item =>
            item.value == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_PACKAGE ||
            item.value == CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_PACKAGE||
            item.value == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_VALUE ||
            item.value == CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_VALUE ||
            item.value == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP ||
            item.value == CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD
        )
        this.unitWalletOptions = [
            {label: "%", value: 1},
        ]
        this.eventOptionMonitoring = this.eventOptions.filter(item =>
            item.value == CONSTANTS.ALERT_EVENT_TYPE.ONE_WAY_LOCK ||
            item.value == CONSTANTS.ALERT_EVENT_TYPE.TWO_WAY_LOCK ||
            item.value == CONSTANTS.ALERT_EVENT_TYPE.ONE_WAY_TWO_WAY_LOCK);

        this.onChangeNotify()
        this.onChangeCheckBox()

        this.formAlert.get("sendTypeEmail").disable({emitEvent:false});
        this.formAlert.get("sendTypeSMS").disable({emitEvent:false});
    }

    ngAfterContentChecked() {

    }

    onSubmitCreate(){
        let me = this;
        Object.keys(this.formAlert.controls).forEach(key => {
            const control = this.formAlert.get(key);
            if (control.invalid) {
              console.log('Field:', key, 'is invalid. Errors:', control.errors);
            }
          });
        for (let i = 0; i < me.customerNameOptions.length; i++) {
            if (me.customerNameOptions[i].value == this.alertInfo.customerId){
                this.alertInfo.customerId = me.customerNameOptions[i].id
            }
        }
        if (me.alertInfo.listAlertReceivingGroupId == null ){
            this.alertInfo.listAlertReceivingGroupId = []
        }
        if (this.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP && this.alertInfo.value == null)  {
            this.alertInfo.value = 1;
        }
        let dataBody = {
            name: this.alertInfo.name,
            customerId: this.alertInfo.customerId?.id,
            contractCode: this.alertInfo.contractCode?.contractCode,
            eventType: this.alertInfo.eventType,
            subscriptionNumber: this.alertInfo.subscriptionNumber,
            groupId: this.alertInfo.groupId,
            interval: this.alertInfo.interval,
            count: this.alertInfo.count,
            unit: this.alertInfo.unit,
            value: this.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP ? this.alertInfo.value * 24 : this.alertInfo.value,
            description: this.alertInfo.description,
            severity: this.alertInfo.severity,
            listAlertReceivingGroupId: this.alertInfo.listAlertReceivingGroupId,
            url: this.alertInfo.url,
            emailList: this.alertInfo.emailList,
            emailSubject: this.alertInfo.emailSubject,
            emailContent: this.alertInfo.emailContent,
            smsList: this.alertInfo.smsList,
            smsContent: this.alertInfo.smsContent,
            ruleCategory : this.alertInfo.ruleCategory,
            actionType: this.alertInfo.actionType,
            notifyInterval:this.alertInfo.notifyInterval * 24,
            notifyRepeat: this.alertInfo.notifyRepeat,
            dataPackCode: this.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP? this.alertInfo.appliedPlan : null,
            walletSubCode: this.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD ? this.alertInfo.walletSubCode : null,
        }
        for(let el of this.listAllField) {
            if(!this.listEnable.includes(el)) {
                if(el != 'receiveGroup') {
                    dataBody[el] = null
                }else {
                    dataBody.listAlertReceivingGroupId = null
                }
            }
        }
        if(me.alertInfo.eventType ==  CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP) {
            dataBody.customerId = null
            dataBody.groupId = null
            dataBody.subscriptionNumber = null
            dataBody.listAlertReceivingGroupId = null
            dataBody.emailList = null
            dataBody.smsList = null
            dataBody.smsContent = null
            dataBody.emailContent = null
        }else {
            dataBody.dataPackCode = null;
        }
        if(me.alertInfo.actionType == CONSTANTS.ALERT_ACTION_TYPE.API) {
            dataBody.listAlertReceivingGroupId = null
            dataBody.emailList = null
            dataBody.smsList = null
            dataBody.smsContent = null
            dataBody.emailContent = null
        }
        if(me.alertInfo.actionType == CONSTANTS.ALERT_ACTION_TYPE.ALERT && me.alertInfo.eventType !== CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP) {
            dataBody.url = null
            dataBody.notifyInterval = null
            dataBody.notifyRepeat = null
        }
        if (me.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD) {
            dataBody.emailList = this.alertInfo.emailList,
            dataBody.smsList = this.alertInfo.smsList
        }
        this.messageCommonService.onload();
        if (this.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD) {
            this.alertService.createAlertWalletThreshold(dataBody, (response)=>{
                me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"));
                me.router.navigate(['/alerts']);
            }, null, ()=>{
                me.messageCommonService.offload();
            })
        } else if (this.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP) {
            this.alertService.createAlertWalletExpiry(dataBody, (response)=>{
                me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"));
                me.router.navigate(['/alerts']);
            }, null, ()=>{
                me.messageCommonService.offload();
            })
        } else {
            this.alertService.createAlert(dataBody, (response) => {
                me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"));
                me.router.navigate(['/alerts']);
            }, null, () => {
                me.messageCommonService.offload();
            })
        }
    }

    onChangeEventOption(value){
        this.alertInfo.value = 1
        if(value == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP){
            this.getListRatingPlan()
            // this.formAlert.get("unit").disable({emitEvent : false})
            this.formAlert.get("value").enable({emitEvent : false})
            this.formAlert.get("customerId").disable({emitEvent : false})
            this.formAlert.get("groupId").disable({emitEvent : false})
            this.formAlert.get("subscriptionNumber").disable({emitEvent : false})
            this.formAlert.get("statusSim").disable({emitEvent : false})
            this.formAlert.get("notifyRepeat").disable({emitEvent : false})
            this.formAlert.get("emailSubject").disable({emitEvent : false})
            this.formAlert.get("emailContent").disable({emitEvent : false})
            this.formAlert.get("smsContent").disable({emitEvent : false})
            this.alertInfo.actionType = CONSTANTS.ALERT_ACTION_TYPE.ALERT;
            this.formAlert.get("actionType").disable({emitEvent : false})
            this.formAlert.get("appliedPlan").enable({emitEvent : false})

        } else{
            this.formAlert.get("customerId").enable({emitEvent : false})
            this.formAlert.get("contractCode").enable({emitEvent : false})
            this.formAlert.get("groupId").enable({emitEvent : false})
            this.formAlert.get("subscriptionNumber").enable({emitEvent : false})
            this.formAlert.get("statusSim").disable({emitEvent : false})
            this.formAlert.get("actionType").enable({emitEvent : false})
            this.formAlert.get("value").enable({emitEvent : false})
            this.formAlert.get("appliedPlan").disable({emitEvent : false})
        }
        if(value == CONSTANTS.ALERT_EVENT_TYPE.SIM_EXP) {
            this.formAlert.get("value").disable({emitEvent : false})
        }
        if(this.alertInfo.ruleCategory ==  CONSTANTS.ALERT_RULE_CATEGORY.MONITORING) {
            this.formAlert.get("value").disable({emitEvent : false})
        }
        if (value == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_PACKAGE) {
            this.alertInfo.emailContent = this.tranService.translate("alert.message.exceededPakage")
            this.alertInfo.smsContent = this.tranService.translate("alert.message.exceededPakage")
        } else if (value == CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_PACKAGE) {
            this.alertInfo.emailContent = this.tranService.translate("alert.message.smsExceededPakage")
            this.alertInfo.smsContent = this.tranService.translate("alert.message.smsExceededPakage")
        } else if (value == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_VALUE) {
            this.alertInfo.emailContent = this.tranService.translate("alert.message.exceededValue")
            this.alertInfo.smsContent = this.tranService.translate("alert.message.exceededValue")
        } else if (value == CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_VALUE) {
            this.alertInfo.emailContent = this.tranService.translate("alert.message.smsExceededValue")
            this.alertInfo.smsContent = this.tranService.translate("alert.message.smsExceededValue")
        } else if (value == CONSTANTS.ALERT_EVENT_TYPE.ONE_WAY_LOCK || value == CONSTANTS.ALERT_EVENT_TYPE.TWO_WAY_LOCK || value == CONSTANTS.ALERT_EVENT_TYPE.ONE_WAY_TWO_WAY_LOCK) {
            this.alertInfo.emailContent = this.tranService.translate("alert.message.status")
            this.alertInfo.smsContent = this.tranService.translate("alert.message.status")
        }
        if (value == CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD) {
            this.alertInfo.emailList = null
            this.alertInfo.smsList = null
            this.alertInfo.unit = CONSTANTS.ALERT_UNIT.PERCENT
            this.alertInfo.value = 1
            this.alertInfo.walletSubCode = null
        }
        this.onChangeCheckBox();
    }

    checkRequiredOutLine(){
        let me = this;
        if (me.alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP ){
            return true;
        }
        return false
    }

    checkRequiredLength(){
        let me = this;
        if(this.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_VALUE || this.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_VALUE){
            return 9999999999
        }else if(this.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_PACKAGE || this.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_PACKAGE){
            return 100
        }
        if (this.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD ) {
            if (me.alertInfo.unit == CONSTANTS.ALERT_UNIT.PERCENT) {
                return 100
            } else if (me.alertInfo.unit == CONSTANTS.ALERT_UNIT.MB || me.alertInfo.unit == CONSTANTS.ALERT_UNIT.SMS) {
                return 9999999999
            }
        }
        return null
    }

    closeForm(){
        this.router.navigate(['/alerts'])
    }

    filerGroupByCustomerOrContractCode(event) {
        console.log(event)
        if(this.alertInfo.customerId != null){
            if (this.userType != CONSTANTS.USER_TYPE.CUSTOMER){
                this.paramSearchGroupSim = {customerCode: this.alertInfo.customerId.customerCode}
                this.paramSearchSim = {customer: this.alertInfo.customerId.customerCode};
                this.alertInfo.groupId = null;
                this.alertInfo.subscriptionNumber = null;
            }else {
                this.paramSearchContract = {customerCode: this.alertInfo.customerId.customerCode}
                this.alertInfo.groupId = null;
                this.alertInfo.subscriptionNumber = null;
                this.alertInfo.contractCode = null;
            }
        }
    }
    getListReceivingGroup() {
        let me = this;
        // me.messageCommonService.onload();
        this.alertService.getAllReceivingGroup({},(response)=>{
            me.groupReceivingOptions = (response || []).map(el => {
                return {
                    ...el,
                    name: `${el.name||'unknown'}`,
                    value: `${el.id||'unknown'}`
                }
            });
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }
    nameChanged(query){
        let me = this

        this.debounceService.set("name",me.alertService.checkName.bind(me.alertService),{name:me.alertInfo.name},(response)=>{
            if (response >= 1){
                me.isAlertNameExisted = true
            }
            else {
                me.isAlertNameExisted = false
            }
        })
    }
    onNameBlur() {
        let me = this;
        let formattedValue = this.alertInfo.name.trim();
        formattedValue = formattedValue.replace(/\s+/g, ' ');
        this.alertInfo.name = formattedValue;
        this.formAlert.get('name').setValue(formattedValue);
        this.debounceService.set("name",me.alertService.checkName.bind(me.alertService),{name:me.alertInfo.name},(response)=>{
            if (response >= 1){
                me.isAlertNameExisted = true
            }
            else {
                me.isAlertNameExisted = false
            }
        })
    }

    disableAll() {
        this.formAlert.get("emailList").disable({emitEvent : false})
        this.formAlert.get("smsList").disable({emitEvent : false})
        this.formAlert.get("emailSubject").disable({emitEvent : false})
        this.formAlert.get("emailContent").disable({emitEvent : false})
        this.formAlert.get("smsContent").disable({emitEvent : false})
        this.isDisableReceiveGroup = true;
    }

    onChangeNotify() {
        if (this.repeat == true) {
            this.alertInfo.notifyRepeat = 1;
            this.formAlert.get("notifyInterval").enable({emitEvent: false})
        } else if (this.repeat == false) {
            this.alertInfo.notifyRepeat = 0
            this.formAlert.get("notifyInterval").disable({emitEvent: false})
        }
    }

    onChangeActionType(){
        if(this.alertInfo.actionType == 0){
            this.formAlert.get("url").disable()

            this.formAlert.get("emailSubject").enable({emitEvent : false})
            this.formAlert.get("emailContent").enable({emitEvent : false})
            this.formAlert.get("smsContent").enable({emitEvent : false})
        }else if(this.alertInfo.actionType == 1){
            this.formAlert.get("url").enable()

            this.formAlert.get("emailSubject").disable({emitEvent : false})
            this.formAlert.get("emailContent").disable({emitEvent : false})
            this.formAlert.get("smsContent").disable({emitEvent : false})
        }
    }

    getListRatingPlan(){
        let me = this;
        if(me.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP) {
            this.trafficWalletService.searchPakageCode({},(response)=>{
                me.appliedPlanOptions = (response || []).map(el =>  ({code: el}))
            })
        }
    }

    onChangeCheckBox() {
        this.listEnable = []
        if(this.alertInfo.typeAlert == undefined || this.alertInfo.typeAlert.length == 0) {
            this.disableAll()
            return
        }
        if (this.alertInfo.typeAlert.includes("Group")) {
            for(let myField of this.listEnableForGroup) {
                if(!this.listEnable.includes(myField)) {
                    this.listEnable.push(myField)
                }
            }
        }
        if (this.alertInfo.typeAlert.includes("Email")) {
            for(let myField of this.listEnableForEmail) {
                if(!this.listEnable.includes(myField)) {
                    this.listEnable.push(myField)
                }
            }
        }
        if (this.alertInfo.typeAlert.includes("SMS")) {
            for(let myField of this.listEnableForSMS) {
                if(!this.listEnable.includes(myField)) {
                    this.listEnable.push(myField)
                }
            }
        }

        for (let el of this.listEnable){
            if(el != 'receiveGroup') {
                this.formAlert.get(el).enable({emitEvent: false})
            }else {
                this.isDisableReceiveGroup = false;
            }
        }
        for(let el of this.listAllField) {
            if(!this.listEnable.includes(el)) {
                if(el != 'receiveGroup') {
                    this.formAlert.get(el).disable({emitEvent: false})
                }else {
                    this.isDisableReceiveGroup = true;
                }
            }
        }
    }

    checkValidValue(event) {
        // cho phep backspace, delete
        if(event.keyCode == 8 || event.keyCode == 46) {
            return;
        }
        // ngoai khoang 0-9 chan (48-57) (96-105)
        if((event.keyCode >= 48 && event.keyCode <= 57) || (event.keyCode >= 96 && event.keyCode <= 105)) {
            return;
        }else {
            event.preventDefault()
        }
    }

    checkExistEmailList() {
        if (this.alertInfo.emailList == null || this.alertInfo.emailList == null ||
            this.alertInfo.emailList == '' || this.formAlert.controls.emailList.errors?.pattern) {
            return false;
        }
        const arr = this.alertInfo.emailList.split(',')
        let duplicate = false;
        const set = new Set();
        for(const el of arr) {
            if(!set.has(el)){
                set.add(el)
            }else {
                duplicate = true;
            }
        }
        return duplicate;
    }

    checkExistSmsList() {
        if (this.alertInfo.smsList == null || this.alertInfo.smsList == null ||
            this.alertInfo.smsList == '' || this.formAlert.controls.smsList.errors?.pattern) {
            return false;
        }
        const arr = this.alertInfo.smsList.split(',')
        let duplicate = false;
        const set = new Set();
        for(const el of arr) {
            if(!set.has(el)){
                set.add(el)
            }else {
                duplicate = true;
            }
        }
        return duplicate;
    }

    checkChange(event){
        if(this.alertInfo.groupId != null && this.alertInfo.subscriptionNumber != null) {
            this.messageCommonService.error(this.tranService.translate("global.message.onlySelectGroupOrSub"))
        }
    }

    check50Email(){
        if (this.alertInfo.emailList == null || this.alertInfo.emailList == null ||
            this.alertInfo.emailList == '' || this.formAlert.controls.emailList.errors?.pattern) {
            return false;
        }
        const arr = this.alertInfo.emailList.split(',')
        if(arr.length > 50) {
            return true;
        }else{
            return false;
        }
    }
    check50Sms(){
        if (this.alertInfo.smsList == null || this.alertInfo.smsList == null ||
            this.alertInfo.smsList == '' || this.formAlert.controls.smsList.errors?.pattern) {
            return false;
        }
        const arr = this.alertInfo.smsList.split(',')
        if(arr.length > 50) {
            return true;
        }else{
            return false;
        }
    }

    checkDisableSave() {
        const invalidControlsAlert = Object.keys(this.formAlert.controls)
            .filter(controlName => this.formAlert.controls[controlName].invalid);
        // console.log("Invalid fields in formAlert: ", invalidControlsAlert);
        if(this.formAlert.invalid || (this.alertInfo.groupId != null && this.alertInfo.subscriptionNumber != null)
            || ((this.checkExistSmsList() || this.check50Sms() || this.checkExistEmailList() || this.check50Email()
            || this.controlAlertReceiving.invalid || this.comboSelectCustomerControl.invalid || this.comboSelectContracCodeControl.invalid
            || this.comboSelectGroupSubControl.invalid || this.comboSelectSubControl.invalid) && this.alertInfo.eventType !== CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP
                && this.alertInfo.eventType !== CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD
            ) || (this.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD && this.controlComboSelectWallet.invalid) || this.isAlertNameExisted)
        {
            return true;
        }else {
            return false;
        }
    }

    checkChangeValueNotify() {
        if(this.alertInfo.value == null || this.alertInfo.value == undefined) {
            this.formAlert.get("notifyRepeat").disable({emitEvent : false})
            this.formAlert.get("notifyInterval").disable({emitEvent : false})
            this.repeat = false
        }else {
            this.formAlert.get("notifyRepeat").enable({emitEvent : false})
        }
    }

    checkValidNotifyRepeat(event) {
        // cho phep backspace, delete
        if(event.keyCode == 8 || event.keyCode == 46) {
            return;
        }
        if(this.alertInfo.notifyInterval != null && this.alertInfo.notifyInterval.toString().length == 2) event.preventDefault();
        // ngoai khoang 0-9 chan (48-57) (96-105)
        if((event.keyCode >= 48 && event.keyCode <= 57) || (event.keyCode >= 96 && event.keyCode <= 105)) {
            return;
        }else {
            event.preventDefault()
        }
    }

    checkValidValueNotify(event) {
        // cho phep backspace, delete
        if(event.keyCode == 8 || event.keyCode == 46) {
            return;
        }
        if(this.alertInfo.value != null && this.alertInfo.value.toString().length == 2) event.preventDefault();
        // ngoai khoang 0-9 chan (48-57) (96-105)
        if((event.keyCode >= 48 && event.keyCode <= 57) || (event.keyCode >= 96 && event.keyCode <= 105)) {
            return;
        }else {
            event.preventDefault()
        }
    }
    loadEventOptions() {
        let me = this;
        // this.eventOptions = [
        //     {name:me.tranService.translate("alert.eventType.exceededPakage"), value:CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_PACKAGE},
        //     {name:me.tranService.translate("alert.eventType.exceededValue"), value:CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_VALUE},
        //     // {name:me.tranService.translate("alert.eventType.sessionEnd"), value:CONSTANTS.ALERT_EVENT_TYPE.SESSION_END},
        //     // {name:me.tranService.translate("alert.eventType.sessionStart"), value:CONSTANTS.ALERT_EVENT_TYPE.SESSION_START},
        //     {name:me.tranService.translate("alert.eventType.smsExceededPakage"), value:CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_PACKAGE},
        //     {name:me.tranService.translate("alert.eventType.smsExceededValue"), value:CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_VALUE},
        //     {name:me.tranService.translate("alert.eventType.owLock"), value:CONSTANTS.ALERT_EVENT_TYPE.ONE_WAY_LOCK},
        //     {name:me.tranService.translate("alert.eventType.twLock"), value:CONSTANTS.ALERT_EVENT_TYPE.TWO_WAY_LOCK},
        //     // {name:me.tranService.translate("alert.eventType.noConection"), value:CONSTANTS.ALERT_EVENT_TYPE.NO_CONECTION},
        //     // {name:me.tranService.translate("alert.eventType.simExp"), value:CONSTANTS.ALERT_EVENT_TYPE.SIM_EXP},
        //     // {name:me.tranService.translate("alert.eventType.dataWalletExp") , value:CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP},
        //     {name:me.tranService.translate("alert.eventType.owtwlock") , value:CONSTANTS.ALERT_EVENT_TYPE.ONE_WAY_TWO_WAY_LOCK}
        // ]
        // if (this.userInfo.type == CONSTANTS.USER_TYPE.ADMIN) {
        //     this.eventOptions.push({name:me.tranService.translate("alert.eventType.dataWalletExp") , value:CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP})
        //     this.eventOptions.push({name:me.tranService.translate("alert.eventType.walletThreshold") , value:CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD})
        // }
        if (this.checkAuthen([CONSTANTS.PERMISSIONS.ALERT.CREATE])) {
            this.eventOptions.push({name:me.tranService.translate("alert.eventType.exceededPakage"), value:CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_PACKAGE})
            this.eventOptions.push({name:me.tranService.translate("alert.eventType.exceededValue"), value:CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_VALUE})
            this.eventOptions.push({name:me.tranService.translate("alert.eventType.smsExceededPakage"), value:CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_PACKAGE})
            this.eventOptions.push({name:me.tranService.translate("alert.eventType.smsExceededValue"), value:CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_VALUE})
            this.eventOptions.push({name:me.tranService.translate("alert.eventType.owLock"), value:CONSTANTS.ALERT_EVENT_TYPE.ONE_WAY_LOCK})
            this.eventOptions.push({name:me.tranService.translate("alert.eventType.twLock"), value:CONSTANTS.ALERT_EVENT_TYPE.TWO_WAY_LOCK})
            this.eventOptions.push({name:me.tranService.translate("alert.eventType.owtwlock") , value:CONSTANTS.ALERT_EVENT_TYPE.ONE_WAY_TWO_WAY_LOCK})
        }

        if (this.checkAuthen([CONSTANTS.PERMISSIONS.ALERT.CREATE_WALLET_EXPIRY])) {
            this.eventOptions.push({name:me.tranService.translate("alert.eventType.dataWalletExp") , value:CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP})
        }
        if (this.checkAuthen([CONSTANTS.PERMISSIONS.ALERT.CREATE_WALLET_THRESHOLD])) {
            this.eventOptions.push({name:me.tranService.translate("alert.eventType.walletThreshold") , value:CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD})
        }
    }

    changeWallet(wallet) {
        let me = this;
        if (wallet != undefined && wallet != null) {
            me.disableUnit = false
        } else {
            me.disableUnit = true
        }
        if (this.wallet == null) {
            this.alertInfo.emailList = null,
            this.alertInfo.smsList = null,
            this.alertInfo.unit = CONSTANTS.ALERT_UNIT.PERCENT ,
            this.unitWalletOptions = [
                {label: "%", value: 1}
            ]
        } else {
            this.alertInfo.walletSubCode = wallet.subCode
            this.alertInfo.emailList = wallet.email,
            this.alertInfo.smsList =  wallet.phone
            this.alertInfo.appliedPlan = wallet.page,
            this.alertInfo.unit = CONSTANTS.ALERT_UNIT.PERCENT
            this.alertInfo.value = 1

            if (this.wallet.trafficType.toUpperCase().trim() == 'Gói Data'.toUpperCase()) {
                this.unitWalletOptions = [
                    {label: "%", value: 1},
                    {label: "MB", value: 2},
                ]
            } else if (this.wallet.trafficType.toUpperCase().trim().includes('Gói SMS'.toUpperCase())) {
                this.unitWalletOptions = [
                    {label: "%", value: 1},
                    {label: "SMS", value: 3}
                ]
            }
        }
    }

    filerGroupByCustomer(event: any) {
        console.log(event)
        if(this.alertInfo.customerId != null && this.alertInfo.contractCode != null){
            this.paramSearchGroupSim = {customerCode: this.alertInfo.customerId.customerCode, contractCode: this.alertInfo.contractCode.contractCode}
            this.paramSearchSim = {customer: this.alertInfo.customerId.customerCode, contractCode: this.utilService.stringToStrBase64(this.alertInfo.contractCode.contractCode)};
            this.alertInfo.groupId = null;
            this.alertInfo.subscriptionNumber = null;
        }
    }
}
