import {Component, Inject, Injector, OnInit} from '@angular/core';
import {ComponentBase} from "../../../component.base";
import {MenuItem} from "primeng/api";
import {ColumnInfo, OptionTable} from "../../common-module/table/table.component";
import {ComboLazyControl} from "../../common-module/combobox-lazyload/combobox.lazyload";
import {AccountService} from "../../../service/account/AccountService";
import {CustomerService} from "../../../service/customer/CustomerService";
import {ContractService} from "../../../service/contract/ContractService";
import {FormBuilder} from "@angular/forms";
import {AutoCompleteCompleteEvent} from "primeng/autocomplete";
import {CONSTANTS} from "../../../service/comon/constants";
import {APILogService} from "../../../service/api-log/APILogService";

@Component({
  selector: 'app-api-log',
  templateUrl: './api-log.component.html',
  styleUrls: ['./api-log.component.scss']
})
export class ApiLogComponent extends ComponentBase implements OnInit {
    userType: number;
    searchInfo: {
        clientId: string | null,
        userName: string | null,
        moduleName: number | null,
        methodName: string | null,
        fullName: string | null,
        email: string | null,
        fromDate: Date | null,
        toDate: Date | null,
    }
    items: MenuItem[];
    home: MenuItem;
    selectItems: Array<any>;
    columns: Array<ColumnInfo>;
    dataSet: {
        content: Array<any>,
        total: number
    };
    optionTable: OptionTable;
    pageNumber: number;
    pageSize: number;
    sort: string;
    listModule: Array<any> = [];
    listApi: Array<any> = [];
    // listCombobox: Array<any> = [];
    minDate: Date;
    maxDate: Date;
    temp: Date;
    controlComboSelectCustomer : ComboLazyControl = new ComboLazyControl();
    paramSearchCustomer :{type: number} = {type: 7};

    constructor(@Inject(AccountService) private accountService: AccountService,
                private apiLogService: APILogService,
                private formBuilder: FormBuilder,
                private injector: Injector) {
        super(injector);
    }
    formSearchAPI: any;
    ngOnInit(): void {
        let me = this;
        const today = new Date();

        // Tạo minDate là 25 tháng trước
        this.minDate = new Date(today.getFullYear(), today.getMonth() - 25, today.getDate());

        // Giả sử maxDate là ngày hiện tại
        this.maxDate = today;
        this.userType = this.sessionService.userInfo.type;
        this.items = [{ label: this.tranService.translate("global.menu.accountmgmt") }, { label: this.tranService.translate("global.menu.apiLogs") }];
        this.home = { icon: 'pi pi-home', routerLink: '/' };
        this.searchInfo = {
            clientId: null,
            userName: null,
            moduleName: null,
            methodName: null,
            fullName: null,
            email: null,
            fromDate: null,
            toDate: null
        }
        this.formSearchAPI = this.formBuilder.group(this.searchInfo);
        this.selectItems = [];
        this.pageNumber = 0;
        this.pageSize = 10;
        this.sort = "requestTime,desc";

        this.optionTable = {
            hasClearSelected: true,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
        },
            this.columns = [
                {
                    name: this.tranService.translate("apiLog.label.clientID"),
                    key: "userName",
                    size: "250px",
                    align: "left",
                    isShow: true,
                    isSort: true,
                },
                {
                    name: this.tranService.translate("apiLog.label.fullName"),
                    key: "fullName",
                    size: "250px",
                    align: "left",
                    isShow: true,
                    isSort: true,
                },
                {
                    name: this.tranService.translate("apiLog.label.email"),
                    key: "email",
                    size: "250px",
                    align: "left",
                    isShow: true,
                    isSort: true,
                },
                {
                    name: this.tranService.translate("apiLog.label.module"),
                    key: "moduleName",
                    size: "150px",
                    align: "left",
                    isShow: true,
                    isSort: true,
                },
                {
                    name: this.tranService.translate("apiLog.label.api"),
                    key: "methodName",
                    size: "250px",
                    align: "left",
                    isShow: true,
                    isSort: true,
                },
                {
                    name: this.tranService.translate("apiLog.label.time"),
                    key: "requestTime",
                    size: "250px",
                    align: "left",
                    isShow: true,
                    isSort: true,
                    funcConvertText(value) {
                        return  me.utilService.convertUnixToDateString(value, true);
                    },
                },
                {
                    name: this.tranService.translate("apiLog.label.status"),
                    key: "errorDesc",
                    size: "125px",
                    align: "left",
                    isShow: true,
                    isSort: true,
                    funcConvertText(value,item){
                        return item.responseStatus == 200 ? me.tranService.translate("apiLog.label.success") : me.tranService.translate("apiLog.label.failed");
                    }
                },
                {
                    name: this.tranService.translate("apiLog.label.errorCode"),
                    key: "responseStatus",
                    size: "175px",
                    align: "left",
                    isShow: true,
                    isSort: true,
                },
            ]
        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
        this.messageCommonService.onload();
        this.getListModule();
        this.messageCommonService.offload();
        this.getListAPI("");
    }

    onSubmitSearch(){
        this.pageNumber = 0;
        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
    }

    search(page, limit, sort, params){
        let me = this;
        this.pageNumber = page;
        this.pageSize = limit;
        this.sort = sort;
        let dataParams = {
            page,
            size: limit,
            sort
        }
        Object.keys(this.searchInfo).forEach(key => {
            if(this.searchInfo[key] != null){
                dataParams[key] = this.searchInfo[key];
                if(key == "fromDate"){
                    dataParams[key] = this.utilService.convertDateToUnix(this.searchInfo.fromDate);
                }
                if(key == "toDate"){
                    this.temp = new Date(this.searchInfo.toDate);
                    this.temp.setDate(this.temp.getDate() + 1)
                    dataParams[key] = this.utilService.convertDateToUnix(this.temp);
                }
            }
        })
        me.messageCommonService.onload();
        this.apiLogService.search(dataParams, (response)=>{
            me.dataSet = {
                content: response.content,
                total: response.totalElements
            }
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }

    getListAPI(module){
        this.messageCommonService.onload();
        this.accountService.getListAPIByModule({module: module ? module : " "},(response)=>{
            this.listApi = response;
        },null,()=>{
            this.messageCommonService.offload()
        })
    }

    getListModule(){
        this.accountService.getListModule((response)=>{
            response.forEach(value => {
                this.listModule.push({ value });
            });
        })
    }

    // onChangeDateFrom(value){
    //     if(value){
    //         this.minDateTo = value;
    //     }else{
    //         this.minDateTo = null
    //     }
    // }
    //
    // onChangeDateTo(value){
    //     if(value){
    //         this.maxDateFrom = value;
    //     }else{
    //         this.maxDateFrom = new Date();
    //     }
    // }

    onChangeModule(){
        this.getListAPI(this.searchInfo.moduleName);
    }

    protected readonly CONSTANTS = CONSTANTS;
}
