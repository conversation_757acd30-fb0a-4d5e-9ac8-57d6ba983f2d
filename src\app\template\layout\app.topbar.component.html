<div class="layout-topbar header-cmp">
    <a class="layout-topbar-logo" routerLink="">
        <img src="assets/images/m2m.png" alt="logo">
        <!-- <img src="assets/images/vnpt_icon.svg" alt="logo"> -->
        <span class="topbar-title" style="font-size: xx-large; color: white; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">{{tranService.translate('global.titlepage.m2SubscriptionManagementSystem')}}</span>
    </a>
    <button #menubutton class="p-link layout-menu-button layout-topbar-button" (click)="layoutService.onMenuToggle()">
        <i class="pi pi-bars"></i>
    </button>

<!--    <button #topbarmenubutton class="p-link layout-topbar-menu-button layout-topbar-button" (click)="layoutService.showProfileSidebar()">-->
<!--        <i class="pi pi-ellipsis-v"></i>-->
<!--    </button>-->

    <div #topbarmenu class="layout-topbar-menu" [ngClass]="{'layout-topbar-menu-mobile-active': layoutService.state.profileSidebarVisible}">
        <choose-language></choose-language>
        <div class="ml-2" *ngIf="userInfo">
            <div (click)="session.toggle($event)" class="cursor-poiter"><i style="vertical-align: 3px;" class="pi pi-fw pi-user"></i>&nbsp;<span style="display: inline-block; max-width: 150px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;"
                 [pTooltip]="userInfo.fullName.length > 20 ? userInfo.fullName : ''">{{userInfo.fullName}}</span></div>
            <p-overlayPanel #session>
                <a class="text-black-alpha-90" [routerLink]="['/profile']" routerLinkActive="router-link-active" *ngIf="checkAuthen([CONSTANTS.PERMISSIONS.PROFILE.VIEW])">
                    <div class="p-2 hover:surface-300" style="min-width: 165px">
                        <i class="pi pi-user-edit"></i>&nbsp;
                        {{tranService.translate("login.label.editProfile")}}
                    </div>
                </a>
                <a class="text-black-alpha-90" [routerLink]="['profile/change-password']" routerLinkActive="router-link-active" >
                    <div class="p-2 hover:surface-300" style="min-width: 165px">
                        <i class="pi pi-lock"></i>&nbsp;
                        {{tranService.translate("login.label.changePassword")}}
                    </div>
                </a>
                <a class="text-black-alpha-90 cursor-pointer" (click)="logout()" routerLinkActive="router-link-active" >
                    <div class="p-2 hover:surface-300" style="min-width: 165px">
                        <i class="pi pi-sign-out"></i>&nbsp;
                        {{tranService.translate("login.label.logout")}}
                    </div>
                </a>
            </p-overlayPanel>
        </div>
    </div>
</div>
