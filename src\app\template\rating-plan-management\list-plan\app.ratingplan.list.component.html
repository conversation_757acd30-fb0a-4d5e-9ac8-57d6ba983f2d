<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{tranService.translate("global.menu.listplan")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
    <div class="col-5 flex flex-row justify-content-end align-items-center">
        <p-button *ngIf="checkAuthen([allPermissions.RATING_PLAN.CREATE])" styleClass="p-button-info" [label]="tranService.translate('global.button.create')" icon="" [routerLink]="['create']" routerLinkActive="router-link-active" ></p-button>
    </div>
</div>

<form [formGroup]="formSearch" (ngSubmit)="onSubmitSearch()" class="pb-2 pt-3 vnpt-field-set"   >
    <p-panel [toggleable]="true" [header]="tranService.translate('global.text.filter')">
        <div class="grid search-grid-3">
        <!-- Ten goi cuoc -->
        <div class="col-3">
                <span class="p-float-label">
                    <input pInputText
                           class="w-full"
                           pInputText id="name"
                           [(ngModel)]="searchInfo.name"
                           formControlName="name"
                    />
                    <label htmlFor="name">{{tranService.translate("ratingPlan.label.planName")}}</label>
                </span>
        </div>
        <!-- Trang thai -->
        <div class="col-3">
                <span class="p-float-label">
                    <p-dropdown styleClass="w-full" [showClear]="true"
                                id="status" [autoDisplayFirst]="false"
                                [(ngModel)]="searchInfo.status"
                                formControlName="status"
                                [options]="listStatus"
                                optionLabel="name"
                                optionValue="value"
                    ></p-dropdown>
                    <label class="label-dropdown" for="status">{{tranService.translate("ratingPlan.label.status")}}</label>
                </span>
        </div>
        <!-- chu ky -->
        <div class="col-3">
                <span class="p-float-label">
                    <p-dropdown styleClass="w-full" [showClear]="true"
                                id="cycleTimeUnit" [autoDisplayFirst]="false"
                                [(ngModel)]="searchInfo.cycleTimeUnit"
                                formControlName="cycleTimeUnit"
                                [options]="listCycle"
                                optionLabel="name"
                                optionValue="value"
                    ></p-dropdown>
                    <label class="label-dropdown" for="cycleTimeUnit">{{tranService.translate("ratingPlan.label.cycle")}}</label>
                </span>
        </div>
        <!-- hinh thuc thanh toan -->
        <div class="col-3">
                <span class="p-float-label">
                    <p-dropdown styleClass="w-full" [showClear]="true"
                                id="paidType" [autoDisplayFirst]="false"
                                [(ngModel)]="searchInfo.paidType"
                                formControlName="paidType"
                                [options]="listPaidType"
                                optionLabel="name"
                                optionValue="value"
                    ></p-dropdown>
                    <label class="label-dropdown" for="paidType">{{tranService.translate("ratingPlan.label.paidType")}}</label>
                </span>
        </div>
        <!-- loai khach hang -->
        <div class="col-3">
                <span class="p-float-label">
                    <p-dropdown styleClass="w-full" [showClear]="true"
                                id="customerType" [autoDisplayFirst]="false"
                                [(ngModel)]="searchInfo.customerType"
                                formControlName="customerType"
                                [options]="listCustomerType"
                                optionLabel="name"
                                optionValue="value"
                    ></p-dropdown>
                    <label class="label-dropdown" for="customerType">{{tranService.translate("ratingPlan.label.customerType")}}</label>
                </span>
        </div>
        <!-- pham vi goi cuoc -->
        <div class="col-3">
                <span class="p-float-label">
                    <p-dropdown styleClass="w-full" [showClear]="true"
                                id="ratingScope" [autoDisplayFirst]="false"
                                [(ngModel)]="searchInfo.ratingScope"
                                formControlName="ratingScope"
                                [options]="listRatingScope"
                                optionLabel="name"
                                optionValue="value"
                    ></p-dropdown>
                    <label class="label-dropdown" for="ratingScope">{{tranService.translate("ratingPlan.label.ratingScope")}}</label>
                </span>
        </div>
        <div class="col-3 pb-0">
            <p-button icon="pi pi-search"
                      styleClass="p-button-rounded p-button-secondary p-button-text button-search"
                      type="submit"
            ></p-button>
        </div>
    </div>
    </p-panel>
</form>

<table-vnpt
    [fieldId]="'id'"
    [(selectItems)]="selectItems"
    [columns]="columns"
    [dataSet]="dataSet"
    [options]="optionTable"
    [loadData]="search.bind(this)"
    [pageNumber]="pageNumber"
    [pageSize]="pageSize"
    [sort]="sort"
    [params]="searchInfo"
    [labelTable]="this.tranService.translate('global.menu.listplan')"
></table-vnpt>


<!-- dialog assign plan -->
<form [formGroup]="formSearchUser" (ngSubmit)="onSubmitSearchUser()">
    <div class="flex justify-content-center dialog-push-group">
        <p-dialog [header]="tranService.translate('ratingPlan.label.assignPlan')" [(visible)]="isShowDialogAssignPlan" [modal]="true" [style]="{ width: '850px' }" [draggable]="false" [resizable]="false">
            <div class="grid">
                <!-- Ten dang nhap -->
                <div class="col-3">
                    <span class="p-float-label">
                        <input pInputText
                               class="w-full"
                               pInputText id="username"
                               [(ngModel)]="searchInfoUser.username"
                               formControlName="username"
                        />
                        <label htmlFor="username">{{tranService.translate("ratingPlan.label.username")}}</label>
                    </span>
                </div>
                <!-- Ho ten -->
                <div class="col-3">
                    <span class="p-float-label">
                        <input pInputText
                               class="w-full"
                               pInputText id="fullName"
                               [(ngModel)]="searchInfoUser.fullName"
                               formControlName="fullName"
                        />
                        <label htmlFor="fullName">{{tranService.translate("ratingPlan.label.fullName")}}</label>
                    </span>
                </div>
                <!-- Thanh pho -->
                <div class="col-3" [class]="userType == allUserType.ADMIN ? '' : 'flex flex-row justify-content-start align-items-center'">
                    <span class="p-float-label" *ngIf="userType == allUserType.ADMIN">
                        <p-dropdown styleClass="w-full" [showClear]="true"
                                id="provinceCode" [autoDisplayFirst]="false"
                                [(ngModel)]="searchInfoUser.provinceCode"
                                [options]="listProvince"
                                optionLabel="display"
                                optionValue="code"
                                formControlName="provinceCode" [filter] = true
                        ></p-dropdown>
                        <label for="provinceCode">{{tranService.translate("ratingPlan.label.province")}}</label>
                    </span>
                    <span *ngIf="userType != allUserType.ADMIN">{{tranService.translate("account.label.province")}}: {{provinceInfo}}</span>
                </div>
                <div class="col-3 pb-0">
                    <p-button icon="pi pi-search"
                              styleClass="p-button-rounded p-button-secondary p-button-text button-search"
                              type="submit"
                    ></p-button>
                </div>
            </div>
            <table-vnpt
                    [fieldId]="'id'"
                    [pageNumber]="pageNumberAssign"
                    [pageSize]="pageSizeAssign"
                    [(selectItems)]="selectItemsUser"
                    [columns]="columnsInfoUser"
                    [dataSet]="dataSetAssignPlan"
                    [options]="optionTableAssignPLan"
                    [loadData]="searchUser.bind(this)"
                    [rowsPerPageOptions]="[5,10,20,25,50]"
                    [scrollHeight]="'400px'"
                    [sort]="sort"
                    [params]="searchInfoUser"
            ></table-vnpt>
            <div class="flex flex-row justify-content-center align-items-center" style="padding-top: 30px">
                <p-button styleClass="p-button-secondary" [label]="tranService.translate('global.button.cancel')" (click)="isShowDialogAssignPlan = false" [style]="{'margin-right': '20px'}"></p-button>
                <p-button styleClass="p-button-info" [label]="tranService.translate('global.button.save')" [disabled]="checkInValidAssignPlan()" (click)="assignPlan()" ></p-button>
            </div>
        </p-dialog>
    </div>
</form>

<div class="flex justify-content-center dialog-vnpt ">
    <p-dialog [header]="tranService.translate('global.menu.detailplan')" [(visible)]="isShowModalDetail" [modal]="true" [style]="{ width: '980px' }" [draggable]="false" [resizable]="false" *ngIf="isShowModalDetail">
        <div class="flex flex-row justify-content-start align-items-start">
            <p-button styleClass="mr-2 p-button-secondary" *ngIf="(ratingPlanInfo.status == planStatuses.CREATE_NEW || ratingPlanInfo.status == planStatuses.DEACTIVATED) && checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN.ACTIVE])" [label]="tranService.translate('global.button.active')" icon="" (click)="active()" ></p-button>
            <p-button styleClass="mr-2 p-button-secondary" *ngIf="ratingPlanInfo.status == planStatuses.PENDING && checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN.APPROVE])" [label]="tranService.translate('global.button.approve')" icon="" (click)="approve()" ></p-button>
            <p-button styleClass="mr-2 p-button-secondary" *ngIf="ratingPlanInfo.status == planStatuses.ACTIVATED && checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN.SUPPEND])" [label]="tranService.translate('global.button.suspend')" icon="" (click)="suspend()" ></p-button>
        </div>
        <p-card  styleClass="h-full" [style]="{'width': 'calc(100% + 16px)'}">
            <div class="col ratingPlan-detail custom-rating-detail pr-0 flex" style="border:1px solid black; margin-bottom: 20px">
                <div class="flex-1 ">
                    <div class="grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.planCode")}}</span>
                        <span class="inline-block col-fixed">{{ratingPlanInfo.code}}</span>
                    </div>
                    <div class=" grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.planName")}}</span>
                        <span class="inline-block col-fixed" style="min-width: 200px; max-width: 400px; word-wrap: break-word; white-space: normal;">{{ratingPlanInfo.name}}</span>
                    </div>
                    <div class=" grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.status")}}</span>
                        <div  class="text-white w-auto col">
                            <span [class]="getClassStatus(ratingPlanInfo.status)">{{getNameStatus(ratingPlanInfo.status)}}</span>
                        </div>
                    </div>
                    <div class=" grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.dispatchCode")}}</span>
                        <span class="inline-block col-fixed">{{ratingPlanInfo.dispatchCode}}</span>
                    </div>
                    <div class=" grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.customerType")}}</span>
                        <span class="inline-block col-fixed">{{getNameCustomerType(ratingPlanInfo.customerType)}}</span>
                    </div>
                    <div class=" grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.description")}}</span>
                        <span class="inline-block col-fixed" style="min-width: 200px; max-width: 400px; word-wrap: break-word; white-space: normal;">{{ratingPlanInfo.description}}</span>
                    </div>
                    <!--                    <div class="mt-1 grid">-->
                    <!--                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.motachuaco")}}</span>-->
                    <!--                    </div>-->
                </div>
                <div class="flex-1">
                    <div class="grid">
                        <span style="min-width: 200px;max-width: 200px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.subscriptionFee")}}</span>
                        <span class="inline-block col-fixed">{{ratingPlanInfo.subscriptionFee}}&nbsp; &nbsp; &nbsp; &nbsp; {{tranService.translate("ratingPlan.text.textDong")}}&nbsp;{{tranService.translate("ratingPlan.text.vat")}}</span>
                    </div>
                    <div class="grid">
                        <span class="inline-block col-fixed">
                            <span>
                                <p-radioButton [disabled]="true" [value]="subscriptionTypes[0].ip" [(ngModel)]="ratingPlanInfo.subscriptionType" inputId="typeIp1"></p-radioButton>
                                &nbsp;
                                <span>{{tranService.translate("ratingPlan.subscriptionType.post")}}</span>
                            </span>
                        </span>
                        <span class="inline-block col-fixed radioButton2" style="padding-left: 108px">
                            <span>
                                <p-radioButton [disabled]="true" [value]="subscriptionTypes[1].ip" [(ngModel)]="ratingPlanInfo.subscriptionType" inputId="typeIp2"></p-radioButton>
                                &nbsp;
                                <span>{{tranService.translate("ratingPlan.subscriptionType.pre")}}</span>
                            </span>
                        </span>
                    </div>

                    <div class="grid">
                        <span style="min-width: 200px;max-width: 200px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.cycle")}}</span>
                        <span class="inline-block col-fixed">{{getCycleTimeUnit(ratingPlanInfo.cycleTimeUnit)}}</span>
                    </div>
                    <div class="grid">
                        <span style="min-width: 200px;max-width: 200px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.cycleInterval")}}</span>
                        <span class="inline-block col-fixed">{{ratingPlanInfo.cycleInterval}}</span>
                    </div>
                    <div class="grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.reload")}}</span>
                        <div style="padding-top: 10px; padding-left: 13px">
                            <p-inputSwitch [(ngModel)]="checkedReload" [disabled]="true"></p-inputSwitch>
                        </div>
                    </div>
                    <div class="grid">
                        <span style="min-width: 200px;max-width: 200px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.ratingScope")}}</span>
                        <span class="inline-block col-fixed">{{getRatingScope(ratingPlanInfo.ratingScope)}}</span>
                    </div>
                    <div class="grid" *ngIf="ratingPlanInfo.ratingScope == planScopes.PROVINCE || ratingPlanInfo.ratingScope == planScopes.CUSTOMER">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.province")}}</span>
                        <span class="inline-block col-fixed" style="width: fit-content !important;">{{myProvices}}</span>
                    </div>
                    <div class="grid" *ngIf="ratingPlanInfo.ratingScope == planScopes.CUSTOMER">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px; cursor: pointer; text-decoration: underline; color: blue; transition: color 0.3s;" class="inline-block col-fixed" (click)="openDialogAddCustomerAccount()">{{tranService.translate("account.label.showCustomerAccount")}}</span>
<!--                        <span class="inline-block col-fixed" style="width: fit-content !important;">{{myProvices}}</span>-->
                    </div>
                </div>
            </div>

            <div class="mt-1 grid">
                <span style="font-size: 20px;margin-left: 10px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.flat")}}</span>
            </div>
            <div class="col ratingPlan-detail custom-rating-detail-limit pr-0 flex"  style="border:1px solid black; margin-bottom: 20px" id = "name">
                <div class="flex-1">
                    <div class="grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.limitDataUsage")}}</span>
                        <span class="inline-block col-fixed">{{ratingPlanInfo.limitDataUsage}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.dataMax")}}</span>
                        <span class="inline-block col-fixed">{{ratingPlanInfo.dataMax}}</span>
                    </div>
                </div>
                <div class="flex-1">
                    <div class="grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.limitSmsInside")}}</span>
                        <span class="inline-block col-fixed">{{ratingPlanInfo.limitSmsInside}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.limitSmsOutside")}}</span>
                        <span class="inline-block col-fixed">{{ratingPlanInfo.limitSmsOutside}}</span>
                    </div>
                </div>
            </div>

            <div class="mt-1 grid header-grid">
                <span style="font-size: 20px;margin-left: 10px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.flexible")}}</span>
                <div style="padding-top: 18px">
                    <p-inputSwitch [(ngModel)]="checkedFlexible" [disabled]="true"></p-inputSwitch>
                </div>
            </div>
            <div class="col ratingPlan-detail pr-0 flex"  style="border:1px solid black;" id = "flexible">
                <div class="flex-1">
                    <div class="grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.feePerDataUnit")}}</span>
                        <span class="inline-block col-fixed">{{ratingPlanInfo.feePerDataUnit}}</span>
                    </div>
                    <div class=" grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.squeezedSpeed")}}</span>
                        <span class="inline-block col-fixed">{{ratingPlanInfo.downSpeed}}</span>
                    </div>
                    <div class=" grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.feeSmsInside")}}</span>
                        <span class="inline-block col-fixed">{{ratingPlanInfo.feeSmsInside}}</span>
                    </div>
                    <div class=" grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.feeSmsOutside")}}</span>
                        <span class="inline-block col-fixed">{{ratingPlanInfo.feeSmsOutside}}</span>
                    </div>
                    <div class=" grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("ratingPlan.label.maximumFee")}}</span>
                        <span class="inline-block col-fixed">{{ratingPlanInfo.maximumFee}}</span>
                    </div>
                </div>
                <div class="flex-1">
                    <div class="grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">/&nbsp; &nbsp; &nbsp; {{ratingPlanInfo.dataRoundUnit}} &nbsp; &nbsp; &nbsp; &nbsp;   KB</span>
                    </div>
                    <div class="grid">
                        <span style="min-width: 200px;max-width: 200px;padding-bottom: 5px" class="inline-block col-fixed">/&nbsp; &nbsp; &nbsp; {{ratingPlanInfo.squeezedSpeed}}</span>
                    </div>
                </div>
            </div>
        </p-card>
    </p-dialog>
</div>
<div class="flex justify-content-center dialog-push-group">
    <p-dialog [header]="tranService.translate('account.label.showCustomerAccount')" [(visible)]="isShowDialogShowCustomerAccount" [modal]="true" [style]="{ width: '1000px' }" [draggable]="false" [resizable]="false">

        <table-vnpt
            [fieldId]="'id1'"
            [columns]="columnsInfoUserForDetail"
            [dataSet]="dataSetAssignPlanForDetail"
            [options]="optionTableShowCustomerAccount"
            scrollHeight="300px"
        ></table-vnpt>
    </p-dialog>
</div>

