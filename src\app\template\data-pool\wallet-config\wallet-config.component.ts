import { Component, Inject, Injector } from '@angular/core';
import { Form<PERSON>uilder, FormControl, FormGroup } from '@angular/forms';
import { MenuItem } from 'primeng/api';
import { ComponentBase } from 'src/app/component.base';
import { WalletConfigService } from 'src/app/service/datapool/WalletConfigService';

@Component({
  selector: 'app-wallet-config',
  templateUrl: './wallet-config.component.html',
  styleUrls: ['./wallet-config.component.scss']
})
export class WalletConfigComponent extends ComponentBase{

  items: MenuItem[];
  home: MenuItem;
  sendType: string[] = [];
  subscriptionDetail:any;

  constructor( @Inject(WalletConfigService) private configService: WalletConfigService,
    injector: Injector, 
    private formBuilder: FormBuilder
  ) {super(injector);}

  walletConfigForm = new FormGroup({
    sendTypeEmail: new FormControl(),
    sendTypeSMS: new FormControl(),
    subscription: new FormControl(),
    noticeDaySender: new FormControl(),
    noticeFrequencyDay: new FormControl(),
    noticeFrequencyNumber: new FormControl()
  });

  ngOnInit(){
    this.items = [{ label: this.tranService.translate(`global.menu.trafficManagement`) }, { label: this.tranService.translate("global.menu.walletConfig") } ];
    this.home = { icon: 'pi pi-home', routerLink: '/' };
    this.walletConfigForm.get('sendTypeSMS').disable({ emitEvent: false });
    this.walletConfigForm.get('noticeFrequencyNumber').disable({ emitEvent: false });
    this.walletConfigForm.get('noticeFrequencyNumber').setValue(1);
  };

  submitForm(){};

}
