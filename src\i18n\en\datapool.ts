export default{
    label:{
        shareMgmt:"Share Management",
        sharePhoneNumber:"Shared Phone Number",
        shareData:"Share Data",
        fullName:"Full Name",
        email:"Email",
        generalInfomation:"General Infomation",
        dataWallet:"Data Wallet",
        dataWalletPlaceHolder:"Choose data wallet",
        shareDate:"Share Date",
        description:"Description",
        receiverPhone:"Receiver Phone",
        remainData:"Remaining Data",
        purchasedData: "Purchased Data",
        revokeMessage:"Are you really sure to revoke sharing ?",
        phone:"Phone",
        phoneFull:"Phone",
        revokeData:"Revoke ${data}",
        sharingData:"Sharing Data (${type})",
        sharingDataNotType:"Sharing Data",
        percentage:"Percentage",
        shareInfo:"Share Info",
        walletCode: "Wallet Code",
        packageName: "Subscription",
        detailSharing:"Detail Sharing",
        payCode: "Pay Code",
        trafficType: "Traffic Type",
        packageCode: "Pakage Code",
        accuracyDateFrom: "Accuracy Date From",
        accuracyDateTo: "Accuracy Date To",
        usedTimeFrom: "Used Time From",
        usedTimeTo: "Used Time To",
        usedTime: "Used Time",
        accuracyDate: "Accuracy Date",
        tax: "Tax",
        accuracyWallet: "Accuracy Wallet",
        sendType:"Send Type",
        appliedSubscription:"Applied Subscription",
        cycle:"Cycle",
        sendNoticeExpired:"Send Notice Expired",
        sendNoticeExpiredBefore:"Send Notice Expired Before",
        day: "Day",
        noticeFrequency:"Auto Notice Frequency",
        time: "Times",
        sharedPhone:"Shared Phone",
        creator:"Creator",
        otpCode:"OTP Code",
        enterOtpCode:"Enter OTP code",
        authenMethod: "Authentication Method",
        subCode: "Subscription Code",
        stt: "STT",
        created: "CREATED",
        activity: "ACTIVITY",
        content: "CONTENT",
        operator: "OPERATOR",
        sharedTime: "Shared Time",
        usedDate: "Share usage until",
        shared: "Shared Data",
        detailWallet: "Detail Wallet",
        selectWallet:"Select Wallet",
        transactionCode:"Transaction Code",
        groupCode: "Group Code",
        groupName: "Group Name",
        detailGroup: "Detail Group Sub",
        editGroupShare: "Edit Group Share",
        createGroupShare: "Create Group Share",
        shareGroup: "Share Group",
        nameSub: "Name Subscription",
        shareNormal: "Share Normal",
        shareByGroup: "Share By Group",
        generalInfo: "General Info",
        listSubOfGroup: "List Sub Of Group",
        autoShareWalletDetail : "Group details shared automatically",
        renewalStatus : "Renewal status",
        autoShareWallet : "List of wallet groups shared automatically",
        walletList : "Wallet List",
        sharePhoneList : "Share Phone Number List",
        methodAutoShare: "Automatic sharing method",
        notification: "Notification",
        registerByPayCode: "Register by Payment Code",
        registerBySubCode: "Register by Wallet Code",
        autoShareGroup: "Auto Share Group List",
        autoShareGroupDetail: "Auto Share Wallet Group Details",
        lstWallet: "Wallet List",
        lstSub: "Shared Phone Number List",
        dateFrom: "From Date",
        dateTo: "Up To Date",
        timeUpTo: "Time Up To",
        autoSharing:"Automatic Sharing",
        typeShare: "Type Share",
        active: "Active Type",
        listShareError : "Error Share Phone Number List",
        downloadErrorFile : "Download Error File",
        addSharePhone : "Add sharing recipients",
        walletCodeShare : "Wallet code/ Payment code",
        viewPhoneList: "View Phone List.",
        phoneShareList: "Shared Phone List",
        status: "Status",
        msisdnShareSucess: "Phone number share success",
        msisdnShareFail: "Phone number share fail",
    },
    button:{
        share:"Share Data",
        equalSharing:"Equal Sharing",
        fixedAllocation:"Fixed Allocation",
        revokeSharing:"Revoke Sharing",
        createWallet: "Add Wallet",
        shareTraffic: "Share Traffic",
        add:"Add",
        delete:"Delete",
        importFile:"Import Receiver",
        shareByGroup: "ShareBy Group",
        deleteSub: "Delete Sub",
        editSub: "Edit Sub",
        addSharePhone : "Add phone numbers to share automatically",
        removeSharePhone : "Remove phone numbers to share automatically",
        dateFrom : "Usage period ends - From date",
        dateTo : "Usage period ends - Until date",
        no: "NO",
        register: "REGISTER",
        yes: "YES",
        addSubToGroupAuto: "Add auto-shared phone number",
        deleteSubInGroupAuto: "Delete auto-shared phone number",
        addShare : "Add each subscriber"
    },
    text: {
        tax: "Enter tax code",
        payCode: "Enter payment code",
        subCode: "Enter subscription code",
        importByFile: "Enter sharing information by file",
        importReceive: "Import recipient file",
        learnMore : "Learn more",
        notify : "Notification",
        nonOTPPayCode: "Will apply OTP-free sharing to all wallets under this payment code",
        nonOTPSubCode: "Will apply OTP-free sharing to this wallet code",
        noteAutoShare: "(When registering for OTP-free sharing, the wallet will be allowed to share automatically)",
        noteRegisPayCode: "(When registering for sharing without OTP, all wallets under this Payment Code will be allowed to share automatically)",
        noteCancelSubCode: "(When canceling OTP-free sharing, the wallet will not be allowed to share automatically. The subscribers who are being shared automatically from this wallet will be canceled)",
        noteCancelPayCode: "(When canceling OTP-free sharing by Payment Code, the wallets under this code will not be shared automatically. The subscribers who are being shared automatically from these wallets will be canceled)",
        hasPayCode: "This wallet has registered for OTP-free sharing by Payment Code '${payCode}'",
        hasntAuto: "This wallet hasn't registered for Auto Sharing",
        chooseRegistrationMethod : "Please choose a registration method",
        downloadErrorMessage : "The list contains invalid fields. Please check the downloaded file for more details."
    },
    placeholder:{
        fullName:"Enter Fullname",
        phone:"Enter Phone",
        email:"Enter Email",
        registerShare: "Register to share without OTP",
        cancelShare: "Cancel sharing without OTP",
        activeType: "Choose Active Type",
        typeShare: "Choose Type Share",
    },
    message:{
        otp:"You will receive a message that contains otp code from M2M",
        resendOtp:"Resend OTP",
        in:"in",
        sec:"second",
        patternError:"Wrong Format. Only Accept (a-z, A-Z, 0-9, -_)",
        digitError: "Phone number must be a number starting with 0 (10-11 characters)",
        delete: "Delete Share Info",
        confirmDelete: "Do you want to delete this share infomation ?",
        confirmRevoke:"Do you want to revoke sharing data ?",
        miniumSMS:"Minium share data is 10 SMS",
        miniumData:"Minium share data is 100 MB",
        exceededData:"Shared data exceeds remaining data",
        errorSearch:"Please select atleast 1 search item",
        existed:"The subscription already exists in the sharing list",
        notValidPhone: "Please choose Vinaphone number",
        existedPhone:"Phone already exists",
        otpIncorrect:"OTP is Incorrect",
        dataError: "Data traffic is multiple of 100 and has minium of 100 MB",
        smsError: "SMS traffic is multiple of 5 and has minium of 5 SMS",
        deleteSub: "Are you sure you want to delete the selected subscription?",
        maximumSubAdd: "Maximum of 50 subscribers can be added only",
        maximumSubDisplay: "Each group can only add a maximum of 3000 subscribers",
        duplicateSub: "Each subscriber is only allowed to belong to 1 group, subscriber ${data} is in group '${groupName}' so cannot be added to this group",
        dublicateShareInfo: "The subscriber number has been added to the list.",
        confirmRegisterAutoShareWallet: "Do you want to register OTP-free sharing for this wallet?",
        confirmRegisterAutoSharePayCode: "Do you want to register for OTP-free sharing for this Payment Code?",
        readMore: "Learn more",
        confirmCancelSubCode:"Do you want to cancel OTP-free sharing for this wallet?",
        confirmCancelPayCode:"Do you want to cancel OTP-free sharing for Payment Code '${payCode}'?",
        registerSuccess: "Registered successfully",
        cancelSuccess: "Cancelled successfully",
        invalidDataUsage : "The minimum data traffic is 100MB and is a multiple of 100",
        invalidSmsUsage : "The minimum data traffic is 100 SMS and is a multiple of 5",
        existSharePhone : "The phone number ${phoneNumber} is already in the group. If you add it multiple times, it will be automatically shared multiple times",
        deleteAutoShare : "Are you sure you want to remove automatic sharing of selected numbers to this wallet group?",
        deleteWarnAutoShare : "If removed from the group, automatic sharing to phone numbers from this wallet group will stop",
        existPhoneInTable: "The phone number ${phoneNumber} is already exist in the table",
        exceedTrafficShare: "Shared traffic for subscribers exceeds ${remainDataWallet} ${typeWallet} remaining in the wallet. Please reduce sharing traffic or delete subscriptions or choose another wallet",
        shareNotifySuccess : "Shared and added to group successfully ${success}/${total} phone number with valid information",
        shareNotifyFail : "Shared and added to group successfully ${success}/${total} phone number with valid information. Invalid information in error list",
        addSuccess : "Add successfully",
        shareOK: "Share successfully",
        shareNotifyBackground: "Request in progress. Processing will take a few minutes due to the large number of shared subscribers (>50). Please check the results later in the Activity History"
    },
    activeType:{
        buy: "Buy data",
        share: "Share data",
        accuracy: "Accuracy wallet",
        registerNonOTP: "Register sharing without OTP",
        cancelRegisterNonOTP: "Cancel register without OTP",
    },
    error:{
        existedGroupCode: "This shared group code already exists"
    },
    renewStatus : {
        notDueYet : "Not due yet",
        dueDate : "Due date",
        expired : "(Expired"
    },
    methodAutoShare: {
        none: "None",
        subCode: "Wallet Code",
        payCode: "Pay Code",
    },
    renewalStatus: {
        notDueYet: "Chưa đến hạn",
        due: "Đến hạn",
        expired: "Hết hạn",
    },
    typeShare:{
        manual: "Manual",
        auto: "Auto"
    },
    activityHistoryStatus: {
        fail: "Fail",
        success: "Success",
        processing: "Processing",
        completed: "Completed",
    }
}
