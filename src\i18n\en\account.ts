export default {
    label: {
        username: "<PERSON><PERSON><PERSON>",
        fullname: "Full Name",
        userType: "User Type",
        email: "Email",
        provinceCode: "Province Code",
        time: "Time",
        status: "Status",
        phone: "Phone Number",
        description: "Description",
        manager: "Manager Level",
        province: "Province/City",
        role: "Role",
        permission:{
            name: "Permission Name",
            object: "Object"
        },
        customerName: "Customer Name",
        oldPass : "Old password",
        newPass : "New password",
        confirmPass: "Confirm password",
        submitChangePass : "Agree & Sign In",
        linkExpired : "This password recovery has expired",
        cmpForgotPass : "M2M SIM - Forgot Password",
        deviceType: "Device Type",
        os: "Operating System",
        ip: "IP Address",
        managerName : "Manager",
        customerAccount : "Superior Customer Accouunt",
        generalInfo: "General Information",
        addCustomerAccount : "Add Customer Accouunt",
        showCustomerAccount : "Show Customer Accouunt",
        notiChangePass: "Password has expired. Please change your password to continue using. The new password is valid for 6 months from the date of the last password change."

    },
    text: {
        detailaccount: "Detail Account",
        infoAccount: "Info Account",
        active: "Active",
        inactive: "Inactive",
        account: "Account",
        titleChangeManageLevel: "Change Manager Level",
        selectAccount: "Select Account",
        inputUsername: "Input Account Name",
        inputFullname: "Input Full Name",
        inputEmail: "Input Email",
        inputPhone: "Input Phone Number",
        selectUserType: "Select User Type",
        selectRoles: "Select Roles",
        selectManager: "Select Manager Level",
        selectProvince: "Select Province/City",
        selectCustomers: "Select Customers",
        disagreePolicy: "You haven't agree to this policy yet",
        typeSelectAll: "Change All",
        typeSelectList: "Change By List",
        selectGDV : "Select manager",
        selectCustomerAccount : "Select superior customer account",
        addCustomer: "Add Customer",
        addContract: "Add Contract Code",
        grantApi : "Grant API",
        module : "Module",
        gen : "Gen",
        working: "Working",
        notWorking : "Not Working"
    },
    usertype: {
        admin: "Admin",
        customer: "Customer",
        district: "Teller",
        province: "Province/City",
        agency: "Agency"
    },
    userstatus: {
        active: "Active",
        inactive: "Inactive"
    },
    button: {
        disagreePolicy: "Disagree Policy",
        viewPolicyProtectPersonalData: "View Personal Data Protection Policy"
    },
    message: {
        customerRequired: "Must select at least one customer",
        managerRequired: '“Manage” cannot be left blank'
    }
}
