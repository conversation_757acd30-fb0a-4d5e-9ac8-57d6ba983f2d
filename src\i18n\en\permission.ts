export default {
    User: {
        User: "User",
        createUser: "Create User",
        deleteUser: "Delete User",
        getUser: "Get User",
        searchUser: "Search User",
        updateUser: "Update User",
        getProfileUser: "View Profile User",
        updateProfile: "Update Profile",
        changeManageData: "Change Manage Data"
    },
    SimGroup: {
        SimGroup: "Subcriber Group",
        createSimGroup: "Create Subcriber Group",
        deleteSimGroup: "Delete Subcriber Group",
        getSimGroup: "Get Subcriber Group",
        searchSimGroup: "Search Subcriber Group",
        updateSimGroup: "Update Subcriber Group"
    },
    Sim: {
        Sim: "Sim",
        createSim: "Create Subcriber",
        deleteSim: "Delete Subcriber",
        getSim: "Get Subcriber",
        searchSim: "Search Subcriber",
        updateSim: "Update Subcriber"
    },
    RatingPlanSim:{
        RatingPlanSim: "Rating Plan Subcriber",
        setRateSim: "Set Rate Subcriber",
        cancelRateSim: "Cancel Rate Subcriber",
        registerByFile: "Register By File",
        registerByList: "Register By List"
    },
    Role: {
        Role: "Role",
        createRole: "Create Role",
        deleteRole: "Delete Role",
        getRole: "Get Role",
        searchRole: "Search Role",
        updateRole: "Update Role"
    },
    Report: {
        Report: "Report",
        getListReportSimStatus: "Get List Report Subcriber Status",
        exportReportSimRatingPLan: "Export Report Subcriber Rating Plan",
        getListReportRequestApiLog: "Get List Report Report Api Log",
        searchReportRequestApi: "Search Report Request Api",
        getListReportMonthly: "Get List Report Monthly",
        getListReportManageSim: "Get List Report Manage Subcriber",
        getListReportHistorySim: "Get List Report History Subcriber",
        getListReportDetailSim: "Get List Report Detail Subcriber",
        getListReportCheckPostage: "Get List Report Check Postage",
        getListReportContract: "Get List Report Contract",
        getListReportBillingCustomer: "Get List Report Billing Customer"
    },
    RatingPlan: {
        RatingPlan: "Rating Plan",
        changeStatusRatingPlan: "Change Status Rating Plan",
        approveRatingPlan: "Approve Rating Plan",
        createRatingPlan: "Create Rating Plan",
        deleteRatingPlan: "Delete Rating Plan",
        getRatingPlan: "Get Rating Plan",
        issueRatingPlan: "Issue Rating Plan",
        searchRatingPlan: "Search Rating Plan",
        updateRatingPlan: "Update Rating Plan",
    },
    Permission:{
        Permission: "Permission",
        getPermission: "Get Permission",
        searchPermission: "Search Permission"
    },
    Device: {
        Device: "Device",
        createDevice: "Create Device",
        deleteDevice: "Delete Device",
        getDevice: "Get Device",
        searchDevice: "Search Device",
        updateDevice: "Update Device",
    },
    Customer: {
        Customer: "Customer",
        changeStatusCustomer: "Change Status Customer",
        getCustomer: "Get Customer",
        searchCustomer: "Search Customer",
        updateCustomer: "Update Customer",
        deleteCustomer: "Delete Customer",
        createCustomer: "Create Customer"
    },
    CustomAlert: {
        CustomAlert: "Custom Alert",
        createAlertConfig: "Create Alert Config",
        deleteAlertConfig: "Delete Alert Config",
        getAlertConfig: "Get Alert Config",
        searchAlertConfig: "Search Alert Config",
        updateAlertConfig: "Update Alert Config"
    },
    AlertRecvGrp: {
        AlertRecvGrp: "Alert Receiving Group",
        createAlertRecvGrp: "Create Alert Receiving Group",
        updateAlertRecvGrp: "Update Alert Receiving Group",
        getAlertRecvGrp: "Get Alert Receiving Group",
        deleteAlertRecvGrp: "Delete Alert Receiving Group",
        searchAlertRecvGrp: "Search Alert Receiving Group"
    },
    RptCfg: {
        RptCfg: "Report Dynamic Config",
        createRptCfg: "Create Report Dynamic Config",
        updateRptCfg: "Update Report Dynamic Config",
        getRptCfg: "Get Report Dynamic Config",
        deleteRptCfg: "Delete Report Dynamic Config",
        searchRptCfg: "Search Report Dynamic Config"
    },
    RptRecvGrp: {
        RptRecvGrp: "Report Dynamic Receiving Group",
        createRptRecvGrp: "Create Report Dynamic Receiving Group",
        updateRptRecvGrp: "Update Report Dynamic Receiving Group",
        getRptRecvGrp: "Get Report Dynamic Receiving Group",
        deleteRptRecvGrp: "Delete Report Dynamic Receiving Group",
        searchRptRecvGrp: "Search Report Dynamic Receiving Group"
    },
    RptSend: {
        RptSend: "Report Send Mail",
        updateRptSend: "Update Report Send Mail"
    },
    Contract: {
        Contract: "Contract",
        getContract: "Detail Contract",
        searchContract: "Search Contract"
    },
    Configuration: {
        Configuration: "Configuration",
        getConfiguration: "Get Configuration",
        searchConfiguration: "Search Configuration",
        updateConfiguration: "Update Configuration"
    },
    ApnSim: {
        ApnSim: "APN Subcriber",
        issueApnSim: "Issue APN Subcriber",
        searchApnSim: "Search APN Subcriber",
        getApnSim: "Detail APN Subcriber"
    },
    Apn: {
        Apn: "Apn",
        activeApn: "Active APN",
        cancelApn: "Cancel APN",
        completeApn: "Complete APN",
        createApn: "Create APN",
        deactiveApn: "Deactive APN",
        getApn: "Get APN",
        issueApn: "Issue APN",
        searchApn: "Search APN",
        sentEmailApn: "Send Email APN",
        updateApn: "Update APN"
    },
    AlertLog: {
        AlertLog: "Alert Log",
        getAlertLog: "Get Alert Log",
        searchAlertLog: "Search Alert Log",
    },
    Alert: {
        Alert: "Alert",
        ackAlert: "ACK Alert",
        getAlert: "Get Alert",
        searchAlert: "Search Alert",
        changeStatusAlert: "Change Status Alert",
        createAlert: "Create Alert",
        updateAlert: "Update Alert",
        deleteAlert: "Delete Alert",
        createAlertWalletThreshold: "Create Alert Wallet Threshold",
        createAlertWalletExpiry: "Create Alert Wallet Expiry",
        updateAlertWalletThreshold: "Update Alert Wallet Expiry",
        updateAlertWalletExpiry: "Update Alert Wallet Expiry",
    },
    RptContent: {
        RptContent: "Dynamic Report Content"
    },
    DynamicChart: {
        DynamicChart: "Dynamic Chart",
        getDashBoardContent: "View Dashboard Content",
    },
    CnfDynamicChart: {
        CnfDynamicChart: "Dynamic Chart Configuration",
        searchCnfDynamicChart: "Search Dynamic Chart Configuration",
        getCnfDynamicChart: "Detail Dynamic Chart Configuration",
        updateCnfDynamicChart: "Update Dynamic Chart Configuration",
        createCnfDynamicChart: "Create Dynamic Chart Configuration",
        deleteCnfDynamicChart: "Delete Dynamic Chart Configuration"
    },
    Log : {
        Log : "Activity Log"

    },
    Ticket: {
        Ticket: "Manage requests",
        getTicket: "View list request",
        createTicket: "Create request",
        updateTicket: "Update request"
    },
    Policy: {
        Policy: "Terms and policies",
        getPersonalDataPolicy: "Personal data protection policy"
    },
    Wallet:{
        Wallet: "Datapool",
        searchWallet: "View and Search Wallet",
        accuracyWallet: "Add Wallet",
        shareWallet: "Share Wallet",
        createShareInfo: "Create Share Info",
        searchShareInfo: "View and Search Share Info",
        walletHistory: "View Wallet History",
        // alertWalletThreshold: "Alert Wallet Threshold",
        // alertWalletExpiry: "Alert Wallet Expiry",
    },
    ShareGroup:{
        ShareGroup: "ShareGroup",
        searchShareGroup: "View Share Group List",
        createShareGroup: "Create Share Group",
        editShareGroup: "Edit Share Group",
        detailShareGroup: "Detail Share Group",
        deleteShareGroup: "Delete Share Group",
    },
    Diagnose: {
        Diagnose: "Diagnosis",
        searchDiagnose: "Search Diagnosis",
    },
    'API Partner': {
        'API Partner': "API Authorization",
    },
    Guide: {
        Guide: "Guide",
        guideIntegration: "Guide Integration",
    }
}
