import {NgModule} from "@angular/core";
import { DataPoolRoutingModule } from "./data-pool.routing.module";
import {DataPoolListComponent} from "./trafficWallet/list/data-pool.list.component";
import {CalendarModule} from "primeng/calendar";
import {CommonVnptModule} from "../common-module/common.module";
import {DialogModule} from "primeng/dialog";
import {CheckboxModule} from "primeng/checkbox";
import {BreadcrumbModule} from "primeng/breadcrumb";
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {InputTextModule} from "primeng/inputtext";
import {ButtonModule} from "primeng/button";
import {CommonModule, DatePipe} from "@angular/common";
import {TrafficWalletService} from "../../service/datapool/TrafficWalletService";
import {FieldsetModule} from "primeng/fieldset";
import {DropdownModule} from "primeng/dropdown";
import {PanelModule} from "primeng/panel";
import {DataPoolDetailComponent} from "./trafficWallet/detail/data-pool.detail.component";
import {CardModule} from "primeng/card";
import {InputTextareaModule} from "primeng/inputtextarea";
import {TableModule} from "primeng/table";
import { ShareDataDetailComponent } from "./share-mgmt/share-data-detail/share-data-detail.component";
import { ShareDataComponent } from "./share-mgmt/share-data/share-data.component";
import { ShareManagementService } from "src/app/service/datapool/ShareManagementService";
import { SimService } from "src/app/service/sim/SimService";
import { WalletConfigComponent } from "./wallet-config/wallet-config.component";
import { WalletConfigService } from "src/app/service/datapool/WalletConfigService";
import { ListShareComponent } from "./share-mgmt/list-share/list-share.component";
import { InputOtpModule } from "../common-module/ng-prime/inputotp/inputotp";
import { RadioButtonModule } from "primeng/radiobutton";
import {HistoryWalletListComponent} from "./history-wallet/history-wallet.list.component";
import {ShareWalletComponent} from "./trafficWallet/share-wallet/share-wallet.component";
import { OverlayPanelModule } from "primeng/overlaypanel";
import {GroupSubWalletListComponent} from "./group-sub/list/group-sub-wallet.list.component";
import {GroupSubWalletService} from "../../service/group-sub-wallet/GroupSubWalletService";
import {GroupSubWalletCreateComponent} from "./group-sub/create/group-sub-wallet.create.component";
import {GroupSubWalletEditComponent} from "./group-sub/edit/group-sub-wallet.edit.component";
import {MultiSelectModule} from "primeng/multiselect";
import {AutoShareWalletDetailComponent} from "./auto-share-wallet/detail/auto-share-wallet.detail.component";
import {WalletListTabComponent} from "./auto-share-wallet/detail/wallet-list-tab/wallet-list-tab.component";
import {SharePhoneListTabComponent} from "./auto-share-wallet/detail/share-phone-list-tab/share-phone-list-tab.component";
import {AutoShareService} from "../../service/datapool/AutoShareService";
import {AutoShareGroupListComponent} from "./auto-share-group/list/auto-share-group.list.component";
import {TabViewModule} from "primeng/tabview";
import {InputNumberModule} from "primeng/inputnumber";
import {SplitButtonModule} from "primeng/splitbutton";

@NgModule({
    imports: [
        DataPoolRoutingModule,
        CalendarModule,
        CommonVnptModule,
        DialogModule,
        CheckboxModule,
        BreadcrumbModule,
        FormsModule,
        InputTextModule,
        ButtonModule,
        CommonModule,
        FieldsetModule,
        DropdownModule,
        PanelModule,
        ReactiveFormsModule,
        CardModule,
        InputTextareaModule,
        TableModule,
        InputOtpModule,
        RadioButtonModule,
        OverlayPanelModule,
        MultiSelectModule,
        TabViewModule,
        InputNumberModule,
        SplitButtonModule,
        RadioButtonModule
    ],
    declarations: [
        ShareDataComponent,
        ShareDataDetailComponent,
        DataPoolListComponent,
        DataPoolDetailComponent,
        WalletConfigComponent,
        ListShareComponent,
        HistoryWalletListComponent,
        ShareWalletComponent,
        GroupSubWalletListComponent,
        GroupSubWalletCreateComponent,
        GroupSubWalletEditComponent,
        AutoShareWalletDetailComponent,
        WalletListTabComponent,
        SharePhoneListTabComponent,
        AutoShareGroupListComponent
    ],
    providers: [
        TrafficWalletService,
        ShareManagementService,
        SimService,
        WalletConfigService,
        DatePipe,
        GroupSubWalletService,
        AutoShareService
    ]
})

export class DataPoolModule {}
