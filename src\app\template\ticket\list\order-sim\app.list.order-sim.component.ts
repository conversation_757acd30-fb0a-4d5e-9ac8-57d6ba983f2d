import {ChangeDetectorRef, Component, Inject, Injector, OnInit} from "@angular/core";
import {ComponentBase} from "../../../../component.base";
import {TicketService} from "../../../../service/ticket/TicketService";
import {AccountService} from "../../../../service/account/AccountService";
import {AbstractControl, FormBuilder, FormGroup, ValidationErrors, ValidatorFn, Validators} from "@angular/forms";
import {MenuItem} from "primeng/api";
import {ColumnInfo, OptionTable} from "../../../common-module/table/table.component";
import {CONSTANTS} from "../../../../service/comon/constants";
import {SimTicketService} from "../../../../service/ticket/SimTicketService";
import {LogHandleTicketService} from "../../../../service/ticket/LogHandleTicketService";
import {b, el} from "@fullcalendar/core/internal-common";
import fa from "suneditor/src/lang/fa";

@Component({
    selector: "list-order-sim-ticket",
    templateUrl: './app.list.order-sim.component.html'
})

export class ListOrderSimTicketComponent extends ComponentBase implements OnInit {
    items: MenuItem[];
    home: MenuItem
    searchInfo: {
        provinceCode: string | null,
        email: string | null,
        contactPhone: string | null,
        contactName: string | null,
        type: number | null,
        status: any | null,
        dateFrom: Date | null,
        dateTo: Date | null,
    };
    columns: Array<ColumnInfo>;
    dataSet: {
        content: Array<any>,
        total: number
    };
    optionAddress: {
        provinceCode: number | null,
        districtCode: number | null,
    }
    selectItems: Array<any>;
    optionTable: OptionTable;
    pageNumber: number;
    pageSize: number;
    sort: string;
    formSearchTicket: any;
    listProvince: Array<any>;
    listTicketType: Array<any>;
    listTicketStatus: Array<any>;
    listTicketStatusSelected: Array<any>;
    mapTicketStatus: any;
    listEmail
        : Array<any>;
    isShowCreateRequest: boolean;
    isShowUpdateRequest: boolean;
    formTicketSim: any;
    formUpdateOrderSim: any
    maxDateFrom: Date | number | string | null = new Date();
    minDateTo: Date | number | string | null = null;
    maxDateTo: Date | number | string | null = new Date();
    ticket: {
        id: number
        contactName: string | null,
        contactEmail: string | null,
        contactPhone: string | null,
        content: string | null,
        note: string | null,
        cause: string | null,
        type: number | null, // 0: thay thế sim, 1: test sim, 2: order sim
        status: number | null,
        statusOld?: number | null,
        assigneeId: number | null
        detailAddress: string | null,
        address: string | null,
        quantity: number | null
        province: any | null,
        district: any | null,
        commune: any | null,
        createdBy: any | null,
        provinceCode: string| null,
    };
    listNotes: any[];
    typeRequest: string
    userInfo: any
    userType: any
    errorMinQuantity: boolean
    errorMaxQuantity: boolean
    selectedProvince: boolean = false;
    selectedDistrict: boolean = false;
    selectedCommune: boolean = false;
    disableSelectDistrict: boolean = true
    disableSelectCommune: boolean = true
    listImsis: number[] = [];
    newImsi: string;
    isShowStatus: boolean
    isRequiredStatus: boolean
    isShowNote: boolean
    isRequiredNote: boolean
    isShowAssignee: boolean
    isRequiredAssignee: boolean
    isShowListImsi: boolean
    isShowListNote: boolean
    isShowImsiInput: boolean
    isEmptyListImsi: boolean
    isEnableButtonSave: boolean
    isShowOrder: boolean
    formSearchHistoryOrder: any;
    isShowMesReqImsi: boolean;
    isShowMesMaxLenImsi: boolean;
    isShowMesExistImsi: boolean;
    searchHistoryOrder: {
        fromDate: Date | null,
        toDate: Date | null
    }
    itemInTable: any
    columnsHistory: any
    optionTableHistory: any
    dataSetHistory: any
    mapForm: any = {}
    formMailInput: any
    listActivatedAccount: number[];
    changeTable: boolean = false;
    constructor(
        @Inject(TicketService) private ticketService: TicketService,
        @Inject(AccountService) private accountService: AccountService,
        @Inject(SimTicketService) private simTicketService: SimTicketService,
        @Inject(LogHandleTicketService) private logHandleTicketService: LogHandleTicketService,
        private cdr: ChangeDetectorRef,
        private formBuilder: FormBuilder,
        private injector: Injector) {
        super(injector);
    }

    ngOnInit() {
        let me = this;
        me.changeTable = false;
        this.userInfo = this.sessionService.userInfo;
        this.searchHistoryOrder = {
            fromDate: null,
            toDate: null
        }
        this.formSearchHistoryOrder = this.formBuilder.group(this.searchHistoryOrder);
        this.isShowCreateRequest = false;
        this.isShowUpdateRequest = false;
        this.isEnableButtonSave = false;
        this.typeRequest = 'create'
        this.userType = CONSTANTS.USER_TYPE;
        this.listImsis = [];
        this.listNotes = [];
        this.newImsi = '';
        this.ticket = {
            id: null,
            contactName: null,
            contactEmail: null,
            contactPhone: null,
            content: null,
            note: null,
            cause: null,
            type: CONSTANTS.REQUEST_TYPE.ORDER_SIM, // 0: thay thế sim, 1: test sim, 2: đặt mua sim
            status: null,
            statusOld: null,
            assigneeId: null,
            address: null,
            quantity: null,
            commune: null,
            district: null,
            province: null,
            detailAddress: null,
            createdBy: null,
            provinceCode: null,
        };
        this.formMailInput = this.formBuilder.group({imsi: ""});
        this.listTicketType = [
            {
                label: this.tranService.translate('ticket.type.orderSim'),
                value: 2
            }
        ],
            this.listTicketStatusSelected = []
        this.mapTicketStatus = {
            0: [{
                label: me.tranService.translate('ticket.status.received'),
                value: 1
            },
                {
                    label: me.tranService.translate('ticket.status.reject'),
                    value: 3
                },
            ],
            1: [
                {
                    label: me.tranService.translate('ticket.status.inProgress'),
                    value: 2
                },
                {
                    label: me.tranService.translate('ticket.status.reject'),
                    value: 3
                }
            ],
            2: [
                {
                    label: me.tranService.translate('ticket.status.done'),
                    value: 4
                },
                {
                    label: me.tranService.translate('ticket.status.reject'),
                    value: 3
                }
            ]
        }
        this.isShowStatus = true;
        this.isRequiredStatus = true;
        this.isShowNote = true;
        this.isRequiredNote = true;
        this.isShowAssignee = true;
        this.isRequiredAssignee = true;
        this.isShowListImsi = false;
        this.isShowListNote = true;
        this.isShowImsiInput = true;
        this.isEmptyListImsi = false;
        this.isEnableButtonSave = false;
        this.isShowOrder = false;
        this.isShowMesReqImsi = false;
        this.isShowMesMaxLenImsi = false;
        this.isShowMesExistImsi = false;
        this.listTicketStatus = [
            {
                label: me.tranService.translate('ticket.status.new'),
                value: 0
            },
            {
                label: me.tranService.translate('ticket.status.received'),
                value: 1
            },
            {
                label: me.tranService.translate('ticket.status.inProgress'),
                value: 2
            },
            {
                label: me.tranService.translate('ticket.status.reject'),
                value: 3
            },
            {
                label: me.tranService.translate('ticket.status.done'),
                value: 4
            }
        ]
        this.searchInfo = {
            provinceCode: null,
            email: null,
            contactPhone: null,
            contactName: null,
            type: CONSTANTS.REQUEST_TYPE.ORDER_SIM,
            status: null,
            dateTo: null,
            dateFrom: null,
        }
        this.optionAddress = {
            provinceCode: 1,
            districtCode: 1,
        }
        this.columns = [
            {
                name: this.tranService.translate("ticket.label.province"),
                key: "provinceName",
                size: "150px",
                align: "left",
                isShow: this.userInfo.type == CONSTANTS.USER_TYPE.ADMIN,
                isSort: true
            },
            {
                name: this.tranService.translate("ticket.label.customerName"),
                key: "contactName",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: true,
                isShowTooltip: true,
                style: {
                    display: 'inline-block',
                    maxWidth: '350px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                }
            }, {
                name: this.tranService.translate("ticket.label.email"),
                key: "contactEmail",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: true,
                isShowTooltip: true,
                style: {
                    display: 'inline-block',
                    maxWidth: '350px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                }
            }, {
                name: this.tranService.translate("ticket.label.phone"),
                key: "contactPhone",
                size: "fit-content",
                align: "left",
                isShow: true,
                isSort: true
            }, {
                name: this.tranService.translate("ticket.label.quantity"),
                key: "quantity",
                size: "fit-content",
                align: "left",
                isShow: true,
                isSort: true
            }, {
                name: this.tranService.translate("ticket.label.deliveryAddress"),
                key: "address",
                size: "fit-content",
                align: "left",
                isShow: true,
                isSort: true,
                isShowTooltip: true,
                style: {
                    display: 'inline-block',
                    maxWidth: '350px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                }
            }, {
                name: this.tranService.translate("ticket.label.content"),
                key: "content",
                size: "fit-content",
                align: "left",
                isShow: true,
                isSort: true,
                isShowTooltip: true,
                style: {
                    display: 'inline-block',
                    maxWidth: '350px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                }
            },
            {
                name: this.tranService.translate("ticket.label.createdDate"),
                key: "createdDate",
                size: "fit-content",
                align: "left",
                isShow: true,
                isSort: true,
                funcConvertText(value) {
                    if (value == null) return null;
                    return me.utilService.convertDateToString(new Date(value))
                },
            },
            {
                name: this.tranService.translate("ticket.label.updatedDate"),
                key: "updatedDate",
                size: "fit-content",
                align: "left",
                isShow: true,
                isSort: true,
                funcConvertText(value) {
                    if (value == null) return null;
                    return me.utilService.convertDateToString(new Date(value))
                },
            },
            {
                name: this.tranService.translate("ticket.label.updateBy"),
                key: "updatedByName",
                size: "fit-content",
                align: "left",
                isShow: true,
                isSort: true
            }, {
                name: this.tranService.translate("ticket.label.status"),
                key: "status",
                size: "fit-content",
                align: "left",
                isShow: true,
                isSort: true,
                funcGetClassname: (value) => {
                    if (value == CONSTANTS.REQUEST_STATUS.NEW) {
                        return ['p-2', 'text-white', "bg-cyan-300", "border-round", "inline-block"];
                    } else if (value == CONSTANTS.REQUEST_STATUS.RECEIVED) {
                        return ['p-2', 'text-white', "bg-bluegray-500", "border-round", "inline-block"];
                    } else if (value == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {
                        return ['p-2', 'text-white', "bg-orange-400", "border-round", "inline-block"];
                    } else if (value == CONSTANTS.REQUEST_STATUS.REJECT) {
                        return ['p-2', 'text-white', "bg-red-500", "border-round", "inline-block"];
                    } else if (value == CONSTANTS.REQUEST_STATUS.DONE) {
                        return ['p-2', 'text-white', "bg-green-500", "border-round", "inline-block"];
                    }
                    return '';
                },
                funcConvertText: function (value) {
                    if (value == CONSTANTS.REQUEST_STATUS.NEW) {
                        return me.tranService.translate("ticket.status.new");
                    } else if (value == CONSTANTS.REQUEST_STATUS.RECEIVED) {
                        return me.tranService.translate("ticket.status.received");
                    } else if (value == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {
                        return me.tranService.translate("ticket.status.inProgress");
                    } else if (value == CONSTANTS.REQUEST_STATUS.REJECT) {
                        return me.tranService.translate("ticket.status.reject");
                    } else if (value == CONSTANTS.REQUEST_STATUS.DONE) {
                        return me.tranService.translate("ticket.status.done");
                    }
                    return "";
                }
            }
        ];

        this.columnsHistory = [
            {
                name: this.tranService.translate("ticket.label.time"),
                key: "createdDate",
                size: "fit-content",
                align: "left",
                isShow: true,
                isSort: false,
                funcConvertText: function (value) {
                    return me.utilService.convertLongDateToString(value);
                }
            },
            {
                name: this.tranService.translate("ticket.label.implementer"),
                key: "userName",
                size: "fit-content",
                align: "left",
                isShow: true,
                isSort: false
            },
            {
                name: this.tranService.translate("ticket.label.content"),
                key: "status",
                size: "fit-content",
                align: "left",
                isShow: true,
                isSort: false,
                funcGetClassname: (value) => {
                    if (value == CONSTANTS.REQUEST_STATUS.NEW) {
                        return ['p-2', 'text-white', "bg-cyan-300", "border-round", "inline-block"];
                    } else if (value == CONSTANTS.REQUEST_STATUS.RECEIVED) {
                        return ['p-2', 'text-white', "bg-bluegray-500", "border-round", "inline-block"];
                    } else if (value == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {
                        return ['p-2', 'text-white', "bg-orange-400", "border-round", "inline-block"];
                    } else if (value == CONSTANTS.REQUEST_STATUS.REJECT) {
                        return ['p-2', 'text-white', "bg-red-500", "border-round", "inline-block"];
                    } else if (value == CONSTANTS.REQUEST_STATUS.DONE) {
                        return ['p-2', 'text-white', "bg-green-500", "border-round", "inline-block"];
                    }
                    return '';
                },
                funcConvertText: function (value) {
                    if (value == CONSTANTS.REQUEST_STATUS.NEW) {
                        return me.tranService.translate("ticket.status.new");
                    } else if (value == CONSTANTS.REQUEST_STATUS.RECEIVED) {
                        return me.tranService.translate("ticket.status.received");
                    } else if (value == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {
                        return me.tranService.translate("ticket.status.inProgress");
                    } else if (value == CONSTANTS.REQUEST_STATUS.REJECT) {
                        return me.tranService.translate("ticket.status.reject");
                    } else if (value == CONSTANTS.REQUEST_STATUS.DONE) {
                        return me.tranService.translate("ticket.status.done");
                    }
                    return "";
                }
            }

        ];

        this.optionTableHistory = {
            hasShowIndex: true,
            paginator: false
        }

        this.dataSetHistory = {
            content: [],
            total: 0
        },


            this.optionTable = {
                hasClearSelected: false,
                hasShowChoose: false,
                hasShowIndex: true,
                hasShowToggleColumn: false,
                action: [
                    {
                        icon: "pi pi-info-circle",
                        tooltip: this.tranService.translate("global.button.view"),
                        func: function (id, item) {
                            me.handleRequest(id, item, 'view')
                        },
                    },
                    {
                        icon: "pi pi-file-edit",
                        tooltip: this.tranService.translate("global.button.edit"),
                        func: function (id, item) {
                            me.handleRequest(id, item, 'update')
                        },
                        funcAppear: function (id, item) {
                            if (me.userInfo.type == CONSTANTS.USER_TYPE.CUSTOMER || me.userInfo.type == CONSTANTS.USER_TYPE.ADMIN) return false;
                            if (!item.updatedBy && !item.assigneeId && (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE || me.userInfo.type == CONSTANTS.USER_TYPE.DISTRICT) && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) return true;
                            if ((me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && item.updatedBy !== me.userInfo.id) ||
                                (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && item.assigneeId != null)) {
                                return false;
                            }
                            if (me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) return true
                            else return false;
                        }
                    },
                    {
                        icon: "pi pi-eye",
                        tooltip: this.tranService.translate("ticket.label.orderHistory"),
                        func: function (id, item) {
                            me.openModalOrder(id, item)
                        },
                    },
                ]
            }
        this.pageNumber = 0;
        this.pageSize = 10;
        this.sort = "createdDate,desc";
        this.dataSet = {
            content: [],
            total: 0
        },
            this.formSearchTicket = this.formBuilder.group(this.searchInfo);
        this.formTicketSim = this.formBuilder.group(this.ticket);
        this.formUpdateOrderSim = this.formBuilder.group(this.ticket);
        this.getListProvince();
        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
        this.errorMinQuantity = false;
        this.errorMaxQuantity = false;
        this.listActivatedAccount = [];
    }

    search(page, limit, sort, params) {
        let me = this;
        me.changeTable = false
        this.pageNumber = page;
        this.pageSize = limit;
        this.sort = sort;
        let dataParams = {
            page,
            size: limit,
            sort
        }
        Object.keys(this.searchInfo).forEach(key => {
            if (this.searchInfo[key] != null) {
                if (key == "dateFrom") {
                    dataParams["dateFrom"] = this.searchInfo.dateFrom.getTime();
                } else if (key == "dateTo") {
                    dataParams["dateTo"] = this.searchInfo.dateTo.getTime();
                } else {
                    dataParams[key] = this.searchInfo[key];
                }
            }
        })
        this.dataSet = {
            content: [],
            total: 0
        }
        me.messageCommonService.onload();
        this.ticketService.searchTicket(dataParams, (response) => {
            me.dataSet = {
                content: response.content,
                total: response.totalElements
            }
            if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE ||
                me.userInfo.type == CONSTANTS.USER_TYPE.DISTRICT) {
                let listAssigneeId = Array.from(new Set(me.dataSet.content.filter(item => item.assigneeId !== null)
                    .map(item => item.assigneeId as number)));

                me.dataSet.content.forEach(item => {
                    if (item.updateBy !== null) {
                        listAssigneeId.push(item.updateBy as number);
                    }
                });

                const statusCheckListId = Array.from(new Set(listAssigneeId));

                me.accountService.getListActivatedAccount(statusCheckListId, (response) => {
                    me.listActivatedAccount = response;
                    me.optionTable = {
                        hasClearSelected: false,
                        hasShowChoose: false,
                        hasShowIndex: true,
                        hasShowToggleColumn: false,
                        action: [
                            {
                                icon: "pi pi-info-circle",
                                tooltip: this.tranService.translate("global.button.view"),
                                func: function (id, item) {
                                    me.handleRequest(id, item, 'view')
                                },
                            },
                            {
                                icon: "pi pi-file-edit",
                                tooltip: this.tranService.translate("global.button.edit"),
                                func: function (id, item) {
                                    me.handleRequest(id, item, 'update')
                                },
                                funcAppear: function (id, item) {
                                    //admin và khách hàng không được sửa
                                    if (me.userInfo.type == CONSTANTS.USER_TYPE.CUSTOMER || me.userInfo.type == CONSTANTS.USER_TYPE.ADMIN) return false;
                                    if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE]) && ( me.listActivatedAccount === undefined || me.listActivatedAccount == null )) return true;
                                    if ((me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) && ((item.assigneeId != null && me.listActivatedAccount.includes(item.assigneeId)))) return false;
            if ((me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) && ((item.assigneeId == null && item.updatedBy != null && me.listActivatedAccount.includes(item.updatedBy) && item.updatedBy != me.userInfo.id))) return false;
                                    if (!item.updatedBy && !item.assigneeId && (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE || me.userInfo.type == CONSTANTS.USER_TYPE.DISTRICT) && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) return true;
                                    if ((me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) && ((item.assigneeId != null && !me.listActivatedAccount.includes(item.assigneeId)) || (item.updatedBy != null && !me.listActivatedAccount.includes(item.updatedBy)))) return true;
                                    if ((me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && item.updatedBy !== me.userInfo.id) ||
                                        (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && item.assigneeId != null)) {
                                        return false;
                                    }
                                    if (me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) return true
                                    else return false;
                                }
                            },
                            {
                                icon: "pi pi-eye",
                                tooltip: this.tranService.translate("ticket.label.orderHistory"),
                                func: function (id, item) {
                                    me.openModalOrder(id, item)
                                },
                            },
                        ]
                    }
                    me.changeTable = true
                })
            }
        }, null, () => {
            me.messageCommonService.offload();
        })
    }

    resetTicket() {
        let me = this;
        this.ticket = {
            id: null,
            contactName: null,
            contactEmail: null,
            contactPhone: null,
            content: null,
            note: null,
            cause: null,
            type: CONSTANTS.REQUEST_TYPE.ORDER_SIM, // 0: thay thế sim,
            status: null,
            statusOld: null,
            assigneeId: null,
            address: null,
            quantity: null,
            province: null,
            district: null,
            commune: null,
            detailAddress: null,
            createdBy: null,
            provinceCode: null,
        };
        me.selectedProvince = false;
        me.selectedDistrict = false;
        me.selectedCommune = false;
    }

    onSubmitSearch() {
        this.pageNumber = 0;
        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
    }

    onSubmitSearchHistory() {
        this.searchHistory(0, 99999999, this.sort, this.searchHistoryOrder);
    }


    searchHistory(page, limit, sort, params) {
        let me = this;
        let dataParams = {
            page: page,
            size: limit,
            ticketId: me.itemInTable.id
        }
        Object.keys(params).forEach(key => {
            if (params[key] != null) {
                if (key == "fromDate") {
                    dataParams["logDateFrom"] = params.fromDate.getTime();
                } else if (key == "toDate") {
                    dataParams["logDateTo"] = params.toDate.getTime();
                } else {
                    dataParams[key] = params[key];
                }
            }
        })
        me.messageCommonService.onload();
        this.logHandleTicketService.search(dataParams, (response) => {
            me.dataSetHistory = {
                content: response.content,
                total: response.totalElements
            }
        }, null, () => {
            me.messageCommonService.offload();
        })
    }

    getListProvince() {
        this.accountService.getListProvince((response) => {
            this.listProvince = response.map(el => {
                return {
                    ...el,
                    display: `${el.code} - ${el.name}`
                }
            })
        })
    }

    // tạo yêu cầu đặt sim
    createOrderSim() {
        let me = this;
        this.messageCommonService.onload()
        let bodySend = {
            contactName: this.ticket.contactName,
            contactEmail: this.ticket.contactEmail,
            contactPhone: this.ticket.contactPhone,
            content: this.ticket.content != null ? this.ticket.content.trim() : null,
            address: this.ticket.detailAddress + ", " + this.ticket.commune.name + ", " + this.ticket.district.name + ", " + this.ticket.province.name,
            note: this.ticket.note != null ?this.ticket.note.trim() : null ,
            type: this.ticket.type,
            quantity: this.ticket.quantity,
        }
        this.ticketService.createTicket(bodySend, (resp) => {
            me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"));
            me.isShowCreateRequest = false
            me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo)
            // get mail admin tinh
            // me.ticketService.getListAssignee({email : '', provinceCode : this.userInfo.provinceCode, page : 0, size: 99999999}, (respAssignee)=>{
            //     let listProvinceConfig = respAssignee.content;
            //     let array = []
            //     for (let user of listProvinceConfig) {
            //         array.push({
            //             userId: user.id,
            //             ticketId: resp.id
            //         })
            //     }
            //     me.ticketService.sendMailNotify(array);
            // })
            // nếu KH đc gán cho GDV thì gửi mail cho GDV và danh sách admin đc cấu hình không thì chỉ gửi mail cho danh sách admin đc cấu hình
            // get mail admin tinh dc cau hinh
            me.ticketService.getDetailTicketConfig(me.userInfo.provinceCode, (resp1) => {
                let array = []
                for (let info of resp1.emailInfos) {
                    array.push({
                        userId: info.userId,
                        ticketId: resp.id
                    })
                }
                if (resp?.assigneeId) {
                    array.push({
                        userId: resp.assigneeId,
                        ticketId: resp.id
                    })
                }
                me.ticketService.sendMailNotify(array);
            })
        }, null, () => {
            me.messageCommonService.offload()
        })
    }

    updateOrderSim() {
        let me = this;
        me.messageCommonService.onload();
        let bodySend = {
            contactName: this.ticket.contactName,
            contactEmail: this.ticket.contactEmail,
            contactPhone: this.ticket.contactPhone,
            content: this.ticket.content,
            address: this.ticket.address,
            note: this.ticket.note,
            type: this.ticket.type,
            status: this.ticket.status,
            cause: this.ticket.cause,
            assigneeId: this.ticket.assigneeId,
            quantity: this.ticket.quantity,
            listLog: this.listNotes,
        }
        let createSimTicketBody = {
            ticketId: this.ticket.id,
            userCustomerId: this.ticket.createdBy,
            userHandleId: this.userInfo.id,
            imsis: this.listImsis,
        }
        // update ticket

        this.ticketService.updateTicket(this.ticket.id, bodySend, (resp) => {
            me.isShowCreateRequest = false
            me.isShowUpdateRequest = false;
            // console.log(resp);

            if (resp.assigneeId != null && resp.assigneeId != undefined) {
                me.ticketService.sendMailNotify([{
                    userId: resp.assigneeId,
                    ticketId: resp.id
                }])
            }
            if (me.ticket.statusOld == CONSTANTS.REQUEST_STATUS.IN_PROGRESS && me.ticket.status == CONSTANTS.REQUEST_STATUS.DONE) {
                me.simTicketService.create(createSimTicketBody, (res) => {
                    me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"));
                    me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo)

                })
            } else {
                me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"));
                me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo)
            }
        }, null, () => {
            me.messageCommonService.offload()
        })
    }

    showModalCreate() {
        let me = this;
        this.isShowCreateRequest = true
        this.typeRequest = 'create'
        this.resetTicket()
        if (this.userInfo.type === CONSTANTS.USER_TYPE.CUSTOMER) {
            this.ticket.contactName = this.userInfo.fullName;
            this.ticket.contactPhone = this.userInfo.phone;
            this.ticket.contactEmail = this.userInfo.email;
        }
        me.selectedProvince = false;
        me.selectedDistrict = false;
        me.selectedCommune = false;
        this.formTicketSim = this.formBuilder.group(this.ticket)
    }

    handleRequest(id, item, typeRequest: string) {
        let me = this
        me.typeRequest = typeRequest;
        this.isShowCreateRequest = false;
        this.isShowUpdateRequest = true;
        this.ticketService.getDetailTicket(item.id, (resp) => {
            this.ticket = {
                id: resp.id,
                contactName: resp.contactName,
                contactEmail: resp.contactEmail,
                contactPhone: resp.contactPhone,
                content: resp.content,
                note: resp.note,
                cause: resp.cause,
                type: resp.type, // 0: thay thế sim, 1: test sim, 2: order sim
                status: null,
                statusOld: resp.status,
                assigneeId: resp.assigneeId,
                quantity: resp.quantity,
                address: resp.address,
                commune: null,
                district: null,
                province: null,
                detailAddress: null,
                createdBy: resp.createdBy,
                provinceCode: resp.provinceCode,
            }
            this.logHandleTicketService.search({ticketId: this.ticket.id}, (res) => {
                this.listNotes = res.content;
                // for (let note of this.listNotes) {
                //     this.mapForm[note.id] = this.formBuilder.group(note);
                // }
                this.listNotes.forEach(note => {
                    this.mapForm[note.id] = this.formBuilder.group({
                        content: ['', [Validators.required, Validators.maxLength(255), this.noWhitespaceValidator()]]
                    });
                });
                me.initVisibleAndRequired();
            })
            this.simTicketService.search({ticketId: this.ticket.id, size: 1000}, (res) => {
                    let imsis: number[] = [];
                    res.content.forEach(item => {
                        imsis.push(item.imsi);
                    })
                    this.listImsis = imsis;
                    me.initVisibleAndRequired();
                }
            )
            this.formUpdateOrderSim = this.formBuilder.group(this.ticket);
            me.initVisibleAndRequired();
        })

        this.ticketService.getDetailTicketConfig(me.userInfo.provinceCode, (resp) => {
            this.listEmail = resp.emailInfos;
        })
    }

    preventCharacter(event) {
        if (event.ctrlKey) {
            return;
        }
        if (event.keyCode == 8 || event.keyCode == 13 || event.keyCode == 37 || event.keyCode == 39) {
            return;
        }
        // Chặn ký tự 'e', 'E' và dấu '+'
        if (event.keyCode == 69 || event.keyCode == 101 || event.keyCode == 107 || event.keyCode == 187) {
            event.preventDefault();
        }
        if (event.keyCode < 48 || event.keyCode > 57) {
            event.preventDefault();
        }
    }

    checkQuantity(event) {
        let me = this;
        if (event.value < 1) {
            me.errorMinQuantity = true;
        } else {
            me.errorMinQuantity = false;
        }
        if (event.value > 99999) {
            me.errorMaxQuantity = true;
        } else {
            me.errorMaxQuantity = false;
        }
    }

    onSelectProvince(event) {
        let me = this;
        if (me.ticket.province != null) {
            me.ticket.district = null
            me.ticket.commune = null
            me.optionAddress.provinceCode = event.code;
            me.disableSelectDistrict = false;
            me.disableSelectCommune = true;
            me.selectedProvince = true;
        } else {
            me.optionAddress.provinceCode = 1
            me.ticket.district = null
            me.ticket.commune = null
            me.disableSelectDistrict = true;
            me.disableSelectCommune = true;
            me.selectedProvince = false;
        }
    }

    onSelectDistrict(event) {
        let me = this;
        if (me.ticket.district != null) {
            me.ticket.commune = null
            me.optionAddress.districtCode = event.code;
            me.disableSelectCommune = false;
            me.selectedDistrict = true;
        } else {
            me.ticket.commune = null
            me.optionAddress.districtCode = 1;
            me.disableSelectCommune = true;
            me.selectedDistrict = false
        }
    }

    onSelectCommune(event) {
        let me = this;
        if (me.ticket.commune != null) {
            me.selectedCommune = true
        } else {
            me.selectedCommune = false
        }
    }

    onChangeDateFrom(value) {
        if (value) {
            this.minDateTo = value;
        } else {
            this.minDateTo = null
        }
    }

    onChangeDateTo(value) {
        if (value) {
            this.maxDateFrom = value;
        } else {
            this.maxDateFrom = new Date();
        }
    }

    addImsi() {
        let me = this;
        if (this.newImsi !== undefined && this.newImsi && !isNaN(Number(this.newImsi))) {
            this.listImsis.push(Number(this.newImsi));
            this.newImsi = '';
        }
        if (this.listImsis.length == 0) {
            this.isEmptyListImsi = true
        } else {
            this.isEmptyListImsi = false
        }
        this.checkVisibleAndRequired()
    }

    removeImsi(i: number) {
        let me = this;
        this.listImsis.splice(i, 1);
        if (this.listImsis.length == 0 && this.ticket.status == CONSTANTS.REQUEST_STATUS.DONE) {
            this.isEmptyListImsi = true
        } else {
            this.isEmptyListImsi = false
        }
        this.checkVisibleAndRequired()
    }

    checkImsiInput() {
        let me = this
        if (this.newImsi !== undefined && this.newImsi != '') {
            if (this.newImsi.toString().length > 18) {
                me.isShowMesMaxLenImsi = true
            } else {
                me.isShowMesMaxLenImsi = false
            }
            if (this.listImsis.includes(Number((this.newImsi)))) {
                me.isShowMesExistImsi = true;
            } else {
                me.isShowMesExistImsi = false;
            }
        }
    }

    getValueStatus(value) {
        let me = this;
        {
            if (value == CONSTANTS.REQUEST_STATUS.NEW) {
                return me.tranService.translate("ticket.status.new");
            } else if (value == CONSTANTS.REQUEST_STATUS.RECEIVED) {
                return me.tranService.translate("ticket.status.received");
            } else if (value == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {
                return me.tranService.translate("ticket.status.inProgress");
            } else if (value == CONSTANTS.REQUEST_STATUS.REJECT) {
                return me.tranService.translate("ticket.status.reject");
            } else if (value == CONSTANTS.REQUEST_STATUS.DONE) {
                return me.tranService.translate("ticket.status.done");
            }
            return "";
        }
    }

    getValueNote(content) {
        let me = this;
        let maxContentLength = 23; // Độ dài tối đa hiển thị nội dung
        if (content != null)
        return content.length > maxContentLength ? content.slice(0, maxContentLength) + '...' : content;
    }

    initVisibleAndRequired() {
        let me = this;
        // view
        if (me.typeRequest == 'view') {
            me.isShowStatus = false;
            me.isRequiredStatus = false;
            me.isShowImsiInput = false;
            me.isShowNote = false;
            me.isShowAssignee = false;
            me.isShowMesReqImsi = false;
            me.isShowMesMaxLenImsi = false;
            if (me.ticket.statusOld == CONSTANTS.REQUEST_STATUS.DONE) {
                me.isShowListImsi = true;
            } else {
                me.isShowListImsi = false;
            }
            if (me.userInfo.type == me.userType.PROVINCE && me.ticket.assigneeId != null) {
                me.isShowAssignee = true;
            } else {
                me.isShowAssignee = false;
            }
        }
        if (me.typeRequest == 'update') {
            // ghi chú, trạng thái, gán xử lý không bắt buộc
            this.isRequiredNote = false
            this.isRequiredStatus = false
            this.isRequiredAssignee = false;

            //tắt nút lưu
            me.isEnableButtonSave = false;

            //Hiển thị chuyển trạng thái, ghi chú
            me.isShowStatus = true;
            me.isShowNote = true;

            //Chỉ hiển thị chuyển xử lý với admin tỉnh khi chưa chuyển xử lý
            if (me.userInfo.type == me.userType.PROVINCE && me.ticket.assigneeId == null) {
                me.isShowAssignee = true;
            } else {
                me.isShowAssignee = false;
            }

            // chỉ cho nhập imsi khi đang xử lý
            if (me.ticket.statusOld == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {
                me.isShowImsiInput = true;
                if (me.ticket.status == CONSTANTS.REQUEST_STATUS.DONE && me.listImsis.length <= 0) {
                    me.isEmptyListImsi = true
                } else {
                    me.isEmptyListImsi = false;
                }
            } else {
                me.isShowImsiInput = false;
            }

            // if (me.ticket.statusOld == CONSTANTS.REQUEST_STATUS.IN_PROGRESS && me.ticket.statusOld == CONSTANTS.REQUEST_STATUS.DONE){
            //     me.isShowListImsi = true;
            // } else {
            //     me.isShowListImsi = false;
            // }

            // Hiển thị imsi khi đang xử lý
            if (me.ticket.statusOld == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {
                me.isShowListImsi = true;
            } else {
                me.isShowListImsi = false;
            }
            // Ẩn chuyển xử lý nếu không phải yêu cầu mới
            if (me.ticket.statusOld != CONSTANTS.REQUEST_STATUS.NEW) {
                me.isShowAssignee = false;
            }

        }

        // trạng thái hoàn thành, từ chối
        if (me.ticket.statusOld == CONSTANTS.REQUEST_STATUS.DONE || me.ticket.statusOld == CONSTANTS.REQUEST_STATUS.REJECT) {
            // me.isShowStatus = false;
            me.isShowAssignee = false;

            if (me.userInfo.type == me.userType.PROVINCE && me.ticket.assigneeId != null) {
                me.isShowAssignee = true;
            } else {
                me.isShowAssignee = false;
            }
        }


        // Ân list note trống
        if (this.listNotes.length == 0) {
            me.isShowListNote = false
        } else {
            me.isShowListNote = true;
        }
        me.isEnableButtonSave = true;
    }

    checkVisibleAndRequired() {
        let me = this;
        // nếu nhập trạng thái thì bắt buộc nhập ghi chú
        if (this.ticket.status != null) {
            me.isRequiredNote = true;
            me.isShowAssignee = false
        } else {
            me.isRequiredNote = false;
            if (me.userInfo.type == me.userType.PROVINCE && me.ticket.assigneeId == null) {
                me.isShowAssignee = true;
            }
        }

        if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE) {
            if (this.ticket.assigneeId == null && this.ticket.status == null) {
                this.isShowStatus = true
                this.isShowNote = true
                this.isShowAssignee = true
            } else if (this.ticket.assigneeId != null && me.listActivatedAccount.includes(me.ticket.assigneeId)) {
                this.isShowStatus = false
                this.isShowNote = false
            } else if (this.ticket.status != null || (this.ticket.cause != null && me.ticket.cause.trim() != '')) {
                this.isShowStatus = true
                this.isShowNote = true
            }
        } else if (me.userInfo.type == CONSTANTS.USER_TYPE.DISTRICT) {
            me.isShowStatus = true
            me.isShowNote = true
        }

        if (me.ticket.statusOld == CONSTANTS.REQUEST_STATUS.IN_PROGRESS && me.ticket.status == CONSTANTS.REQUEST_STATUS.DONE && this.listImsis.length == 0) {
            me.isEmptyListImsi = true
            me.isShowMesReqImsi = true
        } else {
            me.isEmptyListImsi = false;
            me.isShowMesReqImsi = false;
        }

        if (me.ticket.status!= null && (me.ticket.cause != null && me.ticket.cause.trim() == '')) {
            me.isEnableButtonSave = false;
        } else {
            me.isEnableButtonSave = true;
        }

        // Ẩn chuyển xử lý nếu không phải yêu cầu mới
        if (me.ticket.statusOld != CONSTANTS.REQUEST_STATUS.NEW) {
            me.isShowAssignee = false;
        }

        this.cdr.detectChanges();
    }

    openModalOrder(id, item) {
        this.isShowOrder = true;
        this.itemInTable = item;
        this.onSubmitSearchHistory()
    }

    getFirstErrorMessage(): string | null {
        if (this.isShowMesReqImsi) {
            return this.tranService.translate("global.message.required");
        }
        if (this.isShowMesMaxLenImsi) {
            return this.tranService.translate("ticket.message.imsiMaxLength");
        }
        if (this.isShowMesExistImsi) {
            return this.tranService.translate("ticket.message.imsiIsExist");
        }
        return null;
    }
    isFormValid() {
        return Object.values(this.mapForm).every((formGroup: FormGroup) => formGroup.valid);
    }
    noWhitespaceValidator(): ValidatorFn {
        return (control: AbstractControl): ValidationErrors | null => {
            const isWhitespace = (control.value || '').trim().length === 0;
            const isValid = !isWhitespace;
            return isValid ? null : {whitespace: true};
        }
    };
    onKeyDownNote(event): void {
        if (event.key === ' ' && (this.ticket.note == null || this.ticket.note != null && this.ticket.note.trim() === '')) {
            event.preventDefault();
        }

        if (this.ticket.note != null && this.ticket.note.trim() != '') {
            this.ticket.note = this.ticket.note.trimStart().replace(/\s{2,}/g, ' ');
            return;
        }
    }
    onKeyDownContent(event) {
        if (event.key === ' ' && (this.ticket.content == null || this.ticket.content != null && this.ticket.content.trim() === '')) {
            event.preventDefault();
        }

        if (this.ticket.content != null && this.ticket.content.trim() != '') {
            this.ticket.content = this.ticket.content.trimStart().replace(/\s{2,}/g, ' ');
            return;
        }
    }
    onKeyDownCause(event) {
        if (event.key === ' ' && (this.ticket.cause == null || this.ticket.cause != null && this.ticket.cause.trim() === '')) {
            event.preventDefault();
        }

        if (this.ticket.cause != null && this.ticket.cause.trim() != '') {
            this.ticket.cause = this.ticket.cause.trimStart().replace(/\s{2,}/g, ' ');
            return;
        }
    }
    onKeyDownNoteContent(event: KeyboardEvent, note: any): void {
        if (event.key === ' ' && (!note.content || note.content.trim() === '')) {
            event.preventDefault();
        }

        if (note.content && note.content.trim() !== '') {
            note.content = note.content.trimStart().replace(/\s{2,}/g, ' ');
            return;
        }
    }
    protected readonly CONSTANTS = CONSTANTS;
}
