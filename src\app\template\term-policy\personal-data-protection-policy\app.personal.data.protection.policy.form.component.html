<div class="p-2">
    <p class="text-center font-bold text-xl uppercase">Thông báo về việc bảo vệ và xử lý dữ liệu cá nhân</p>

    <p class="text-justify"><PERSON><PERSON><PERSON> gửi Quý khách hàng.</p>

    <p class="text-justify">Xin trân trọng thông báo đến Quý Khách hàng về việc xử lý dữ liệu cá nhân của Khách hàng theo Nghị định số 13/2023/NĐ-CP v/v bảo vệ dữ liệu cá nhân được Chính phủ ban hành ngày 17/04/2023.</p>

    <p class="text-justify">Chúng tôi đặc biệt coi trọng quyền riêng tư cũng như đề cao tính bảo mật dữ liệu của khách hàng. <PERSON><PERSON> vậy, chúng tôi rất cẩn trọng và nghiêm ngặt xử lý các dữ liệu cá nhân được Quý Khách hàng đã cung cấp. <PERSON>, chúng tôi cam kết luôn tuân thủ đầy đủ và nghiêm chỉnh tất cả các quy định của pháp luật và sẽ luôn nỗ lực để bảo vệ dữ liệu cá nhân của Quý Khách hàng theo Chính Sách Bảo Mật Dữ liệu Cá nhân của Tập đoàn VNPT.</p>

    <p class="text-justify">Nhằm bảo đảm quyền lợi khách hàng và tuân thủ quy định pháp luật, hệ thống sẽ thường xuyên cập nhật, sửa đổi, bổ sung Điều khoản và Chính sách nêu trên. Các thay đổi đó sẽ có hiệu lực kể từ thời điểm được công bố <a (click)="goToPageContent()" style="cursor: pointer;">tại đây</a>. Khi Quý Khách tiếp tục sử dụng dịch vụ sau khi các thay đổi này có hiệu lực sẽ được coi là đã chấp thuận các sửa đổi, bổ sung đó. Quý Khách vui lòng thường xuyên kiểm tra <a (click)="goToPageContent()" style="cursor: pointer;">tại đây</a> để theo dõi các thay đổi, cập nhật của chúng tôi.</p>

    <p class="text-justify">Chi tiết của Chính sách bảo vệ dữ liệu cá nhân, Quý khách vui lòng truy cập <a (click)="goToPageContent()" style="cursor: pointer;">tại đây</a>.</p>

    <p class="text-justify">
        <span>Trân trọng thông báo</span>
        <br>
        <span>Hệ thống Quản lý Thuê bao M2M.</span>
    </p>
    <div class="flex flex-row justify-content-center align-items-center">
        <div>
            <p-checkbox [(ngModel)]="agreePolicy" [binary]="true" inputId="binary"></p-checkbox>
            <span class="ml-2" style="vertical-align: 2px;">{{tranService.translate("global.text.readandagree")}} <a (click)="goToPageContent()" style="cursor: pointer;"><span class="text-color-info" [ngStyle]="{color: 'var(--highlight-text-color)'}">{{tranService.translate("global.menu.termpolicy")}}</span></a></span>
        </div>
        <div>
            <p-button class="p-button-secondary ml-8" [label]="tranService.translate('global.button.confirm')" [disabled]="!agreePolicy" (onClick)="agreePersonalDataProtectionPolicy()"></p-button>
        </div>
    </div>
</div>
