<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <p-breadcrumb class="max-w-full col-12" [model]="items" [home]="home"></p-breadcrumb>
</div>

<p-card styleClass="mt-3 responsive-form">
    <div class="w-full">
        <p-tabView (onChange)="onTabChange($event)">
            <p-tabPanel header="{{tranService.translate('account.label.generalInfo')}}">
                <div class="flex flex-row justify-content-between profile-create" *ngIf="accountResponse">
                    <div style="width: 49%;">
                        <!-- username -->
                        <div class="w-full field grid">
                            <label htmlFor="accountName" class="col-fixed" style="width:180px">{{tranService.translate("account.label.username")}}</label>
                            <div class="col">
                                {{accountResponse.username}}
                            </div>
                        </div>
                        <!-- status -->
                        <div class="w-full field grid">
                            <label htmlFor="fullName" class="col-fixed" style="width:180px">{{tranService.translate("account.label.status")}}</label>
                            <div class="col wrap-div">
                                {{getStringUserStatus(accountResponse.status)}}
                            </div>
                        </div>
                        <!-- fullname -->
                        <div class="w-full field grid ">
                            <label htmlFor="fullName" class="col-fixed" style="width:180px;height: fit-content;">{{tranService.translate("account.label.fullname")}}</label>
                            <div class="col wrap-div" style="width: calc(100% - 180px);overflow-wrap: break-word;">
                                {{accountResponse.fullName}}
                            </div>
                        </div>
                        <!-- phone -->
                        <div class="w-full field grid">
                            <label htmlFor="phone" class="col-fixed" style="width:180px">{{tranService.translate("account.label.phone")}}</label>
                            <div class="col">
                                {{accountResponse.phone}}
                            </div>
                        </div>
                        <!-- email -->
                        <div class="w-full field grid">
                            <label htmlFor="email" class="col-fixed" style="width:180px;height: fit-content;">{{tranService.translate("account.label.email")}}</label>
                            <div class="col wrap-div" style="width: calc(100% - 180px);overflow-wrap: break-word;">
                                {{accountResponse.email}}
                            </div>
                        </div>
                        <!-- description -->
                        <div class="w-full field grid">
                            <label htmlFor="description" class="col-fixed" style="width:180px">{{tranService.translate("account.label.description")}}</label>
                            <div class="col">
                                {{accountResponse.description}}
                            </div>
                        </div>
                    </div>
                    <div style="width: 49%;">
                        <!-- loai tai khoan -->
                        <div class="w-full field grid">
                            <label for="userType" class="col-fixed" style="width:180px">{{tranService.translate("account.label.userType")}}</label>
                            <div class="col">
                                <span>{{getStringUserType(accountResponse.type)}}</span>
                            </div>
                        </div>
                        <!-- Tinh thanh pho -->
                        <div class="w-full field grid" *ngIf="accountInfo.userType != optionUserType.ADMIN && accountInfo.userType != optionUserType.AGENCY">
                            <label htmlFor="province" class="col-fixed" style="width:180px">{{tranService.translate("account.label.province")}}</label>
                            <div class="col">
                                <span>{{accountResponse.provinceName}} ({{accountResponse.provinceCode}})</span>
                            </div>
                        </div>
                        <!--                &lt;!&ndash; ten khach hang &ndash;&gt;-->
                        <!--                <div class="w-full field grid" *ngIf="accountInfo.userType == optionUserType.CUSTOMER">-->
                        <!--                    <label htmlFor="roles" class="col-fixed" style="width:180px;height: fit-content;">{{tranService.translate("account.label.customerName")}}</label>-->
                        <!--                    <div class="col" style="max-width: calc(100% - 180px) !important;">-->
                        <!--                        <div *ngFor="let item of accountResponse.customers">-->
                        <!--                            {{ item.customerName + ' - ' + item.customerCode}}-->
                        <!--                        </div>-->
                        <!--                    </div>-->
                        <!--                </div>-->
                        <!-- GDV quan ly-->
                        <div class="w-full field grid" [class]="accountInfo.userType == optionUserType.CUSTOMER && (userType == optionUserType.ADMIN || userType == optionUserType.PROVINCE) ? '' : 'hidden'">
                            <label htmlFor="roles" class="col-fixed" style="width:180px">{{tranService.translate("account.label.managerName")}}</label>
                            <div class="col" style="max-width: calc(100% - 180px) !important;">
                                {{ accountResponse?.manager?.username }}
                            </div>
                        </div>
                        <!-- Danh sach tai khoan khach hang -->
                        <div class="w-full field grid align-items-start" *ngIf="accountInfo.userType == optionUserType.DISTRICT">
                            <label htmlFor="roles" class="col-fixed" style="width:180px">{{tranService.translate("account.label.customerAccount")}}</label>
                            <div class="col" style="max-width: calc(100% - 180px) !important;">
                                <div *ngFor="let item of accountResponse?.userManages">
                                    {{ item.username}}
                                </div>
                            </div>
                        </div>
                        <!-- Tài khoản khách hàng root khi-->
                        <div class="w-full field grid align-items-start" *ngIf="accountInfo.userType == optionUserType.CUSTOMER && !accountResponse?.isRootCustomer">
                            <label htmlFor="roles" class="col-fixed" style="width:180px">{{tranService.translate("account.label.customerAccount")}}</label>
                            <div class="col" style="max-width: calc(100% - 180px) !important;">
                                <div *ngIf="accountResponse?.rootAccount?.username">
                                    {{ accountResponse.rootAccount.username}}
                                </div>
                            </div>
                        </div>
                        <!-- nhom quyen -->
                        <div class="w-full field grid">
                            <label htmlFor="roles" class="col-fixed" style="width:180px; height: fit-content;">{{tranService.translate("account.label.role")}}</label>
                            <div class="col" style="max-width: calc(100% - 180px) !important;">
                                <!-- <div>{{getStringRoles()}}</div> -->
                                <div *ngFor="let item of accountResponse.roles">
                                    {{ item.roleName}}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </p-tabPanel>
            <p-tabPanel header="{{tranService.translate('global.menu.listcustomer')}}" *ngIf="accountInfo.userType ==  CONSTANTS.USER_TYPE.CUSTOMER">
                <div class="flex flex-row justify-content-center gap-3 mt-4">
                    <input style="min-width: 35vw"  type="text" pInputText [placeholder]="tranService.translate('sim.label.quickSearch')" (keydown.enter)="onSearchCustomer(true)" [(ngModel)]="paramQuickSearchCustomer.keyword" [ngModelOptions]="{standalone: true}">
                    <p-button icon="pi pi-search"
                              styleClass="ml-3 p-button-rounded p-button-secondary p-button-text button-search"
                              type="button"
                              (click)="onSearchCustomer(true)"
                    ></p-button>
                </div>
                <table-vnpt
                    [fieldId]="'id'"
                    [pageNumber]="paginationCustomer.page"
                    [pageSize]="paginationCustomer.size"
                    [columns]="columnInfoCustomer"
                    [dataSet]="dataSetCustomer"
                    [options]="optionTableCustomer"
                    [loadData]="searchCustomer.bind(this)"
                    [rowsPerPageOptions]="[5,10,20,25,50]"
                    [scrollHeight]="'400px'"
                    [sort]="paginationCustomer.sortBy"
                    [params]="paramQuickSearchCustomer"
                ></table-vnpt>
            </p-tabPanel>
            <p-tabPanel header="{{tranService.translate('global.menu.listbill')}}" *ngIf="accountInfo.userType == CONSTANTS.USER_TYPE.CUSTOMER">
                <div class="flex flex-row justify-content-center gap-3 mt-4">
                    <input style="min-width: 35vw"  type="text" pInputText [placeholder]="tranService.translate('sim.label.quickSearch')" (keydown.enter)="onSearchContract(true)" [(ngModel)]="paramQuickSearchContract.keyword" [ngModelOptions]="{standalone: true}">
                    <p-button icon="pi pi-search"
                              styleClass="ml-3 p-button-rounded p-button-secondary p-button-text button-search"
                              type="button"
                              (click)="onSearchContract(true)"
                    ></p-button>
                </div>
                <table-vnpt
                    [fieldId]="'id'"
                    [pageNumber]="paginationContract.page"
                    [pageSize]="paginationContract.size"
                    [columns]="columnInfoContract"
                    [dataSet]="dataSetContract"
                    [options]="optionTableContract"
                    [loadData]="searchContract.bind(this)"
                    [rowsPerPageOptions]="[5,10,20,25,50]"
                    [scrollHeight]="'400px'"
                    [sort]="paginationContract.sortBy"
                    [params]="paramQuickSearchContract"
                ></table-vnpt>
            </p-tabPanel>
            <p-tabPanel header="{{tranService.translate('account.text.grantApi')}}" *ngIf="userType == optionUserType.CUSTOMER && genGrantApi.secretKey != null" [pt]="'ProfileTab'">
                <div class="mb-3">
                    <p-panel [showHeader]="false">
                        <div class="flex gap-2">
                            <p-radioButton
                                    [label]="tranService.translate('account.text.working')"
                                    value="1"
                                    class="p-3"
                                    [(ngModel)]="statusGrantApi"
                                    [disabled]="true"
                                    [ngModelOptions]="{standalone: true}"
                            ></p-radioButton>

                            <p-radioButton
                                    [label]="tranService.translate('account.text.notWorking')"
                                    value="0"
                                    class="p-3"
                                    [(ngModel)]="statusGrantApi"
                                    [disabled]="true"
                                    [ngModelOptions]="{standalone: true}"
                            ></p-radioButton>
                        </div>
                        <div class="flex gap-3 align-items-center">
                            <div class="col-6">
                                <div class="flex align-items-center">
                                    <label style="min-width: 100px" class="mr-3">Client ID</label>
                                    <input [(ngModel)]="genGrantApi.clientId"  [disabled]="true" [ngModelOptions]="{standalone: true}" class="w-full" type="text" pInputText>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="flex align-items-center">
                                    <label style="min-width: 100px" class="mr-3">Secret Key</label>
                                    <div class="w-full flex align-items-center">
                                        <input class="w-full mr-2" style="padding-right: 30px;"
                                               [(ngModel)]="genGrantApi.secretKey"
                                               [ngModelOptions]="{standalone: true}"
                                               [type]="isShowSecretKey ? 'text': 'password'"
                                               pInputText
                                               [disabled]="true"
                                        />
                                        <label style="margin-left: -30px;z-index: 1;" *ngIf="isShowSecretKey == false" class="pi pi-eye toggle-password" (click)="isShowSecretKey = true"></label>
                                        <label style="margin-left: -30px;z-index: 1;" *ngIf="isShowSecretKey == true" class="pi pi-eye-slash toggle-password" (click)="isShowSecretKey = false"></label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </p-panel>
                </div>
                <div>
                    <p-panel [showHeader]="false" class="  ">
                        <div class="flex gap-3 align-items-center">
                            <div class="col-3">
                                <p-dropdown class="w-full"
                                            [showClear]="true"
                                            [(ngModel)]="paramsSearchGrantApi.module"
                                            [ngModelOptions]="{standalone: true}"
                                            [options]="listModule"
                                            optionLabel="name"
                                            optionValue="value"
                                            [emptyFilterMessage]="tranService.translate('global.text.nodata')"
                                            filter="true"
                                            [placeholder]="tranService.translate('account.text.module')"
                                ></p-dropdown>
                            </div>
                            <div class="col-3">
                                <input [(ngModel)]="paramsSearchGrantApi.api" [ngModelOptions]="{standalone: true}" class="w-full mr-2" type="text" pInputText placeholder="API"/>
                            </div>
                            <p-button icon="pi pi-search"
                                      styleClass="ml-3 p-button-rounded p-button-secondary p-button-text button-search"
                                      type="button"
                                      (click)="onSearchGrantApi(true)"
                            ></p-button>
                        </div>

                        <table-vnpt
                                [fieldId]="'id'"
                                [pageNumber]="paginationGrantApi.page"
                                [pageSize]="paginationGrantApi.size"
                                [columns]="columnInfoGrantApi"
                                [dataSet]="dataSetGrantApi"
                                [options]="optionTableGrantApi"
                                [loadData]="searchGrantApi.bind(this)"
                                [rowsPerPageOptions]="[5,10,20,25,50]"
                                [scrollHeight]="'400px'"
                                [sort]="paginationGrantApi.sortBy"
                                [params]="paramsSearchGrantApi"
                        ></table-vnpt>
                    </p-panel>
                </div>
            </p-tabPanel>
        </p-tabView>
    </div>
    <div class="flex justify-content-center">
        <p-button *ngIf="checkAuthen([CONSTANTS.PERMISSIONS.PROFILE.UPDATE])" [label]="tranService.translate('global.menu.editAccount')"  styleClass="p-button-info" class="mx-5 " (click)="goToEdit()"></p-button>
        <p-button [label]="tranService.translate('global.button.changePass')" styleClass="p-button-info bg-cyan-500 border-none" class="mx-5" (click)="goToChangePass()"></p-button>
<!--        <p-button [label]="tranService.translate('global.button.back')" class="px-5" styleClass="p-button-info"></p-button>-->
    </div>
</p-card>
