export default {
    emailIncorect: "Thông tin đăng nhập không chính xác. <PERSON>ui lòng thử lại.",
    passwordIncorect: "Thông tin đăng nhập không chính xác. Vui lòng thử lại.",
    accountInactive: "Thông tin đăng nhập không chính xác. Vui lòng thử lại.",
    length: "less than ${0} and great than ${1}",
    object: {
        not: {
            found: "Không tìm thấy trường ${0} với giá trị ${1}"
        }
    },
    field: {
        must: {
            be: {
                not: {
                    null: "${0} must be not null"
                }
            }
        }
    },
    duplicate: {
        name: "duplicate name"
    },
    invalid: {
        field: "field ${0} invalid",
        email: "Email không tồn tại!",
        phone: {
            number: "invalid phone number"
        },
        password: "Mật khẩu hiện tại sai",
        type: "invalid type",
        isdn: {
            empty: "<PERSON>hu<PERSON> bao không được để trống",
            format: "Thu<PERSON> bao không đúng định dạng",
            not: {
                permission: "Không có quyền trên thuê bao",
                inact: "Thuê bao phải có trạng thái Khóa 1 chiều, Khóa 2 chiều",
                exist: "Thuê bao không tồn tại"
            },
            duplicated: "Thuê bao bị trùng",
            status: {
                purge: "Sim đã hủy",
            }
        },
        msisdn: {
            not: {
                active: "Thuê bao phải có trạng thái Hoạt động"
            },
            register: {
                rate: {
                    pending: "Thuê bao phải có trạng thái Hoạt động"
                }
            }
        },
        rating: {
            empty: "Gói cước không được để trống",
            format: "Gói cước không đúng định dạng",
            not: {
                permission: "Không có quyền trên gói cước",
                inact: "Gói cước phải có trạng thái hoạt động",
                exist: "Gói cước không tồn tại"
            },
            customer:{
                not: {
                    permission: "Gói cước không cho phép đăng ký theo nhóm và file. Vui lòng đăng ký đơn lẻ!",
                }
            }
        },
        file: {
            form: {
                format: "Định dạng file không hợp lệ",
                extension: "Sai định dạng file excel",
                maxrow: "Số lượng bản ghi quá giới hạn 1000"
            }
        }
    },
    forbbiden: {
        resource: "Không có quyền thao tác",
    },
    data: {
        format: "Invalid data format"
    },
    user: {
        not: {
            found: "${0} user not found"
        }
    },
    valid: {
        assert: {
            false: "Must be false",
            true: "Must be true"
        },
        decimal: {
            max: "Must be less than ${0}",
            min: "Must be greater than ${0}"
        },
        digits: "Numeric value out of bounds (<${0} digits>.<${1} digits> expected)",
        email: "Must be a well-formed email address",
        max: "Must be less than or equal to ${0}",
        min: "Must be greater than or equal to ${0}",
        negative: {
            "": "Must be less than 0",
            or: {
                zero: "Must be less than or equal to 0"
            }
        },
        not: {
            blank: "Must not be blank",
            empty: "Must not be empty",
            null: "Field cannot NULL."
        },
        null: "Must be null",
        range: "Must be between ${0} and ${1}",
        past: {
            "": "Must be a past date",
            or: {
                present: "Must be a date in the past or in the present"
            }
        },
        pattern: 'Must match "${0}"',
        phone: {
            pattern: "Invalid phone number"
        },
        positive: {
            "": "Must be greater than 0",
            or: {
                zero: "Must be greater than or equal to 0"
            }
        },
        size: "Size must be between ${0} and ${1}",
        length: "Size must be between ${0} and ${1}"
    },
    id: {
        must: {
            be: {
                null: "Id must be null"
            }
        }
    },
    passwords: {
        do: {
            not: {
                match: "Mật khẩu không khớp"
            }
        }
    },
    the: {
        new: {
            password: {
                must: {
                    be: {
                        different: {
                            from: {
                                the: {
                                    old: {
                                        one: "the new password must be different from the old one"
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    },
    exists: "${0} exists",
    bad: {
        request: "Bad request"
    },
    role: {
        not: {
            working: "Bạn không có quyền truy cập hệ thống. Hãy liên hệ quản trị viên để được hỗ trợ"
        }
    },
    report: {
        query: "Lôi câu truy vấn",
        limit: {
            row: "Số lượng bản ghi báo cáo quá lớn"
        }
    },
    status: {
        400: "Bad request",
        401: "Unauthorized",
        403: "Forbidden",
        404: "Not found",
        406: "Not Acceptable",
        408: "Request Timeout",
        409: "Conflict",
        500: "Internal Server Error"
    },
    register:{
        rate:{
            groupsim: {
                empty: "Danh sách sim rỗng"
            },
            in: {
                other: {
                    process: "Thuê bao phải có trạng thái Hoạt động"
                }
            }
        }
    }
}
