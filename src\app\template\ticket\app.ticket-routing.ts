import {NgModule} from "@angular/core";
import {RouterModule} from "@angular/router";
import {ListTicketConfigComponent} from "./list-config/app.config.list.component";
import DataPage from "../../service/data.page";
import {ListTestSimTicketComponent} from "./list/app.list.test-sim.component";
import {ListReplaceSimTicketComponent} from "./list/app.list.replace-sim.component";
import {ListOrderSimTicketComponent} from "./list/order-sim/app.list.order-sim.component";
import {ListActiveSimTicketComponent} from "./list/active-sim/app.list.active-sim.component";
import {ListSimIssuedComponent} from "./list/order-sim/sim-ticket/app.list.sim-issued.component";
import {CONSTANTS} from "../../service/comon/constants";
import {ListDiagnoseTicketComponent} from "./list/diagnose/app.list.diagnose.component";
import {DataPageWithUserType} from "../../service/DataPageWithUserType";


@NgModule({
  imports: [
    RouterModule.forChild([
      {path: 'list-config', component: ListTicketConfigComponent, data: new DataPage("ticket.menu.requestConfig")},
      {path: 'list-test-sim', component: ListTestSimTicketComponent, data: new DataPage("ticket.menu.testSim", [CONSTANTS.PERMISSIONS.TICKET.VIEW_LIST])},
      {path: 'list-replace-sim', component: ListReplaceSimTicketComponent, data: new DataPage("ticket.menu.replaceSim", [CONSTANTS.PERMISSIONS.TICKET.VIEW_LIST])},
      {path: 'list-order-sim', component: ListOrderSimTicketComponent, data: new DataPage("ticket.menu.orderSim", [CONSTANTS.PERMISSIONS.TICKET.VIEW_LIST])},
      {path: 'list-active-sim', component: ListActiveSimTicketComponent, data: new DataPage("ticket.menu.activeSim", [CONSTANTS.PERMISSIONS.TICKET.VIEW_LIST])},
      {path: 'list-order-sim/sims', component: ListSimIssuedComponent, data: new DataPage("ticket.menu.listIssuedSim", [CONSTANTS.PERMISSIONS.TICKET.VIEW_LIST])},
      {path: 'list-diagnose', component: ListDiagnoseTicketComponent, data: new DataPageWithUserType("ticket.menu.diagnose", [CONSTANTS.PERMISSIONS.TICKET.VIEW_LIST], [CONSTANTS.USER_TYPE.PROVINCE, CONSTANTS.USER_TYPE.ADMIN, CONSTANTS.USER_TYPE.CUSTOMER])},
    ]),
  ],
  exports: [RouterModule],
})
export class AppTicketRouting {
}
