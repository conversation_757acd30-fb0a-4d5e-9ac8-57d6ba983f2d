import {AfterContentChecked, Component, Injector, OnInit} from '@angular/core';
import {MenuItem, TreeNode} from "primeng/api";
import {ActivatedRoute, Router} from "@angular/router";
import {UtilService} from "../../../../service/comon/util.service";
import {TranslateService} from "../../../../service/comon/translate.service";
import {MessageCommonService} from "../../../../service/comon/message-common.service";
import {FormBuilder} from "@angular/forms";
import {RolesService} from "../../../../service/account/RolesService";
import {CONSTANTS} from "../../../../service/comon/constants";
import {ComponentBase} from "../../../../component.base";

@Component({
  selector: 'app-app.roles.create',
  templateUrl: './app.roles.create.component.html',
})
export class AppRolesCreateComponent extends ComponentBase implements OnInit, AfterContentChecked{
    constructor(
                public rolesService: RolesService,
                private formBuilder: FormBuilder,
                private injector: Injector
    ) {
        super(injector);
    }

    roleInfo: {
        name: string| null,
        type: number|null,
        status: number|null,
        description: string|null,
        roles: Array<any>,
    };
    rolesSubmit: {
        name: string| null,
        description: string| null,
        type: number|null,
        status: number|null,
        permissionIds: Array<any>,
    }
    formRole: any;
    items: Array<MenuItem>;
    home: MenuItem;
    isRoleNameExisted: boolean = false;
    userTypes: Array<any>;
    statusRoles: Array<any>;
    userType: number;
    files: TreeNode[];

    selectedFiles: TreeNode[];
    dataSet: {
        content: TreeNode[],
        total: number
    };
    roleId: number;

    ngAfterContentChecked(): void {
        // console.log(this.roleInfo.roles);
    }

    ngOnInit(): void {
        if (!this.checkAuthen([CONSTANTS.PERMISSIONS.ROLE.CREATE])) {window.location.hash = "/access";}
        let me = this;
        this.userType = this.sessionService.userInfo.type;
        this.home = { icon: 'pi pi-home', routerLink: '/' };
        this.items = [
            { label: this.tranService.translate("global.menu.accountmgmt") },
            { label: this.tranService.translate("global.menu.listroles"), routerLink:"/roles" },
            { label: this.tranService.translate("global.button.create") }
        ];

        let fullTypeRole = [
            {name: this.tranService.translate("roles.type.admin"),value:CONSTANTS.ROLE_TYPE.ADMIN, accepts:[CONSTANTS.ROLE_TYPE.ADMIN]},
            {name: this.tranService.translate("roles.type.all"),value:CONSTANTS.ROLE_TYPE.ALL, accepts:[CONSTANTS.ROLE_TYPE.ADMIN]},
            {name: this.tranService.translate("roles.type.customer"),value:CONSTANTS.ROLE_TYPE.CUSTOMER,accepts:[CONSTANTS.ROLE_TYPE.ADMIN, CONSTANTS.ROLE_TYPE.CUSTOMER, CONSTANTS.ROLE_TYPE.PROVINCE, CONSTANTS.ROLE_TYPE.TELLER]},
            {name: this.tranService.translate("roles.type.province"),value:CONSTANTS.ROLE_TYPE.PROVINCE,accepts:[CONSTANTS.ROLE_TYPE.ADMIN]},
            {name: this.tranService.translate("roles.type.teller"),value:CONSTANTS.ROLE_TYPE.TELLER,accepts:[CONSTANTS.ROLE_TYPE.ADMIN, CONSTANTS.ROLE_TYPE.PROVINCE]},
            {name: this.tranService.translate("roles.type.agency"),value:CONSTANTS.ROLE_TYPE.AGENCY,accepts:[CONSTANTS.ROLE_TYPE.ADMIN, CONSTANTS.ROLE_TYPE.PROVINCE, CONSTANTS.ROLE_TYPE.TELLER, CONSTANTS.ROLE_TYPE.AGENCY]},
        ]
        this.userTypes = fullTypeRole.filter(el => el.accepts.includes(this.userType));
        let fullStatusRoles = [
            {name: this.tranService.translate("roles.status.active"),value:CONSTANTS.ROlES_STATUS.ACTIVE},
            {name: this.tranService.translate("roles.status.inactive"),value:CONSTANTS.ROlES_STATUS.INACTIVE},
        ]
        this.statusRoles = fullStatusRoles;
        this.roleInfo = {
            name: null,
            type: this.userTypes[0].value,
            status: this.statusRoles[0].value,
            roles: null,
            description: null,
        }
        this.formRole = this.formBuilder.group(this.roleInfo);
        this.dataSet ={
            content: [],
            total: 0
        }
        this.roleId = parseInt(this.route.snapshot.paramMap.get("id"));
        this.messageCommonService.onload();
        this.rolesService.getTreeRoles((response)=>{
            // let userAuthens = this.sessionService.userInfo.authorities;
            // for (let i = 0; i < userAuthens.length; i++) {
            //     // for (let j = 0; j < )
            //     response.forEach(el => {
            //         for (let j = 0; j < el.children.length; j++) {
            //             if(el.children[j].label == userAuthens[i]){
            //                 me.dataSet.content.push(el.children[j]);
            //             }
            //         }
            //     })
            // }

            me.dataSet = {
                content: response,
                total: response.length
            }
            me.dataSet.content.forEach(el => {
                if (el.label == "RptContent"){
                    el.label = me.tranService.translate(`permission.RptContent.RptContent`)
                    el.children.forEach(item => {
                        item.label = item.data.description != null ? item.data.description : el.data.permissionKey
                    })
                }else {
                    el.label = me.tranService.translate(`permission.${el.key}.${el.key}`)
                    if(el.children){
                        el.children.forEach(item => {
                            item.label = me.tranService.translate(`permission.${el.key}.${item.key}`, null, item.data.description)
                        })
                    }
                }
            })
            me.dataSet = {
                content: [{
                    label: this.tranService.translate("global.text.all"),
                    key: "all",
                    // children: me.dataSet.content,
                    children: response,
                    data: null,
                    expanded: true
                }],
                total: 0
            }
            // let permissionIds = [1, 2, 3, 4, 5];
            // me.roleInfo.roles = [];
            // me.dataSet.content.forEach(el => {
            //     if(el.children != null){
            //         let total = 0;
            //         el.children.forEach(item => {
            //             if(permissionIds.includes(item.data.id)){
            //                 me.roleInfo.roles.push(item);
            //                 total ++;
            //             }
            //         });
            //         if(total != 0 && total == el.children.length){
            //             me.roleInfo.roles.push(el);
            //         }
            //     }
            // })
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }

    onSubmitCreate(){
        let me = this;
        let permissionIds = this.roleInfo.roles.filter(el => el.data != null)
                                                .map(el => el.data.id);
        // console.log(permissionIds);
        this.rolesSubmit = {
            name: this.roleInfo.name,
            description: this.roleInfo.description,
            type: this.roleInfo.type,
            status: this.roleInfo.status,
            permissionIds: permissionIds,
        }

        // console.log(this.rolesSubmit)
        this.rolesService.createRole(this.rolesSubmit,  (response)=>{
            // me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);
            me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"));
            this.router.navigate(['/roles'])
        },(error)=>{
            me.messageCommonService.error(me.tranService.translate("global.message.saveError"))
        })
    }

    closeForm(){
        this.router.navigate(['/roles'])
    }

    checkInvalidCreate(){
        return this.formRole.invalid || this.roleInfo.roles == null || this.roleInfo.roles.length == 0;
    }

    nameChanged(query){
        let me = this
        this.debounceService.set("name",me.rolesService.checkName.bind(me.rolesService),{query:me.roleInfo.name},(response)=>{
            if (response == 1){
                me.isRoleNameExisted = true
            }
            else {
                me.isRoleNameExisted = false
            }
        })
    }
}
