import { Component, Inject, OnInit, AfterContentChecked, Injector } from "@angular/core";
import { MenuItem } from "primeng/api";
import { ColumnInfo, OptionTable } from "../../common-module/table/table.component";
import { SimService } from "src/app/service/sim/SimService";
import { FormBuilder, FormControl } from "@angular/forms";
import { CONSTANTS } from "src/app/service/comon/constants";
import { GroupSimService } from "src/app/service/group-sim/GroupSimService";
import { CustomerService } from "src/app/service/customer/CustomerService";
import { RatingPlanService } from "src/app/service/rating-plan/RatingPlanService";
import { ComponentBase } from "src/app/component.base";
import { ProvinceService } from "src/app/service/account/ProvinceService";

@Component({
    selector: "app-sim-list",
    templateUrl: './app.sim.list.component.html'
})
export class AppSimListComponent extends ComponentBase implements OnInit, AfterContentChecked{
    items: MenuItem[];
    home: MenuItem
    searchInfo: {
        msisdn: string|null,
        imsi: string|null,
        ratingPlanId:number|null,
        contractCode: string|null,
        contractor: string|null,
        status: any,
        simGroupId: number|null,
        customer: string|null,
        dateFrom: Date|null,
        dateTo: Date|null,
        apnId: string|null,
        simType: number|null,
        provinceCode : null,
        userId : null,
        loggable : boolean | null
    };
    searchInfoStandard:any;
    columns: Array<ColumnInfo>;
    dataSet: {
        content: Array<any>,
        total: number
    };
    dataStore: Array<any>;
    selectItems: Array<{imsi:number,msisdn: any,groupName:string|null,[key:string]:any}>;
    optionTable: OptionTable;
    pageNumber: number;
    pageSize: number;
    sort: string;
    itemExports: Array<MenuItem>;
    itemPushGroups: Array<MenuItem>;
    formSearchSim: any;
    formCreateGroupSim: any;
    dataCreateGroupSim: {
        name: string|null,
        groupKey: string|null,
        description: string|null
    };
    isExistsGroupKey: boolean|false;
    statuSims: Array<any>;
    typeSims: Array<any>;
    listRatingPlan: Array<any>;
    listGroupSim: Array<any>;
    listCustomer: Array<any>;
    listGroupSimToSelect: Array<any>;
    maxDateFrom: Date|number|string|null = new Date();
    minDateTo: Date|number|string|null = null;
    maxDateTo: Date|number|string|null = new Date();
    isShowDialogCreateGroup:boolean = false;
    isShowDialogPushGroup:boolean = false;
    groupSimSelected: number|null;
    userType: number;
    provinceCode: any;
    provinceName: any;
    customerCode: any;
    customerName: any;
    groupScope: number;
    groupScopeObjects: any = CONSTANTS.GROUP_SCOPE;
    allPermissions = CONSTANTS.PERMISSIONS;
    paramSearchGroupSim: any = {};
    listProvince = [];
    isShowModalDeleteSim : boolean = false;
    isShowModalDetailSim : boolean = false;
    optionuserType : any;
    simId: string;
    detailSim:any = {};
    detailStatusSim: any={};
    detailCustomer:any={};
    detailRatingPlan: any={};
    detailContract: any={};
    detailAPN: any={};
    searchPanelCollaps = true;
    quickSearchValue = null
    isSearchAfterOpenChange = false
    isSearchAfterCollapChange = false
    // tranService: TranslateService;
    constructor(@Inject(SimService) private simService: SimService,
                private formBuilder: FormBuilder,
                @Inject(GroupSimService) private groupSimService: GroupSimService,
                @Inject(CustomerService) private customerService: CustomerService,
                @Inject(RatingPlanService) private ratingPlanService: RatingPlanService,
                private provinceService: ProvinceService,
                private injector: Injector) {
        super(injector);
    }
    addTooltip(event: any) {
        setTimeout(() => {
            const toggler = event.originalEvent.target.closest('.p-panel-titlebar-toggler');
            if (toggler) {
                toggler.setAttribute('pTooltip', 'Toggle Panel');
                toggler.setAttribute('tooltipPosition', 'top');
            }
        });
    }
    ngOnInit(){
        let me = this;
        this.userType = this.sessionService.userInfo.type;
        this.optionuserType = CONSTANTS.USER_TYPE;
        this.selectItems = [];
        this.detailSim = {};
        this.searchInfoStandard = {
            msisdn: null,
            imsi: null,
            ratingPlanId: null,
            contractCode: null,
            contractor: null,
            status: null,
            simGroupId: null,
            customer: null,
            dateFrom: null,
            dateTo: null,
            apnId: null,
        };
        this.searchInfo = {
            msisdn: null,
            imsi: null,
            ratingPlanId: null,
            contractCode: null,
            contractor: null,
            status: null,
            simGroupId: null,
            customer: null,
            dateFrom: null,
            dateTo: null,
            apnId: null,
            simType: null,
            provinceCode : null,
            userId : null,
            loggable : null
        };
        this.dataCreateGroupSim = {
            groupKey: null,
            name: null,
            description: null
        }
        this.quickSearchValue = "";
        this.formSearchSim = this.formBuilder.group(this.searchInfo);
        this.formCreateGroupSim = this.formBuilder.group(this.dataCreateGroupSim);
        this.items = [{ label: this.tranService.translate("global.menu.simmgmt") }, { label: this.tranService.translate("global.menu.listsim") },];
        this.itemExports = this.itemExports = [

            {
                label: this.tranService.translate("global.button.exportExelSelect"),
                command: ()=>{
                    if(me.selectItems.length == 0 || me.selectItems.length > CONSTANTS.MAX_ROW_EXCEL_EXPORT){
                        me.helpExelExport(0);
                        return;
                    }
                    me.messageCommonService.onload();
                    me.simService.exportExelSelected({lstMsisdn: me.selectItems.map(el => el.msisdn)})
                },
            },
            {
                label: this.tranService.translate("global.button.exportExelFilter"),
                command: ()=>{
                    if(me.checkDisabledExportExelFilter()){
                        me.helpExelExport(1);
                        return;
                    }
                    me.messageCommonService.onload();
                    let dataParams:any = {};
                    me.updateParams(dataParams)
                    delete dataParams.page;
                    delete dataParams.size;
                    delete dataParams.sort;
                    if((!this.isSearchAfterOpenChange && !this.searchPanelCollaps) || (this.isSearchAfterCollapChange && this.searchPanelCollaps && this.quickSearchValue != null && this.quickSearchValue != undefined))
                        dataParams = {'keySearch' : this.quickSearchValue}
                    me.simService.exportExels(dataParams);
                },
            },
            {
                label: this.tranService.translate("global.button.exportSelect"),
                command: ()=>{
                    if(me.selectItems.length == 0 || me.selectItems.length > CONSTANTS.MAX_ROW_EXPORT){
                        me.helpExport(0);
                        return;
                    }
                    me.messageCommonService.onload();
                    me.simService.exportSimSelected({lstMsisdn: me.selectItems.map(el => el.msisdn)})
                },
            },
            {
                label: this.tranService.translate("global.button.exportFilter"),
                command: ()=>{
                    if(me.checkDisabledExportFilter()){
                        me.helpExport(1);
                        return;
                    }
                    me.messageCommonService.onload();
                    let dataParams:any = {};
                    me.updateParams(dataParams)
                    delete dataParams.page;
                    delete dataParams.size;
                    delete dataParams.sort
                    if((!this.isSearchAfterOpenChange && !this.searchPanelCollaps) || (this.isSearchAfterCollapChange && this.searchPanelCollaps && this.quickSearchValue != null && this.quickSearchValue != undefined))
                        dataParams = {'keySearch' : this.quickSearchValue}
                    me.simService.exportSim(dataParams);
                },
            }
        ];
        this.itemPushGroups = [
            {
                label: this.tranService.translate("groupSim.scope.admin"),
                command: ()=>{
                    if(me.checkDisablePushSimToGroup(CONSTANTS.GROUP_SCOPE.GROUP_ADMIN)){
                        me.groupScope = CONSTANTS.GROUP_SCOPE.GROUP_ADMIN;
                        me.getListGroupSimForSelectPush(CONSTANTS.GROUP_SCOPE.GROUP_ADMIN);
                        me.isShowDialogPushGroup = true

                    }
                },
                visible: me.checkDisablePushSimToGroup(CONSTANTS.GROUP_SCOPE.GROUP_ADMIN)
            },
            {
                label: this.tranService.translate("groupSim.scope.province"),
                command: ()=>{
                    if(me.checkDisablePushSimToGroup(CONSTANTS.GROUP_SCOPE.GROUP_PROVINCE)){
                        me.groupScope = CONSTANTS.GROUP_SCOPE.GROUP_PROVINCE;
                        me.getListGroupSimForSelectPush(CONSTANTS.GROUP_SCOPE.GROUP_PROVINCE);
                        me.isShowDialogPushGroup = true;
                    }else{
                        me.messageCommonService.info(me.tranService.translate("sim.text.sameProvince"),me.tranService.translate("sim.text.pushSim"));
                    }
                },
                visible: me.userType == CONSTANTS.USER_TYPE.PROVINCE
            },
            {
                label: this.tranService.translate("groupSim.scope.customer"),
                command: ()=>{
                    if(me.checkDisablePushSimToGroup(CONSTANTS.GROUP_SCOPE.GROUP_CUSTOMER)){
                        me.groupScope = CONSTANTS.GROUP_SCOPE.GROUP_CUSTOMER;
                        me.getListGroupSimForSelectPush(CONSTANTS.GROUP_SCOPE.GROUP_CUSTOMER);
                        me.isShowDialogPushGroup = true;
                    }else{
                        me.messageCommonService.info(me.tranService.translate("sim.text.sameCustomer"),me.tranService.translate("sim.text.pushSim"));
                    }
                },
            }
        ]
        this.home = { icon: 'pi pi-home', routerLink: '/' };

        this.statuSims = [
            // {
            //     value: CONSTANTS.SIM_STATUS.READY,
            //     name: this.tranService.translate("sim.status.ready")
            // },
            {
                value: [CONSTANTS.SIM_STATUS.ACTIVATED],
                name: this.tranService.translate("sim.status.activated")
            },
            {
                value: [CONSTANTS.SIM_STATUS.INACTIVED],
                name: this.tranService.translate("sim.status.inactivated")
            },
            {
                value: [CONSTANTS.SIM_STATUS.DEACTIVATED],
                name: this.tranService.translate("sim.status.deactivated")
            },
            {
                value: [CONSTANTS.SIM_STATUS.PURGED],
                name: this.tranService.translate("sim.status.purged")
            },
            {
                value: [15 + CONSTANTS.SIM_STATUS.ACTIVATED, 15 + CONSTANTS.SIM_STATUS.READY],
                name: this.tranService.translate("sim.status.processingChangePlan")
            },
            {
                value: [10 + CONSTANTS.SIM_STATUS.ACTIVATED, 10 + CONSTANTS.SIM_STATUS.READY],
                name: this.tranService.translate("sim.status.processingRegisterPlan")
            },
            {
                value: [20 + CONSTANTS.SIM_STATUS.ACTIVATED, 20 + CONSTANTS.SIM_STATUS.READY],
                name: this.tranService.translate("sim.status.waitingCancelPlan")
            },
        ]

        this.typeSims = [
            {
                value: [CONSTANTS.SIM_TYPE.UNKNOWN],
                name: this.tranService.translate("sim.type.unknown")
            },
            {
                value: [CONSTANTS.SIM_TYPE.ESIM],
                name: this.tranService.translate("sim.type.esim")
            },
            {
                value: [CONSTANTS.SIM_TYPE.SIM],
                name: this.tranService.translate("sim.type.sim")
            },
        ]

        this.columns = [{
            name: this.tranService.translate("sim.label.sothuebao"),
            key: "msisdn",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: true,
            style:{
                cursor: "pointer",
             color: "var(--mainColorText)"
            },
            funcClick(id, item) {
                me.simId = id.toString();
                me.getDetailSim();
                me.isShowModalDetailSim = true;
            },
            funcGetClassname(){
                return "cursor-pointer"
            }
        },{
            name: this.tranService.translate("sim.label.imsi"),
            key: "imsi",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: true
        },
        {
            name: this.tranService.translate("sim.label.dungluong"),
            key: "usagedData",
            size: "150px",
            align: "right",
            isShow: true,
            isSort: true,
            funcConvertText: function(value){
                return me.utilService.bytesToMegabytes(value);
            }
        },{
            name: this.tranService.translate("sim.label.tengoicuoc"),
            key: "ratingPlanName",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: true
        },{
            name: this.tranService.translate("sim.label.nhomsim"),
            key: "groupName",
            size: "150px",
            align: "left",
            isShow: false,
            isSort: true,
        }, {
            name: this.tranService.translate("sim.label.trangthaiketnoi"),
            key: "connectionStatus",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: true,
            funcGetClassname: (value) => {
                if(value == 'ON'){
                    return ['p-2', 'text-green-800', "bg-green-100","border-round","inline-block"];
                }else {
                    return ['p-2', 'text-50', "surface-500", "border-round","inline-block"];
                }
            },
            funcConvertText: function(value){
                return value == null? 'UNKNOWN' : value;
            },
            funcCustomizeToolTip: function(value, item){
                if(item.originalConnectionStatus == 0){
                    return me.tranService.translate("sim.label.statusDetach")
                }else if(item.originalConnectionStatus == 1){
                    return me.tranService.translate("sim.label.statusNotAttach")
                }else if(item.originalConnectionStatus == 2){
                    return me.tranService.translate("sim.label.statusAttach")
                }else if(item.originalConnectionStatus == 3){
                    return me.tranService.translate("sim.label.statusNotConnect")
                }else if(item.originalConnectionStatus == 4){
                    return me.tranService.translate("sim.label.statusConnect")
                }else if(item.originalConnectionStatus == 5){
                    return me.tranService.translate("sim.label.statusNetwork")
                }else{
                    return item.originalConnectionStatus
                }
            },
            isShowTooltip:true
         },
            {
            name: this.tranService.translate("sim.label.trangthaisim"),
            key: "status",
            size: "150px",
            align: "left",
            isShow: false,
            isSort: true,
            funcGetClassname: (value) => {
                if(value == 0){
                    return ['p-1' , "border-round", "border-400", "text-color","inline-block"];
                }else if(value == CONSTANTS.SIM_STATUS.READY){
                    // return ['p-1', "bg-blue-600", "border-round","inline-block"];
                    return ['p-2', "text-green-800", "bg-green-100","border-round","inline-block"];
                }else if(value == CONSTANTS.SIM_STATUS.ACTIVATED){
                    return ['p-2', 'text-green-800', "bg-green-100","border-round","inline-block"];
                }else if(value == CONSTANTS.SIM_STATUS.INACTIVED){
                    return ['p-2', 'text-yellow-800', "bg-yellow-100", "border-round","inline-block"];
                }else if(value == CONSTANTS.SIM_STATUS.DEACTIVATED){
                    return ['p-2', 'text-indigo-600', "bg-indigo-100", "border-round","inline-block"];
                }else if(value == CONSTANTS.SIM_STATUS.PURGED){
                    return ['p-2', 'text-red-700', "bg-red-100", "border-round","inline-block"];
                }else if(value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY){
                    return ['p-2', 'text-cyan-800', "bg-cyan-100", "border-round","inline-block"];
                }else if(value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY){
                    return ['p-2', 'text-teal-800', "bg-teal-100", "border-round","inline-block"];
                }else if(value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY){
                    return ['p-2', 'text-orange-700', "bg-orange-100", "border-round","inline-block"];
                }
                return [];
            },
            funcConvertText: (value)=>{
                if(value == 0){
                    return me.tranService.translate("sim.status.inventory");
                }else if(value == CONSTANTS.SIM_STATUS.READY){
                    // return me.tranService.translate("sim.status.ready");
                    return me.tranService.translate("sim.status.activated");
                }else if(value == CONSTANTS.SIM_STATUS.ACTIVATED){
                    return me.tranService.translate("sim.status.activated");
                }else if(value == CONSTANTS.SIM_STATUS.DEACTIVATED){
                    return me.tranService.translate("sim.status.deactivated");
                }else if(value == CONSTANTS.SIM_STATUS.PURGED){
                    return me.tranService.translate("sim.status.purged");
                }else if(value == CONSTANTS.SIM_STATUS.INACTIVED){
                    return me.tranService.translate("sim.status.inactivated");
                }else if(value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY){
                    return this.tranService.translate("sim.status.processingChangePlan");
                }else if(value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY){
                    return this.tranService.translate("sim.status.processingRegisterPlan");
                }else if(value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY){
                    return this.tranService.translate("sim.status.waitingCancelPlan");
                }
                return "";
            },
            style:{
                color: "white"
            }
        },
            {
            name: this.tranService.translate("sim.label.ngaykichhoat"),
            key: "activatedDate",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: true,
            funcConvertText: function(value){
                return me.utilService.convertLongDateToString(value);
            }
        }, {
            name: this.tranService.translate("sim.label.startDate"),
            key: "startDate",
            size: "150px",
            align: "left",
            isShow: false,
            isSort: true,
            funcConvertText: function(value){
            return me.utilService.convertLongDateToString(value);
        }
        },
            {
            name: this.tranService.translate("sim.label.maapn"),
            key: "apnId",
            size: "150px",
            align: "left",
            isShow: false,
            isSort: true
        },{
            name: this.tranService.translate("sim.label.khachhang"),
            key: "customerName",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: true
        },{
            name: this.tranService.translate("sim.label.makhachhang"),
            key: "customerCode",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: true
        },{
            name: this.tranService.translate("sim.label.mahopdong"),
            key: "contractCode",
            size: "150px",
            align: "left",
            isShow: true,
            isSort: true
        },{
            name: this.tranService.translate("sim.label.ngaylamhopdong"),
            key: "contractDate",
            size: "150px",
            align: "left",
            isShow: false,
            isSort: true,
            funcConvertText:(value)=>{
                return me.utilService.convertLongDateToString(value);
            }
        },{
            name: this.tranService.translate("sim.label.nguoilamhopdong"),
            key: "contractInfo",
            size: "150px",
            align: "left",
            isShow: false,
            isSort: true
        },
            {
                name: this.tranService.translate("sim.label.simType"),
                key: "simType",
                size: "150px",
                align: "left",
                isShow: false,
                isSort: true,
                funcGetClassname: (value) => {
                    if(value == CONSTANTS.SIM_TYPE.UNKNOWN){
                        return ['p-1' , "text-orange-600", "bg-orange-100", "border-round","inline-block"];
                    }else if(value == CONSTANTS.SIM_TYPE.ESIM){
                        return ['p-2', "text-blue-600", "bg-blue-100","border-round","inline-block"];
                    }else if(value == CONSTANTS.SIM_TYPE.SIM){
                        return ['p-2', 'text-green-600', "bg-green-100","border-round","inline-block"];
                    }
                    return [];
                },
                funcConvertText: (value)=>{
                    if(value == CONSTANTS.SIM_TYPE.UNKNOWN){
                        return me.tranService.translate("sim.type.unknown")
                    }else if(value == CONSTANTS.SIM_TYPE.ESIM){
                        return me.tranService.translate("sim.type.esim")
                    }else if(value == CONSTANTS.SIM_TYPE.SIM){
                        return me.tranService.translate("sim.type.sim")
                    }
                    return "";
                },
                style:{
                    color: "white"
                }
            }
    ];

        this.optionTable = {
            hasClearSelected:false,
            hasShowChoose: true,
            hasShowIndex: true,
            hasShowToggleColumn: true,
        }
        this.pageNumber = 0;
        this.pageSize= 10;
        this.sort = "msisdn,asc"
        let contractCodeDefault = this.route.snapshot.paramMap.get("contractCode");
        let searchPanelCollapsRoute = this.route.snapshot.paramMap.get("searchPanelCollapsRoute");
        if((contractCodeDefault || "").length > 0){
            this.searchInfo.contractCode = contractCodeDefault;
        }
        if((searchPanelCollapsRoute || "").length > 0 && searchPanelCollapsRoute == 'false'){
            this.searchPanelCollaps = false;
        }
        this.dataSet ={
            content: [],
            total: 0
        }

        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
        this.getListProvince();
    }

    getListProvince(){
        let me = this;
        this.provinceService.getListProvince((response)=>{
            me.listProvince = response;
        })
    }

    onCollapsedChange(collapsed: boolean) {
        // this.isSearchAfterOpenChange = false
        // this.isSearchAfterCollapChange = false
        console.log('Panel collapsed state:', collapsed);
        this.searchPanelCollaps = collapsed
    }

    resetField(){
        this.formSearchSim.reset();
    }

    ngAfterContentChecked(): void {
        let me = this;
        if(this.isShowDialogPushGroup == false){
            this.groupSimSelected = null;
        }
        if(this.isShowDialogCreateGroup == false){
            this.formCreateGroupSim.reset();
        }
    }

    quickSearch(){
        event.preventDefault();
        let params = {}
        if (this.quickSearchValue != null && this.quickSearchValue != undefined)
            params = {
                keySearch: this.quickSearchValue.trim()
            }
        console.log(params)
        this.search(0, this.pageSize, this.sort, params);
    }

    onSubmitSearch(){
        this.pageNumber = 0;
        this.searchInfo.loggable = true;
        this.search(0, this.pageSize, this.sort, this.searchInfo);
    }

    updateParams(dataParams){
        let me = this;
        Object.keys(this.searchInfo).forEach(key => {
            if(this.searchInfo[key] != null){
                if(key == "dateFrom"){
                    dataParams["contractDateFrom"] = this.searchInfo.dateFrom.getTime();
                }else if(key == "dateTo"){
                    dataParams["contractDateTo"] = this.searchInfo.dateTo.getTime();
                }else if(key == "contractCode"){
                    dataParams["contractCode"] = me.utilService.stringToStrBase64(this.searchInfo.contractCode);
                }
                else{
                    dataParams[key] = this.searchInfo[key];
                }
            }
        })
    }

    search(page, limit, sort, params){
        if(this.searchPanelCollaps){
            this.isSearchAfterCollapChange = true
            this.isSearchAfterOpenChange = false
        }else{
            this.isSearchAfterCollapChange = false
            this.isSearchAfterOpenChange = true
        }
        this.pageNumber = page;
        this.pageSize = limit;
        this.sort = sort;
        let me = this;
        let dataParams = {
            page,
            size: limit,
            sort
        }
        this.updateParams(dataParams);
        me.messageCommonService.onload();
        if(this.searchPanelCollaps){
            params["page"] = page;
            params["size"] = limit
            params["sort"] = sort
            params["loggable"] = true
            params["keySearch"] = this.quickSearchValue.trim();
            console.log(params)
            this.simService.quickSearch(params, (response)=>{
                // console.log(response.content)
                me.dataSet = {
                    content: response.content,
                    total: response.totalElements
                }
                me.searchInfoStandard = {...me.searchInfo}
                let list = []
                for (const sim of me.dataSet.content) {
                    list.push(sim.msisdn)
                }
                // lấy trạng thái kết nối
                this.simService.getConnectionStatus(list, (resp) =>{
                    let data = [...resp]
                    for (const sim of me.dataSet.content) {
                        for(let el of data) {
                            if(sim.msisdn == el.msisdn) {
                                sim.connectionStatus = this.getSimStatus(el.userstate)
                                sim.originalConnectionStatus = el.userstate
                            }
                        }
                    }
                    // console.log(data)
                }, null, ()=> {});
                this.simService.getDataUsed(list, (resp)=>{
                    for (const sim of me.dataSet.content) {
                        for(let el of resp) {
                            if(sim.msisdn == el.msisdn) {
                                sim.usagedData = el.usedData
                            }
                        }
                    }
                })

            }, null, ()=>{
                me.messageCommonService.offload();
            })
            return
        }
        dataParams["page"] = page;
        dataParams["size"] = limit
        dataParams["sort"] = sort
        this.simService.search(dataParams, (response)=>{
            console.log(response.content)
            me.dataSet = {
                content: response.content,
                total: response.totalElements
            }
            me.searchInfoStandard = {...me.searchInfo}
            let list = []
            for (const sim of me.dataSet.content) {
                list.push(sim.msisdn)
            }
            // lấy trạng thái kết nối
            this.simService.getConnectionStatus(list, (resp) =>{
                let data = [...resp]
                for (const sim of me.dataSet.content) {
                    for(let el of data) {
                        if(sim.msisdn == el.msisdn) {
                            sim.connectionStatus = this.getSimStatus(el.userstate)
                            sim.originalConnectionStatus = el.userstate
                        }
                    }
                }
                // console.log(data)
            }, null, ()=> {});
            this.simService.getDataUsed(list, (resp)=>{
                for (const sim of me.dataSet.content) {
                    for(let el of resp) {
                        if(sim.msisdn == el.msisdn) {
                            sim.usagedData = el.usedData
                        }
                    }
                }
            })

        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }

    getSimStatus(status) {
        if(status == null || status == undefined){
            return 'UNKNOWN'
        }
        if(status !== null && status !== undefined) {
            if(status == '0' || status == '5'){
                return 'OFF'
            } else if (status == '1' || status == '2' || status == '3' || status == '4') {
                return 'ON'
            } else {
                return 'NOT FOUND'
            }
        }else return 'NOT FOUND'
    }

    getTooltipStatus(status){
        if(status == 0){
            return this.tranService.translate("sim.label.statusDetach")
        }else if(status == 1){
            return this.tranService.translate("sim.label.statusNotAttach")
        }else if(status == 2){
            return this.tranService.translate("sim.label.statusAttach")
        }else if(status == 3){
            return this.tranService.translate("sim.label.statusNotConnect")
        }else if(status == 4){
            return this.tranService.translate("sim.label.statusConnect")
        }else if(status == 5){
            return this.tranService.translate("sim.label.statusNetwork")
        }else{
            return status
        }
    }

    checkInValidPushToGroupSim(){
        if(this.selectItems.length == 0){
            return true;
        }
        let flag = false;
        for(let i = 0;i<this.selectItems.length;i++){
            if((this.selectItems[i].groupName || "") != ""){
                flag = true;
                break;
            }
        }
        return flag;
    }


    getListGroupSimForSelectPush(type){
        let me = this;
        let params = {scope: type};
        if(type == CONSTANTS.GROUP_SCOPE.GROUP_ADMIN){
        }else if(type == CONSTANTS.GROUP_SCOPE.GROUP_PROVINCE){
            params['provinceCode'] = this.provinceCode;
        }else if(type == CONSTANTS.GROUP_SCOPE.GROUP_CUSTOMER){
            params['customerCode'] = this.customerCode;
        }
        this.paramSearchGroupSim = {...params};
    }

    onChangeDateFrom(value){
        if(value){
            this.minDateTo = value;
        }else{
            this.minDateTo = null
        }
    }

    onChangeDateTo(value){
        if(value){
            this.maxDateFrom = value;
        }else{
            this.maxDateFrom = new Date();
        }
    }

    pushGroupSim(type){
        let me = this;
        if(type == 0){//push group available
            if(this.groupSimSelected == null || this.groupSimSelected == undefined){
                return;
            }
            this.messageCommonService.onload();
            this.simService.pushSimToGroup(this.selectItems.map(el => el.msisdn), {id: this.groupSimSelected}, (response)=>{
                setTimeout(function(){
                    me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);
                })
                me.messageCommonService.success(me.tranService.translate("global.message.addGroupSuccess"));
                me.selectItems = [];
            })
            this.isShowDialogPushGroup = false;
        }else{//create new group
            let group = {
                id: null,
                groupKey: this.dataCreateGroupSim.groupKey,
                name: this.dataCreateGroupSim.name,
                customerCode: this.customerCode,
                description: this.dataCreateGroupSim.description,
                provinceCode: this.provinceCode,
                scope: this.groupScope
            }
            this.messageCommonService.onload();
            this.simService.pushSimToGroup(this.selectItems.map(el => el.msisdn), group, (response)=>{
                setTimeout(function(){
                    me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);
                })
                me.messageCommonService.success(me.tranService.translate("global.message.addGroupSuccess"));
                me.selectItems = [];
            })
            this.isShowDialogCreateGroup = false;
        }
    }

    checkExistGroupKey(){
        if(this.dataCreateGroupSim.groupKey?.trim().length > 0){
            let me = this;
            this.debounceService.set("groupKey", me.groupSimService.groupkeyCheckExisted.bind(me.groupSimService), {}, {query: me.dataCreateGroupSim.groupKey},(response)=>{
                me.isExistsGroupKey = response == 1;
            })
        }
    }

    helpExport(type){
        if(this.selectItems.length == 0 && type == 0){
            this.messageCommonService.info(this.tranService.translate("global.message.conditionExportFilterEmpty"),this.tranService.translate("global.button.export"))
        }else if(this.selectItems.length > CONSTANTS.MAX_ROW_EXPORT || this.dataSet.total > CONSTANTS.MAX_ROW_EXPORT){
            this.messageCommonService.info(this.tranService.translate("global.message.conditionExportFilter"),this.tranService.translate("global.button.export"))
        }

    }

    helpExelExport(type){
        if(this.selectItems.length == 0 && type == 0){
            this.messageCommonService.info(this.tranService.translate("global.message.conditionExportFilterEmpty"),this.tranService.translate("global.button.export"))
        }else if(this.selectItems.length > CONSTANTS.MAX_ROW_EXCEL_EXPORT || this.dataSet.total > CONSTANTS.MAX_ROW_EXCEL_EXPORT){
            this.messageCommonService.info(this.tranService.translate("global.message.conditionExportExelFilter"),this.tranService.translate("global.button.export"))
        }

    }

    checkDisabledExportFilter(){
        return JSON.stringify(this.searchInfo) != JSON.stringify(this.searchInfoStandard) || this.dataSet.total == null
            || (this.dataSet.total != null && (this.dataSet.total == 0 || this.dataSet.total > CONSTANTS.MAX_ROW_EXPORT));
    }

    checkDisabledExportExelFilter(){
        return JSON.stringify(this.searchInfo) != JSON.stringify(this.searchInfoStandard) || this.dataSet.total == null
            || (this.dataSet.total != null && (this.dataSet.total == 0 || this.dataSet.total > CONSTANTS.MAX_ROW_EXCEL_EXPORT));
    }

    checkDisablePushSimToGroup(type):boolean{
        if(type == CONSTANTS.GROUP_SCOPE.GROUP_ADMIN){
            return this.userType == CONSTANTS.USER_TYPE.ADMIN;
        }
        if((this.selectItems || []).length == 0) return false;
        if(type == CONSTANTS.GROUP_SCOPE.GROUP_PROVINCE){
            if([CONSTANTS.USER_TYPE.ADMIN, CONSTANTS.USER_TYPE.PROVINCE, CONSTANTS.USER_TYPE.DISTRICT].includes(this.userType)){
                this.provinceCode = this.selectItems[0]['provinceCode'];
                this.provinceName = null;
                for(let i = 0;i<this.listProvince.length;i++){
                    if(this.listProvince[i].code == this.provinceCode){
                        this.provinceName = this.listProvince[i].name;
                        break;
                    }
                }
                if(this.provinceName == null){
                    this.provinceName = this.selectItems[0]['provinceName'];
                }
                for(let i = 1; i < this.selectItems.length;i++){
                    if(this.selectItems[i]['provinceCode'] != this.provinceCode){
                        return false;
                    }
                }
                return true;
            }
        }else if(type == CONSTANTS.GROUP_SCOPE.GROUP_CUSTOMER){
            if([CONSTANTS.USER_TYPE.ADMIN, CONSTANTS.USER_TYPE.PROVINCE, CONSTANTS.USER_TYPE.DISTRICT, CONSTANTS.USER_TYPE.CUSTOMER].includes(this.userType)){
                this.customerCode = this.selectItems[0]['customerCode'];
                this.customerName = this.selectItems[0]['customerName'];
                this.provinceCode = this.selectItems[0]['provinceCode'];
                for(let i = 1; i < this.selectItems.length;i++){
                    if(this.selectItems[i]['customerCode'] != this.customerCode){
                        return false;
                    }
                }
                return true;
            }
        }
        return false;
    }

    deleteSim(){
        let me = this;
        let listMsisdn = this.selectItems.map(e => e.msisdn)
        this.isShowModalDeleteSim = false;
        // TODO : call api xóa sim
        me.messageCommonService.onload();
        this.simService.deleteListSim(listMsisdn, (response)=>{
            me.isShowModalDeleteSim = false;
            me.messageCommonService.success(me.tranService.translate("global.message.deleteSuccess"));
            me.selectItems = []
        },null , ()=>{
            me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo)
        })
    }

    showModalDeleteSim(){
        if(this.selectItems.length === 0) return;
        this.isShowModalDeleteSim = true;
    }

    getDetailSim(){
        let me = this;
        me.resetDetailSim()
        // this.messageCommonService.onload();
        me.simService.getById(me.simId, (response)=>{
            me.detailSim = {
                ...response
            }

            me.getDetailCustomer();
            me.getDetailApn();
            me.getStatusSim()
            me.getDetailRatingPlan()
            me.getDetailContract()
            // this.messageCommonService.onload();
            // Promise.all([
            //     this.getStatusSim(),
            //     this.getDetailRatingPlan(),
            //     this.getDetailContract()
            // ])
            //     .then(([statusSim,ratingPlan, contract]) => {
            //     })
            //     .catch(error => {
            //         this.messageCommonService.error(this.tranService.translate("global.message.errorLoading"));
            //         console.error("Error loading:", error);
            //         this.isShowModalDetailSim = false;
            //     })
            //     .finally(() => {
            //         this.messageCommonService.offload();
            //     });
            me.getDetailApn();
            me.simService.getConnectionStatus([me.simId], (resp)=>{
                me.detailSim.connectionStatus = resp[0].userstate
            }, ()=>{})
        }, null,()=>{
            this.messageCommonService.offload();
        })
    }

    resetDetailSim(){
        let me = this;
        me.detailContract = {}
        me.detailCustomer = {}
        me.detailRatingPlan = {}
        me.detailStatusSim = {}
        me.detailSim = {}
    }

    getStatusSim(): Promise<any> {
        return new Promise((resolve, reject) => {
            this.simService.getDetailStatus(
                this.detailSim.msisdn,
                (response) => {
                    this.detailStatusSim =  {
                        statusData: response.gprsStatus == 1,
                        statusReceiveCall: response.icStatus == 1,
                        statusSendCall: response.ocStatus == 1,
                        statusWorldCall: response.iddStatus == 1,
                        statusReceiveSms: response.smtStatus == 1,
                        statusSendSms: response.smoStatus == 1
                    };
                    resolve(response);
                },
                (error) => {
                    reject(error);
                },
                () => {

                }
            );
        });
    }

    getDetailCustomer(){
        this.detailCustomer = {
            name: this.detailSim.customerName,
            code: this.detailSim.customerCode
        }
    }

    getDetailRatingPlan(): Promise<any> {
        return new Promise((resolve, reject) => {
            this.simService.getDetailPlanSim(
                this.detailSim.msisdn,
                (response) => {
                    this.detailRatingPlan = { ...response };
                    resolve(response);
                },
                (error) => {
                    reject(error);
                },
                () => {

                }
            );
        });
    }

    getDetailContract(): Promise<any> {
        return new Promise((resolve, reject) => {
            this.simService.getDetailContract(
                this.utilService.stringToStrBase64(this.detailSim.contractCode),
                (response) => {
                    this.detailContract = response;
                    resolve(response);
                },
                (error) => {
                    reject(error);
                },
                () => {

                }
            );
        });
    }


    getDetailApn(){
        this.detailAPN = {
            code: this.detailSim.apnCode,
            type: "Kết nối bằng 3G",
            ip: 0,
            rangeIp: this.detailSim.ip
        }
    }

    getNameStatus(value){
        if(value == 0){
            return this.tranService.translate("sim.status.inventory");
        }else if(value == CONSTANTS.SIM_STATUS.READY){
            // return this.tranService.translate("sim.status.ready");
            return this.tranService.translate("sim.status.activated");
        }else if(value == CONSTANTS.SIM_STATUS.ACTIVATED){
            return this.tranService.translate("sim.status.activated");
        }else if(value == CONSTANTS.SIM_STATUS.DEACTIVATED){
            return this.tranService.translate("sim.status.deactivated");
        }else if(value == CONSTANTS.SIM_STATUS.PURGED){
            return this.tranService.translate("sim.status.purged");
        }else if(value == CONSTANTS.SIM_STATUS.INACTIVED){
            return this.tranService.translate("sim.status.inactivated");
        }else if(value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY){
            return this.tranService.translate("sim.status.processingChangePlan");
        }else if(value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY){
            return this.tranService.translate("sim.status.processingRegisterPlan");
        }else if(value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY){
            return this.tranService.translate("sim.status.waitingCancelPlan");
        }
        return "";
    }

    getClassStatus(value){
        if(value == 0){
            return ['p-1' , "border-round", "border-400", "text-color","inline-block"];
        }else if(value == CONSTANTS.SIM_STATUS.READY){
            // return ['p-1', "bg-blue-600", "border-round","inline-block"];
            return ['p-2', "text-green-800", "bg-green-100","border-round","inline-block"];
        }else if(value == CONSTANTS.SIM_STATUS.ACTIVATED){
            return ['p-2', 'text-green-800', "bg-green-100","border-round","inline-block"];
        }else if(value == CONSTANTS.SIM_STATUS.INACTIVED){
            return ['p-2', 'text-yellow-800', "bg-yellow-100", "border-round","inline-block"];
        }else if(value == CONSTANTS.SIM_STATUS.DEACTIVATED){
            return ['p-2', 'text-indigo-600', "bg-indigo-100", "border-round","inline-block"];
        }else if(value == CONSTANTS.SIM_STATUS.PURGED){
            return ['p-2', 'text-red-700', "bg-red-100", "border-round","inline-block"];
        }else if(value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY){
            return ['p-2', 'text-cyan-800', "bg-cyan-100", "border-round","inline-block"];
        }else if(value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY){
            return ['p-2', 'text-teal-800', "bg-teal-100", "border-round","inline-block"];
        }else if(value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY){
            return ['p-2', 'text-orange-700', "bg-orange-100", "border-round","inline-block"];
        }
        return [];
    }

    getServiceType(value) {
        if(value == CONSTANTS.SERVICE_TYPE.PREPAID) return this.tranService.translate("sim.serviceType.prepaid")
        else if(value == CONSTANTS.SERVICE_TYPE.POSTPAID) return this.tranService.translate("sim.serviceType.postpaid")
        else return ""
    }

    protected readonly Object = Object;
}
