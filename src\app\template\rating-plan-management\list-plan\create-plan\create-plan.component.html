<!-- <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb> -->
<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{tranService.translate("global.menu.listplan")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
</div>
<p-card class="p-4" styleClass="responsive-form-plans">
    <form action="" [formGroup]="createPlanForm" (submit)="submitForm()">
        <div class="pt-0 shadow-2 border-round-md m-1 flex p-fluid p-formgrid grid">
            <div class="col-6 grid-col" >
                <div class=" grid px-4 pt-4 flex flex-row flex-nowrap responsive-size-input">
                    <label htmlFor="code"  style="min-width: 140px;" class="col-12 mb-2 md:col-2 md:mb-0">{{tranService.translate("ratingPlan.label.planCode")}}<span class="text-red-500">*</span></label>
                    <div class="col-12 md:col-10 flex-1">
                        <input formControlName="code" pInputText id="code" type="text" [placeholder]="placeHolder.planCode"/>
                    </div>
                </div>

                <div class="grid px-4 flex flex-row flex-nowrap mb-3 responsive-size-input">
                    <div style="min-width: 140px;"></div>
                    <div class="col-11 md:col-11 py-0">
                        <div *ngIf="isPlanCodeValid && createPlanForm.get('code').hasError('required')" class="text-red-500">
                            {{tranService.translate("ratingPlan.error.requiredError")}}
                        </div>
                        <div *ngIf="isPlanCodeValid && createPlanForm.get('code').hasError('maxlength')" class="text-red-500">
                            {{tranService.translate("ratingPlan.error.lengthError_64")}}
                        </div>
                        <div *ngIf="isPlanCodeValid && createPlanForm.get('code').hasError('invalidCharacters')" class="text-red-500">
                            {{tranService.translate("ratingPlan.error.characterError_code")}}
                        </div>
                        <div *ngIf="isPlanCodeValid && createPlanForm.get('code').hasError('exited')" class="text-red-500">
                            {{tranService.translate("ratingPlan.error.existedCodeError")}}
                        </div>
                    </div>
                </div>

                <div class="field grid px-4 flex flex-row flex-nowrap responsive-size-input">
                    <label htmlFor="name"  style="min-width: 140px;" class="col-12 mb-2 md:col-2 md:mb-0">{{tranService.translate("ratingPlan.label.planName")}}<span class="text-red-500">*</span></label>
                    <div class="col-12 md:col-10 flex-1">
                        <input formControlName="name" pInputText id="name" type="text" [placeholder]="placeHolder.planName">
                    </div>
                </div>

                <div class="grid px-4 flex flex-row flex-nowrap mb-3 responsive-size-input">
                    <div style="min-width: 140px;"></div>
                    <div class="col-11 md:col-11 py-0">
                        <div *ngIf="isPlaneNameValid && createPlanForm.get('name').hasError('required')" class="text-red-500">
                            {{tranService.translate("ratingPlan.error.requiredError")}}
                        </div>
                        <div *ngIf="isPlaneNameValid && createPlanForm.get('name').hasError('maxlength')" class="text-red-500">
                            {{tranService.translate("ratingPlan.error.lengthError_255")}}
                        </div>
                        <div *ngIf="isPlaneNameValid && createPlanForm.get('name').hasError('invalidCharacters')" class="text-red-500">
                            {{tranService.translate("global.message.wrongFormatName")}}
                        </div>
                        <div *ngIf="isPlaneNameValid && createPlanForm.get('name').hasError('exited')" class="text-red-500">
                            {{tranService.translate("ratingPlan.error.existedNameError")}}
                        </div>
                    </div>
                </div>

                <div class="field grid px-4 flex flex-row flex-nowrap responsive-size-input">
                    <label htmlFor="dispatchCode"  style="min-width: 140px;" class="col-12 mb-2 md:col-2 md:mb-0">{{tranService.translate("ratingPlan.label.dispatchCode")}}<span class="text-red-500">*</span></label>
                    <div class="col-12 md:col-10 flex-1">
                        <input formControlName="dispatchCode" pInputText id="dispatchCode" type="text" [placeholder]="placeHolder.dispatchCode"/>
                    </div>
                </div>

                <div class="grid px-4 flex flex-row flex-nowrap mb-3 responsive-size-input">
                    <div style="min-width: 140px;"></div>
                    <div class="col-11 md:col-11 py-0">
                        <div *ngIf="isDispatchCodeValid && createPlanForm.get('dispatchCode').hasError('required')" class="text-red-500">
                            {{tranService.translate("ratingPlan.error.requiredError")}}
                        </div>
                        <div *ngIf="isDispatchCodeValid && createPlanForm.get('dispatchCode').hasError('maxlength')" class="text-red-500">
                            {{tranService.translate("ratingPlan.error.lengthError_64")}}
                        </div>
                        <div *ngIf="isDispatchCodeValid && createPlanForm.get('dispatchCode').hasError('invalidCharacters')" class="text-red-500">
                            {{tranService.translate("ratingPlan.error.characterError_code")}}
                        </div>
                    </div>
                </div>

                <div class="field grid px-4 flex flex-row flex-nowrap responsive-size-input">
                    <label htmlFor="customerType"  style="min-width: 140px;" class="col-12 mb-2 md:col-2 md:mb-0">{{tranService.translate("ratingPlan.label.customerType")}}<span class="text-red-500">*</span></label>
                    <div class="col-12 md:col-10 flex-1">
                        <p-dropdown formControlName="customerType" [options]="customerTypes" optionLabel="name" optionValue="id" autoDisplayFirst="false" [placeholder]="placeHolder.customerType"></p-dropdown>
                    </div>
                </div>

                <div class="grid px-4 flex flex-row flex-nowrap mb-3 responsive-size-input">
                    <div style="min-width: 140px;"></div>
                    <div class="col-11 md:col-11 py-0">
                        <div *ngIf="isCustomerTypeValid && createPlanForm.get('customerType').hasError('required')" class="text-red-500">
                            {{tranService.translate("ratingPlan.error.requiredError")}}
                        </div>
                    </div>
                </div>

                <div class="grid px-4 flex flex-row flex-nowrap responsive-size-input">
                    <label htmlFor="description"  style="min-width: 140px;" class="col-12 mb-2 md:col-2 md:mb-0">{{tranService.translate("ratingPlan.label.description")}}</label>
                    <div class="col-12 md:col-10 flex-1">
                        <textarea formControlName="description" id="description" rows="4" cols="30" [placeholder]="placeHolder.description" pInputText></textarea>
                    </div>
                </div>

                <div class="grid px-4 flex flex-row flex-nowrap mb-3 responsive-size-input">
                    <div style="min-width: 140px;"></div>
                    <div class="col-11 md:col-11 py-0">
                        <div *ngIf="isDescriptionValid && createPlanForm.get('description').hasError('maxlength')" class="text-red-500">
                            {{tranService.translate("ratingPlan.error.lengthError_255")}}
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-6 grid-col">
                <div class="field grid px-4 pt-4 flex flex-row flex-nowrap responsive-size-input" >
                    <label htmlFor="subscriptionFee"  style="min-width: 140px;" class="col-12 mb-2 md:col-2 md:mb-0">{{tranService.translate("ratingPlan.label.subscriptionFee")}}<span class="text-red-500">*</span></label>
                    <div class="col-12 md:col-10 flex-1">
                        <input formControlName="subscriptionFee" pInputText id="subscriptionFee" type="number" (keydown)="blockMinus($event)" (input)="checkInputValue($event)"  [placeholder]="placeHolder.subscriptionFee"/>
                    </div>
                    <div class="my-auto pr-1" style="min-width: 90px;">
                        {{tranService.translate("ratingPlan.text.textDong")}} <br/> {{tranService.translate("ratingPlan.text.vat")}}
                    </div>
                </div>

                <div class="grid px-4 flex flex-row flex-nowrap mb-3 responsive-size-input">
                    <div style="min-width: 140px;"></div>
                    <div class="col-11 md:col-11 py-0">
                        <div *ngIf="isSubscriptionFeeValid && createPlanForm.get('subscriptionFee').hasError('required')" class="text-red-500">
                            {{tranService.translate("ratingPlan.error.requiredError")}}
                        </div>
                        <div *ngIf="isSubscriptionFeeValid && createPlanForm.get('subscriptionFee').hasError('max')" class="text-red-500">
                            {{tranService.translate("ratingPlan.error.lengthError_number")}}
                        </div>
                        <div *ngIf="isSubscriptionFeeValid && createPlanForm.get('subscriptionFee').hasError('min')" class="text-red-500">
                            {{tranService.translate("global.message.min",{value: 0})}}
                        </div>
                    </div>
                </div>

                <div class="field grid px-4 pb-2 custom-responsive-field">
                    <div class="col-12 mb-2 md:col-2 md:mb-0 flex flex-row align-items-center radio-group wrapper">
                        <div *ngFor="let paidType of paidCategories" class="field-checkbox my-auto"  style="min-width: 140px; min-height:35px;">
                            <p-radioButton formControlName="paidType" [inputId]="paidType.key" name="paidType" [value]="paidType.key" [(ngModel)]="selectedPaidCategory"></p-radioButton>
                            <label [for]="paidType.key" class="ml-2">{{ paidType.name }}</label>
                        </div>
                    </div>
                    <div class="switch-group-wrapper flex flex-row align-items-center mt-2">
                        <label htmlFor="reload"  style="min-width: 130px;" class="col-12 mb-2 md:col-2 md:mb-0 responsive-switch-label">{{tranService.translate("ratingPlan.label.autoReload")}}</label>
                        <div class="col-12 md:col-10 flex-1">
                            <p-inputSwitch formControlName="reload" class="flex align-items-center"></p-inputSwitch>
                        </div>
                    </div>
                </div>
                <div class="field grid px-4 flex flex-row flex-nowrap responsive-size-input" >
                    <label htmlFor="cycle"  style="min-width: 140px;" class="col-12 mb-2 md:col-2 md:mb-0">{{tranService.translate("ratingPlan.label.cycle")}}<span class="text-red-500">*</span></label>
                    <div class="col-12 md:col-10 flex-1">
                        <p-dropdown formControlName="cycleTimeUnit" [options]="cycles" optionLabel="name" optionValue="id" autoDisplayFirst="true" [placeholder]="placeHolder.planCycle"></p-dropdown>
                    </div>
                    <div class="my-auto pr-1" style="min-width: 90px;">
                    </div>
                </div>

                <div class="grid px-4 flex flex-row flex-nowrap mb-3 responsive-size-input">
                    <div style="min-width: 140px;"></div>
                    <div class="col-11 md:col-11 py-0">
                        <div *ngIf="isPlanCycleValid && createPlanForm.get('cycleTimeUnit').hasError('required')" class="text-red-500">
                            {{tranService.translate("ratingPlan.error.requiredError")}}
                        </div>
                    </div>
                </div>

                <div class="field grid px-4 flex flex-row flex-nowrap responsive-size-input">
                    <label htmlFor="cycleInterval"  style="min-width: 140px;" class="col-12 mb-2 md:col-2 md:mb-0">{{tranService.translate("ratingPlan.label.duration")}}<span class="text-red-500">*</span></label>
                    <div class="col-12 md:col-10 flex-1">
                        <input formControlName="cycleInterval" pInputText id="cycleInterval" type="number" (keydown)="blockMinus($event)" (input)="checkInputValue($event)" [placeholder]="placeHolder.duration"/>
                    </div>
                    <div *ngIf="createPlanForm.get('cycleTimeUnit').value == '1'" class="my-auto pr-1" style="min-width: 90px;">
                        {{tranService.translate("ratingPlan.cycle.day")}}
                    </div>
                    <div *ngIf="createPlanForm.get('cycleTimeUnit').value == '3'" class="my-auto pr-1" style="min-width: 90px;">
                        {{tranService.translate("ratingPlan.cycle.month")}}
                    </div>
                    <div *ngIf="createPlanForm.get('cycleTimeUnit').value != '1' && createPlanForm.get('cycleTimeUnit').value != '3'" class="my-auto pr-1" style="min-width: 90px;">
                        {{tranService.translate("ratingPlan.text.dayMonth")}}
                    </div>
                </div>

                <div class="grid px-4 flex flex-row flex-nowrap mb-3 responsive-size-input">
                    <div style="min-width: 140px;"></div>
                    <div class="col-11 md:col-11 py-0">
                        <div *ngIf="isDurationValid && createPlanForm.get('cycleInterval').hasError('required')" class="text-red-500">
                            {{tranService.translate("ratingPlan.error.requiredError")}}
                        </div>
                        <div *ngIf="isDurationValid && createPlanForm.get('cycleInterval').hasError('max')" class="text-red-500">
                            {{tranService.translate("ratingPlan.error.lengthError_number")}}
                        </div>
                        <div *ngIf="isDurationValid && createPlanForm.get('cycleInterval').hasError('min')" class="text-red-500">
                            {{tranService.translate("global.message.min",{value: 0})}}
                        </div>
                    </div>
                </div>

                <div class="field grid px-4 flex flex-row flex-nowrap responsive-size-input" >
                    <label htmlFor="ratingScope"  style="min-width: 140px;" class="col-12 mb-2 md:col-2 md:mb-0">{{tranService.translate("ratingPlan.label.ratingScope")}}<span class="text-red-500">*</span></label>
                    <div class="col-12 md:col-10 flex-1">
                        <p-dropdown (onChange)="onDropdownChange($event)" formControlName="ratingScope" [options]="ratingScopes" optionLabel="name" optionValue="id" autoDisplayFirst="false" [placeholder]="placeHolder.planScope"></p-dropdown>
                    </div>
                    <div class="my-auto pr-1" style="min-width: 90px;">
                    </div>
                </div>

                <div class="grid px-4 flex flex-row flex-nowrap mb-3 responsive-size-input">
                    <div style="min-width: 140px;"></div>
                    <div class="col-11 md:col-11 py-0">
                        <div *ngIf="isPlanScopeValid && createPlanForm.get('ratingScope').hasError('required')" class="text-red-500">
                            {{tranService.translate("ratingPlan.error.requiredError")}}
                        </div>
                    </div>
                </div>

                <div *ngIf="isProvince" class="field grid px-4 flex flex-row flex-nowrap responsive-size-input" >
                    <label htmlFor="provinceCode" style="min-width: 140px;" class="col-fixed">{{tranService.translate("ratingPlan.label.province")}}<span class="text-red-500">*</span></label>
                    <div class="col" style="max-width: calc(100% - 230px);">
                        <p-multiSelect *ngIf="userType == allUserType.ADMIN"
                                       formControlName="provinceCode"
                                       [options]="provinces"
                                       [showToggleAll]="false"
                                       display="chip" optionLabel="name"
                                       optionValue="code" autoDisplayFirst="false"
                                       [placeholder]="placeHolder.provinceCode"
                                       (onChange)="changeProvince()"
                        ></p-multiSelect>
                        <span *ngIf="userType != allUserType.ADMIN">{{provinceInfo}}</span>
                    </div>
                    <div class="col-fixed" style="min-width: 90px;">
                    </div>
                </div>
                <div class="grid px-4 flex flex-row flex-nowrap mb-3">
                    <div style="min-width: 140px;"></div>
                    <div class="col-11 md:col-11 py-0">
                        <div *ngIf="isProvinceCodeValid && createPlanForm.get('provinceCode').hasError('required')" class="text-red-500">
                            {{tranService.translate("ratingPlan.error.requiredError")}}
                        </div>
                    </div>
                </div>
                <div *ngIf="isCustomer" class="field grid px-4 flex flex-row flex-nowrap3 responsive-size-input">
                    <label htmlFor="roles" class="col-fixed" style="min-width: 140px; cursor: pointer; text-decoration: underline; color: blue; transition: color 0.3s;" (click)="openDialogAddCustomerAccount()">{{tranService.translate("account.label.addCustomerAccount")}}</label>
                    <!--                    <div class="col" style="max-width: calc(100% - 230px) !important;">-->
                    <!--                        <vnpt-select-->
                    <!--                            [control]="controlComboSelect"-->
                    <!--                            class="w-full"-->
                    <!--                            [(value)]="customerCode"-->
                    <!--                            [placeholder]="tranService.translate('account.text.selectCustomers')"-->
                    <!--                            objectKey="searchUserForRatingPlan"-->
                    <!--                            paramKey="keySearch"-->
                    <!--                            keyReturn="id"-->
                    <!--                            displayPattern="${provinceName}-${username}-${fullName}"-->
                    <!--                            typeValue="primitive"-->
                    <!--                            [required]="isCustomer"-->
                    <!--                            (onchange)="onChangeCustomers()"-->
                    <!--                        ></vnpt-select>-->
                    <!--                    </div>-->
                </div>


            </div>
        </div>
        <h4 class="ml-2">{{tranService.translate("ratingPlan.label.flat")}}</h4>
        <div class="pt-0 shadow-2 border-round-md m-1 flex p-fluid p-formgrid grid">
            <div class="flex-1">
                <div class="field grid px-4 pt-4 flex flex-row flex-nowrap responsive-size-input">
                    <label htmlFor="limitDataUsage"  style="min-width: 170px;" class="col-12 mb-2 md:col-2 md:mb-0">{{tranService.translate("ratingPlan.label.freeData")}}<span class="text-red-500">*</span></label>
                    <div class="col-12 md:col-10 flex-1">
                        <input formControlName="limitDataUsage" pInputText id="limitDataUsage" type="number" (keydown)="blockMinus($event)"  (input)="checkInputValue($event)" [placeholder]="placeHolder.freeData"/>
                    </div>
                    <div class="my-auto pr-1" style="min-width: 40px;">
                        (MB)
                    </div>
                </div>

                <div class="grid px-4 flex flex-row flex-nowrap mb-3 responsive-size-input">
                    <div style="min-width: 170px;"></div>
                    <div class="col-11 md:col-11 py-0">
                        <div *ngIf="isFreeDataValid && createPlanForm.get('limitDataUsage').hasError('required')" class="text-red-500">
                            {{tranService.translate("ratingPlan.error.requiredError")}}
                        </div>
                        <div *ngIf="isFreeDataValid && createPlanForm.get('limitDataUsage').hasError('max')" class="text-red-500">
                            {{tranService.translate("ratingPlan.error.lengthError_number")}}
                        </div>
                        <div *ngIf="isFreeDataValid && createPlanForm.get('limitDataUsage').hasError('min')" class="text-red-500">
                            {{tranService.translate("global.message.min",{value: 0})}}
                        </div>
                    </div>
                </div>
                <div class="field grid px-4 flex flex-row flex-nowrap responsive-size-input">
                    <label htmlFor="limitDataUsage"  style="min-width: 170px;" class="col-12 mb-2 md:col-2 md:mb-0">{{tranService.translate("ratingPlan.placeHolder.dataMax")}}<span class="text-red-500">*</span></label>
                    <div class="col-12 md:col-10 flex-1">
                        <input formControlName="dataMax" pInputText id="dataMax" type="number" (keydown)="blockMinus($event)"  (input)="checkInputValue($event)" [placeholder]="placeHolder.dataMax"/>
                    </div>
                    <div class="my-auto pr-1" style="min-width: 40px;">
                        (MB)
                    </div>
                </div>

                <div class="grid px-4 flex flex-row flex-nowrap mb-3 responsive-size-input">
                    <div style="min-width: 170px;"></div>
                    <div class="col-11 md:col-11 py-0">
                        <div *ngIf="isDataMaxValid && createPlanForm.get('dataMax').hasError('required')" class="text-red-500">
                            {{tranService.translate("ratingPlan.error.requiredError")}}
                        </div>
                        <div *ngIf="isDataMaxValid && createPlanForm.get('dataMax').hasError('max')" class="text-red-500">
                            {{tranService.translate("ratingPlan.error.lengthError_number")}}
                        </div>
                        <div *ngIf="isDataMaxValid && createPlanForm.get('dataMax').hasError('min')" class="text-red-500">
                            {{tranService.translate("global.message.min",{value: 0})}}
                        </div>
                    </div>
                </div>

<!--                <div class="field grid px-4 pb-3 flex flex-row flex-nowrap">-->
<!--                    <label htmlFor="outsideSMSFree"  style="min-width: 170px;" class="col-12 mb-2 md:col-2 md:mb-0">{{tranService.translate("ratingPlan.label.outsideSMSFree")}}</label>-->
<!--                    <div class="col-12 md:col-10 flex-1">-->
<!--                        <input formControlName="limitSmsOutside" pInputText id="outsideSMSFree" type="number" (keydown)="blockMinus($event)"  (input)="checkInputValue($event)" [placeholder]="placeHolder.outsideSMSFree"/>-->
<!--                    </div>-->
<!--                    <div class="my-auto pr-1" style="min-width: 40px;"></div>-->
<!--                </div>-->

<!--                <div class="grid px-4 flex flex-row flex-nowrap mb-3">-->
<!--                    <div style="min-width: 170px;"></div>-->
<!--                    <div class="col-11 md:col-11 py-0">-->
<!--                        <div *ngIf="isLimitOutsideSMSFreeValid && createPlanForm.get('limitSmsOutside').hasError('max')" class="text-red-500">-->
<!--                            {{tranService.translate("ratingPlan.error.lengthError_number")}}-->
<!--                        </div>-->
<!--                        <div *ngIf="isLimitOutsideSMSFreeValid && createPlanForm.get('limitSmsOutside').hasError('min')" class="text-red-500">-->
<!--                            {{tranService.translate("global.message.min",{value: 0})}}-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </div>-->
            </div>
            <div class="flex-1">
                <div class="field grid px-4 pt-4 flex flex-row flex-nowrap responsive-size-input" >
                    <label htmlFor="insideSMSFree"  style="min-width: 170px;" class="col-12 md:col-2 md:mb-0">{{tranService.translate("ratingPlan.label.insideSMSFree")}}</label>
                    <div class="col-12 md:col-10 flex-1">
                        <input formControlName="limitSmsInside" pInputText id="insideSMSFree" type="number" (keydown)="blockMinus($event)" (input)="checkInputValue($event)"  [placeholder]="placeHolder.insideSMSFree"/>
                    </div>
                </div>

                <div class="grid px-4 flex flex-row flex-nowrap mb-3 responsive-size-input">
                    <div style="min-width: 170px;"></div>
                    <div class="col-11 md:col-11 py-0">
                        <div *ngIf="isLimitInsideSMSFreeValid && createPlanForm.get('limitSmsInside').hasError('max')" class="text-red-500">
                            {{tranService.translate("ratingPlan.error.lengthError_number")}}
                        </div>
                        <div *ngIf="isLimitInsideSMSFreeValid && createPlanForm.get('limitSmsInside').hasError('min')" class="text-red-500">
                            {{tranService.translate("global.message.min",{value: 0})}}
                        </div>
                    </div>
                </div>
                <div class="field grid px-4 flex flex-row flex-nowrap responsive-size-input">
                    <label htmlFor="outsideSMSFree"  style="min-width: 170px;" class="col-12 mb-2 md:col-2 md:mb-0">{{tranService.translate("ratingPlan.label.outsideSMSFree")}}</label>
                    <div class="col-12 md:col-10 flex-1">
                        <input formControlName="limitSmsOutside" pInputText id="outsideSMSFree" type="number" (keydown)="blockMinus($event)"  (input)="checkInputValue($event)" [placeholder]="placeHolder.outsideSMSFree"/>
                    </div>
                    <!--                    <div class="my-auto pr-1" style="min-width: 40px;"></div>-->
                </div>

                <div class="grid px-4 flex flex-row flex-nowrap mb-3 responsive-size-input">
                    <div style="min-width: 170px;"></div>
                    <div class="col-11 md:col-11 py-0">
                        <div *ngIf="isLimitOutsideSMSFreeValid && createPlanForm.get('limitSmsOutside').hasError('max')" class="text-red-500">
                            {{tranService.translate("ratingPlan.error.lengthError_number")}}
                        </div>
                        <div *ngIf="isLimitOutsideSMSFreeValid && createPlanForm.get('limitSmsOutside').hasError('min')" class="text-red-500">
                            {{tranService.translate("global.message.min",{value: 0})}}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="flex flex-row gap-3 ml-2 mt-4 mb-3">
            <h4 class="m-0">{{tranService.translate("ratingPlan.label.flexible")}}</h4>
            <p-inputSwitch formControlName="flexible" class="" [(ngModel)]="isFlexible" (onChange)="onSwitchChange()"></p-inputSwitch>
        </div>
        <div class="pt-0 shadow-2 border-round-md mb-4 m-1 flex p-fluid p-formgrid flex-column grid">
            <div class="field grid px-4 pt-4 flex flex-row flex-nowrap responsive-size-input">
                <label htmlFor="feePerDataUnit" [style.color]="!isFlexible ? 'gray' : 'black'" style="min-width: 190px;" class="col-12 md:col-2 md:mb-0">{{tranService.translate("ratingPlan.label.feePerUnit")}}<span class="text-red-500">*</span></label>
                <div class="col-12 md:col-10 flex-1">
                    <input class="feePerDataUnit" formControlName="feePerDataUnit" pInputText id="feePerDataUnit" type="number" (keydown)="blockMinus($event)"  (input)="checkInputValue($event)" [disabled]="!isFlexible" [placeholder]="placeHolder.feePerUnit"/>
                </div>
                <div class="my-auto mx-auto" [style.color]="!isFlexible ? 'gray' : 'black'" style="min-width: 10px;">/</div>
                <div class="col-12 md:col-10 flex-1">
                    <input formControlName="dataRoundUnit" pInputText id="dataRoundUnit" type="number" (keydown)="blockMinus($event)"  (input)="checkInputValue($event)" [disabled]="!isFlexible" [placeholder]="placeHolder.feePerUnit"/>
                </div>
                <div [style.color]="!isFlexible ? 'gray' : 'black'" class="my-auto" style="min-width: 40px;">(KB)</div>
            </div>

            <div class="grid px-4 flex flex-row flex-nowrap mb-3">
                <div style="min-width: 190px;" class="col-12 md:col-2 md:mb-0 p-0 responsive-div-error-2"></div>
                <div class="col-11 md:col-5 py-0">
                    <div *ngIf="isFeePerUnitNumberatorValid && createPlanForm.get('feePerDataUnit').hasError('required')" class="text-red-500">
                        {{tranService.translate("ratingPlan.error.requiredError")}}
                    </div>
                    <div *ngIf="isFeePerUnitNumberatorValid && createPlanForm.get('feePerDataUnit').hasError('max')" class="text-red-500">
                        {{tranService.translate("ratingPlan.error.lengthError_number")}}
                    </div>
                    <div *ngIf="isFeePerUnitNumberatorValid && createPlanForm.get('feePerDataUnit').hasError('min')" class="text-red-500">
                        {{tranService.translate("global.message.min",{value: 0})}}
                    </div>
                    <!-- <div *ngIf="isShowValid && createPlanForm.get('dataRoundUnit').hasError('max')" class="text-red-500">
                        {{tranService.translate("ratingPlan.error.lengthError_number")}}
                    </div> -->
                </div>
                <div class="col-11 md:col-5 py-0 responsive-error-2">
                    <div *ngIf="isFeePerUnitDenominatorValid && createPlanForm.get('dataRoundUnit').hasError('required')" class="text-red-500">
                        {{tranService.translate("ratingPlan.error.requiredError")}}
                    </div>
                    <div *ngIf="isFeePerUnitDenominatorValid && createPlanForm.get('dataRoundUnit').hasError('max')" class="text-red-500">
                        {{tranService.translate("ratingPlan.error.lengthError_number")}}
                    </div>
                    <div *ngIf="isFeePerUnitDenominatorValid && createPlanForm.get('dataRoundUnit').hasError('min')" class="text-red-500">
                        {{tranService.translate("global.message.min",{value: 0})}}
                    </div>
                    <!-- <div *ngIf="isShowValid && createPlanForm.get('dataRoundUnit').hasError('max')" class="text-red-500">
                        {{tranService.translate("ratingPlan.error.lengthError_number")}}
                    </div> -->
                </div>
            </div>

            <div class="field grid px-4 flex flex-row flex-nowrap responsive-size-input">
                <label htmlFor="downSpeed" [style.color]="!isFlexible ? 'gray' : 'black'" style="min-width: 190px;" class="col-12 md:col-2 md:mb-0">{{tranService.translate("ratingPlan.label.squeezeSpeed")}}</label>
                <div class="col-12 md:col-10 flex-1">
                    <input formControlName="downSpeed" pInputText id="downSpeed" type="number" (keydown)="blockMinus($event)"  (input)="checkInputValue($event)" [disabled]="!isFlexible" [placeholder]="placeHolder.squeezedSpeed"/>
                </div>
                <div class="my-auto mx-auto" [style.color]="!isFlexible ? 'gray' : 'black'" style="min-width: 10px;">/</div>
                <div class="col-12 md:col-10 flex-1">
                    <input formControlName="squeezedSpeed" pInputText id="squeezedSpeed" type="number" (keydown)="blockMinus($event)"  (input)="checkInputValue($event)" [disabled]="!isFlexible" [placeholder]="placeHolder.squeezedSpeed"/>
                </div>
                <div [style.color]="!isFlexible ? 'gray' : 'black'" class="my-auto" style="min-width: 40px;">(Kbps)</div>
            </div>

            <div class="grid px-4 flex flex-row flex-nowrap mb-3">
                <div style="min-width: 190px;" class="col-12 md:col-2 md:mb-0 p-0 responsive-div-error-2"></div>
                <div class="col-11 md:col-5 py-0">
                    <div *ngIf="isSqueezeSpeedNumberatorValid && createPlanForm.get('downSpeed').hasError('max')" class="text-red-500">
                        {{tranService.translate("ratingPlan.error.lengthError_number")}}
                    </div>
                    <div *ngIf="isSqueezeSpeedNumberatorValid && createPlanForm.get('downSpeed').hasError('min')" class="text-red-500">
                        {{tranService.translate("global.message.min",{value: 0})}}
                    </div>
                </div>
                <div class="col-11 md:col-5 py-0 responsive-error-2">
                    <div *ngIf="isSqueezeSpeedDenominatorValid && createPlanForm.get('squeezedSpeed').hasError('max')" class="text-red-500">
                        {{tranService.translate("ratingPlan.error.lengthError_number")}}
                    </div>
                    <div *ngIf="isSqueezeSpeedDenominatorValid && createPlanForm.get('squeezedSpeed').hasError('min')" class="text-red-500">
                        {{tranService.translate("global.message.min",{value: 0})}}
                    </div>
                </div>
            </div>

            <div class="field grid px-4 flex flex-row flex-nowrap responsive-div">
                <label htmlFor="feeSmsInside" [style.color]="!isFlexible ? 'gray' : 'black'" style="min-width: 190px;" class="col-12 md:col-2 md:mb-0">{{tranService.translate("ratingPlan.label.feePerInsideSMS")}}</label>
                <div class="col-12 md:col-10 flex-1 ">
                    <input  formControlName="feeSmsInside" pInputText id="feeSmsInside" type="number" (keydown)="blockMinus($event)" (input)="checkInputValue($event)"  [disabled]="!isFlexible" [placeholder]="placeHolder.feePerInsideSMS"/>
                </div>
                <div class="my-auto mx-auto" [style.color]="!isFlexible ? 'gray' : 'black'" style="min-width: 10px;"></div>
                <div class="col-12 md:col-10 flex-1">
                </div>
                <div [style.color]="!isFlexible ? 'gray' : 'black'" class="my-auto" style="min-width: 40px;"></div>
            </div>

            <div class="grid px-4 flex flex-row flex-nowrap mb-3">
                <div style="min-width: 190px;" class="col-12 md:col-2 md:mb-0 p-0 responsive-div-error"></div>
                <div class="col-11 md:col-11 py-0 responsive-error-1">
                    <div *ngIf="isFeePerInsideSMSValid && createPlanForm.get('feeSmsInside').hasError('max')" class="text-red-500">
                        {{tranService.translate("ratingPlan.error.lengthError_number")}}
                    </div>
                    <div *ngIf="isFeePerInsideSMSValid && createPlanForm.get('feeSmsInside').hasError('min')" class="text-red-500">
                        {{tranService.translate("global.message.min",{value: 0})}}
                    </div>
                </div>
            </div>

            <div class="field grid px-4 flex flex-row flex-nowrap responsive-div">
                <label htmlFor="feeSmsOutside" [style.color]="!isFlexible ? 'gray' : 'black'" style="min-width: 190px;" class="col-12 md:col-2 md:mb-0">{{tranService.translate("ratingPlan.label.feePerOutsideSMS")}}</label>
                <div class="col-12 md:col-10 flex-1 feeSmsOutside">
                    <input formControlName="feeSmsOutside" pInputText id="feeSmsOutside" type="number" (keydown)="blockMinus($event)"  (input)="checkInputValue($event)" [disabled]="!isFlexible" [placeholder]="placeHolder.feePerOutsideSMS"/>
                </div>
                <div class="my-auto mx-auto" [style.color]="!isFlexible ? 'gray' : 'black'" style="min-width: 10px;"></div>
                <div class="col-12 md:col-10 flex-1">
                </div>
                <div [style.color]="!isFlexible ? 'gray' : 'black'" class="my-auto" style="min-width: 40px;"></div>
            </div>

            <div class="grid px-4 flex flex-row flex-nowrap mb-3">
                <div style="min-width: 190px;" class="col-12 md:col-2 md:mb-0 p-0 responsive-div-error"></div>
                <div class="col-11 md:col-11 py-0 responsive-error-1">
                    <div *ngIf="isFeePerOutsideSMSValid && createPlanForm.get('feeSmsOutside').hasError('max')" class="text-red-500">
                        {{tranService.translate("ratingPlan.error.lengthError_number")}}
                    </div>
                    <div *ngIf="isFeePerOutsideSMSValid && createPlanForm.get('feeSmsOutside').hasError('min')" class="text-red-500">
                        {{tranService.translate("global.message.min",{value: 0})}}
                    </div>
                </div>
            </div>

            <div class="field grid px-4 pb-3 flex flex-row flex-nowrap responsive-div" >
                <label htmlFor="maximumFee" [style.color]="!isFlexible ? 'gray' : 'black'" style="min-width: 190px;" class="col-12 md:col-2 md:mb-0">{{tranService.translate("ratingPlan.label.maxFee")}}</label>
                <div class="col-12 md:col-10 flex-1 maximumFee">
                    <input formControlName="maximumFee" pInputText id="maximumFee" type="number" (keydown)="blockMinus($event)"  (input)="checkInputValue($event)" [disabled]="!isFlexible" [placeholder]="placeHolder.maxFee"/>
                </div>
                <div class="my-auto mx-auto" [style.color]="!isFlexible ? 'gray' : 'black'" style="min-width: 10px;"></div>
                <div class="col-12 md:col-10 flex-1">
                </div>
                <div [style.color]="!isFlexible ? 'gray' : 'black'" class="my-auto" style="min-width: 40px;"></div>
            </div>

            <div class="grid px-4 flex flex-row flex-nowrap mb-3">
                <div style="min-width: 190px;" class="col-12 md:col-2 md:mb-0 p-0 responsive-div-error"></div>
                <div class="col-11 md:col-11 py-0 responsive-error-1">
                    <div *ngIf="isMaxFeeValid && createPlanForm.get('maximumFee').hasError('max')" class="text-red-500">
                        {{tranService.translate("ratingPlan.error.lengthError_number")}}
                    </div>
                    <div *ngIf="isMaxFeeValid && createPlanForm.get('maximumFee').hasError('min')" class="text-red-500">
                        {{tranService.translate("global.message.min",{value: 0})}}
                    </div>
                </div>
            </div>
        </div>
        <div class="flex flex-row justify-content-center gap-3 p-2">
            <a routerLink="/plans"><button pButton [label]="tranService.translate('global.button.cancel')" class="p-button-secondary p-button-outlined" type="button"></button></a>
            <button pButton [label]="tranService.translate('global.button.save')" class="p-button-info" type="submit" [disabled]="isPlanCodeExisted||isPlanNameExisted||createPlanForm.invalid"></button>
        </div>
    </form>
</p-card>

<form [formGroup]="formSearchUser" (ngSubmit)="onSubmitSearchUser()">
    <div class="flex justify-content-center dialog-push-group">
        <p-dialog [header]="tranService.translate('account.label.addCustomerAccount')" [(visible)]="isShowDialogAddCustomerAccount" [modal]="true" [style]="{ width: '850px' }" [draggable]="false" [resizable]="false">
            <div class="grid">
                <!-- Ten dang nhap -->
                <div class="col-3">
                    <span class="p-float-label">
                        <input pInputText
                               class="w-full"
                               [(ngModel)]="searchInfoUser.username"
                               pInputText id="username"
                               formControlName="username"
                        />
                        <label htmlFor="username">{{tranService.translate("ratingPlan.label.username")}}</label>
                    </span>
                </div>
                <!-- Ho ten -->
                <div class="col-3">
                    <span class="p-float-label">
                        <input pInputText
                               class="w-full"
                               [(ngModel)]="searchInfoUser.fullName"
                               pInputText id="fullName"
                               formControlName="fullName"
                        />
                        <label htmlFor="fullName">{{tranService.translate("ratingPlan.label.fullName")}}</label>
                    </span>
                </div>
                <!-- Thanh pho -->
                <div class="col-3" [class]="userType == allUserType.ADMIN ? '' : 'flex flex-row justify-content-start align-items-center'">
                    <span class="p-float-label" *ngIf="userType == allUserType.ADMIN">
                        <p-multiSelect styleClass="w-full" [showClear]="true"
                                    id="provinceCode" [autoDisplayFirst]="false"
                                       [(ngModel)]="searchInfoUser.provinceCode"
                                    [options]="listProvince"
                                    optionLabel="name"
                                    optionValue="code"
                                    formControlName="provinceCode"
                        ></p-multiSelect>
                        <label for="provinceCode">{{tranService.translate("ratingPlan.label.province")}}</label>
                    </span>
                    <span *ngIf="userType != allUserType.ADMIN">{{tranService.translate("account.label.province")}}: {{provinceInfo}}</span>
                </div>
                <div class="col-3 pb-0">
                    <p-button icon="pi pi-search"
                              styleClass="p-button-rounded p-button-secondary p-button-text button-search"
                              type="submit"
                    ></p-button>
                </div>
            </div>
            <table-vnpt
                [fieldId]="'id'"
                [pageNumber]="pageNumberAssign"
                [pageSize]="pageSizeAssign"
                [(selectItems)]="selectItemsUser"
                [columns]="columnsInfoUser"
                [dataSet]="dataSetAssignPlan"
                [options]="optionTableAddCustomerAccount"
                [loadData]="searchUser.bind(this)"
                [rowsPerPageOptions]="[5,10,20, 25, 50]"
                [scrollHeight]="'400px'"
                [sort]="sort"
                [params]="searchInfoUser"
            ></table-vnpt>
<!--            <div class="flex flex-row justify-content-center align-items-center" style="padding-top: 30px">-->
<!--&lt;!&ndash;                <p-button styleClass="p-button-secondary" [label]="tranService.translate('global.button.cancel')" (click)="isShowDialogAddCustomerAccount = false" [style]="{'margin-right': '20px'}"></p-button>&ndash;&gt;-->
<!--&lt;!&ndash;                <p-button styleClass="p-button-info" [label]="tranService.translate('global.button.save')" (click)="isShowDialogAddCustomerAccount = false" ></p-button>&ndash;&gt;-->
<!--            </div>-->
        </p-dialog>
    </div>
</form>
