import {Component, Inject, Injector, OnInit} from "@angular/core";
import {TranslateService} from "../../../service/comon/translate.service";
import {UtilService} from "../../../service/comon/util.service";
import {ActivatedRoute, Router} from "@angular/router";
import {MenuItem} from "primeng/api";
import {DeviceService} from "../../../service/device/DeviceService";
import {Form, FormBuilder, FormGroup} from "@angular/forms";
import {MessageCommonService} from "../../../service/comon/message-common.service";
import {AutoCompleteCompleteEvent} from "primeng/autocomplete";
import {ComponentBase} from "../../../component.base";
import {CONSTANTS} from "../../../service/comon/constants";

@Component({
    selector: "app-device-create",
    templateUrl: './app.device.create.component.html',
})
export class AppDeviceCreateComponent extends ComponentBase implements OnInit {
    items: MenuItem[];
    deviceInfo: {
        imei: string | null,
        location: string | null,
        msisdn: string | null,
        country: string | null,
        category: string | null,
        expiredDate: Date | string | null,
        deviceType: string | null,
        note: string | null,
        iotLink: number|null,
    }
    home: MenuItem;
    formCreateDevice: FormGroup;
    //Danh sách thuê bao chưa gán thiết bị
    listSubscription: any[] | undefined;
    filteredSubscription: any[] | undefined;
    showValidationMsisdnError: boolean = false;
    notPermissionMisidn: boolean = false;
    msisdnEntered: boolean = true;
    msisdnPattern = /^84\d{9,10}$/;
    msisdnInputPattern = /^\d{1,12}$/;
    msisdn: number;
    msi: number;
    debounceTimeout: any;
    isShowExistsImei: boolean = false;
    constructor(@Inject(DeviceService) private deviceService: DeviceService,
                private formBuilder: FormBuilder,
                injector: Injector,
    ) {
        super(injector);
    }

    ngOnInit(): void {
        if (!this.checkAuthen([CONSTANTS.PERMISSIONS.DEVICE.CREATE])) {window.location.hash = "/access";}

        this.home = {icon: 'pi pi-home', routerLink: '/'};
        this.items = [{label: this.tranService.translate("global.menu.devicemgmt"), routerLink: '/devices'},
            { label: this.tranService.translate("global.menu.listdevice"), routerLink: "/devices" },
            {label: this.tranService.translate("global.menu.devicecreate")}];
        this.deviceInfo = {
            imei: null,
            location: null,
            msisdn: null,
            country: null,
            category: null,
            expiredDate: null,
            deviceType: null,
            note: null,
            iotLink: null,
        }
        this.formCreateDevice = this.formBuilder.group(this.deviceInfo)
    }

    goBack() {
        window.history.back();
    }
    getListSubscription() {
        let me = this;
        this.deviceService.getListSubscription(me.msi,(response) => {
            if(response == 0) {
                me.notPermissionMisidn = true;
            } else {
                me.notPermissionMisidn = false;
                me.listSubscription = response
                    .map(el => {
                        return {
                            msisdn: el,
                        }
                    });
            }
        });

    }
    create() {
        let me = this;
        let dataBody = this.formCreateDevice.value;
        dataBody.iotLink === true ? dataBody.iotLink = 1 : dataBody.iotLink = 0;
        dataBody.msisdn = me.msisdn;
        // console.log("Saved details:", dataBody);
        me.messageCommonService.onload();
        me.deviceService.checkMsisdnAndDevice(me.msisdn, (response) => {
            if (response.countSim <= 0) {
                me.messageCommonService.error(me.tranService.translate("device.text.msisdnNotExists"))
            } else if (response.countDevice > 0) {
                me.messageCommonService.error(me.tranService.translate("device.text.msisdnAssign"))
            } else {
                me.deviceService.createDevice(dataBody, (response) => {
                    me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"));
                    me.router.navigate(['/devices']);
                })
            }
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }
    filterMsisdn(event: AutoCompleteCompleteEvent) {
        let me = this;
        let filtered: any[] = [];
        let query = event.query;

        this.showValidationMsisdnError = !me.msisdnPattern.test(query);
        if (me.listSubscription) {
            for (let i = 0; i < (me.listSubscription as any[]).length; i++) {
                let subscription = (me.listSubscription as any[])[i];
                if (subscription.msisdn.toString().toLowerCase().indexOf(query.toLowerCase()) >= 0) {
                    filtered.push(subscription);
                }
            }
            me.filteredSubscription = filtered.slice(0, 100);
        } else if (me.msisdnInputPattern.test(query)){
            me.deviceService.getListSubscription(me.msi,(response) => {
                if(response == 0) {
                    me.notPermissionMisidn = true;
                } else {
                    me.notPermissionMisidn = false;
                    me.listSubscription = response
                        .map(el => {
                            return {
                                msisdn: el,
                            }
                        });
                    for (let i = 0; i < (me.listSubscription as any[]).length; i++) {
                        let subscription = (me.listSubscription as any[])[i];
                        if (subscription.msisdn.toString().toLowerCase().indexOf(query.toLowerCase()) >= 0) {
                            filtered.push(subscription);
                        }
                    }
                    me.filteredSubscription = filtered.slice(0, 100);
                }
            });
        }
    }
    onSelect($event) {
        let me = this;
        //selected
        let msisdnSelected = $event.msisdn;
        if (msisdnSelected != undefined) {
            me.notPermissionMisidn = false;
            me.showValidationMsisdnError = false;
            me.msisdnEntered = true;
            me.msisdn = msisdnSelected;
        }
    }
    onInput($event) {
        let me = this;
        me.msisdnEntered = true;
        let msisdnInput = me.formCreateDevice.controls['msisdn'].value;
        if (msisdnInput.length == 0) {
            me.msisdnEntered = false;
        }
        //input
        me.msi = Number(msisdnInput);
        // Hủy bỏ timeout hiện tại (nếu có)

        if (me.msisdnPattern.test(msisdnInput)) {
                me.getListSubscription();
                me.msisdn = msisdnInput;
                // console.log("inputok")
        } else {
            if (me.debounceTimeout) {
                clearTimeout(me.debounceTimeout);
            }
            me.showValidationMsisdnError = !me.msisdnPattern.test(msisdnInput);
            // Thiết lập một timeout mới để gọi API sau một khoảng thời gian nhất định (ví dụ: 500ms)
            me.debounceTimeout = setTimeout(() => {
                if (me.msisdnInputPattern.test(msisdnInput)) {
                    me.getListSubscription();
                }
            }, 500);
        }
    }

    checkExistsImei(){
        let me = this;
        if((this.deviceInfo.imei || "").trim() != ""){
            const imeiPattern = /^[a-zA-Z0-9]{2,64}$/;
            if (this.deviceInfo.imei  != null && imeiPattern.test(this.deviceInfo.imei.trim())) {
                this.debounceService.set("checkExistsImei", this.deviceService.checkExistsImeiDevice.bind(this.deviceService), this.deviceInfo.imei.trim(), (response) => {
                    if (response >= 1) {
                        me.isShowExistsImei = true;
                    } else {
                        me.isShowExistsImei = false;
                    }
                })
            }
        }
    }
    protected readonly CONSTANTS = CONSTANTS;
}
