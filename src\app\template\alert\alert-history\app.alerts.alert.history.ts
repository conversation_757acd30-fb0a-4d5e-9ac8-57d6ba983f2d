import {AfterContentChecked, Component, Inject, Injector, OnInit} from "@angular/core";
import {MenuItem} from "primeng/api";
import {FormBuilder} from "@angular/forms";
import {ColumnInfo, OptionTable} from "../../common-module/table/table.component";
import {CONSTANTS} from "../../../service/comon/constants";
import {CustomerService} from "../../../service/customer/CustomerService";
import {AlertService} from "../../../service/alert/AlertService";
import {ComponentBase} from "../../../component.base";

@Component({
    selector: "app-alerts-alert-history",
    templateUrl: "./app.alerts.alert.history.html"
})
export class AppAlertsAlertHistoryComponent extends ComponentBase implements OnInit, AfterContentChecked {
    items: MenuItem[];
    home: MenuItem;
    optionTable: OptionTable;
    columns: Array<ColumnInfo>;
    formSearchAlertHistory: any;
    searchInfoStandard: any;
    selectItems: Array<{ id: string, [key: string]: any }>;
    searchInfo: {
        msisdn: string | null,
        statusSim: number | null,
        fromDate: Date | null,
        toDate: Date | null,
        customerId: string | null,
        actionType: number | null,
        ratingPlanCode : string | null,
        dataPoolSubCode : string | null,
    }
    pageNumber: number;
    pageSize: number;
    sort: string;
    dataSet: {
        content: Array<any>,
        total: number,
    };
    maxDateFrom: Date | number | string | null = new Date();
    minDateTo: Date | number | string | null = null;
    maxDateTo: Date | number | string | null = new Date();
    eventTypes: Array<{ value: string | number, name: string }>;
    listCustomer: Array<any>;
    listCustomerOrigin: Array<any>;
    msisdnPattern = /^\d{0,12}$/;
    eventOptions : Array<any>
    actionOptions : Array<any>
    appliedPlanOptions : Array<any>

    constructor(@Inject(AlertService) private alertService: AlertService,
                @Inject(CustomerService) private customerService: CustomerService,
                private formBuilder: FormBuilder,
                injector: Injector) {
        super(injector)
    }

    ngOnInit(): void {
        let me = this
        this.selectItems = [];
        this.home = {icon: 'pi pi-home', routerLink: '/'};
        this.items = [{
            label: this.tranService.translate("global.menu.alerts"),
            routerLink: '/alerts'
        }, {label: this.tranService.translate("global.menu.alerthistory")}];
        this.searchInfoStandard = {
            msisdn: null,
            statusSim: null,
            fromDate: null,
            toDate: null,
            customerId: null,
        }
        this.searchInfo = {
            msisdn: null,
            statusSim: null,
            fromDate: null,
            toDate: null,
            customerId: null,
            actionType: null,
            ratingPlanCode : null,
            dataPoolSubCode : null,
        }
        this.eventTypes = [
            {
                value: CONSTANTS.ALERT_STATUS_SIM.OUT_PLAN,
                name: this.tranService.translate("alert.statusSim.outPlan")
            },
            {
                value: CONSTANTS.ALERT_STATUS_SIM.OUT_LINE,
                name: this.tranService.translate("alert.statusSim.outLine")
            },
            // {
            //     value: CONSTANTS.ALERT_STATUS_SIM.DISCONNECTED,
            //     name: this.tranService.translate("alert.statusSim.disconnected")
            // },
            // {
            //     value: CONSTANTS.ALERT_STATUS_SIM.NEW_CONNECTION,
            //     name: this.tranService.translate("alert.statusSim.newConnection")
            // }
        ]

        this.eventOptions = [
            {name:me.tranService.translate("alert.eventType.exceededPakage"), value:CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_PACKAGE},
            {name:me.tranService.translate("alert.eventType.exceededValue"), value:CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_VALUE},
            // {name:me.tranService.translate("alert.eventType.sessionEnd"), value:CONSTANTS.ALERT_EVENT_TYPE.SESSION_END},
            // {name:me.tranService.translate("alert.eventType.sessionStart"), value:CONSTANTS.ALERT_EVENT_TYPE.SESSION_START},
            {name:me.tranService.translate("alert.eventType.smsExceededPakage"), value:CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_PACKAGE},
            {name:me.tranService.translate("alert.eventType.smsExceededValue"), value:CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_VALUE},
            {name:me.tranService.translate("alert.eventType.owLock"), value:CONSTANTS.ALERT_EVENT_TYPE.ONE_WAY_LOCK},
            {name:me.tranService.translate("alert.eventType.twLock"), value:CONSTANTS.ALERT_EVENT_TYPE.TWO_WAY_LOCK},
            // {name:me.tranService.translate("alert.eventType.noConection"), value:CONSTANTS.ALERT_EVENT_TYPE.NO_CONECTION},
            // {name:me.tranService.translate("alert.eventType.simExp"), value:CONSTANTS.ALERT_EVENT_TYPE.SIM_EXP},
            // {name:me.tranService.translate("alert.eventType.dataWalletExp"), value:CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP},
            {name:me.tranService.translate("alert.eventType.owtwlock") , value:CONSTANTS.ALERT_EVENT_TYPE.ONE_WAY_TWO_WAY_LOCK},
            // {name:me.tranService.translate("alert.eventType.walletThreshold") , value:CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD}
        ]
        if (this.checkAuthen([CONSTANTS.PERMISSIONS.ALERT.CREATE_WALLET_EXPIRY])) {
            this.eventOptions.push({name:me.tranService.translate("alert.eventType.dataWalletExp") , value:CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP})
        }
        if (this.checkAuthen([CONSTANTS.PERMISSIONS.ALERT.CREATE_WALLET_THRESHOLD])) {
            this.eventOptions.push({name:me.tranService.translate("alert.eventType.walletThreshold") , value:CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD})
        }

        this.actionOptions = [
            {name:this.tranService.translate("alert.actionType.alert"), value:CONSTANTS.ALERT_ACTION_TYPE.ALERT}
            // ,
            // {name:this.tranService.translate("alert.actionType.api"), value:CONSTANTS.ALERT_ACTION_TYPE.API}
        ]

        this.formSearchAlertHistory = this.formBuilder.group(this.searchInfo);
        this.optionTable = {
            hasClearSelected: false,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
        }
        this.columns = [
            {
                name: this.tranService.translate("sim.label.sothuebao"),
                key: "msisdn",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
                funcConvertText: (value, item) => {
                    return value || item?.dataPoolPhoneActive;
                }
            },
            {
                name: this.tranService.translate("historyRegisterPlan.label.ratingPlan"),
                key: "ratingPlan",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("sim.label.dataPoolSubCode"),
                key: "dataPoolSubCode",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("sim.label.khachhangvathue"),
                key: "customerName",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
                funcConvertText: (value, item) => {
                    return value || item?.dataPoolTax
                }
            },
            {
                name: this.tranService.translate("alert.label.event"),
                key: "simStatus",
                size: "150px",
                align: "left",
                funcConvertText(value) {
                    if(value == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_PACKAGE){
                        return me.tranService.translate("alert.eventType.exceededPakage");
                    }else if(value == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_VALUE){
                        return me.tranService.translate("alert.eventType.exceededValue");
                    }else if(value == CONSTANTS.ALERT_EVENT_TYPE.SESSION_END){
                        return me.tranService.translate("alert.eventType.sessionEnd");
                    }else if(value == CONSTANTS.ALERT_EVENT_TYPE.SESSION_START){
                        return me.tranService.translate("alert.eventType.sessionStart");
                    }else if(value == CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_PACKAGE){
                        return me.tranService.translate("alert.eventType.smsExceededPakage");
                    }else if(value == CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_VALUE){
                        return me.tranService.translate("alert.eventType.smsExceededValue");
                    }else if(value == CONSTANTS.ALERT_EVENT_TYPE.ONE_WAY_LOCK){
                        return me.tranService.translate("alert.eventType.owLock");
                    }else if(value == CONSTANTS.ALERT_EVENT_TYPE.TWO_WAY_LOCK){
                        return me.tranService.translate("alert.eventType.twLock");
                    }else if(value == CONSTANTS.ALERT_EVENT_TYPE.NO_CONECTION){
                        return me.tranService.translate("alert.eventType.noConection");
                    }else if(value == CONSTANTS.ALERT_EVENT_TYPE.SIM_EXP){
                        return me.tranService.translate("alert.eventType.simExp");
                    }else if(value == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP){
                        return me.tranService.translate("alert.eventType.dataWalletExp");
                    }else if(value == CONSTANTS.ALERT_EVENT_TYPE.ONE_WAY_TWO_WAY_LOCK){
                        return me.tranService.translate("alert.eventType.owtwlock");
                    }else if (value == CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD){
                        return me.tranService.translate("alert.eventType.walletThreshold");
                    }else{
                        return "";
                    }
                },
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("alert.label.action"),
                key: "actionType",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
                funcConvertText(value) {
                    if(value == CONSTANTS.ALERT_ACTION_TYPE.ALERT){
                        return me.tranService.translate("alert.actionType.alert");
                    }else if(value == CONSTANTS.ALERT_ACTION_TYPE.API){
                        return me.tranService.translate("alert.actionType.api");
                    }else{
                        return "";
                    }
                },
            },
            {
                name: this.tranService.translate("sim.label.dataPoolEmail"),
                key: "dataPoolEmail",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
                isShowTooltip: true,
                style: {
                    display: 'inline-block',
                    maxWidth: '350px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                },
                funcConvertText: (value, item) => {
                    let arrayValue=[], arrayAlertEmails=[], arrayReceivingGroup=[]
                    if(value)
                        arrayValue = value.split(', ').map(item => item.trim())
                    if(item.alertEmails)
                        arrayAlertEmails = item.alertEmails.split(', ').map(item => item.trim())
                    if(item.receivingGroupEmails)
                        arrayReceivingGroup = item.receivingGroupEmails.split(', ').map(item => item.trim());
                    return Array.from(new Set([...arrayValue, ...arrayAlertEmails, ...arrayReceivingGroup])).join(', ')
                }
            },
            {
                name: this.tranService.translate("sim.label.dataPoolPhoneActive"),
                key: "dataPoolPhoneActive",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
                isShowTooltip: true,
                style: {
                    display: 'inline-block',
                    maxWidth: '350px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                },
                funcConvertText: (value, item) => {
                    let arrayValue=[], arrayAlertMsisdns=[], arrayReceivingGroup=[]
                    if(value)
                        arrayValue = value.split(', ').map(item => item.trim())
                    if(item.alertMsisdns)
                        arrayAlertMsisdns = item.alertMsisdns.split(', ').map(item => item.trim())
                    if(item.receivingGroupMsisdns)
                        arrayReceivingGroup = item.receivingGroupMsisdns.split(', ').map(item => item.trim());
                    return Array.from(new Set([...arrayValue, ...arrayAlertMsisdns, ...arrayReceivingGroup])).join(', ')
                }
            },
            {
                name: this.tranService.translate("alert.label.contentEmail"),
                key: "emailContent",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
                isShowTooltip: true,
                style: {
                    display: 'inline-block',
                    maxWidth: '350px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                }
            },            {
                name: this.tranService.translate("alert.label.contentSms"),
                key: "smsContent",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
                isShowTooltip: true,
                style: {
                    display: 'inline-block',
                    maxWidth: '350px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                }
            },
            {
                name: this.tranService.translate("alert.label.time"),
                key: "time",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
                funcConvertText: (value) => {
                    return this.utilService.convertDateTimeToString(new Date(value))
                }
            },
        ];
        this.pageNumber = 0;
        this.pageSize = 10;
        this.sort = "time,desc";
        this.dataSet = {
            content: [],
            total: 0,
        }
        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
    }

    onSubmitSearch() {
        let me = this;
        me.pageNumber = 0;
        if (me.searchInfo.msisdn == null || me.msisdnPattern.test(me.searchInfo.msisdn)) {
            me.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
        } else {
            me.messageCommonService.warning(me.tranService.translate("global.message.invalidSubsciption"));
            me.dataSet = {
                content: [],
                total: 0,
            }
        }
    }

    search(page, limit, sort, params) {
        let me = this;
        this.pageNumber = page;
        this.pageSize = limit;
        this.sort = sort;
        let dataParams = {
            page,
            size: limit,
            sort
        }
        Object.keys(this.searchInfo).forEach(key => {
            if (this.searchInfo[key] != null) {
                if (key == "fromDate") {
                    dataParams["fromDate"] = me.utilService.convertDateToString(me.searchInfo.fromDate);
                } else if (key == "toDate") {
                    dataParams["toDate"] = me.utilService.convertDateToString(me.searchInfo.toDate);
                } else {
                    dataParams[key] = this.searchInfo[key];
                }
                if(key == "customerId"){
                    dataParams['dataPoolTax'] = dataParams['customerId']
                    dataParams['customerName'] = dataParams['customerId']
                    delete dataParams['customerId']
                }
            }
        })
        me.messageCommonService.onload();
        this.alertService.getListAlertHistory(dataParams, (response) => {
            me.dataSet = {
                content: response.content,
                total: response.totalElements
            }
        }, null, () => {
            me.messageCommonService.offload();
        })
    }

    onChangeDateFrom(value) {
        if (value) {
            this.minDateTo = value;
        } else {
            this.minDateTo = null
        }
    }

    onChangeDateTo(value) {
        if (value) {
            this.maxDateFrom = value;
        } else {
            this.maxDateFrom = new Date();
        }
    }

    ngAfterContentChecked(): void {
    }
}
