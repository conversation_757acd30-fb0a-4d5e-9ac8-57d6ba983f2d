import {Component, Inject, Injector, OnInit} from "@angular/core";
import {ComponentBase} from "../../../../component.base";
import {CONSTANTS, isVinaphoneNumber} from "../../../../service/comon/constants";
import {GroupSubWalletService} from "../../../../service/group-sub-wallet/GroupSubWalletService";
import {MenuItem} from "primeng/api";
import {FormControl, FormGroup, Validators, FormBuilder} from "@angular/forms";
import {ComboLazyControl} from "../../../common-module/combobox-lazyload/combobox.lazyload";
import {PhoneInfo, PhoneInGroup, ShareDetail} from "../../data-pool.type-data";
import {TrafficWalletService} from "../../../../service/datapool/TrafficWalletService";
import {ShareManagementService} from "../../../../service/datapool/ShareManagementService";
import {noneWhitespaceValidator} from "../../../common-module/validatorCustoms";
import {OptionInputFile} from "../../../common-module/input-file/input.file.component";
import * as Excel from "exceljs";
import * as moment from "moment/moment";
import {saveAs} from "file-saver";
import * as FileSaver from "file-saver";

@Component({
    selector: 'app-create-group-sub',
    templateUrl: './group-sub-wallet.create.component.html',
})

export class GroupSubWalletCreateComponent extends ComponentBase implements OnInit {
    createGroupForm = new FormGroup({
        groupCode: new FormControl("", [Validators.required,Validators.maxLength(16), Validators.pattern('^[A-Za-z0-9_-]+$')]),
        groupName: new FormControl("",[Validators.required, Validators.maxLength(255), Validators.pattern('^[a-zA-Z0-9\\- _\\u00C0-\\u024F\\u1E00-\\u1EFF]+$'), noneWhitespaceValidator()]),
        description: new FormControl("", [Validators.maxLength(255)]),
    });
    items: MenuItem[];
    home: MenuItem;
    boxSimAddController: ComboLazyControl = new ComboLazyControl();
    displayAddSim: boolean = false;
    phoneReceiptSelect: string = "";
    isClickCreate: boolean = true;
    phoneList : PhoneInGroup[] = [];
    isValidPhone: boolean = true;
    isShowDialogAddSub: boolean = false;
    isShowDialogAddFile: boolean = false;
    isShowErrorUpload: boolean = false;
    fileObject: any;
    messageErrorUpload: string| null;
    shareList: ShareDetail[];
    listGroup: any = [];
    isExistGroupCode: boolean;
    userInfo: any;
    formInstance: any;
    formObject: {
        file: any
    }
    textDescription: string | null;
    options!: OptionInputFile;
    invalid: "required" | "maxsize" | "invalidtype" | null;
    constructor(
        injector: Injector,
        private formBuilder: FormBuilder,
        private groupSubWalletService: GroupSubWalletService,
        @Inject(TrafficWalletService) private walletService: TrafficWalletService,
        @Inject(ShareManagementService) private shareService: ShareManagementService,
    ) {
        super(injector);
    }

    ngOnInit() {
        let me = this;
        this.isExistGroupCode = false;
        this.formObject = {
            file: null
        }
        this.formInstance = this.formBuilder.group(this.formObject);
        this.textDescription = this.tranService.translate("global.button.uploadFile");
        me.userInfo = this.sessionService.userInfo;
        this.options = {
            type: ['xls','xlsx'],
            messageErrorType: this.tranService.translate("global.message.wrongFileExcel"),
            maxSize: 10,
            unit: "MB",
            required: true,
            isShowButtonUpload: true,
            actionUpload: this.uploadFile.bind(this),
            disabled: false
        }
        if (me.checkAuthen([CONSTANTS.PERMISSIONS.SHARE_GROUP.CREATE])) {
            this.items = [{ label: this.tranService.translate("global.menu.trafficManagement") }, { label: this.tranService.translate("global.menu.listGroupSub"), routerLink: "/data-pool/group/listGroupSub" }, { label: this.tranService.translate("datapool.label.createGroupShare") }];
            this.home = { icon: 'pi pi-home', routerLink: '/' };
            this.shareList = [];
            me.getAllGroup();
        } else {
            window.location.hash = "/access";
        }
    }

    getAllGroup () {
        let me = this;
        me.messageCommonService.onload();
        me.groupSubWalletService.getAllGroup((response) => {
            me.listGroup = response;
        }, null, () => {
            me.messageCommonService.offload()
        })
    }

    getListShareInfoCbb(params, callback) {
        return this.shareService.getListShareInfoCbb(params, (response)=>{
            this.phoneList = response.content;
            callback(response)
        });
    }

    submitForm () {
        let me = this;
        let dataParams = {
            groupCode: me.createGroupForm.value.groupCode.trim(),
            groupName: me.createGroupForm.value.groupName.trim(),
            description: me.createGroupForm.value.description.trim(),
            listSub: me.shareList
        };
        this.messageCommonService.onload()
        this.groupSubWalletService.create({},dataParams,{},(response)=>{
            me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"))
            this.router.navigate([`/data-pool/group/edit/${response.id}`]);
        }, (error) => {
            if (error.error.error.errorCode === "error.duplicate.value") {
                me.messageCommonService.error(this.tranService.translate("datapool.error.existedGroupCode"));
            }
        },
            () => {
            me.messageCommonService.offload();
        })
    }

    checkValidAdd(){
        this.isClickCreate = true
        if(!this.phoneList.find(dta => dta.phoneReceipt.toString() === this.phoneReceiptSelect)){
            this.isClickCreate = false
        }else{
            this.isClickCreate = true
        }
        if(this.phoneReceiptSelect == ""|| this.phoneReceiptSelect == null || this.phoneReceiptSelect === undefined){
            this.isClickCreate = true
        }

        const regex = /^0[0-9]{9,10}$/;
        const inputValue = this.phoneReceiptSelect;
        this.isValidPhone = regex.test(inputValue);
    }

    cleanArray(arr: any[]): any[] {
        return arr.filter(item => item !== null && item !== undefined && item !== "");
    }

    addSubToGroup() {
        let me = this;
        me.isShowDialogAddSub = true;
    }

    addSubFile() {
        let me = this;
        me.isShowDialogAddFile = true;
    }

    cancelAddSub() {
        let me = this;
        me.isShowDialogAddSub = false;
        me.shareList = [];
        me.phoneReceiptSelect = "";
    }

    addPhone(data){
        let me = this;
        if(data === null || data === undefined){
            return;
        }
        me.isClickCreate = false
        const value = me.phoneList.find(dta => dta.phoneReceipt === data);
        const phone = String(data)?.replace(/^0/,"84");
        //check trước khi vào
        let exists = this.shareList.some(item => item.phoneReceipt.toString() === phone);
        if (me.shareList.length >= CONSTANTS.SHARE_GROUP.LIMIT_ADD) {
            me.messageCommonService.error(me.tranService.translate("datapool.message.maximumSubAdd"));
            return;
        } else if (me.shareList.length > CONSTANTS.SHARE_GROUP.LIMIT) {
            me.messageCommonService.error(me.tranService.translate("datapool.message.maximumSubDisplay"));
            return;
        } else if (exists) {
            console.log("vào check trc")
            me.messageCommonService.error(me.tranService.translate("datapool.message.dublicateShareInfo"));
            return;
        }

        if (value?.idGroup) {
            me.messageCommonService.error(me.tranService.translate('datapool.message.duplicateSub', {data: data, groupName: value?.groupName}));
        } else {
            me.addPhoneTable(value, data)
            /**
             * UAT 2.4 issue 31
             * Khi add số thuê bao được chia sẻ, nếu đã có trong danh sách thì bỏ qua check số đó là thuê bao vinaphone hay không, trong các trường hợp sau:
             * - Chia sẻ thường
             * - Nhóm chia sẻ tự động > thêm sdt chia sẻ tự động
             * - Thêm thuê bao vào nhóm
             * - icon chia sẻ ở Danh sách ví
             */
            // me.messageCommonService.onload()
            // me.walletService.checkParticipant({phoneNumber : phone},
            //     (response)=>{
            //         if(response.error_code === "0" && (response.result === "02" || response.result === "11")){
            //             me.addPhoneTable(value, data)
            //         }else if(response.error_code === "0" && response.result === "0"){
            //             if(isVinaphoneNumber(data)){
            //                 me.addPhoneTable(value, data)
            //             }else{
            //                 me.messageCommonService.error(me.tranService.translate("datapool.message.notValidPhone"))
            //             }
            //         }else{
            //             me.messageCommonService.error(me.tranService.translate("datapool.message.notValidPhone"))
            //         }
            //     },
            //     null,()=>{
            //         me.messageCommonService.offload();
            //     })
        }
    }

    addPhoneNotInSelect(phone) {
        let me = this;

        if(!phone){
            return;
        }

        const value = me.phoneList.find(dta => dta.phoneReceipt === phone);
        const phoneValid = String(phone)?.replace(/^0/,"84");
        //check trước khi vào
        let exists = this.shareList.some(item => item.phoneReceipt.toString() === phoneValid);
        if (me.shareList.length >= CONSTANTS.SHARE_GROUP.LIMIT_ADD) {
            me.messageCommonService.error(me.tranService.translate("datapool.message.maximumSubAdd"));
            return;
        } else if (me.shareList.length > CONSTANTS.SHARE_GROUP.LIMIT) {
            me.messageCommonService.error(me.tranService.translate("datapool.message.maximumSubDisplay"));
            return;
        } else if (exists) {
            console.log("vào check trc")
            me.messageCommonService.error(me.tranService.translate("datapool.message.dublicateShareInfo"));
            return;
        }
        me.groupSubWalletService.checkPhoneBelongGroup({phoneNumber: phone}, (response)=>{


            if (response) {
                me.messageCommonService.error(me.tranService.translate('Thuê bao này đã thuộc nhóm khác'));
            } else {
                if (value?.idGroup) {
                    me.messageCommonService.error(`Thuê bao đang thuộc nhóm "${value.groupName}"`);
                } else {
                    me.addPhoneTable(value, phone);
                }
                /**
                 * bỏ check số vina
                 */
                // me.messageCommonService.onload()
                // me.walletService.checkParticipant({phoneNumber : phoneValid},
                //     (response)=>{
                //         if (value?.idGroup) {
                //             me.messageCommonService.error(`Thuê bao đang thuộc nhóm "${value.groupName}"`);
                //         } else if(response.error_code === "0" && (response.result === "02" || response.result === "11") && !value?.idGroup){
                //             me.addPhoneTable(value, phone);
                //             me.phoneReceiptSelect = "";
                //         } else if(response.error_code === "0" && response.result === "0" && !value?.idGroup){
                //             if(isVinaphoneNumber(phone)){
                //                 me.addPhoneTable(value, phone);
                //                 me.phoneReceiptSelect = "";
                //             }else{
                //                 me.messageCommonService.error(me.tranService.translate("datapool.message.notValidPhone"))
                //             }
                //         }else{
                //             me.messageCommonService.error(me.tranService.translate("datapool.message.notValidPhone"))
                //         }
                //     },
                //     null,()=>{
                //         me.messageCommonService.offload();
                //     })
                me.phoneReceiptSelect = "";
                this.getListShareInfoCbb.call(this)
            }
        },null ,null );
    }

    addPhoneTable(value, data){
        console.log(value)
        let me = this;
        let exists = this.shareList.some(item => item.phoneReceipt === data);
        if(value){
            if (me.shareList.length >= CONSTANTS.SHARE_GROUP.LIMIT_ADD) {
                me.messageCommonService.error(me.tranService.translate("datapool.message.maximumSubAdd"));
            } else if (me.shareList.length >= CONSTANTS.SHARE_GROUP.LIMIT) {
                me.messageCommonService.error(me.tranService.translate("datapool.message.maximumSubDisplay"));
            } else if (exists) {
                me.messageCommonService.error(me.tranService.translate("datapool.message.dublicateShareInfo"));
            } else {
                me.shareList.unshift(value)
                setTimeout(function(){
                    me.phoneReceiptSelect = "";
                },100);
            }
        }else{
            let pushData: ShareDetail = {
                id: value?.id || "",
                phoneReceipt: data,
                name:value?.name || "",
                email:value?.email || "",
            }
            if (me.shareList.length >= CONSTANTS.SHARE_GROUP.LIMIT_ADD) {
                me.messageCommonService.error(me.tranService.translate("datapool.message.maximumSubAdd"));
            } else if (me.shareList.length > CONSTANTS.SHARE_GROUP.LIMIT) {
                me.messageCommonService.error(me.tranService.translate("datapool.message.maximumSubDisplay"));
            } else if (exists) {
                me.messageCommonService.error(me.tranService.translate("datapool.message.dublicateShareInfo"));
            } else {
                me.shareList.unshift(pushData)
            }
        }
        me.isClickCreate = true
    }

    changeDataName(event, i){
        const shareValue = event.target.value
        this.shareList[i].name = shareValue
    }

    changeDataMail(event, i){
        const shareValue = event.target.value
        this.shareList[i].email = shareValue
        this.isAllEmailsValid();
    }

    isMailInvalid(email:string){
        if (!email){
            return false
        }
        // const pattern:RegExp = /^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$/
        const pattern: RegExp = /^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)*$/;
        return !pattern.test(email);
    }
    // Hàm kiểm tra xem tất cả email trong listSub có hợp lệ không
    isAllEmailsValid(): boolean {
        return this.shareList.every(item => !this.isMailInvalid(item.email));
    }

    deleteItem(i){
        this.messageCommonService.confirm(this.tranService.translate("datapool.button.deleteSub"),
            this.tranService.translate("datapool.message.confirmDelete"),{
                ok: ()=>{
                    const a = this.shareList[i].data
                    if(a){
                        this.shareList[i].data = null
                        this.shareList[i].percent = null
                    }
                    this.shareList = this.shareList.filter((item,index) => index != i);
                }
            })
    }
    onNameBlur() {
        let me = this;
        let formattedValue = this.createGroupForm.get('groupName').value;
        formattedValue = formattedValue.trim().replace(/\s+/g, ' ');
        this.createGroupForm.get('groupName').setValue(formattedValue);
    }
    onCodeBlur() {
        let me = this;
        let value = this.createGroupForm.get('groupCode').value;
        this.groupSubWalletService.checkExistGroupCode({groupCode: value}, (res) => {
            if (res == true) {
                this.isExistGroupCode = true;
            } else {
                this.isExistGroupCode = false
            }
        })
    }

    clearFileCallback(){
        this.isShowErrorUpload = false;
    }

    uploadFile(objectFile: any) {
        let me = this;
        if(objectFile.size >= 1048576){
            this.messageCommonService.error("Dung lượng file vượt quá dung lượng tối đa")
            return
        }
        let dataParams = {
            groupCode: me.createGroupForm.value.groupCode.trim(),
            groupName: me.createGroupForm.value.groupName.trim(),
            description: me.createGroupForm.value.description.trim(),
        };
        me.messageCommonService.onload();
        this.groupSubWalletService.uploadFile(objectFile, dataParams, async (response) => {
            const createdId = response.headers.get('CREATED-ID');
            const dataError = [];
            const errorMessageCode = {
                '10': 'Tham số đầu vào không hợp lệ',
                '400': response => dataError.push(response?.headers?.get('cause')),
                '401': 'Kích thước file vượt quá giới hạn',
                '402': 'File tải lên thừa cột',
                '403': 'File tải lên thiếu cột',
                '404': 'File tải lên trùng cột',
                '405': 'Không thể lấy thông tin hàng từ file excel',
                '501': response => dataError.push(response?.headers?.get('Content-Disposition')),
                '430': 'Sai định dạng file mẫu',
                '440': 'File vượt quá 3000 SĐT',
                '450': 'Tổng số điện thoại trong nhóm và file đã vượt quá giới hạn 3000 SĐT. Vui lòng kiểm tra lại dữ liệu!'
            };

            if(createdId){
                me.messageCommonService.success('Import người được chia sẻ thành công');
                me.isShowDialogAddFile = false;
                this.router.navigate(['/data-pool/group/edit', createdId]);
            }

            if (response?.headers?.get('cause') === '0') {


            } else {
                me.isShowErrorUpload = true;
                const errorMessage = errorMessageCode[response?.headers?.get('cause')] || 'Lỗi không xác định';
                console.log(errorMessageCode)
                if (typeof errorMessage === 'function') {
                    errorMessage(response);
                    if (!response?.body) {
                        const fileName = response?.headers?.get('Content-Disposition');
                        const workbook = new Excel.Workbook();
                        const buf = await workbook.xlsx.writeBuffer();
                        const spliceFileName = fileName.substring(0, fileName.length - 5);
                        const exportFileName = ''.concat(spliceFileName, '_Danh sách lỗi_', moment().format('DDMMYYYYHHMMss'));
                        // download the processed file
                        await saveAs(new Blob([buf]), `${exportFileName}.xlsx`);
                    } else {
                        const dateMoment = moment().format('DDMMYYYYHHmmss');
                        const name = (objectFile.name || objectFile.fileName).split('.');
                        me.exportFile(response?.body, `${name[0]}_Danh sách lỗi_${dateMoment}`, '');
                        me.messageCommonService.error(me.tranService.translate('datapool.text.downloadErrorMessage'), null, 10000)
                    }
                } else {
                    me.messageCommonService.error(errorMessage);
                }
            }

        },null,()=>{
            this.messageCommonService.offload()
        })
    }

    exportFile = (bytes, fileName, fileType) => {

        const file = new Blob([bytes], {
            type: fileType || 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        });
        FileSaver.saveAs(file, fileName);
    };


    downloadTemplate(){
        this.groupSubWalletService.downloadTemplate();
    }

    changeFile(event){
        let file = event.target.files[0];
        this.fileObject = file;
        if(this.fileObject == null){
            this.textDescription = this.tranService.translate("global.button.uploadFile");
            this.checkValid();
            return;
        }
        let filename = file.name;
        let filesize = Math.round(file.size/1024);
        let suffix = "KB";
        if(filesize/1024 > 2){
            filesize = Math.round(filesize/1024);
            suffix = "MB";
        }
        this.textDescription = `${filename} ${this.utilService.convertNumberToString(filesize)}(${suffix})`
        this.checkValid();
    }


    resetFile(){
        this.formObject.file = null;
        this.textDescription = this.tranService.translate("global.button.uploadFile");
        this.fileObject = null;
        this.checkValid();
    }

    checkValid(){
        this.invalid = null;
        if(this.fileObject){
            if(this.options.type){
                let extension = this.fileObject.name.substring(this.fileObject.name.lastIndexOf(".")+1, this.fileObject.name.length);
                if(!this.options.type.includes(extension)){
                    this.invalid = "invalidtype";
                }
            }
            if(this.options.maxSize && this.invalid == null){
                let comparesize = this.options.maxSize;
                if(this.options.unit == "KB"){
                    comparesize = comparesize * 1024;
                }else if(this.options.unit == "MB"){
                    comparesize = comparesize * 1024 * 1024;
                }else if(this.options.unit == "GB"){
                    comparesize = comparesize * 1024 * 1024 * 1024;
                }
                if(this.fileObject.size > comparesize){
                    this.invalid = "maxsize";
                }
            }
        }else{
            if(this.options.required){
                this.invalid = "required";
            }
        }
    }

    reset(){
        this.formObject.file = null;
        this.fileObject = null;
        this.invalid = null;
        this.options.disabled = false;
    }

    upload(){
        this.options.actionUpload(this.fileObject);
    }

    protected readonly CONSTANTS = CONSTANTS;
}
