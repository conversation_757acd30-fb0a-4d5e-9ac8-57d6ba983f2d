import {AfterContentChecked, Inject, Injector, OnChanges, OnInit} from '@angular/core';
import { Component } from '@angular/core';
import { MenuItem } from 'primeng/api';
import { ComponentBase } from 'src/app/component.base';
import { LayoutService } from "src/app/service/app.layout.service";
import { CONSTANTS } from 'src/app/service/comon/constants';
import { TranslateService } from 'src/app/service/comon/translate.service';
import { environment } from 'src/environments/environment';
import {TicketService} from "../../service/ticket/TicketService";

interface ItemMenu{
    label: string,
    icon?: string,
    routerLink?: Array<string>,
    items?: Array<ItemMenu>,
    routerLinkActiveOptions?: {
        paths?: string,
        queryParams?: string,
        matrixParams?: string,
        fragment?: string
    },
    badge?: string,
    url?: Array<string>,
    target?: string
}
@Component({
    selector: 'app-menu',
    templateUrl: './app.menu.component.html'
})
export class AppMenuComponent extends ComponentBase implements OnInit{
    // model: ItemMenu[] = [];
    model:  {
                label: string,
                items: any[] // MenuItem[]
            }[] = [];

    constructor(public layoutService: LayoutService, injector: Injector,
                @Inject(TicketService) private ticketService: TicketService) {
        super(injector);
    }

    oldScrollTop = 0;

    handleOpenBigSidebar(){
        this.layoutService.changeSize('big')
        let node = document.querySelector(".display-subElement");
        node?.remove();
    }

    handleScroll(event: any){
        let node = document.querySelector(".display-subElement");
        node?.remove();
        // let delTa = this.oldScrollTop - event.target["scrollTop"];
        // this.oldScrollTop = event.target["scrollTop"];
        // let node = document.querySelector(".display-subElement")as HTMLDivElement;
        // if (node) {
        //   let currentTop = parseInt(node.style.top || "0", 10);
        //   let newTop = currentTop + delTa;

        //   // Giới hạn vị trí top để không bị quá mức cho phép
        // //   if (newTop < 0) {
        // //     newTop = 0;
        // //   }

        //   // Gán lại giá trị top cho phần tử
        //   node.style.top = newTop + "px";
        // }
      }

      isActiveRoute(item: any): boolean {
        if (item.routerLink) {
          const url = item.routerLink.join('/') || '';
          return this.router.isActive(url, false) || this.router.url.includes(url) || this.router.url+"/"==url;
        }

        if (item.items && item.items.length > 0) {
          return item.items.some(subItem => this.isActiveRoute(subItem));
        }

        return false;
      }


    ngOnInit() {
        // console.log(this.isActiveRoute("/accounts"))
        let userType  = this.sessionService.userInfo.type;

        this.model = [
            {
                label: "",
                items: [
                    //dashboard
                    {
                        label: this.tranService.translate("global.menu.dashboard"),
                        icon: "pi pi-fw pi-chart-bar",
                        routerLink: ["/dashboard"],
                        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.DYNAMICCHART.VIEW_LIST])
                    },
                    //sim management
                    {
                        label: this.tranService.translate("global.menu.simmgmt"),
                        icon: "pi pi-fw pi-slack",
                        items: [
                            {
                                label: this.tranService.translate("global.menu.listsim"),
                                routerLink: ["/sims"],
                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.SIM.VIEW_LIST])
                            },
                            {
                                label: this.tranService.translate("global.menu.groupSim"),
                                // icon: "pi pi-fw pi-server",
                                routerLink:["/sims/group"],
                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.GROUP_SIM.VIEW_LIST])
                            },
                            {
                                label: this.tranService.translate("global.menu.contract"),
                                // icon: "pi pi-fw pi-server",
                                routerLink:["/sims/contract"],
                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.CONTRACT.VIEW_LIST])
                            }
                            // ,
                            // {
                            //     label:  this.tranService.translate("recharge.label.menu"),
                            //     routerLink: ["/sims/recharge-money"],
                            // }
                        ],
                        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.SIM.VIEW_LIST, CONSTANTS.PERMISSIONS.GROUP_SIM.VIEW_LIST, CONSTANTS.PERMISSIONS.CONTRACT.VIEW_LIST])
                    },
                    //ticket
                    {
                        label: this.tranService.translate("ticket.menu.requestMgmt"),
                        icon: "pi pi-fw pi-file-excel",
                        items: [
                            {
                                label: this.tranService.translate("ticket.menu.testSim"),
                                routerLink: ["/ticket/list-test-sim"],
                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.VIEW_LIST]),
                                isShowNotify: this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.ADMIN
                                    || this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE
                                    || this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.DISTRICT,
                                key : CONSTANTS.KEY_NOTIFY.TEST_SIM
                            },
                            {
                                label: this.tranService.translate("ticket.menu.replaceSim"),
                                routerLink: ["/ticket/list-replace-sim"],
                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.VIEW_LIST]),
                                isShowNotify: this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.ADMIN
                                    || this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE
                                    || this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.DISTRICT,
                                key : CONSTANTS.KEY_NOTIFY.REPLACE_SIM
                            },{ //yêu cầu đặt mua sim
                                label: this.tranService.translate("ticket.menu.orderSim"),
                                routerLink: ["/ticket/list-order-sim"],
                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.VIEW_LIST]),
                                isShowNotify: this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.ADMIN
                                    || this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE
                                    || this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.DISTRICT,
                                key : CONSTANTS.KEY_NOTIFY.ORDER_SIM,
                                command: () => this.router.navigate(["/ticket/list-order-sim"]),
                                items: [
                                    { //Danh sách sim ticket
                                        label: this.tranService.translate("ticket.menu.listIssuedSim"),
                                        routerLink: ["/ticket/list-order-sim/sims"],
                                        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.VIEW_LIST]),
                                    },
                                ]
                            },
                            { //yêu cầu kích hoạt sim
                                label: this.tranService.translate("ticket.menu.activeSim"),
                                routerLink: ["/ticket/list-active-sim"],
                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.VIEW_LIST]),
                                isShowNotify: this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.ADMIN
                                    || this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE ,
                                key : CONSTANTS.KEY_NOTIFY.ACTIVE_SIM,
                            },
                            { //yêu cầu chẩn đoán
                                label: this.tranService.translate("ticket.menu.diagnose"),
                                routerLink: ["/ticket/list-diagnose"],
                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.VIEW_LIST])
                                    && (this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.ADMIN ||
                                        this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE ||
                                        this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.CUSTOMER
                                    ),
                                isShowNotify: this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.ADMIN
                                    || this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE,
                                key : CONSTANTS.KEY_NOTIFY.DIAGNOSE,
                            },
                            {
                                label: this.tranService.translate("ticket.menu.requestConfig"),
                                routerLink: ["/ticket/list-config/"],
                                visible: this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.ADMIN || this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE,
                            }
                        ],
                        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.VIEW_LIST]),
                        isShowNotify: this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.ADMIN
                            || this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE
                            || this.sessionService.userInfo.type == CONSTANTS.USER_TYPE.DISTRICT,
                        key : CONSTANTS.KEY_NOTIFY.TICKET
                    },
                    //device management
                    {
                        label: this.tranService.translate("global.menu.devicemgmt"),
                        icon: "pi pi-fw pi-calculator",
                        items: [
                            {
                                label: this.tranService.translate("global.menu.listdevice"),
                                routerLink: ["/devices"],
                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.DEVICE.VIEW_LIST]),
                            },
                        ],
                        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.DEVICE.VIEW_LIST]),
                    },
                    //APN Sim
                    {
                        label: this.tranService.translate("global.menu.apnsim"),
                        icon: "pi pi-fw pi-wifi",
                        items: [
                            {
                                label: this.tranService.translate("global.menu.apnsimlist"),
                                routerLink: ["/apnsim"],
                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.APN_SIM.VIEW_LIST])
                            },
                        ],
                        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.APN_SIM.VIEW_LIST])
                    },
                    //Cảnh báo
                    {
                        label: this.tranService.translate("global.menu.rule"),
                        icon: "pi pi-fw pi-bell",
                        items: [
                            {
                                label: this.tranService.translate("global.menu.alertSettings"),
                                routerLink: ["/alerts"],
                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.ALERT.VIEW_LIST])

                            },
                            {
                                label: this.tranService.translate("global.menu.alertReceivingGroup"),
                                routerLink: ["/alerts/receiving-group"],
                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.ALERT_RECEIVING_GROUP.VIEW_LIST])
                            },
                            {
                                label: this.tranService.translate("global.menu.alertHistory"),
                                routerLink: ["/alerts/history"],
                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.ALERT_HISTORY.VIEW_LIST]),
                            }
                        ],
                        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.ALERT.VIEW_LIST, CONSTANTS.PERMISSIONS.ALERT_RECEIVING_GROUP.VIEW_LIST,CONSTANTS.PERMISSIONS.ALERT_HISTORY.VIEW_LIST])
                    },
                    // data pool
                    {
                        label: this.tranService.translate("global.menu.trafficManagement"),
                        icon: "pi pi-fw pi-wallet",
                        items: [
                            {
                                label: this.tranService.translate("global.menu.subTrafficManagement"),
                                routerLink:["/data-pool/walletMgmt/list"],
                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.DATAPOOL.VIEW_WALLET])
                            },
                            {
                                label: this.tranService.translate("global.menu.shareManagement"),
                                routerLink:["/data-pool/shareMgmt/listShare"],
                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.DATAPOOL.VIEW_SHARE])
                            },
                            // {
                            //     label: this.tranService.translate("global.menu.walletConfig"),
                            //     routerLink:["/data-pool/config"]
                            // },
                            {
                                label: this.tranService.translate("global.menu.historyWallet"),
                                routerLink:["/data-pool/history"],
                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.DATAPOOL.VIEW_HISTORY_WALLET])
                            },
                            {
                                label: this.tranService.translate("global.menu.listGroupSub"),
                                routerLink:["/data-pool/group/listGroupSub"],
                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.SHARE_GROUP.VIEW_LIST])
                            },
                            {
                                label: this.tranService.translate("global.menu.autoShareGroup"),
                                routerLink:["/data-pool/auto-share-group/list"],
                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.AUTO_SHARE_GROUP.VIEW_LIST])
                            },

                        ],
                        // visible: true,
                        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.DATAPOOL.VIEW_WALLET, CONSTANTS.PERMISSIONS.DATAPOOL.VIEW_SHARE, CONSTANTS.PERMISSIONS.DATAPOOL.VIEW_HISTORY_WALLET, CONSTANTS.PERMISSIONS.SHARE_GROUP.VIEW_LIST])
                    },
                    //plan management
                    {
                        label: this.tranService.translate("global.menu.ratingplanmgmt"),
                        icon: "pi pi-fw pi-star",
                        items: [
                            {
                                label: this.tranService.translate("global.menu.listplan"),
                                routerLink: ["/plans"],
                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN.VIEW_LIST])
                            },
                            {
                                label: this.tranService.translate("global.menu.registerplan"),
                                routerLink: ["/plans/registers"],
                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN_SIM.REGISTER_PLAN])
                            },
                        ],
                        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN.VIEW_LIST, CONSTANTS.PERMISSIONS.RATING_PLAN_SIM.REGISTER_PLAN])
                    },
                    //account management
                    {
                        label: this.tranService.translate("global.menu.accountmgmt"),
                        icon: "pi pi-fw pi-users",
                        items: [
                            {
                                label: this.tranService.translate("global.menu.listaccount"),
                                routerLink: ["/accounts"],
                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.ACCOUNT.VIEW_LIST])
                            },
                            {
                                label: this.tranService.translate("global.menu.listroles"),
                                routerLink: ["/roles"],
                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.ROLE.VIEW_LIST])
                            },
                            {
                                label: this.tranService.translate("global.menu.listpermissions"),
                                routerLink: ["/permissions"],
                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.PERMISSION.VIEW_LIST])
                            },
                            {
                                label: this.tranService.translate("global.menu.termpolicy"),
                                routerLink: ["/policies"],
                                visible: userType == CONSTANTS.USER_TYPE.CUSTOMER || userType == CONSTANTS.USER_TYPE.AGENCY
                            },
                            {
                                label: this.tranService.translate("global.menu.termpolicyhistory"),
                                routerLink: ["/policies/history"],
                                visible: userType == CONSTANTS.USER_TYPE.CUSTOMER || userType == CONSTANTS.USER_TYPE.AGENCY
                            },
                            {
                                label: this.tranService.translate("logs.menu.log"),
                                routerLink: ["/history-activity/list"],
                                visible: (userType == CONSTANTS.USER_TYPE.ADMIN || userType == CONSTANTS.USER_TYPE.PROVINCE
                                || userType == CONSTANTS.USER_TYPE.DISTRICT) && this.checkAuthen([CONSTANTS.PERMISSIONS.LOG.VIEW_LIST])
                             },
                            {
                                label: this.tranService.translate("global.menu.apiLogs"),
                                routerLink: ["/accounts/logApi"],
                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.ACCOUNT.SEARCH_LOG_API])
                            },
                        ],
                        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.ACCOUNT.VIEW_LIST,CONSTANTS.PERMISSIONS.ROLE.VIEW_LIST,CONSTANTS.PERMISSIONS.PERMISSION.VIEW_LIST])
                    },
                    //customer management
                    {
                        label: this.tranService.translate("global.menu.customermgmt"),
                        icon: "pi pi-fw pi-users",
                        items: [
                            {
                                label: this.tranService.translate("global.menu.listcustomer"),
                                routerLink: ["/customers"],
                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.CUSTOMER.VIEW_LIST])
                            },
                        ],
                        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.CUSTOMER.VIEW_LIST])
                    },
                    //chẩn đoán
                    {
                        label: this.tranService.translate("diagnose.menu.diagnose"),
                        icon: "pi pi-search-plus",
                        routerLink: ['/diagnose'],
                        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.DIAGNOSE.VIEW_LIST])
                    },


                    //order management
                    {
                        label: this.tranService.translate("global.menu.ordermgmt"),
                        icon: "pi pi-fw pi-truck",
                        items: [
                            {
                                label: this.tranService.translate("global.menu.listorder"),
                                routerLink: ["/orders"]
                            },
                        ],
                        visible: false
                    },



                    //extra service
                    {
                        label: this.tranService.translate("global.menu.extraservice"),
                        icon: "pi pi-fw pi-th-large",
                        routerLink: ["/extra-services"],
                        visible: false
                    },
                    //logs
                    {
                        label: this.tranService.translate("global.menu.log"),
                        icon: "pi pi-fw pi-book",
                        routerLink: ["/logs"],
                        visible: false
                    },
                    //report
                    {
                        label: this.tranService.translate("global.menu.report"),
                        icon: "pi pi-fw pi-file-excel",
                        items: [
                            {
                                label: this.tranService.translate("global.menu.dynamicreport"),
                                routerLink: ["/reports/report-dynamic/"],
                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.REPORT_DYNAMIC.VIEW_LIST])
                            },
                            {
                                label: this.tranService.translate("permission.RptContent.RptContent"),
                                routerLink: ["/reports/report-dynamic/report-content"]
                            },
                            {
                                label: this.tranService.translate("global.menu.dynamicreportgroup"),
                                routerLink: ["/reports/group-report-dynamic/"],
                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.GROUP_REPORT_DYNAMIC.VIEW_LIST])
                            },
                        ],
                        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.REPORT_DYNAMIC.VIEW_LIST, CONSTANTS.PERMISSIONS.GROUP_REPORT_DYNAMIC.VIEW_LIST], true)
                    },

                    //configuration
                    {
                        label: this.tranService.translate("global.menu.configuration"),
                        icon: "pi pi-fw pi-cog",
                        routerLink: ["/configuration"],
                        visible: false
                    },
                    //troubleshoot
                    {
                        label: this.tranService.translate("global.menu.troubleshoot"),
                        icon: "pi pi-fw pi-wrench",
                        routerLink: ["/troubleshoot"],
                        visible: false
                    },
                    //config chart
                    {
                        label: this.tranService.translate("global.menu.charts"),
                        icon: "pi pi-fw pi-th-large",
                        routerLink: ["/config-chart"],
                        visible: this.checkAuthen([CONSTANTS.PERMISSIONS.CONFIG_DYNAMIC_CHART.VIEW_LIST])
                    },
                     //guide
                    {
                        label: this.tranService.translate("global.menu.manual"),
                        icon: "pi pi-fw pi-question-circle",
                        items: [
                            {
                                label: this.tranService.translate("global.menu.userGuide"),
                                icon: "pi pi-fw pi-info-circle",
                                routerLink: ["/docs"],
                            },
                            {
                                label: this.tranService.translate("global.menu.integrationGuide"),
                                icon: "pi pi-fw pi-info-circle",
                                routerLink: ["/docs/integration"],
                                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.GUIDE.INTEGRATION])
                            },
                        ],

                        // target: "_blank"
                    },
                ]
            },
        ];
    }
}
