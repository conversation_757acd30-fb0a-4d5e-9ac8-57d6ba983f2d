import {Component, ContentChild, Injector, OnInit, TemplateRef, ViewChild} from "@angular/core";
import {ComponentBase} from "../../../../component.base";
import {MenuItem} from "primeng/api";
import {TrafficWalletService} from "../../../../service/datapool/TrafficWalletService";
import { ColumnInfo, OptionTable } from "src/app/template/common-module/table/table.component";
import {CONSTANTS} from "../../../../service/comon/constants";
import {AutoShareService} from "../../../../service/datapool/AutoShareService";
import {WalletListTabComponent} from "./wallet-list-tab/wallet-list-tab.component";
import {SharePhoneListTabComponent} from "./share-phone-list-tab/share-phone-list-tab.component";
import {an} from "@fullcalendar/core/internal-common";

export enum AutoShareWalletType {
    WALLET_LIST_TAB,
    SHARE_PHONE_LIST_TAB
}

@Component({
    selector: "auto-share-wallet-detail",
    templateUrl: './auto-share-wallet.detail.component.html',
    styleUrls : ['./auto-share-wallet.component.scss']
})
export class AutoShareWalletDetailComponent extends ComponentBase implements OnInit {
    optionTable: OptionTable;
    pageNumber: number;
    pageSize: number;
    sort: string;
    constructor(
        public trafficWalletService: TrafficWalletService,
        public autoShareService: AutoShareService,
        injector: Injector
    ) {
        super(injector);
    }
    searchInfoStandard:any;
    columns: Array<ColumnInfo>;
    dataSet: {
        content: Array<any>,
        total: number
    };
    items: Array<MenuItem>;
    home: MenuItem;
    groupAutoShareInfo : any
    walletDetail: any;
    subCodeId:string;
    listDetail:any;
    PERMISSIONS = CONSTANTS.PERMISSIONS;
    readonly AutoShareWalletType = AutoShareWalletType;
    currentServiceTab: AutoShareWalletType = AutoShareWalletType.WALLET_LIST_TAB;
    isSwitchingTab: boolean;
    @ContentChild(TemplateRef)
    walletListTemplateRef: TemplateRef<any>;
    @ContentChild(TemplateRef)
    sharePhoneListTemplateRef: TemplateRef<any>;
    trafficType : any

    @ViewChild(WalletListTabComponent) walletListTabComponent: WalletListTabComponent;
    @ViewChild(SharePhoneListTabComponent) sharePhoneListTabComponent: SharePhoneListTabComponent;

    getDetail(id) {
        let me = this;
        me.messageCommonService.onload();
        this.autoShareService.getDetail({id: id}, (response) => {
            this.groupAutoShareInfo = response;
            this.trafficType = response.trafficType
        }, null, () => {
            me.messageCommonService.offload();
        })
    }

    goToWalletListTab(): void {
        this.isSwitchingTab = true;
        this.currentServiceTab = AutoShareWalletType.WALLET_LIST_TAB;
    }

    goToSharePhoneListTab(): void {
        this.isSwitchingTab = true;
        this.currentServiceTab = AutoShareWalletType.SHARE_PHONE_LIST_TAB;
    }

    getWalletListActivationTemplateRef(templateRef: TemplateRef<any>): void {
        setTimeout((): void => {
            this.walletListTemplateRef = templateRef;
        });
    }

    getSharePhoneListTemplateRef(templateRef: TemplateRef<any>): void {
        setTimeout((): void => {
            this.sharePhoneListTemplateRef = templateRef;
        });
    }

    ngOnInit(): void {
        let id = this.route.snapshot.paramMap.get('id');
        this.getDetail(id)
        this.items = [
            {label: this.tranService.translate("global.menu.trafficManagement")},
            {label: this.tranService.translate("datapool.label.autoShareWallet"), routerLink: "../../list"},
            {label: this.tranService.translate("global.button.view")}
        ];
        this.home = {icon: 'pi pi-home', routerLink: '/'};
    }
}
