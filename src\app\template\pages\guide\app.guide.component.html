
<style>
    .p-menuitem-link{
        display: flex;
    }
</style>
<div class="layout-topbar header-cmp">
    <a class="layout-topbar-logo" routerLink="">
        <img src="assets/images/m2m.png" alt="logo">
        <span style="font-size: xx-large; color: white; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">{{tranService.translate('global.titlepage.m2SubscriptionManagementSystem')}}</span>
    </a>

    <div #topbarmenu class="layout-topbar-menu" [ngClass]="{'layout-topbar-menu-mobile-active': layoutService.state.profileSidebarVisible}">
        <choose-language></choose-language>
        <div class="ml-2" *ngIf="userInfo">
            <div (click)="session.toggle($event)" class="cursor-poiter"><i class="pi pi-fw pi-user"></i>&nbsp;<span style="display: inline-block; max-width: 150px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;"
                 [pTooltip]="userInfo.fullName.length > 20 ? userInfo.fullName : ''">{{userInfo.fullName}}</span></div>
            <p-overlayPanel #session>
                <a class="text-black-alpha-90" [routerLink]="['/profile']" routerLinkActive="router-link-active" *ngIf="checkAuthen([CONSTANTS.PERMISSIONS.PROFILE.VIEW])">
                    <div class="p-2 hover:surface-300">
                        <i class="pi pi-user-edit"></i>&nbsp;
                        {{tranService.translate("login.label.editProfile")}}
                    </div>
                </a>
                <a class="text-black-alpha-90" [routerLink]="['profile/change-password']" routerLinkActive="router-link-active" >
                    <div class="p-2 hover:surface-300">
                        <i class="pi pi-lock"></i>&nbsp;
                        {{tranService.translate("login.label.changePassword")}}
                    </div>
                </a>
                <a class="text-black-alpha-90 cursor-pointer" (click)="logout()" routerLinkActive="router-link-active" >
                    <div class="p-2 hover:surface-300">
                        <i class="pi pi-sign-out"></i>&nbsp;
                        {{tranService.translate("login.label.logout")}}
                    </div>
                </a>
            </p-overlayPanel>
        </div>
    </div>
</div>
<div class="flex flex-row justify-content-between align-items-start" style="margin-top: 56px; min-height: 100vh">
    <div style="width: 350px;min-width: 350px; min-height: 100vh;">
        <div class="flex align-items-center">
            <a routerLink="">
                <button pButton pRipple type="button" label="Back" class="p-button-outlined p-button-secondary"></button>
            </a>
            <span style="font-size: 14px;
        text-align: center;
        font-weight: bold;
        text-transform: uppercase;
        padding: 12px 8px;">{{projectTitle}}</span>
        </div>

        <p-divider styleClass="m-0 bold " [align]="'center'" type="solid"></p-divider>
        <p-panelMenu [model]="items" [style]="{'width':'100%', 'margin-top': '20px'}" [multiple]="false" [tabindex]="tabindex"></p-panelMenu>
        <!-- <p-tieredMenu [model]="items"></p-tieredMenu> -->
    </div>
    <div class="flex-grow-1" style="min-height: 100vh;">
        <div class="w-full p-2" style="box-sizing: border-box; min-height: calc(100vh - 56px);">
            <div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
                <div class="">
                    <div class="text-xl font-bold mb-1">{{pageTitle}}</div>
                    <p-breadcrumb class="max-w-full col-7" [model]="pathItems" [home]="home"></p-breadcrumb>
                </div>
                <div class="col-5 flex flex-row justify-content-end align-items-center">

                </div>
            </div>
            <div id="page-content" class="bg-white p-2 border-round mt-4 se-wrapper se-wrapper-inner se-wrapper-wysiwyg sun-editor-editable" [class]="pageContent == null ? 'hidden' : ''">

            </div>
            <div class="bg-white p-2 border-round mt-4" *ngIf="(listPageChildren || []).length > 0">
                <ul>
                    <li class="p-2" *ngFor="let page of listPageChildren">
                        <span style="cursor: pointer;" (click)="handleClickMenu({item: page}, 1)">{{page.label}}</span>
                    </li>
                </ul>
            </div>
            <div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round mt-4" *ngIf="pagePrevious != null || pageNext != null">
                <div>
                    <div *ngIf="pagePrevious" >
                        <div style="width: 280px" class="flex flex-row justify-content-start align-items-center mb-3"><i class="pi pi-arrow-left mr-4"></i><div className="ml-12px"></div></div>
                        <p-button styleClass="p-button-info" (click)="handleClickMenu({item: pagePrevious}, 1)">
                            {{ pagePrevious.label }}
                        </p-button>
                    </div>
                </div>
                <div>
                    <div *ngIf="pageNext" style="text-align: right;">
                        <div style="width: 280px" class="flex flex-row justify-content-end align-items-center mb-3"><div></div><i class="pi pi-arrow-right ml-4"></i></div>
                        <p-button styleClass="p-button-info" (click)="handleClickMenu({item: pageNext}, 1)">
                            {{ pageNext.label }}
                        </p-button>
                    </div>
                </div>
            </div>
        </div>
<!--        <div class="layout-footer">-->
<!--            <img src="assets/images/m2m.png" alt="Logo" height="40px" class="mr-2"/>-->
<!--        &lt;!&ndash;    <span class="font-medium ml-2">VNPT Technology</span>&ndash;&gt;-->
<!--        </div>-->
    </div>
</div>
