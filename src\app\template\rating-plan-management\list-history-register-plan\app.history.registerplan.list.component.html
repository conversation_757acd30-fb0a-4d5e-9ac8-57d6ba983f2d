<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{tranService.translate("global.menu.historyRegister")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
<!--    <div class="col-5 flex flex-row justify-content-end align-items-center">-->

<!--    </div>-->
</div>

<form [formGroup]="formSearch" (ngSubmit)="onSubmitSearch()" class="pb-2 pt-3 vnpt-field-set">
    <p-panel [toggleable]="true" [header]="tranService.translate('global.text.filter')">
        <div class="grid search-grid-3">
            <!-- So thue bao -->
            <div class="col-3">
                <span class="p-float-label">
                    <input pInputText
                           class="w-full"
                           pInputText id="isdn"
                           [(ngModel)]="searchInfo.isdn"
                           formControlName="isdn"
                    />
                    <label htmlFor="isdn">{{tranService.translate("historyRegisterPlan.label.isdn")}}</label>
                </span>
            </div>
            <!--            &lt;!&ndash; khach hang &ndash;&gt;-->
            <!--            <div class="col-3">-->
            <!--                <span class="p-float-label">-->
            <!--                    <input pInputText-->
            <!--                           class="w-full"-->
            <!--                           pInputText id="customerName"-->
            <!--                           [(ngModel)]="searchInfo.customerName"-->
            <!--                           formControlName="customerName"-->
            <!--                    />-->
            <!--                    <label htmlFor="customerName">{{tranService.translate("historyRegisterPlan.label.customerName")}}</label>-->
            <!--                </span>-->
            <!--            </div>-->
            <!-- khach hang -->
            <div class="col-3">
                <!-- <span class="p-float-label">
                    <p-dropdown styleClass="w-full"
                                [showClear]="true"
                                id="customer" [autoDisplayFirst]="false"
                                [(ngModel)]="searchInfo.customerCode"
                                formControlName="customerCode"
                                [options]="listCustomer"
                                optionLabel="display"
                                [filter]="true" filterBy="display"
                                optionValue="name"
                    ></p-dropdown>
                    <label htmlFor="customer">{{tranService.translate("sim.label.khachhang")}}</label>
                </span> -->
                <div class="relative">
                    <vnpt-select
                        class="w-full"
                        [(value)]="searchInfo.customerCode"
                        [placeholder]="tranService.translate('sim.label.khachhang')"
                        objectKey="customer"
                        paramKey="customerName"
                        keyReturn="customerCode"
                        displayPattern="${customerName} - ${customerCode}"
                        typeValue="primitive"
                        [floatLabel]="true"
                        [isMultiChoice]="false"
                    ></vnpt-select>
                </div>
            </div>
            <!-- tu ngay -->
            <div class="col-3 pb-0">
                <span class="p-float-label">
                    <p-calendar styleClass="w-full"
                                id="fromDate"
                                [(ngModel)]="searchInfo.fromDate"
                                formControlName="fromDate"
                                [showIcon]="true"
                                [showClear]="true"
                                dateFormat="dd/mm/yy"
                                [maxDate]="maxDateFrom"
                                (onSelect)="onChangeDateFrom(searchInfo.fromDate)"
                                (onInput)="onChangeDateFrom(searchInfo.fromDate)"
                    ></p-calendar>
                    <label class="label-calendar" htmlFor="fromDate">{{tranService.translate("historyRegisterPlan.label.fromDate")}}</label>
                </span>
            </div>
            <!-- den ngay -->
            <div class="col-3 pb-0">
                <span class="p-float-label">
                    <p-calendar styleClass="w-full"
                                id="toDate"
                                [(ngModel)]="searchInfo.toDate"
                                formControlName="toDate"
                                [showIcon]="true"
                                [showClear]="true"
                                dateFormat="dd/mm/yy"
                                [minDate]="minDateTo"
                                [maxDate]="maxDateTo"
                                (onSelect)="onChangeDateTo(searchInfo.toDate)"
                                (onInput)="onChangeDateTo(searchInfo.toDate)"
                    />
                    <label class="label-calendar" htmlFor="toDate">{{tranService.translate("historyRegisterPlan.label.toDate")}}</label>
                </span>
            </div>
            <div class="col-3 pb-0">
                <p-button icon="pi pi-search"
                          styleClass="p-button-rounded p-button-secondary p-button-text button-search"
                          type="submit"
                ></p-button>
            </div>
        </div>
    </p-panel>
</form>

<table-vnpt
    [fieldId]="'imsi'"
    [(selectItems)]="selectItems"
    [columns]="columns"
    [dataSet]="dataSet"
    [options]="optionTable"
    [loadData]="search.bind(this)"
    [pageNumber]="pageNumber"
    [pageSize]="pageSize"
    [sort]="sort"
    [params]="searchInfo"
    [labelTable]="this.tranService.translate('global.menu.historyRegister')"
></table-vnpt>
