import {AfterContentChecked, Component, Inject, Injector, OnInit} from '@angular/core';
import {AccountService} from "../../../../service/account/AccountService";
import {FormBuilder} from "@angular/forms";
import {MenuItem} from "primeng/api";
import {CONSTANTS} from "../../../../service/comon/constants";
import {CustomerService} from "../../../../service/customer/CustomerService";
import {AlertService} from "../../../../service/alert/AlertService";
import {SimService} from "../../../../service/sim/SimService";
import {GroupSimService} from "../../../../service/group-sim/GroupSimService";
import {ComponentBase} from "../../../../component.base";
import {ComboLazyControl} from 'src/app/template/common-module/combobox-lazyload/combobox.lazyload';
import {TrafficWalletService} from 'src/app/service/datapool/TrafficWalletService';
import {RatingPlanService} from "../../../../service/rating-plan/RatingPlanService";

@Component({
    selector: 'app-app.alert.edit',
    templateUrl: './app.alert.edit.component.html',
})
export class AppAlertEditComponent extends ComponentBase implements OnInit, AfterContentChecked {
    constructor(
        @Inject(AccountService) private accountService: AccountService,
        @Inject(CustomerService) private customerService: CustomerService,
        @Inject(AlertService) private alertService: AlertService,
        @Inject(SimService) private simService: SimService,
        @Inject(GroupSimService) private groupSimService: GroupSimService,
        @Inject(RatingPlanService) private ratingPlanService: RatingPlanService,
        @Inject(TrafficWalletService) private trafficWalletService: TrafficWalletService,
        private formBuilder: FormBuilder,
        private injector: Injector
    ) {
        super(injector);
    }
    userType: number;
    items: MenuItem[];
    home: MenuItem;
    formAlert: any;
    alertInfo: {
        name: string | null,
        customerId: any,
        contractCode: any,
        statusSim: number | null,
        subscriptionNumber: string | null,
        groupId: string | null,
        interval: number | null,
        count: number | null,
        unit: number | null,
        value: number | null,
        description: string | null,
        severity: string | null,
        listAlertReceivingGroupId: Array<any> | null,
        url: string | null,
        emailList: string | null,
        emailSubject: string | null,
        emailContent: string | null,
        smsList: string | null
        smsContent: string | null,
        ruleCategory: number | null,
        eventType: number | null,
        appliedPlan: Array<any>,
        actionType: number | null,
        walletName: string | null,
        notifyInterval: number | null,
        notifyRepeat: number | null;
        typeAlert: Array<string>;
        sendTypeEmail: boolean;
        sendTypeSMS: boolean;
        walletSubCode: string| null
        createdBy: number| null
    };
    paramUpdateSubCode: {
        code: string|null,
    }
    wallet:any;
    statusSimOptions: Array<any>;
    optionStatusSim: any;
    unitOptions: Array<any>;
    unitWalletOptions: Array<any>;
    severityOptions: Array<any>;
    customerNameOptions: Array<{ name: any, value: any, id: any }>;
    groupOptions: Array<any>;
    listGroupByCustomer: Array<any>;
    listSimByCustomer: Array<any>;
    subscriptionNumberOptions: Array<any>;
    groupReceivingOptions: Array<any>;
    isShowDialogActive: boolean;
    statusAlert: any;
    isAlertNameExisted: boolean = false;
    alertId = this.route.snapshot.paramMap.get("id");
    comboSelectCustomerControl: ComboLazyControl = new ComboLazyControl();
    comboSelectContracCodeControl: ComboLazyControl = new ComboLazyControl();
    comboSelectSubControl: ComboLazyControl = new ComboLazyControl();
    comboSelectGroupSubControl: ComboLazyControl = new ComboLazyControl();
    comboSelectRatePlanControl: ComboLazyControl = new ComboLazyControl();
    paramSearchGroupSim = {};
    paramSearchContract = {};
    paramSearchSim = {};

    appliedPlanOptions: Array<any>;
    actionOptions: Array<any>;
    eventOptions: Array<any>;
    ruleOptions: Array<any>;
    repeat: boolean = false;
    isPlanExisted: boolean = false;
    eventOptionManagement: Array<any>;
    eventOptionMonitoring: Array<any>;
    alertResponse: any = {}
    isDisableReceiveGroup : boolean = false;
    listAllField : Array<any>
    listEnableForGroup : Array<any>
    listEnableForEmail : Array<any>
    listEnableForSMS : Array<any>
    listEnable : Array<any>
    controlAlertReceiving : ComboLazyControl = new ComboLazyControl();
    controlComboSelectEventType : ComboLazyControl = new ComboLazyControl();
    userInfo: any;

    readonly CONSTANTS = CONSTANTS;
    controlComboSelectWallet: ComboLazyControl = new ComboLazyControl();
    disableUnit: boolean;
    ngOnInit(): void {
        let me = this;
        this.userType = this.sessionService.userInfo.type;
        this.items = [{label: this.tranService.translate("global.menu.alertSettings")}, {
            label: this.tranService.translate("global.menu.alertList"),
            routerLink: "/alerts"
        }, {label: this.tranService.translate("global.button.edit")}];
        this.home = {icon: 'pi pi-home', routerLink: '/'};
        this.optionStatusSim = CONSTANTS.ALERT_STATUS_SIM;
        this.statusAlert = CONSTANTS.ALERT_STATUS;
        me.listAllField = ['receiveGroup', 'emailSubject','emailContent','smsContent','smsList','emailList']
        me.listEnableForGroup = ['receiveGroup', 'emailSubject','emailContent','smsContent']
        me.listEnableForEmail = ['emailSubject','emailContent','emailList']
        me.listEnableForSMS = ['smsList','smsContent']
        me.listEnable = []
        this.disableUnit = false;
        this.alertInfo = {
            name: null,
            customerId: null,
            contractCode: null,
            statusSim: null,
            subscriptionNumber: null,
            groupId: null,
            interval: null,
            count: null,
            unit: null,
            value: null,
            description: null,
            severity: null,
            listAlertReceivingGroupId: [],
            url: null,
            emailList: null,
            emailSubject: null,
            emailContent: null,
            smsList: null,
            smsContent: null,
            ruleCategory: 1,
            eventType: null,
            appliedPlan: [],
            actionType: 0,
            walletName: null,
            notifyInterval: null,
            notifyRepeat: null,
            typeAlert: [],
            sendTypeEmail: true,
            sendTypeSMS: null,
            walletSubCode: null,
            createdBy: null,
        }
        this.paramUpdateSubCode = {
            code: me.alertInfo.walletSubCode,
        }
        this.formAlert = this.formBuilder.group(this.alertInfo);
        this.userInfo = this.sessionService.userInfo;
        this.ruleOptions = [];
        this.eventOptions = [];
        this.loadEventOptions();
        // this.eventOptions = [
        //     {
        //         name: me.tranService.translate("alert.eventType.exceededPakage"),
        //         value: CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_PACKAGE
        //     },
        //     {
        //         name: me.tranService.translate("alert.eventType.exceededValue"),
        //         value: CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_VALUE
        //     },
        //     {
        //         name: me.tranService.translate("alert.eventType.sessionEnd"),
        //         value: CONSTANTS.ALERT_EVENT_TYPE.SESSION_END
        //     },
        //     {
        //         name: me.tranService.translate("alert.eventType.sessionStart"),
        //         value: CONSTANTS.ALERT_EVENT_TYPE.SESSION_START
        //     },
        //     {
        //         name: me.tranService.translate("alert.eventType.smsExceededPakage"),
        //         value: CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_PACKAGE
        //     },
        //     {
        //         name: me.tranService.translate("alert.eventType.smsExceededValue"),
        //         value: CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_VALUE
        //     },
        //     {name: me.tranService.translate("alert.eventType.owLock"), value: CONSTANTS.ALERT_EVENT_TYPE.ONE_WAY_LOCK},
        //     {name: me.tranService.translate("alert.eventType.twLock"), value: CONSTANTS.ALERT_EVENT_TYPE.TWO_WAY_LOCK},
        //     {
        //         name: me.tranService.translate("alert.eventType.noConection"),
        //         value: CONSTANTS.ALERT_EVENT_TYPE.NO_CONECTION
        //     },
        //     {name: me.tranService.translate("alert.eventType.simExp"), value: CONSTANTS.ALERT_EVENT_TYPE.SIM_EXP},
        //     {
        //         name: me.tranService.translate("alert.eventType.dataWalletExp"),
        //         value: CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP
        //     },
        //     {
        //         name: me.tranService.translate("alert.eventType.owtwlock"),
        //         value: CONSTANTS.ALERT_EVENT_TYPE.ONE_WAY_TWO_WAY_LOCK
        //     },
        // ]

        if (this.checkAuthen([CONSTANTS.PERMISSIONS.ALERT.UPDATE])) {
            this.ruleOptions.push({name:this.tranService.translate("alert.ruleCategory.monitoring"), value: CONSTANTS.ALERT_RULE_CATEGORY.MONITORING})
            this.ruleOptions.push({name:this.tranService.translate("alert.ruleCategory.management"), value: CONSTANTS.ALERT_RULE_CATEGORY.MANAGEMENT})
        } else if (CONSTANTS.PERMISSIONS.ALERT.UPDATE_WALLET_THRESHOLD || CONSTANTS.PERMISSIONS.ALERT.UPDATE_WALLET_EXPIRY) {
            this.ruleOptions.push({name:this.tranService.translate("alert.ruleCategory.management"), value: CONSTANTS.ALERT_RULE_CATEGORY.MANAGEMENT})
        }
        console.log(this.ruleOptions)
        // this.eventOptionManagement = this.eventOptions.filter(item =>
        //     item.value == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_PACKAGE ||
        //     item.value == CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_PACKAGE ||
        //     item.value == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_VALUE ||
        //     item.value == CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_VALUE ||
        //     item.value == CONSTANTS.ALERT_EVENT_TYPE.SIM_EXP ||
        //     item.value == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP)
        //
        // this.eventOptionMonitoring = this.eventOptions.filter(item =>
        //     item.value == CONSTANTS.ALERT_EVENT_TYPE.ONE_WAY_LOCK ||
        //     item.value == CONSTANTS.ALERT_EVENT_TYPE.TWO_WAY_LOCK ||
        //     item.value == CONSTANTS.ALERT_EVENT_TYPE.ONE_WAY_TWO_WAY_LOCK ||
        //     item.value == CONSTANTS.ALERT_EVENT_TYPE.NO_CONECTION ||
        //     item.value == CONSTANTS.ALERT_EVENT_TYPE.SESSION_START ||
        //     item.value == CONSTANTS.ALERT_EVENT_TYPE.SESSION_END);
        this.eventOptionManagement = this.eventOptions.filter(item =>
            item.value == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_PACKAGE ||
            item.value == CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_PACKAGE||
            item.value == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_VALUE ||
            item.value == CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_VALUE ||
            item.value == CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD ||
            item.value == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP )

        this.eventOptionMonitoring = this.eventOptions.filter(item =>
            item.value == CONSTANTS.ALERT_EVENT_TYPE.ONE_WAY_LOCK ||
            item.value == CONSTANTS.ALERT_EVENT_TYPE.TWO_WAY_LOCK ||
            item.value == CONSTANTS.ALERT_EVENT_TYPE.ONE_WAY_TWO_WAY_LOCK);

        this.statusSimOptions = [
            {name: this.tranService.translate("alert.statusSim.outPlan"), value: 1},
            {name: this.tranService.translate("alert.statusSim.outLine"), value: 2},
            {name: me.tranService.translate("alert.eventType.subExp"), value: 12},
            {name: me.tranService.translate("alert.eventType.dataWalletExp"), value: 13},
        ]
        this.unitOptions = [
            {name: "KB", value: 1},
            {name: "Mb", value: 2},
            {name: "Gb", value: 3}
        ]
        this.unitWalletOptions = [
            {label: "%", value: 1},
            {label: "MB", value: 2},
            {label: "SMS", value: 3},
        ]
        this.severityOptions = [
            {name: this.tranService.translate("alert.severity.critical"), value: CONSTANTS.ALERT_SEVERITY.CRITICAL},
            {name: this.tranService.translate("alert.severity.major"), value: CONSTANTS.ALERT_SEVERITY.MAJOR},
            {name: this.tranService.translate("alert.severity.minor"), value: CONSTANTS.ALERT_SEVERITY.MINOR},
            {name: this.tranService.translate("alert.severity.info"), value: CONSTANTS.ALERT_SEVERITY.INFO}
        ]
        this.customerNameOptions = []

        this.groupOptions = []

        this.subscriptionNumberOptions = []

        this.groupReceivingOptions = []

        // this.ruleOptions = [
        //     {
        //         name: this.tranService.translate("alert.ruleCategory.monitoring"),
        //         value: CONSTANTS.ALERT_RULE_CATEGORY.MONITORING
        //     },
        //     {
        //         name: this.tranService.translate("alert.ruleCategory.management"),
        //         value: CONSTANTS.ALERT_RULE_CATEGORY.MANAGEMENT
        //     }
        // ]
        this.actionOptions = [
            {name: this.tranService.translate("alert.actionType.alert"), value: CONSTANTS.ALERT_ACTION_TYPE.ALERT}
            // ,
            // {name: this.tranService.translate("alert.actionType.api"), value: CONSTANTS.ALERT_ACTION_TYPE.API}
        ]

        this.getDetail()

        this.formAlert.get("sendTypeEmail").disable({emitEvent: false});
        this.formAlert.get("sendTypeSMS").disable({emitEvent: false});
        this.disableAll()
    }

    disableAll() {
        this.formAlert.get("emailList").disable({emitEvent : false})
        this.formAlert.get("smsList").disable({emitEvent : false})
        this.formAlert.get("emailSubject").disable({emitEvent : false})
        this.formAlert.get("emailContent").disable({emitEvent : false})
        this.formAlert.get("smsContent").disable({emitEvent : false})
        this.isDisableReceiveGroup = true;
    }

    onChangeNotify() {
        if (this.repeat == true) {
            this.alertInfo.notifyRepeat = 1;
            this.formAlert.get("notifyInterval").enable({emitEvent: false})
        } else if (this.repeat == false) {
            this.alertInfo.notifyRepeat = 0
            this.formAlert.get("notifyInterval").disable({emitEvent: false})
        }
    }

    getDetail() {
        let me = this;
        let alertId = this.route.snapshot.paramMap.get("id");
        me.messageCommonService.onload()
        this.alertService.getById(parseInt(alertId), (response) => {
            me.alertResponse = {...response};
            me.alertInfo = response;
            me.alertInfo.name = response.name;
            me.alertInfo.customerId = {id: response.customerId};
            me.alertInfo.contractCode = {contractCode: response.contractCode};
            // me.alertInfo.customerCode = response.customerCode;
            me.alertInfo.subscriptionNumber = response.subscriptionNumber;
            me.alertInfo.description = response.description;
            me.alertInfo.groupId = response.groupId;
            me.alertInfo.listAlertReceivingGroupId = response.listAlertReceivingGroup;
            me.alertInfo.emailList = response.emailList;
            me.alertInfo.emailSubject = response.emailSubject;
            me.alertInfo.emailContent = response.emailContent;
            me.alertInfo.smsList = response.smsList;
            me.alertInfo.smsContent = response.smsContent;
            me.alertInfo.url = response.url;
            me.alertInfo.interval = response.interval;
            me.alertInfo.count = response.count;
            me.alertInfo.unit = response.unit;
            me.alertInfo.value = response.eventType == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP ? response.value / 24 : response.value,
            me.alertInfo.severity = response.severity;
            me.alertInfo.actionType = response.actionType;
            me.alertInfo.ruleCategory = response.ruleCategory;
            me.alertInfo.eventType = response.eventType;
            me.alertInfo.appliedPlan = response.dataPackCode;
            me.alertInfo.notifyInterval = response.notifyInterval / 24;
            me.alertInfo.walletSubCode = response.walletSubCode;
            me.alertInfo.createdBy = response.createdBy;
            if (response.notifyRepeat == 1) {
                this.repeat = true
            } else if (response.notifyRepeat == 0) {
                this.repeat = false
            }
            if(me.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.SIM_EXP || me.alertInfo.ruleCategory == CONSTANTS.ALERT_RULE_CATEGORY.MONITORING) {
                this.formAlert.get("value").disable({emitEvent : false})
            }
            this.paramUpdateSubCode = {
                code: me.alertInfo.walletSubCode,
            }
            //Với Cảnh báo ví, chi người tạo sửa
            if (me.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP) {
                if (!this.checkAuthen([CONSTANTS.PERMISSIONS.ALERT.UPDATE_WALLET_EXPIRY]) || me.alertInfo.createdBy == null || me.alertInfo.createdBy != me.userInfo.id) {
                    window.location.hash = "/access";
                }
            } else if (me.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD) {
                if (!this.checkAuthen([CONSTANTS.PERMISSIONS.ALERT.UPDATE_WALLET_THRESHOLD]) || me.alertInfo.createdBy == null || me.alertInfo.createdBy != me.userInfo.id) {
                    window.location.hash = "/access";
                }
            } else {
                if (!this.checkAuthen([CONSTANTS.PERMISSIONS.ALERT.UPDATE])) {
                    window.location.hash = "/access";
                }
            }

            me.alertInfo.notifyRepeat = response.notifyRepeat
            me.onChangeNotify()
            me.getListRatingPlan()
            me.restoreTypeAlert(response);
            if(me.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP) {
                this.formAlert.get("actionType").disable({emitEvent : false})
            }
        }, null, () => {
            me.messageCommonService.offload();
        })
    }

    ngAfterContentChecked() {

    }


    onSubmitCreate() {
        let me = this;
        if (this.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP && this.alertInfo.value == null)  {
            this.alertInfo.value = 1;
        }
        let dataBody = {
            id: this.alertId,
            name: this.alertInfo.name,
            customerId: this.alertInfo.customerId?.id,
            contractCode: this.alertInfo.customerId?.contractCode,
            eventType: this.alertInfo.eventType,
            subscriptionNumber: this.alertInfo.subscriptionNumber,
            groupId: this.alertInfo.groupId,
            interval: this.alertInfo.interval,
            count: this.alertInfo.count,
            unit: this.alertInfo.unit,
            value: this.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP ? this.alertInfo.value * 24 : this.alertInfo.value,
            description: this.alertInfo.description,
            severity: this.alertInfo.severity,
            listAlertReceivingGroupId: this.alertInfo.listAlertReceivingGroupId,
            url: this.alertInfo.url,
            emailList: this.alertInfo.emailList,
            emailSubject: this.alertInfo.emailSubject,
            emailContent: this.alertInfo.emailContent,
            smsList: this.alertInfo.smsList,
            smsContent: this.alertInfo.smsContent,
            ruleCategory: this.alertInfo.ruleCategory,
            actionType: this.alertInfo.actionType,
            notifyInterval: this.alertInfo.notifyInterval * 24,
            notifyRepeat: this.alertInfo.notifyRepeat,
            dataPackCode: this.alertInfo.appliedPlan,
            statusSim : me.alertResponse.status,
            walletSubCode: this.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD ? this.alertInfo.walletSubCode : null,
        }
        for(let el of this.listAllField) {
            if(!this.listEnable.includes(el)) {
                if(el != 'receiveGroup') {
                    dataBody[el] = null
                }else {
                    dataBody.listAlertReceivingGroupId = null
                }
            }
        }
        if(me.alertInfo.eventType ==  CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP) {
            dataBody.customerId = null
            dataBody.groupId = null
            dataBody.subscriptionNumber = null
            dataBody.listAlertReceivingGroupId = null
            dataBody.emailList = null
            dataBody.smsList = null
            dataBody.smsContent = null
            dataBody.emailContent = null
        }else {
            dataBody.dataPackCode = null;
        }
        if(me.alertInfo.actionType == CONSTANTS.ALERT_ACTION_TYPE.API) {
            dataBody.listAlertReceivingGroupId = null
            dataBody.emailList = null
            dataBody.smsList = null
            dataBody.smsContent = null
            dataBody.emailContent = null
        }
        if(me.alertInfo.actionType == CONSTANTS.ALERT_ACTION_TYPE.ALERT && me.alertInfo.eventType !== CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP) {
            dataBody.url = null
            dataBody.notifyInterval = null
            dataBody.notifyRepeat = null
        }
        if (me.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD) {
            dataBody.emailList = this.alertInfo.emailList,
            dataBody.smsList = this.alertInfo.smsList
        }
        this.messageCommonService.onload();
        if (me.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP) {
            this.alertService.updateAlertWalletExpiry(this.alertId, dataBody, (response) => {
                me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"));
                me.router.navigate(['/alerts']);
            }, null, () => {
                me.messageCommonService.offload();
            })
        } else if (me.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD) {
            this.alertService.updateAlertWalletThreshold(this.alertId, dataBody, (response) => {
                me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"));
                me.router.navigate(['/alerts']);
            }, null, () => {
                me.messageCommonService.offload();
            })
        } else {
            this.alertService.updateAlert(this.alertId, dataBody, (response) => {
                me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"));
                me.router.navigate(['/alerts']);
            }, null, () => {
                me.messageCommonService.offload();
            })
        }
    }

    onChangeEventOption(value){
        this.alertInfo.value = 1
        if(value == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP){
            this.getListRatingPlan()
            // this.formAlert.get("unit").disable({emitEvent : false})
            this.formAlert.get("value").enable({emitEvent : false})
            this.formAlert.get("customerId").disable({emitEvent : false})
            this.formAlert.get("groupId").disable({emitEvent : false})
            this.formAlert.get("subscriptionNumber").disable({emitEvent : false})
            this.formAlert.get("statusSim").disable({emitEvent : false})
            this.formAlert.get("notifyRepeat").disable({emitEvent : false})
            this.formAlert.get("emailSubject").disable({emitEvent : false})
            this.formAlert.get("emailContent").disable({emitEvent : false})
            this.formAlert.get("smsContent").disable({emitEvent : false})
            this.alertInfo.actionType = CONSTANTS.ALERT_ACTION_TYPE.ALERT;
            this.formAlert.get("actionType").disable({emitEvent : false})
            this.formAlert.get("appliedPlan").enable({emitEvent : false})
        } else{
            this.formAlert.get("customerId").enable({emitEvent : false})
            this.formAlert.get("contractCode").enable({emitEvent : false})
            this.formAlert.get("groupId").enable({emitEvent : false})
            this.formAlert.get("subscriptionNumber").enable({emitEvent : false})
            this.formAlert.get("statusSim").disable({emitEvent : false})
            this.formAlert.get("actionType").enable({emitEvent : false})
            this.formAlert.get("value").enable({emitEvent : false})
            this.formAlert.get("appliedPlan").disable({emitEvent : false})
        }
        if(value == CONSTANTS.ALERT_EVENT_TYPE.SIM_EXP) {
            this.formAlert.get("value").disable({emitEvent : false})
        }
        if(this.alertInfo.ruleCategory ==  CONSTANTS.ALERT_RULE_CATEGORY.MONITORING) {
            this.formAlert.get("value").disable({emitEvent : false})
        }
        if (value == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_PACKAGE) {
            this.alertInfo.emailContent = this.tranService.translate("alert.message.exceededPakage")
            this.alertInfo.smsContent = this.tranService.translate("alert.message.exceededPakage")
        } else if (value == CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_PACKAGE) {
            this.alertInfo.emailContent = this.tranService.translate("alert.message.smsExceededPakage")
            this.alertInfo.smsContent = this.tranService.translate("alert.message.smsExceededPakage")
        } else if (value == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_VALUE) {
            this.alertInfo.emailContent = this.tranService.translate("alert.message.exceededValue")
            this.alertInfo.smsContent = this.tranService.translate("alert.message.exceededValue")
        } else if (value == CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_VALUE) {
            this.alertInfo.emailContent = this.tranService.translate("alert.message.smsExceededValue")
            this.alertInfo.smsContent = this.tranService.translate("alert.message.smsExceededValue")
        } else if (value == CONSTANTS.ALERT_EVENT_TYPE.ONE_WAY_LOCK || value == CONSTANTS.ALERT_EVENT_TYPE.TWO_WAY_LOCK || value == CONSTANTS.ALERT_EVENT_TYPE.ONE_WAY_TWO_WAY_LOCK) {
            this.alertInfo.emailContent = this.tranService.translate("alert.message.status")
            this.alertInfo.smsContent = this.tranService.translate("alert.message.status")
        } else if (value == CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD) {
            this.alertInfo.emailList = null
            this.alertInfo.smsList = null
            this.alertInfo.unit = CONSTANTS.ALERT_UNIT.PERCENT
            this.alertInfo.value = 1
            this.alertInfo.walletSubCode = null
        }
        this.onChangeCheckBox();
    }

    checkRequiredOutLine(){
        let me = this;
        if (me.alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP ){
            return true;
        }
        return false
    }

    closeForm() {
        this.router.navigate(['/alerts'])
    }


    deleteAlert() {
        let me = this;
        me.messageCommonService.confirm(
            me.tranService.translate("global.message.titleConfirmDeletePlan"),
            me.tranService.translate("global.message.confirmDeletePlan"),
            {
                ok: () => {
                    // me.ratingPlanService.deleteById(me.planId,(response)=>{
                    me.messageCommonService.success(me.tranService.translate("global.message.deleteSuccess"));
                    me.router.navigate(['/alerts']);
                    // })
                },
                cancel: () => {
                    // me.messageCommonService.error(me.tranService.translate("global.message.deleteFail"));
                }
            }
        )
    }

    showDialogActive() {
        this.isShowDialogActive = true;
    }

    getClassStatus(value) {
        if (value == CONSTANTS.ALERT_STATUS.ACTIVE) {
            return ['p-1', "bg-green-600", "border-round", "inline-block"];
        } else if (value == CONSTANTS.ALERT_STATUS.INACTIVE) {
            return ['p-1', "bg-red-600", "border-round", "inline-block"];
        }
        return [];
    }

    getNameStatus(value) {
        if (value == CONSTANTS.ALERT_STATUS.ACTIVE) {
            return this.tranService.translate("alert.status.active");
        } else if (value == CONSTANTS.ALERT_STATUS.INACTIVE) {
            return this.tranService.translate("alert.status.inactive");
        }
        return "";
    }

    onEdit() {
        let me = this;
        me.router.navigate([`/alerts/edit/${this.alertId}`]);
    }

    nameChanged(event) {
        let me = this
        this.isAlertNameExisted = false;
        if (this.alertInfo.name == this.alertResponse.name) return;
        this.debounceService.set("name", me.alertService.checkName.bind(me.alertService), {name: me.alertInfo.name}, (response) => {
            if (response >= 1) {
                me.isAlertNameExisted = true
            } else {
                me.isAlertNameExisted = false
            }
        })
    }
    onNameBlur() {
        let me = this;
        this.isAlertNameExisted = false;
        if (this.alertInfo.name == this.alertResponse.name) return;
        let formattedValue = this.alertInfo.name.trim();
        formattedValue = formattedValue.replace(/\s+/g, ' ');
        this.alertInfo.name = formattedValue;
        this.formAlert.get('name').setValue(formattedValue);
        this.debounceService.set("name",me.alertService.checkName.bind(me.alertService),{name:me.alertInfo.name},(response)=>{
            if (response >= 1){
                me.isAlertNameExisted = true
            }
            else {
                me.isAlertNameExisted = false
            }
        })
    }

    // filerGroupByCustomer(event, customerCode?: string) {
    //     if (this.alertInfo.customerId != null) {
    //         this.paramSearchGroupSim = {customerCode: customerCode ? customerCode : this.alertInfo.customerId.customerCode}
    //         this.paramSearchSim = {customer: customerCode ? customerCode : this.alertInfo.customerId.customerCode};
    //         if (customerCode == undefined) {
    //             this.alertInfo.groupId = null;
    //             this.alertInfo.subscriptionNumber = null;
    //         }
    //     }
    // }
    filerGroupByCustomer(event: any) {
        console.log(event)
        if(this.alertInfo.customerId != null && this.alertInfo.contractCode != null){
            this.paramSearchGroupSim = {customerCode: this.alertInfo.customerId.customerCode, contractCode: this.alertInfo.contractCode.contractCode}
            this.paramSearchSim = {customer: this.alertInfo.customerId.customerCode, contractCode: this.utilService.stringToStrBase64(this.alertInfo.contractCode.contractCode)};
            this.alertInfo.groupId = null;
            this.alertInfo.subscriptionNumber = null;
        }
    }
    filerGroupByCustomerOrContractCode(event) {
        if(this.alertInfo.customerId != null){
            if (this.userType != CONSTANTS.USER_TYPE.CUSTOMER){
                this.paramSearchGroupSim = {customerCode: this.alertInfo.customerId.customerCode}
                this.paramSearchSim = {customer: this.alertInfo.customerId.customerCode};
                this.alertInfo.groupId = null;
                this.alertInfo.subscriptionNumber = null;
            }else {
                this.paramSearchContract = {customerCode: this.alertInfo.customerId.customerCode}
                this.alertInfo.groupId = null;
                this.alertInfo.subscriptionNumber = null;
                this.alertInfo.contractCode = null;
            }
        }
    }

    onChangeActionType() {
        if (this.alertInfo.actionType == 0) {
            this.formAlert.get("url").disable()

            this.formAlert.get("emailSubject").enable({emitEvent: false})
            this.formAlert.get("emailContent").enable({emitEvent: false})
            this.formAlert.get("smsContent").enable({emitEvent: false})
        } else if (this.alertInfo.actionType == 1) {
            this.formAlert.get("url").enable()

            this.formAlert.get("emailSubject").disable({emitEvent: false})
            this.formAlert.get("emailContent").disable({emitEvent: false})
            this.formAlert.get("smsContent").disable({emitEvent: false})
        }
    }

    checkRequiredLength(){
        let me = this
        if(this.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_VALUE || this.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_VALUE){
            return 9999999999
        }else if(this.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_PACKAGE || this.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_PACKAGE){
            return 100
        }
        if (this.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD ) {
            if (me.alertInfo.unit == CONSTANTS.ALERT_UNIT.PERCENT) {
                return 100
            } else if (me.alertInfo.unit == CONSTANTS.ALERT_UNIT.MB || me.alertInfo.unit == CONSTANTS.ALERT_UNIT.SMS) {
                return 9999999999
            }
        }
        return null
    }

    restoreTypeAlert(response: any): any {
        this.alertInfo.typeAlert = []
        if (response.listAlertReceivingGroupId != null && response.listAlertReceivingGroupId.length > 0) {
            this.alertInfo.typeAlert.push("Group")
        }
        if (response.emailList != null) {
            this.alertInfo.typeAlert.push("Email")
        }
        if (response.smsList != null) {
            this.alertInfo.typeAlert.push("SMS")
        }
        this.onChangeCheckBox()
    }

    getListRatingPlan() {
        let me = this;
        if (me.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP) {
            this.trafficWalletService.searchPakageCode({}, (response) => {
                me.appliedPlanOptions = (response || []).map(el => ({code: el}))
                if(me.alertResponse.dataPackCode != null && me.alertResponse.dataPackCode.length > 0) {
                    me.appliedPlanOptions.push(...me.alertResponse.dataPackCode.map(el=> ({code : el})))
                }
            })
        } else if (me.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD) {
            let walletOptions = []
            me.trafficWalletService.search({subCode: me.alertInfo.walletSubCode}, (response) => {
                walletOptions = (response.content || [])
                if (walletOptions.length > 0) {
                    console.log("len")
                    if (walletOptions[0].trafficType.toUpperCase().trim() == 'Gói Data'.toUpperCase()) {
                        this.unitWalletOptions = [
                            {label: "%", value: 1},
                            {label: "MB", value: 2},
                        ]
                    } else if (walletOptions[0].trafficType.toUpperCase().trim().includes('Gói SMS'.toUpperCase())) {
                        this.unitWalletOptions = [
                            {label: "%", value: 1},
                            {label: "SMS", value: 3}
                        ]
                    }
                }
            })
        }
    }

    onChangeCheckBox() {
        let me = this;
        this.listEnable = []
        if(this.alertInfo.typeAlert.length == 0) {
            this.disableAll()
            return
        }
        if (this.alertInfo.typeAlert.includes("Group")) {
            for(let myField of this.listEnableForGroup) {
                if(!this.listEnable.includes(myField)) {
                    this.listEnable.push(myField)
                }
            }
        }
        if (this.alertInfo.typeAlert.includes("Email")) {
            for(let myField of this.listEnableForEmail) {
                if(!this.listEnable.includes(myField)) {
                    this.listEnable.push(myField)
                }
            }
        }
        if (this.alertInfo.typeAlert.includes("SMS")) {
            for(let myField of this.listEnableForSMS) {
                if(!this.listEnable.includes(myField)) {
                    this.listEnable.push(myField)
                }
            }
        }

        for (let el of this.listEnable){
            if(el != 'receiveGroup') {
                this.formAlert.get(el).enable({emitEvent: false})
            }else {
                this.isDisableReceiveGroup = false;
            }
        }
        for(let el of this.listAllField) {
            if(!this.listEnable.includes(el)) {
                if(el != 'receiveGroup') {
                    this.formAlert.get(el).disable({emitEvent: false})
                }else {
                    this.isDisableReceiveGroup = true;
                }
            }
        }
    }

    checkValidValue(event) {
        // cho phep backspace, delete
        if(event.keyCode == 8 || event.keyCode == 46) {
            return;
        }
        // ngoai khoang 0-9 chan (48-57) (96-105)
        if((event.keyCode >= 48 && event.keyCode <= 57) || (event.keyCode >= 96 && event.keyCode <= 105)) {
            return;
        }else {
            event.preventDefault()
        }
    }

    checkExistEmailList() {
        if (this.alertInfo.emailList == null || this.alertInfo.emailList == null ||
            this.alertInfo.emailList == '' || this.formAlert.controls.emailList.errors?.pattern) {
            return false;
        }
        const arr = this.alertInfo.emailList.split(',')
        let duplicate = false;
        const set = new Set();
        for(const el of arr) {
            if(!set.has(el)){
                set.add(el)
            }else {
                duplicate = true;
            }
        }
        return duplicate;
    }

    checkExistSmsList() {
        if (this.alertInfo.smsList == null || this.alertInfo.smsList == null ||
            this.alertInfo.smsList == '' || this.formAlert.controls.smsList.errors?.pattern) {
            return false;
        }
        const arr = this.alertInfo.smsList.split(',')
        let duplicate = false;
        const set = new Set();
        for(const el of arr) {
            if(!set.has(el)){
                set.add(el)
            }else {
                duplicate = true;
            }
        }
        return duplicate;
    }

    checkChange(event){
        if(this.alertInfo.groupId != null && this.alertInfo.subscriptionNumber != null) {
            this.messageCommonService.error(this.tranService.translate("global.message.onlySelectGroupOrSub"))
        }
    }

    check50Email(){
        if (this.alertInfo.emailList == null || this.alertInfo.emailList == null ||
            this.alertInfo.emailList == '' || this.formAlert.controls.emailList.errors?.pattern) {
            return false;
        }
        const arr = this.alertInfo.emailList.split(',')
        if(arr.length > 50) {
            return true;
        }else{
            return false;
        }
    }
    check50Sms(){
        if (this.alertInfo.smsList == null || this.alertInfo.smsList == null ||
            this.alertInfo.smsList == '' || this.formAlert.controls.smsList.errors?.pattern) {
            return false;
        }
        const arr = this.alertInfo.smsList.split(',')
        if(arr.length > 50) {
            return true;
        }else{
            return false;
        }
    }

    checkDisableSave() {
        // const invalidControlsAlert = Object.keys(this.formAlert.controls)
        //     .filter(controlName => this.formAlert.controls[controlName].invalid);
        // console.log("Invalid fields in formAlert: ", invalidControlsAlert);

        if(this.formAlert.invalid || (this.alertInfo.groupId != null && this.alertInfo.subscriptionNumber != null)
            || ((this.checkExistSmsList() || this.check50Sms() || this.checkExistEmailList() || this.check50Email()
                || this.controlAlertReceiving.invalid || this.comboSelectCustomerControl.invalid || this.comboSelectContracCodeControl.invalid
                || this.comboSelectGroupSubControl.invalid || this.comboSelectSubControl.invalid) && this.alertInfo.eventType !== CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP
                && this.alertInfo.eventType !== CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD
            ) || (this.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD && this.controlComboSelectWallet.invalid) || this.isAlertNameExisted)
        {
            return true;
        }else {
            return false;
        }
    }

    checkChangeValueNotify() {
        if(this.alertInfo.value == null || this.alertInfo.value == undefined) {
            this.formAlert.get("notifyRepeat").disable({emitEvent : false})
            this.formAlert.get("notifyInterval").disable({emitEvent : false})
            this.repeat = false
        }else {
            this.formAlert.get("notifyRepeat").enable({emitEvent : false})
        }
    }

    checkValidNotifyRepeat(event) {
        // cho phep backspace, delete
        if(event.keyCode == 8 || event.keyCode == 46) {
            return;
        }
        if(this.alertInfo.notifyInterval != null && this.alertInfo.notifyInterval.toString().length == 2) event.preventDefault();
        // ngoai khoang 0-9 chan (48-57) (96-105)
        if((event.keyCode >= 48 && event.keyCode <= 57) || (event.keyCode >= 96 && event.keyCode <= 105)) {
            return;
        }else {
            event.preventDefault()
        }
    }

    checkValidValueNotify(event) {
        // cho phep backspace, delete
        if(event.keyCode == 8 || event.keyCode == 46) {
            return;
        }
        if(this.alertInfo.value != null && this.alertInfo.value.toString().length == 2) event.preventDefault();
        // ngoai khoang 0-9 chan (48-57) (96-105)
        if((event.keyCode >= 48 && event.keyCode <= 57) || (event.keyCode >= 96 && event.keyCode <= 105)) {
            return;
        }else {
            event.preventDefault()
        }
    }
    loadEventOptions() {
        let me = this;
        // this.eventOptions = [
        //     {name:me.tranService.translate("alert.eventType.exceededPakage"), value:CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_PACKAGE},
        //     {name:me.tranService.translate("alert.eventType.exceededValue"), value:CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_VALUE},
        //     // {name:me.tranService.translate("alert.eventType.sessionEnd"), value:CONSTANTS.ALERT_EVENT_TYPE.SESSION_END},
        //     // {name:me.tranService.translate("alert.eventType.sessionStart"), value:CONSTANTS.ALERT_EVENT_TYPE.SESSION_START},
        //     {name:me.tranService.translate("alert.eventType.smsExceededPakage"), value:CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_PACKAGE},
        //     {name:me.tranService.translate("alert.eventType.smsExceededValue"), value:CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_VALUE},
        //     {name:me.tranService.translate("alert.eventType.owLock"), value:CONSTANTS.ALERT_EVENT_TYPE.ONE_WAY_LOCK},
        //     {name:me.tranService.translate("alert.eventType.twLock"), value:CONSTANTS.ALERT_EVENT_TYPE.TWO_WAY_LOCK},
        //     // {name:me.tranService.translate("alert.eventType.noConection"), value:CONSTANTS.ALERT_EVENT_TYPE.NO_CONECTION},
        //     // {name:me.tranService.translate("alert.eventType.simExp"), value:CONSTANTS.ALERT_EVENT_TYPE.SIM_EXP},
        //     // {name:me.tranService.translate("alert.eventType.dataWalletExp") , value:CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP},
        //     {name:me.tranService.translate("alert.eventType.owtwlock") , value:CONSTANTS.ALERT_EVENT_TYPE.ONE_WAY_TWO_WAY_LOCK}
        //
        // ]
        // if (this.userInfo.type == CONSTANTS.USER_TYPE.ADMIN) {
        //     this.eventOptions.push({name:me.tranService.translate("alert.eventType.dataWalletExp") , value:CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP})
        //     this.eventOptions.push({name:me.tranService.translate("alert.eventType.walletThreshold") , value:CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD})
        //
        // }
        if (this.checkAuthen([CONSTANTS.PERMISSIONS.ALERT.UPDATE])) {
            this.eventOptions.push({name:me.tranService.translate("alert.eventType.exceededPakage"), value:CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_PACKAGE})
            this.eventOptions.push({name:me.tranService.translate("alert.eventType.exceededValue"), value:CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_VALUE})
            this.eventOptions.push({name:me.tranService.translate("alert.eventType.smsExceededPakage"), value:CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_PACKAGE})
            this.eventOptions.push({name:me.tranService.translate("alert.eventType.smsExceededValue"), value:CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_VALUE})
            this.eventOptions.push({name:me.tranService.translate("alert.eventType.owLock"), value:CONSTANTS.ALERT_EVENT_TYPE.ONE_WAY_LOCK})
            this.eventOptions.push({name:me.tranService.translate("alert.eventType.twLock"), value:CONSTANTS.ALERT_EVENT_TYPE.TWO_WAY_LOCK})
            this.eventOptions.push({name:me.tranService.translate("alert.eventType.owtwlock") , value:CONSTANTS.ALERT_EVENT_TYPE.ONE_WAY_TWO_WAY_LOCK})
        }

        if (this.checkAuthen([CONSTANTS.PERMISSIONS.ALERT.UPDATE_WALLET_EXPIRY])) {
            this.eventOptions.push({name:me.tranService.translate("alert.eventType.dataWalletExp") , value:CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP})
        }
        if (this.checkAuthen([CONSTANTS.PERMISSIONS.ALERT.UPDATE_WALLET_THRESHOLD])) {
            this.eventOptions.push({name:me.tranService.translate("alert.eventType.walletThreshold") , value:CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD})
        }
    }

    // changeWallet(wallet) {
    //     if (this.wallet == null) {
    //         this.alertInfo.emailList = null,
    //             this.alertInfo.smsList = null,
    //             this.alertInfo.unit = CONSTANTS.ALERT_UNIT.PERCENT ,
    //             this.unitWalletOptions = [
    //                 {label: "%", value: 1}
    //             ]
    //     } else {
    //         this.alertInfo.walletSubCode = wallet.subCode
    //         this.alertInfo.emailList = wallet.email,
    //             this.alertInfo.smsList =  wallet.phone
    //         this.alertInfo.appliedPlan = wallet.page,
    //             this.alertInfo.unit = CONSTANTS.ALERT_UNIT.PERCENT
    //         this.alertInfo.value = 1
    //
    //         if (this.wallet.trafficType.toUpperCase().trim() == 'Gói Data'.toUpperCase()) {
    //             this.unitWalletOptions = [
    //                 {label: "%", value: 1},
    //                 {label: "MB", value: 2},
    //             ]
    //         } else if (this.wallet.trafficType.toUpperCase().trim() == 'Gói SMS'.toUpperCase()) {
    //             this.unitWalletOptions = [
    //                 {label: "%", value: 1},
    //                 {label: "SMS", value: 3}
    //             ]
    //         }
    //     }
    // }
    changeWalletSubCode(subCode: string) {

        let me = this;
        if (subCode != undefined && subCode != null) {
            me.disableUnit = false
        } else {
            me.disableUnit = true
        }
        me.trafficWalletService.search({subCode: me.alertInfo.walletSubCode, code: me.alertInfo.walletSubCode, getAll: "1"}, (response) => {
            let walletOptions = (response.content || [])
            if (walletOptions.length > 0) {
                me.alertInfo.emailList = walletOptions[0].email
                me.alertInfo.smsList = walletOptions[0].phone
                me.alertInfo.unit = CONSTANTS.ALERT_UNIT.PERCENT
                if (walletOptions[0].trafficType.toUpperCase().trim() == 'Gói Data'.toUpperCase()) {
                    this.unitWalletOptions = [
                        {label: "%", value: 1},
                        {label: "MB", value: 2},
                    ]
                } else if ( walletOptions[0].trafficType.toUpperCase().trim().includes('Gói SMS'.toUpperCase())) {
                    this.unitWalletOptions = [
                        {label: "%", value: 1},
                        {label: "SMS", value: 3}
                    ]
                }
            }
        })
    }
}
