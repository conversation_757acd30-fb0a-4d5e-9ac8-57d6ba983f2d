<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{tranService.translate("groupSim.breadCrumb.group")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
    <div class="flex gap-3 responsive-container">
        <button
            [disabled]="createGroupForm.invalid || isExistGroupCode"
            type="button"
            pButton
            [label]="tranService.translate('global.button.addSubToGroup')"
            (click)="addSubToGroup()"
        >
        </button>
        <button pButton [disabled]="createGroupForm.invalid || isExistGroupCode" type="button" [label]="tranService.translate('groupSim.label.addByFile')" (click)="addSubFile()" class="p-button-success"></button>
    </div>
</div>
<form
    action=""
    [formGroup]="createGroupForm"
    (submit)="submitForm()"
    (keydown.enter)="$event.preventDefault()"
    class ="responsive-form">

    <div class="mt-3">
        <p-card>
            <div class="gap-4 mt-3 px-2 mb-0">
                <div class="flex flex-column gap-2 flex-1">
                    <label htmlFor="groupCode" class="my-auto" style="min-width: 110px;">{{tranService.translate("groupSim.label.groupKey")}}<span class="text-red-500">*</span></label>
                    <input (blur)="onCodeBlur()" pInputText id="groupKey" formControlName="groupCode" type="text" [placeholder]="tranService.translate('groupSim.placeHolder.groupKey')" />
                </div>
                <div class="flex flex-row gap-4 px-2 py-0 m-0">
                    <div *ngIf="createGroupForm.controls['groupCode']?.dirty && createGroupForm.controls['groupCode'].hasError('required')" class="text-red-500">
                        {{tranService.translate("groupSim.error.requiredError")}}
                    </div>
                    <div *ngIf="createGroupForm.controls.groupCode.errors?.['maxlength']" class="text-red-500">
                        {{tranService.translate("groupSim.error.lengthError_16")}}
                    </div>
                    <div *ngIf="createGroupForm.controls.groupCode.errors?.['pattern']" class="text-red-500">
                        {{tranService.translate("groupSim.error.characterError_code")}}
                    </div>
                    <div *ngIf="isExistGroupCode" class="text-red-500">
                        {{tranService.translate("datapool.error.existedGroupCode")}}
                    </div>
                </div>
                <div class="flex flex-column gap-2 flex-1 mt-3">
                    <label htmlFor="groupName" class="my-auto" style="min-width: 110px;">{{tranService.translate("groupSim.label.groupName")}}<span class="text-red-500">*</span></label>
                    <input (blur)="onNameBlur()" pInputText id="groupName" formControlName="groupName" type="text" class="w-full" [placeholder]="tranService.translate('groupSim.placeHolder.groupName')"/>
                </div>
                <div class="flex flex-row gap-4 px-2 py-0 m-0">
                    <div *ngIf="createGroupForm.controls['groupName']?.dirty && (createGroupForm.controls['groupName'].hasError('required') || createGroupForm.controls.groupName.errors?.['whitespace'])" class="text-red-500">
                        {{tranService.translate("groupSim.error.requiredError")}}
                    </div>
                    <div *ngIf="createGroupForm.controls.groupName.errors?.['maxlength']" class="text-red-500">
                        {{tranService.translate("groupSim.error.lengthError_255")}}
                    </div>
                    <div *ngIf="createGroupForm.controls.groupName.errors?.['pattern']" class="text-red-500">
                        {{tranService.translate("groupSim.error.characterError_name")}}
                    </div>
                </div>
            </div>
            <div class="w-full mt-3 px-2">
                <div class="flex flex-column gap-2 flex-1">
                    <label htmlFor="description">{{tranService.translate("datapool.label.description")}}</label>
                    <textarea
                        class="w-full" style="resize: none;"
                        rows="5"
                        [autoResize]="false"
                        pInputTextarea id="description"
                        formControlName="description"
                        [placeholder]="tranService.translate('sim.text.inputDescription')"
                    ></textarea>
                </div>
            </div>
            <div class="w-full field grid px-2 m-0 py-0 mb-3">
                <div *ngIf="createGroupForm.get('description').invalid && createGroupForm.get('description').dirty">
                    <div *ngIf="createGroupForm.get('description').errors.maxlength" class="text-red-500" >{{tranService.translate("global.message.maxLength",{len:255})}}</div>
                </div>
            </div>

            <div class="flex justify-content-center col-12 md:col-12 py-0 gap-3 mt-4">
                <p-button type="button" [label]="tranService.translate('groupSim.label.buttonCancel')"
                    styleClass="p-button-outlined p-button-secondary"
                    routerLink="/data-pool/group/listGroupSub"
                ></p-button>
                <p-button styleClass="p-button-info" [label]="tranService.translate('groupSim.label.buttonSave')" type="submit" [disabled]="createGroupForm.invalid || isExistGroupCode"></p-button>
            </div>
        </p-card>
    </div>

    <div class="mt-3">
        <p-dialog [contentStyle]="{'overflow':'visible'}" class="w-full" [header]="tranService.translate('global.button.addSubToGroup')" [(visible)]="isShowDialogAddSub" [modal]="true" [style]="{ width: '800px', overflowY :'scroll', maxHeight : '80%', height: '400px' }"  [draggable]="false" [resizable]="false">
            <div class="mt-5 flex flex-row gap-3 justify-content-between">
                <div class="flex flex-row gap-3 col-12">
                    <div class="col-5" style="max-width: calc(100% - 1px) !important;">
                        <vnpt-select
                            [(value)]="phoneReceiptSelect"
                            (onchange)="checkValidAdd()"
                            (onSelectItem)="addPhone(phoneReceiptSelect)"
                            [isAutoComplete]="true"
                            [isMultiChoice]="false"
                            paramKey="phoneReceipt"
                            keyReturn="phoneReceipt"
                            [lazyLoad]="true"
                            [placeholder]="tranService.translate('datapool.label.receiverPhone')"
                            displayPattern="${phoneReceipt}"
                            [loadData]="getListShareInfoCbb.bind(this)"
                        ></vnpt-select>
                    </div>
                    <button [disabled]="isClickCreate || !isValidPhone" type="button" pButton [label]="tranService.translate('groupSim.label.buttonAddSim')" (click)="addPhoneNotInSelect(phoneReceiptSelect)"></button>
                </div>
            </div>
            <div class="mb-5 flex flex-row gap-3 justify-content-between text-red-500 px-1">
                <div *ngIf="!isValidPhone">
                    {{tranService.translate("datapool.message.digitError")}}
                </div>
            </div>
            <p-table [value]="shareList" [tableStyle]="{ 'min-width': '50rem' }">
                <ng-template pTemplate="header">
                    <tr>
                        <th>{{tranService.translate("datapool.label.phone")}}</th>
                        <th>{{tranService.translate('datapool.label.fullName')}}</th>
                        <th>{{tranService.translate('datapool.label.email')}}</th>
                        <th></th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-list let-index="rowIndex">
                    <tr>
                        <td>{{ list.phoneReceipt }}</td>
                        <td>
                            <input type="text" (input)="changeDataName($event, index)" pInputText [value]="list.name" [readonly]="userInfo.type == CONSTANTS.USER_TYPE.CUSTOMER && list.createdBy!= null && list.createdBy != userInfo.id">
                            <div class="text-red-500" *ngIf="list.name?.length >=50">
                                {{tranService.translate("global.message.maxLength",{len:50})}}
                            </div>
                            <div *ngIf="!utilService.checkValidCharacterVietnamese(list.name)" class="text-red-500">
                                {{tranService.translate("global.message.wrongFormatName",{len:150})}}
                            </div>
                        </td>
                        <td>
                            <input type="text" (input)="changeDataMail($event, index)" pInputText [value]="list.email" [readonly]="userInfo.type == CONSTANTS.USER_TYPE.CUSTOMER && list.createdBy!= null && list.createdBy != userInfo.id">
                            <div class="text-red-500" *ngIf="list.email?.length >=100">
                                {{tranService.translate("global.message.maxLength",{len:100})}}
                            </div>
                            <div class="text-red-500" *ngIf="isMailInvalid(list.email)">
                                {{tranService.translate("global.message.formatEmail")}}
                            </div>
                        </td>
                        <td><button type="button" pButton class="p-button-outlined" (click)="deleteItem(index)"><i class="pi pi-trash"></i></button></td>
                    </tr>
                </ng-template>
            </p-table>
            <div *ngIf="shareList.length == 0">
                <div class="flex justify-content-center align-items-center" style="height: 100px; min-height: 120px; text-align: center;">
                    <div class="box-item-empty">
                        <span class="pi pi-inbox" style="font-size: x-large;">&nbsp;</span>{{tranService.translate("global.text.nodata")}}
                    </div>
                </div>
            </div>
            <div class="flex justify-content-center col-12 md:col-12 py-0 gap-3 mt-4">
                <button pButton pRipple type="button" [label]="tranService.translate('groupSim.label.buttonCancel')" class="p-button-outlined p-button-secondary" (click)="cancelAddSub()"></button>
                <p-button styleClass="p-button-info" [label]="tranService.translate('groupSim.label.buttonSave')" type="submit" [disabled]="createGroupForm.invalid || shareList.length == 0 || !isAllEmailsValid()"></p-button>
            </div>
        </p-dialog>
    </div>
</form>

<style>
    .button-reset-file{
        width: fit-content;
        height: fit-content;
        top: 50%;
        position: absolute;
        right: 12px;
        z-index: 2;
        transform: translateY(-50%);
        line-height: 14px;
    }
</style>

<p-dialog [contentStyle]="{'overflow':'visible'}" class="w-full responsive-dialog-2" [header]="tranService.translate('groupSim.label.addPhoneByFile')" [(visible)]="isShowDialogAddFile" [modal]="true" [style]="{ width: '800px', overflowY :'scroll', maxHeight : '80%' }"  [draggable]="false" [resizable]="false" (onHide)="reset()">
    <div class="w-full field grid">
        <div class="col-10 flex flex-column justify-content-start">
            <div class="w-full h-auto flex flex-row justify-content-start align-items-center">
                <div class="relative mr-2" [style]="{'width': (options.isShowButtonUpload?'80%':'100%')}">
                    <div class="h-full w-full absolute top-0 left-0 z-1 opacity-0">
                        <input
                            type="file"
                            [(ngModel)]="formObject.file"
                            class="h-full w-full"
                            [class]="options.disabled?'':'cursor-pointer'"
                            (change)="changeFile($event)"
                            [disabled]="options.disabled"
                        />
                    </div>
                    <div [class]="options.disabled?'bg-black-alpha-10':''"  class="w-full border-1 border-black-alpha-40 border-round border-dotted flex flex-row justify-content-center align-items-center" style="box-sizing: border-box;min-height: 42px;">
                        <div class="max-w-full overflow-hidden text-overflow-ellipsis p-2 pl-4 pr-4 white-space" style="box-sizing: border-box;">
                            {{fileObject?textDescription:tranService.translate("global.button.uploadFile")}}
                        </div>
                    </div>
                    <div *ngIf="fileObject != null && !options.disabled" class="cursor-pointer button-reset-file" (click)="resetFile()">
                        <i class="pi pi-times"></i>
                    </div>
                </div>
            </div>
            <div>
                <small class="text-red-500" *ngIf="invalid =='required'">{{tranService.translate("global.message.required")}}</small>
                <small class="text-red-500" *ngIf="invalid =='maxsize'">{{tranService.translate("global.message.maxsizeupload",{len:50})}}</small>
                <small class="text-red-500" *ngIf="invalid =='invalidtype'">{{options.messageErrorType ? options.messageErrorType : tranService.translate("global.message.invalidtypeupload")}}</small>
            </div>
        </div>
        <div class="col-2 flex flex-row justify-content-end align-items-center">
            <p-button icon="pi pi-download" [pTooltip]="tranService.translate('global.button.downloadTemp')" styleClass="p-button-outlined p-button-secondary" (click)="downloadTemplate()"></p-button>
        </div>
    </div>
<!--    <div class="grid"><div class="col pt-0"><small class="text-red-500" *ngIf="isShowErrorUpload">{{messageErrorUpload}}</small></div></div>-->
    <div class="flex justify-content-center gap-3">
        <button [disabled]="invalid || fileObject == null || options.disabled" pButton type="button" [pTooltip]="tranService.translate('global.button.upFile')" (click)="upload()">{{tranService.translate("global.button.save")}}</button>
        <button pButton (click)="isShowDialogAddFile = false"  class="p-button-outlined p-button-secondary" type="button">{{tranService.translate("global.button.cancel")}}</button>
    </div>
</p-dialog>

