<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{tranService.translate("global.menu.historyWallet")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
</div>
<form [formGroup]="formSearch" (ngSubmit)="onSubmitSearch()" class="pb-2 pt-3 vnpt-field-set">
    <p-panel [toggleable]="true" [header]="tranService.translate('global.text.filter')">
        <div>
            <div class="grid search-grid-3">
                <div class="col-3">
                    <span class="p-float-label">
                        <p-dropdown styleClass="w-full"
                                    id="activeType"
                                    formControlName="activeType"
                                    [(ngModel)]="searchInfo.activeType"
                                    [showClear]="true"
                                    [options]="activeTypeOption"
                                    optionLabel="name"
                                    optionValue="value"
                                    [placeholder]="tranService.translate('datapool.placeholder.activeType')"
                        ></p-dropdown>
                        <label class="label-dropdown" htmlFor="activeType">{{tranService.translate("datapool.label.active")}}</label>
                    </span>
                </div>
                <div class="col-3">
                    <span class="p-float-label">
                        <p-dropdown styleClass="w-full"
                                    id="typeShare"
                                    formControlName="typeShare"
                                    [(ngModel)]="searchInfo.typeShare"
                                    [showClear]="true"
                                    [options]="typeShareOption"
                                    optionLabel="name"
                                    optionValue="value"
                                    [placeholder]="tranService.translate('datapool.placeholder.typeShare')"
                        ></p-dropdown>
                        <label class="label-dropdown" htmlFor="fromDate">{{tranService.translate("datapool.label.typeShare")}}</label>
                    </span>
                </div>
                <div class="col-3">
                    <span class="p-float-label">
                        <p-calendar styleClass="w-full"
                                    id="fromDate"
                                    formControlName="fromDate"
                                    [(ngModel)]="searchInfo.fromDate"
                                    [showIcon]="true"
                                    [showClear]="true"
                                    dateFormat="dd/mm/yy"
                                    [maxDate]="maxDateFrom"
                                    (onSelect)="onChangeDateFrom(searchInfo.fromDate)"
                                    (onInput)="onChangeDateFrom(searchInfo.fromDate)"
                                    [showButtonBar]="true"
                        ></p-calendar>
                        <label class="label-calendar" htmlFor="fromDate">{{tranService.translate("report.label.fromDate")}}</label>
                    </span>
                </div>
                <!--            dateTo-->
                <div class="col-3">
                    <span class="p-float-label">
                        <p-calendar styleClass="w-full"
                                    id="toDate"
                                    formControlName="toDate"
                                    [(ngModel)]="searchInfo.toDate"
                                    [showIcon]="true"
                                    [showClear]="true"
                                    dateFormat="dd/mm/yy"
                                    [minDate]="minDateTo"
                                    [maxDate]="maxDateTo"
                                    (onSelect)="onChangeDateTo(searchInfo.toDate)"
                                    (onInput)="onChangeDateTo(searchInfo.toDate)"
                                    [showButtonBar]="true"
                        />
                        <label class="label-calendar" htmlFor="toDate">{{tranService.translate("report.label.toDate")}}</label>
                    </span>
                </div>
                <div class="col-3">
                    <span class="p-float-label">
                        <p-dropdown styleClass="w-full" [showClear]="true"
                            id="status" [autoDisplayFirst]="false"
                            [(ngModel)]="searchInfo.status"
                            formControlName="status"
                            [options]="statuses"
                            optionLabel="name"
                            optionValue="value"
                        ></p-dropdown>
                        <label class="label-dropdown" for="status">{{tranService.translate("datapool.label.status")}}</label>
                    </span>
                </div>
                <div class="col-3 pb-0">
                    <p-button icon="pi pi-search"
                              styleClass="p-button-rounded p-button-secondary p-button-text button-search"
                              type="submit"></p-button>
                </div>
            </div>
        </div>
    </p-panel>

</form>
<table-vnpt
    [fieldId]="'id'"
    [(selectItems)]="selectItems"
    [columns]="columns"
    [dataSet]="dataSet"
    [loadData]="search.bind(this)"
    [options]="optionTable"
    [pageNumber]="pageNumber"
    [pageSize]="pageSize"
    [params]="searchInfo"
    [labelTable]="this.tranService.translate('global.menu.historyWallet')"
></table-vnpt>
<p-dialog
        [modal]="true"
        [(visible)]="showContent"
        [style]="{ width: '50rem' }"
        [breakpoints]="{ '1199px': '75vw', '575px': '90vw' }">
      <p style="max-width: 50rem;white-space: break-spaces;word-wrap: break-word;">
          {{contentHistory}}
      </p>
</p-dialog>

<p-dialog [header]="tranService.translate('datapool.label.phoneShareList')" [(visible)]="isShowPhonelist" [modal]="true" [style]="{ width: '60vw' }" [draggable]="false" [resizable]="false" (onHide)="onHidePhoneList()" styleClass="responsive-dialog-listShare">
    <div class="flex justify-content-end">
        <p-button styleClass="mr-2 p-button-outlined"
              tooltipPosition="right"
              [pTooltip]="tranService.translate('datapool.label.downloadErrorFile')"
              icon="pi pi-download" (onClick)="downloadPhoneList()"></p-button>
    </div>
    <table-vnpt
        #phoneList
        [tableId]="'tbHistoryPhoneListByGroup'"
        [fieldId]="'id'"
        [columns]="columnPhones"
        [dataSet]="dataSetPhone"
        [options]="optionTablePhone"
        [pageNumber]="pageNumberPhone"
        [pageSize]="pageSizePhone"
        [sort]="sortPhone"
        [loadData]="pagingDataPhone.bind(this)"
        [params]="searchInfoPhone"
        [labelTable]=""
        [styleOutLineTable]="styleTable"
    ></table-vnpt>
</p-dialog>
