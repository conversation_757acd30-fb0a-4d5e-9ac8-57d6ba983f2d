export default {
    label: {
        imei: "IMEI",
        location: "Address",
        subcriber: "Subscriber",
        country: "Country",
        category: "Category",
        expireFrom: "Expiration date from",
        expireTo: "Expiration date to",
        expireDate: "Expiration date",
        deviceType: "Device Type",
        msisdn: "Subscription Number",
        note: "Note",
        importByFile: "Import by file",
        iotLink: "Managed by IoT platform",
    },
    text: {
        messageSuccess: "Save Success",
        textResultImportByFile: "The error registration list is being downloaded",
        wrongFormat: "File must be a excel (xlsx)",
        tooBig: "Import file to big",
        columnInvalid: "file is not in correct format",
        msisdnEmpty: "Msisdn is emptly",
        msisdnNotExists: "Msisdn not exists",
        msisdnAssign: "The subscription number has been assigned to another device",
        msisdnInvalid: "Invalid subscription number",
        msisdnIsEmptly: "Subscription number cannot be empty",
        msisdnIsDuplicate: "Duplicate subscriber number in file",
        imeiIsDuplicate: "Duplicate IMEI in file",
        expiredDateInvalid: "Expiration date must be in dd/mm/yyyy format",
        msisdnNotPermission: "The subscription number does not exist or there are no permissions on the subscription number",
        imeiIsExist: "IMEI has been assigned to another device",
        maxRowImport: "File cannot exceed 1000 lines",
        imeiLen: "Invalid information IMEI . Please enter 2 to 64 characters excluding special characters",
        deviceTypeLen: "Invalid information DEVICE TYPE. Please enter 2 to 64 characters excluding special characters",
        countryLen: "Invalid information MADEIN. Please enter 2 to 32 characters excluding special characters",
    }
}
