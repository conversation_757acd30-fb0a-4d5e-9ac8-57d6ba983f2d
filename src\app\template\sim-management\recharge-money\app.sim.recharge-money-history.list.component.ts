import {Component, Inject, Injector, OnInit} from "@angular/core";
import {ComponentBase} from "../../../component.base";
import {SimService} from "../../../service/sim/SimService";
import {RechargeMoneyService} from "../../../service/recharge-money/RechargeMoneyService";
import {FormBuilder, Validators} from "@angular/forms";
import {MenuItem} from "primeng/api";
import {ColumnInfo} from "../../common-module/table/table.component";
import {CONSTANTS} from "../../../service/comon/constants";
import {OptionInputFile} from "../../common-module/input-file/input.file.component";
import {a, an} from "@fullcalendar/core/internal-common";
import Recharge from "../../../../i18n/en/recharge";
import recharge from "../../../../i18n/en/recharge";

@Component({
    selector: "app-recharge-money-history",
    templateUrl: "./app.sim.recharge-money-history.list.component.html",
})

export class AppRechargeMoneyComponent extends ComponentBase implements OnInit{
    rechargeMoney: {
        rechargeAmount,
        paymentMethod,
    }
    searchInfo: {
        paymentMethod: string|null,
        totalAmount: number|null,
        fromDate:  Date|null,
        toDate : Date| null,
        content : string|null,
        status : number|null,
        rechargeAmount : number|null,
        provinceCode : string|null,
        msisdn : string | null ,
    }
    listStatus: Array<any>;
    items: MenuItem[];
    home: MenuItem;
    columns: Array<ColumnInfo>;
    dataSet: {
        content: Array<any>,
        total: number
    };
    pageNumber: number;
    pageSize: number;
    sort: string;
    formSearch: any;
    fileObject: any;
    mapFormSimImports: any;
    mapPlanNameError: any;
    mapImsiError: any;
    optionInputFile: OptionInputFile;
    simImportsOrigin: Array<any>;
    pageSizeSimImport: number = 10;
    rowFirstSimImport: number = 0;
    simImports: Array<any>;
    maxDateFrom: Date|number|string|null = new Date();
    minDateTo: Date|number|string|null = null;
    maxDateTo: Date|number|string|null = new Date();
    topupValue: Array<any>;
    topupType: Array<any>;
    rechargeStatus: Array<any>;
    formSearchRecharge: any;
    isShowDialogRecharge: boolean = false;
    constructor(@Inject(RechargeMoneyService) private rechargeMoneyService: RechargeMoneyService,
    private injector: Injector,
    private formBuilder: FormBuilder) {
        super(injector);
    }
    ngOnInit(): void {
        let me = this;
        this.topupType = [
            {
                value: CONSTANTS.RECHARGE_TYPE.TOPUP,
                name: this.tranService.translate("recharge.type.topup")
            },
            {
                value: [CONSTANTS.RECHARGE_TYPE.EZPAY],
                name: this.tranService.translate("recharge.type.ezpay")
            },
            ];
        this.rechargeStatus = [
            {
                value: [CONSTANTS.RECHARGE_TYPE.TOPUP],
                name: this.tranService.translate("recharge.type.topup")
            },
            {
                value: [CONSTANTS.RECHARGE_TYPE.EZPAY],
                name: this.tranService.translate("recharge.type.ezpay")
            },
            ],
        this.topupValue = [10000, 20000, 50000, 100000, 200000, 500000, 1000000, 2000000, 5000000];
        this.searchInfo = {
            paymentMethod: null,
            totalAmount:null,
            fromDate:  null,
            toDate :  null,
            content : null,
            status : null,
            rechargeAmount :null,
            provinceCode : null,
            msisdn :  null ,
        }
        this.formSearchRecharge = this.formBuilder.group(this.searchInfo);
        this.optionInputFile = {
            type: ['xls','xlsx'],
            messageErrorType: this.tranService.translate("global.message.wrongFileExcel"),
            maxSize: 10,
            unit: "MB",
            required: true,
            isShowButtonUpload: true,
            actionUpload: this.uploadFile.bind(this),
            disabled: false
        }
        this.pageNumber = 0;
        this.pageSize = 10;
        this.sort = "createdDate,desc";
        this.simImportsOrigin = undefined;
        this.dataSet = {
            content: [
            ],
            total: 0
        }
        this.rechargeMoney = {
            rechargeAmount: 0,
            paymentMethod: "EZPAY",
        }
        this.columns = [
            {
                name: this.tranService.translate("recharge.label.paymentMethod"),
                key: "paymentMethod",
                size: "250px",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("recharge.label.totalAmount"),
                key: "totalAmount",
                size: "250px",
                align: "left",
                isShow: true,
                isSort: false
            },
            {
                name: this.tranService.translate("recharge.label.content"),
                key: "content",
                size: "200px",
                align: "left",
                isShow: true,
                isSort: false
            },
            {
                name: this.tranService.translate("recharge.label.status"),
                key: "status",
                size: "200px",
                align: "left",
                isShow: true,
                isSort: false,
                funcGetClassname: (value) => {
                    if(value == CONSTANTS.RECHARGE_STATUS.PENDING){
                        return ['p-2', "text-green-600", "bg-green-100","border-round","inline-block"];
                    }else if(value == CONSTANTS.RECHARGE_STATUS.PAID){
                        return ['p-2', 'text-orange-600', "bg-orange-100","border-round","inline-block"];
                    }
                    return [];
                },
                funcConvertText: (value)=>{
                    if(value == CONSTANTS.RECHARGE_STATUS.PENDING){
                        return me.tranService.translate("recharge.status.pending");
                    }else if(value == CONSTANTS.RECHARGE_STATUS.PAID){
                        return me.tranService.translate("recharge.status.paid");
                    }
                 return "";
                }
            },
            {
                name: this.tranService.translate("recharge.label.createdDate"),
                key: "createdDate",
                size: "175px",
                align: "left",
                isShow: true,
                isSort: false,
                funcConvertText(value){
                    if(value == null) return "";
                    return me.utilService.convertDateToString(new Date(value));
                }
            }
        ]
        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
    }
    importByFile() {
        let me = this;
        me.isShowDialogRecharge = true;
        me.observableService.next(CONSTANTS.OBSERVABLE.KEY_INPUT_FILE_VNPT, {});
    }
    downloadTemplate() {
        this.rechargeMoneyService.downloadTemplate();
    }
    clearFileCallback(){}
    uploadFile(objectFile: any){
        let me = this;
        me.messageCommonService.onload();
        console.log(me.rechargeMoney.rechargeAmount)

        this.rechargeMoneyService.uploadByFile(objectFile, me.rechargeMoney,(res) => {
            console.log(res);
            let me = this;
            me.simImportsOrigin = undefined;
            this.optionInputFile.disabled = true;
            let response = res.rechargeMoneyRowItemList || [];
            if (res.total == 0) {
                me.messageCommonService.error(me.tranService.translate(recharge.notify.vain));
                me.isShowDialogRecharge = false;
                return;
            }
            if(res.total == res.success && res.message.toUpperCase() == "ok".toUpperCase()){
                me.messageCommonService.success(me.tranService.translate(recharge.notify.success));
                me.isShowDialogRecharge = false;
                return;
            }else if (res.error > 0){
                me.messageCommonService.warning(me.tranService.translate("recharge.notify.warning", {error: res.error, total: res.total}));
            }

            //0 normal, 1 error imsi, 2 error planName
            let index = 0;
            me.mapFormSimImports = {};
            me.mapPlanNameError = {};
            me.mapImsiError = {};
            let excludeDescription = ['error.invalid.isdn.empty',"error.invalid.isdn.not.format","error.invalid.rating.empty","error.invalid.rating.not.format"];
            response.forEach(el => {
                if(!excludeDescription.includes(el.description)){
                        if(el.msisdn != null && el.msisdn != "" && /^(\+?84)[1-9][0-9]{8,9}$/.test(el.msisdn)){
                            if((el.description || "") != ""){
                                if(el.description in me.mapImsiError){
                                    me.mapImsiError[el.description].push(el.msisdn);
                                }else{
                                    me.mapImsiError[el.description] = [el.msisdn];
                                }
                            }
                        }
                }else{
                    el.description = ""
                }
                el['keyForm'] = `keyForm${index++}`;
                me.mapFormSimImports[el['keyForm']] = me.formBuilder.group(el);
                me.mapFormSimImports[el['keyForm']].controls['msisdn'].setValidators([Validators.required, Validators.pattern('^(\\+?84)[1-9][0-9]{8,9}$')]);
                me.mapFormSimImports[el['keyForm']].controls['msisdn'].updateValueAndValidity();
            });
            me.rowFirstSimImport = 0;
            me.pageSizeSimImport = 10;
            me.simImportsOrigin = [...response];
            me.simImports = me.simImportsOrigin.slice(0, 10);
        })
    }
    updateParams(dataParams){
        Object.keys(this.searchInfo).forEach(key => {
            if(this.searchInfo[key] != null){
                if(key == "fromDate"){
                    dataParams["fromDate"] = this.searchInfo.fromDate.getTime();
                }else if(key == "toDate"){
                    dataParams["toDate"] = this.searchInfo.toDate.getTime();
                }else{
                    dataParams[key] = this.searchInfo[key];
                }
            }
        })
    }
    search(page, limit, sort, params){
        this.pageNumber = page;
        this.pageSize = limit;
        this.sort = sort;
        let me = this;
        let dataParams = {
            page,
            size: limit,
            sort
        }
        this.updateParams(dataParams)
        me.messageCommonService.onload();
        console.log(dataParams)
        this.rechargeMoneyService.search(dataParams, (response)=>{
            me.dataSet = {
                content: response.content,
                total: response.totalElements
            }
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }
    onSearch(){
        this.search(0,this.pageSize, this.sort, this.searchInfo)
    }
    onChangeDateFrom(value){
        if(value){
            this.minDateTo = value;
        }else{
            this.minDateTo = null
        }
    }

    onChangeDateTo(value){
        if(value){
            this.maxDateFrom = value;
        }else{
            this.maxDateFrom = new Date();
        }
    }
    removeItemSimImport(item, index){
        // console.log(index);
        this.simImportsOrigin.splice(index, 1);
        this.simImports = this.simImportsOrigin.slice(this.rowFirstSimImport, this.rowFirstSimImport + this.pageSizeSimImport)
        delete this.mapFormSimImports[item['keyForm']];
        if(this.simImportsOrigin.length == 0){
            this.isShowDialogRecharge = false;
        }
    }
    pagingResultSimImport(event){
        let first = event.first;
        let size = event.rows;
        this.rowFirstSimImport = first;
        this.pageSizeSimImport = size;
        this.simImports = this.simImportsOrigin.slice(first, first + size);
    }
    checkValueSimImportChange(item){
        if(item.rechargeAmount != null && item.msisdn != null){
            let description = "";
            let keyImsis = Object.keys(this.mapImsiError);
            let keyPlans = Object.keys(this.mapPlanNameError);
            for(let i = 0; i < keyImsis.length;i++){
                if(this.mapImsiError[keyImsis[i]].includes(item.msisdn)){
                    description = keyImsis[i];
                    break;
                }
            }
            if(description == ""){
                for(let i = 0; i < keyPlans.length;i++){
                    if(this.mapPlanNameError[keyPlans[i]].includes(item.rechargeAmount)){
                        description = keyPlans[i];
                        break;
                    }
                }
            }
            // console.log(description);
            if(description.indexOf("duplicated") >= 0){
                let len = this.simImportsOrigin.map(el => el.msisdn).filter(el => el == item.msisdn).length;
                if(len == 1){
                    description = "";
                    this.simImportsOrigin.forEach(el => {
                        if(el.description.indexOf("duplicated") >= 0 && el.msisdn == item){
                            el.description = "";
                        }
                    })
                }
            }
            item.description = description;
        }
    }
    protected readonly recharge = Recharge;
}
