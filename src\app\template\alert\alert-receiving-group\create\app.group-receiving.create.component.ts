import {AfterContentChecked, Component, Inject, Injector, OnInit} from '@angular/core';
import {AccountService} from "../../../../service/account/AccountService";
import {FormBuilder} from "@angular/forms";
import {MenuItem} from "primeng/api";
import {ColumnInfo, OptionTable} from "../../../common-module/table/table.component";
import {ComponentBase} from "../../../../component.base";
import {ReceivingGroupService} from "../../../../service/alert/ReceivingGroup";

@Component({
  selector: 'app-app.group-receiving.create',
  templateUrl: './app.group-receiving.create.component.html',
})
export class AppGroupReceivingCreateComponent extends ComponentBase implements OnInit, AfterContentChecked{
    constructor(
                @Inject(ReceivingGroupService) private receivingGroupService: ReceivingGroupService,
                private formBuilder: FormBuilder,
                private injector: Injector
    ) {
        super(injector);
    }
    items: MenuItem[];
    home: MenuItem;
    formReceivingGroup : any;
    formMailInput : any;
    receivingGroupInfo: {
        name: string|null,
        description: string|null,
        emails: string|null,
        smsList: string|null,
    };
    myEmails: Array<any>|null;
    mySmsList: Array<any>|null;
    selectItems: Array<any> = [];
    columns: Array<ColumnInfo>;
    dataSet: {
        content: Array<any>,
        total: number
    };
    optionTable: OptionTable;
    email: {}

    formSMSInput : any;
    selectItemsSms: Array<any> = [];
    columnsSms: Array<ColumnInfo>;
    dataSetSms: {
        content: Array<any>,
        total: number
    };
    optionTableSms: OptionTable;
    sms: {};
    isRGNameExisted: boolean = false;
    isRGEmailExisted: boolean = false;
    isRGSmsExisted: boolean = false;

    ngOnInit(): void {
        let me = this;
        this.items = [{ label: this.tranService.translate("global.menu.groupReceiving") }, { label: this.tranService.translate("global.menu.groupReceivingList"), routerLink:"/alerts/receiving-group"  }, { label: this.tranService.translate("global.button.create") }];
        this.home = { icon: 'pi pi-home', routerLink: '/' };

        this.receivingGroupInfo = {
            name: null,
            description: null,
            emails: null,
            smsList: null,
        }
        this.myEmails= []
        this.mySmsList = []
        this.formReceivingGroup = this.formBuilder.group(this.receivingGroupInfo);
        this.formMailInput = this.formBuilder.group({email: ""});
        this.dataSet = {
            content: [],
            total: 0
        }
        this.selectItems = [];
        this.columns = [
            {
                name: this.tranService.translate("alert.receiving.emails"),
                key: "emails",
                size: "90%",
                align: "left",
                isShow: true,
                isSort: false,
            },
        ];
        this.optionTable = {
            hasClearSelected: true,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
            action: [
                {
                    icon: "pi pi-trash",
                    tooltip: this.tranService.translate("alert.text.removeAlert"),
                    func: function(id, item){
                        me.removeEmail(item)
                    },
                }
            ]
        };
        this.dataSet = {
            content: [],
            total: 0
        }

        this.formSMSInput = this.formBuilder.group({sms: ""});
        this.selectItemsSms = [];
        this.columnsSms = [
            {
                name: this.tranService.translate("alert.receiving.sms"),
                key: "smsList",
                size: "90%",
                align: "left",
                isShow: true,
                isSort: false,
            },
        ];
        this.optionTableSms = {
            hasClearSelected: true,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
            action: [
                {
                    icon: "pi pi-trash",
                    tooltip: this.tranService.translate("alert.text.removeSms"),
                    func: function(id, item){
                        me.removeSms(item)
                    },
                }
            ]
        };
        this.search();
        this.searchSms();
    }
    ngAfterContentChecked(): void {
    }
    onSubmitCreate(){
        let dataBody = {
            // username: this.accountInfo.accountName,
            name: this.receivingGroupInfo.name,
            description: this.receivingGroupInfo.description,
            emails: this.receivingGroupInfo.emails,
            msisdns: this.receivingGroupInfo.smsList,
        }
        this.messageCommonService.onload();
        let me = this;
        this.receivingGroupService.createReceivingGroup(dataBody, (response)=>{
            me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"));
            // me.router.navigate(['/accounts/edit/'+response.id]);
            me.router.navigate(['/alerts/receiving-group']);
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }
    closeForm(){
        this.router.navigate(['/alerts/receiving-group'])
    }

    addEmail(val){
        let me = this;
        me.dataSet.content.push({emails :val})
        me.myEmails.push(val)
        me.receivingGroupInfo.emails = me.myEmails.join(', ').toString()
        me.email = ""
        // console.log(me.myEmails)
        // console.log(me.receivingGroupInfo.emails)
    }
    search(){
        let me = this
        me.dataSet = {
            content: [],
            total: 0
        }
    }
    removeEmail(val){
        let me = this
        me.dataSet.content.splice(me.dataSet.content.indexOf(val), 1)
        me.myEmails.splice(me.myEmails.indexOf(val), 1)
        me.receivingGroupInfo.emails = me.myEmails.join(', ').toString()
        // console.log(me.receivingGroupInfo.emails)
    }

    addSms(val){
        let me = this;

        me.dataSetSms.content.push({smsList :val})
        me.mySmsList.push(val)
        me.receivingGroupInfo.smsList = me.mySmsList.join(', ').toString()
        me.sms = ""
    }
    searchSms(){
        let me = this
        me.dataSetSms = {
            content: [],
            total: 0
        }
    }
    removeSms(val){
        let me = this
        me.dataSetSms.content.splice(me.dataSetSms.content.indexOf(val), 1)
        me.mySmsList.splice(me.mySmsList.indexOf(val), 1)
        me.receivingGroupInfo.smsList = me.mySmsList.join(', ').toString()
    }
    nameChanged(query){
        let me = this
        this.debounceService.set("name",me.receivingGroupService.checkName.bind(me.receivingGroupService),{name:me.receivingGroupInfo.name},(response)=>{
            if (response == 1){
                me.isRGNameExisted = true
            }
            else {
                me.isRGNameExisted = false
            }
        })
    }
    emailChanged(query){
        let me = this;
        for (let i = 0; i < me.myEmails.length; i++) {
            if (me.myEmails[i] == query){
                this.isRGEmailExisted = true
                return
            }
            else {
                this.isRGEmailExisted = false
            }
        }
    }
    smsChanged(query){
        let me = this;
        for (let i = 0; i < me.mySmsList.length; i++) {
            if (me.mySmsList[i] == query){
                this.isRGSmsExisted = true
                return
            }
            else {
                this.isRGSmsExisted = false
            }
        }
    }
    checkFormInfo(){
        let me = this;
        if ((me.receivingGroupInfo.emails != null && me.receivingGroupInfo.emails != "" || me.myEmails.length > 0 )
            || (me.receivingGroupInfo.smsList != null && me.receivingGroupInfo.smsList != "" || me.mySmsList.length > 0 ))
        {
            return false;
        }
        return true;
    }

}
