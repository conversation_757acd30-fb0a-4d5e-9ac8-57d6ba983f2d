import { Inject, Injectable } from "@angular/core";
import { HttpService } from "../comon/http.service";

@Injectable({ providedIn: 'root'})
export class AccountService{
    private prefixApi: string;
    constructor(@Inject(HttpService) private httpService:HttpService) {
        this.prefixApi = "/user-mgmt";
    }

    public search(params:{[key: string]: string}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(`${this.prefixApi}/search`, {}, params, callback, errorCallBack, finallyCallback);
    }

    public getById(id: number, callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/${id}`,{timeout: 180000}, {}, callback, errorCallback, finallyCallback);
    }

    // public getByKey(key: string, value: string, callback?: Function, errorCallback?:Function, finallyCallback?: Function){
    //     this.httpService.get(`${this.prefixApi}/getBy<PERSON>ey`,{}, {key, value}, callback, errorCallback, finallyCallback);
    // }
    public changeStatus(id: number, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.put(`${this.prefixApi}/change-status/${id}`,{},{},{},callback, errorCallBack, finallyCallback);
    }

    public deleleUser(id: number, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.delete(`${this.prefixApi}/delete/${id}`, {}, {}, callback, errorCallBack, finallyCallback);
    }

    public getUserAssignedOnRatingPlan(params:{[key: string]: string}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(`${this.prefixApi}/getUserAssignedOnRatingPlan`, {}, params, callback, errorCallBack, finallyCallback);
    }

    public getListProvince(callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/get-province`, {}, {}, callback, errorCallback, finallyCallback);
    }

    public getListProvinceByCode(body, callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.post(`${this.prefixApi}/get-province-by-code`, {},body,{}, callback, errorCallback, finallyCallback);
    }

    public getListRole(param,callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/get-list-role`, {},param, callback, errorCallback, finallyCallback);
    }

    public createAccount(body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.post(`${this.prefixApi}/create`, {},body,{}, callback, errorCallback, finallyCallback);
    }

    public updateAccount(id,body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.put(`${this.prefixApi}/update/${id}`, {timeout: 180000},body,{}, callback, errorCallback, finallyCallback);
    }

    public checkAccount(email?:string,username?:string,callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/check-exist`, {},{email,username}, callback, errorCallback, finallyCallback);
    }

    public changePassword(header,body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.post(`${this.prefixApi}/change-password`, header,body,{}, callback, errorCallback, finallyCallback);
    }

    public forgotPasswordInit(email: string,callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.postNoHeader(`${this.prefixApi}/forgot-password/init?`,{}, {},{email}, callback, errorCallback, finallyCallback);
    }

    public forgotPasswordFinish(body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.postNoHeader(`${this.prefixApi}/forgot-password/finish?`,{}, body,{}, callback, errorCallback, finallyCallback);
    }

    public validateTokenEmail(body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.postNoHeader(`${this.prefixApi}/validate-token-mail`,{}, body,{}, callback, errorCallback, finallyCallback);
    }

    public viewProfile(callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/view-profile`,{}, {}, callback, errorCallback, finallyCallback);
    }

    public updateProfile(body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.put(`${this.prefixApi}/update-profile`, {},body,{}, callback, errorCallback, finallyCallback);
    }

    public getListConfirmPolicyHistory(callback: Function){
        this.httpService.get(`${this.prefixApi}/history-confirm-policy`, {}, {}, callback);
    }

    public agreePolicy(policyId: number, body, callback, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.post(`${this.prefixApi}/policy/agree/${policyId}`, {}, body, {}, callback,errorCallback, finallyCallback);
    }

    public disagreePolicy(policyId: number, callback, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.put(`${this.prefixApi}/policy/disagree/${policyId}`, {}, {}, {}, callback, errorCallback, finallyCallback);
    }

    public searchAccountUserOfUser(params:{[key: string]: string}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(`${this.prefixApi}/getmanaged-customer-accounts`, {}, params, callback, errorCallBack, finallyCallback);
    }

    public changeManageData(data, callback?:Function, errorCallback?:Function, finallyCallback?:Function){
        this.httpService.post(`${this.prefixApi}/change-user-for-manager`, {}, data, {}, callback,errorCallback, finallyCallback);
    }
    //
    // public rejectPolicy(policyId: number, callback, errorCallback?:Function, finallyCallback?: Function){
    //     this.httpService.put(`${this.prefixApi}/policy/reject/${policyId}`, {}, {}, {}, callback, errorCallback, finallyCallback);
    // }
    public getCustomerAccount(params:{[key: string]: string}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(`${this.prefixApi}/get-no-one-managed-customer-accounts`, {}, params, callback, errorCallBack, finallyCallback);
    }
    public getAccountHistory(params:{[key: string]: string}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(`${this.prefixApi}/searchUserForActivityLog`, {}, params, callback, errorCallBack, finallyCallback);
    }
    public getListActivatedAccount( body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.post(`${this.prefixApi}/getListActivatedAccount`, {},body,{}, callback, errorCallback, finallyCallback);
    }
    public loadDropdown(params:{[key:string]:any}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(`${this.prefixApi}/dropdown`,{timeout: 180000}, params,callback, errorCallBack, finallyCallback);
    }

    public searchGrantApi(params:{[key: string]:string} ,callback?:Function, errorCallback?:Function, finallyCallback?: Function ){
        this.httpService.get(`${this.prefixApi}/list-api`, {}, params, callback, errorCallback, finallyCallback)
    }

    public getListModule(callback?:Function, errorCallback?:Function, finallyCallback?: Function ){
        this.httpService.get(`${this.prefixApi}/list-module`, {}, null, callback, errorCallback, finallyCallback)
    }

    public createClientAuthen( body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.post(`/client-authentication/create`, {},body,{}, callback, errorCallback, finallyCallback);
    }

    public getListAPI(params:{[key:string]:any}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(`${this.prefixApi}/list-api`,{timeout: 180000}, params,callback, errorCallBack, finallyCallback);
    }

    public getListAPI2(params:{[key:string]:any}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(`${this.prefixApi}/list-api-customer-child`,{timeout: 180000}, params,callback, errorCallBack, finallyCallback);
    }

    public getListAPIByModule(params:{[key:string]:any}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(`${this.prefixApi}/list-api/get-by-modulename`,{timeout: 180000}, params,callback, errorCallBack, finallyCallback);
    }
}
