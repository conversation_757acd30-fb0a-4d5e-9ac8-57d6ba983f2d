import {Component, Injector, OnInit} from '@angular/core';
import {MenuItem} from "primeng/api";
import {RatingPlanService} from "../../../service/rating-plan/RatingPlanService";
import {FormBuilder} from "@angular/forms";
import {ColumnInfo, OptionTable} from "../../common-module/table/table.component";
import {CustomerService} from "../../../service/customer/CustomerService";
import {ComponentBase} from "../../../component.base";

@Component({
  selector: 'app-app.history.registerplan.list',
  templateUrl: './app.history.registerplan.list.component.html',
})
export class AppHistoryRegisterplanListComponent extends ComponentBase implements OnInit {
    constructor(
                public ratingPlanService: RatingPlanService,
                private customerService: CustomerService,
                private formBuilder: FormBuilder,
                private injector: Injector
    ) {
        super(injector);
    }
    items: MenuItem[];
    home: MenuItem;
    pageNumber: number;
    pageSize: number;
    sort: string;
    formSearch: any;
    selectItems: Array<any> = [];
    searchInfo: {
        time: string | null,
        isdn: string | null,
        customerCode: string | null,
        ratingPlan: string | null,
        actionType: string | null,
        status: string | null,
        modifyBy: string | null,
        fromDate: Date|null,
        toDate: Date|null,
    };
    columns: Array<ColumnInfo>;
    optionTable: OptionTable;
    dataSet: {
        content: Array<any>,
        total: number
    };
    listCustomer: Array<any>;
    maxDateFrom: Date|number|string|null = new Date();
    minDateTo: Date|number|string|null = null;
    maxDateTo: Date|number|string|null = new Date();
    maxDateValue: Date;
    minDateValue: Date;

    ngOnInit(): void {
        let me = this;
        // console.log(this.maxDateValue)
        this.items = [{ label: this.tranService.translate("global.menu.ratingplanmgmt") }, { label: this.tranService.translate("global.menu.registerplan"),routerLink:"/plans/registers" }, { label: this.tranService.translate("global.menu.historyRegister") }];
        this.home = { icon: 'pi pi-home', routerLink: '/' };
        this.searchInfo = {
            time: null,
            isdn: null,
            customerCode: null,
            ratingPlan: null,
            actionType: null,
            status: null,
            modifyBy: null,
            fromDate: null,
            toDate: null,
        }
        this.formSearch = this.formBuilder.group(this.searchInfo);
        this.selectItems = [];
        this.pageNumber = 0;
        this.pageSize = 10;
        this.sort = "customerName,asc";
        this.optionTable = {
            hasClearSelected: true,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
            // action: [
            //     {
            //         icon: "pi pi-user-edit",
            //         tooltip: this.tranService.translate("global.button.edit"),
            //         func: function(id, item){
            //             me.router.navigate([`/plans/edit/${id}`]);
            //         },
            //         funcAppear: function(id, item) {
            //             return true;
            //         }
            //     },
            //     {
            //         icon: "pi pi-trash",
            //         tooltip: this.tranService.translate("global.button.delete"),
            //         func: function(id, item){
            //             me.messageCommonService.confirm(
            //                 me.tranService.translate("global.message.titleConfirmDeleteAccount"),
            //                 me.tranService.translate("global.message.confirmDeleteAccount"),
            //                 {
            //                     ok:()=>{
            //                         // me.ratingPlanService.demo((response)=>{
            //                         //     me.messageCommonService.success(me.tranService.translate("global.message.deleteSuccess"));
            //                         //     me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);
            //                         // })
            //                     },
            //                     cancel: ()=>{
            //                         // me.messageCommonService.error(me.tranService.translate("global.message.deleteFail"));
            //                     }
            //                 }
            //             )
            //         },
            //         funcAppear: function(id, item) {
            //             return true;
            //         }
            //     },
            // ]
        },
        this.columns = [
            {
                name: this.tranService.translate("historyRegisterPlan.label.time"),
                key: "time",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("historyRegisterPlan.label.isdn"),
                key: "isdn",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
                style:{
                    cursor: "pointer",
             color: "var(--mainColorText)"
                },
                funcGetRouting(item) {
                    return [`/sims/detail/${item.isdn}`]
                },
            },
            {
                name: this.tranService.translate("historyRegisterPlan.label.customerName"),
                key: "customerName",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("historyRegisterPlan.label.ratingPlan"),
                key: "ratingPlan",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("historyRegisterPlan.label.actionType"),
                key: "actionType",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
                funcConvertText(value) {
                    if(value == 1){
                        return me.tranService.translate("historyRegisterPlan.actionType.assignPlan");
                    }else if(value == 2){
                        return me.tranService.translate("historyRegisterPlan.actionType.switchPlan");
                    }else {
                        return "";
                    }
                },
            },
            {
                name: this.tranService.translate("historyRegisterPlan.label.status"),
                key: "status",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
                funcConvertText(value) {
                    if(value == 200){
                        return me.tranService.translate("historyRegisterPlan.status.success");
                    }else {
                        return me.tranService.translate("historyRegisterPlan.status.unsuccessful");
                    }
                },
                funcGetClassname: (value) => {
                    if(value == 200){
                        return ['p-2', 'text-green-800', "bg-green-100", "border-round","inline-block"];
                    }else {
                        return ['p-2', "text-red-700", "bg-red-100","border-round","inline-block"];
                    }
                },
                style:{
                    color:"white"
                }
            },
            {
                name: this.tranService.translate("historyRegisterPlan.label.activeDate"),
                key: "activeDate",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("historyRegisterPlan.label.expiredDate"),
                key: "expiredDate",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("historyRegisterPlan.label.modifyBy"),
                key: "modifyBy",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
            }
        ]
        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
    }

    onSubmitSearch(){
        this.pageNumber = 0;
        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
    }

    search(page, limit, sort, params){
        let me = this;
        this.pageNumber = page;
        this.pageSize = limit;
        this.sort = sort;
        let dataParams = {
            page,
            size: limit,
            sort
        }
        Object.keys(this.searchInfo).forEach(key => {
            if(this.searchInfo[key] != null){
                dataParams[key] = this.searchInfo[key];
            }
        })
        this.dataSet = {
            content: [],
            total: 0
        }
        me.messageCommonService.onload();
        this.ratingPlanService.searchHistory(dataParams, (response)=>{
            me.dataSet.content=response.content.map((item:any)=>{
                item.time=this.convertToDDMMYYYY(item.time)
                return item;
            });
            me.dataSet.total = response.totalElements
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }
    convertToDDMMYYYY(input: string): string {
        const date = new Date(input);
        // const day = date.getUTCDate().toString().padStart(2, '0'); // Thêm số 0 phía trước nếu là số đơn vị
        // const month = (date.getUTCMonth() + 1).toString().padStart(2, '0'); // Thêm số 0 phía trước nếu là số đơn vị và +1 vì getMonth() trả về từ 0-11
        // const year = date.getUTCFullYear().toString();

        return date.toLocaleString('en-GB');
    }

    onChangeDateTo(value){
        let me  = this;
        if(value){
            this.maxDateFrom = value;
            me.searchInfo.toDate = value.getTime() + 23*60*60*1000 + 59*60*1000 + 59*1000;
        }else{
            this.maxDateFrom = new Date();
            me.searchInfo.toDate = null;
        }
    }

    onChangeDateFrom(value){
        let me  = this;
        if(value){
            this.minDateTo = value;
            me.searchInfo.fromDate = value.getTime();
        }else{
            this.minDateTo = null;
            me.searchInfo.fromDate = null;
        }
    }
}
