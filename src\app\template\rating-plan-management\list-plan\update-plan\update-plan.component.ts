import {On<PERSON><PERSON>roy, Injector} from '@angular/core';
import {Component, Inject, inject} from '@angular/core';
import {
    AbstractControl,
    AsyncValidatorFn, FormBuilder,
    FormControl,
    FormGroup,
    ValidationErrors,
    ValidatorFn,
    Validators
} from '@angular/forms';
import {MenuItem} from 'primeng/api';
import {Observable, Subscription, debounceTime, distinctUntilChanged, map, of, switchMap, take} from 'rxjs';
import {ComponentBase} from 'src/app/component.base';
import {AccountService} from 'src/app/service/account/AccountService';
import {CONSTANTS} from 'src/app/service/comon/constants';
import {RatingPlanService} from 'src/app/service/rating-plan/RatingPlanService';
import {ComboLazyControl} from "../../../common-module/combobox-lazyload/combobox.lazyload";
import {ColumnInfo, OptionTable} from "../../../common-module/table/table.component";

interface DropDownValue {
    id: number | string;
    name: string;
}

@Component({
    selector: 'app-update-plan',
    templateUrl: './update-plan.component.html',
})
export class UpdatePlanComponent extends ComponentBase implements OnDestroy {
    idForEdit: number;
    home: MenuItem = {icon: 'pi pi-home', routerLink: '/'};
    items: MenuItem[] = [
        {label: this.tranService.translate("global.menu.ratingplanmgmt"), routerLink: '../../'},
        {label: this.tranService.translate("global.menu.listplan"), routerLink: '../../'},
        {label: this.tranService.translate("global.button.edit")},
    ];
    isPlanCodeExisted: boolean = false;
    isPlanNameExisted: boolean = false;
    currentPlanCodeInput: string;
    currentPlanNameInput: string;
    initialData: any;
    updatePlanForm: FormGroup

    customerTypes: DropDownValue[] | undefined = [
        {
            id: 1,
            name: this.tranService.translate("ratingPlan.customerType.personal"),
        },
        {
            id: 2,
            name: this.tranService.translate("ratingPlan.customerType.enterprise"),
        },
        {
            id: 0,
            name: this.tranService.translate("ratingPlan.customerType.agency"),
        },
        // ,
        // {
        //   id: 0,
        //   name: this.tranService.translate("ratingPlan.customerType.agency"),
        // },
    ];
    ratingScopes: DropDownValue[] | undefined = [
        {
            id: 0,
            name: this.tranService.translate("ratingPlan.ratingScope.nativeWide"),
        },
        {
            id: 2,
            name: this.tranService.translate("ratingPlan.ratingScope.province"),
        },
        {
            id: 1,
            name: this.tranService.translate("ratingPlan.ratingScope.customer"),
        },
    ];
    userType = this.sessionService.userInfo.type;
    allUserType = CONSTANTS.USER_TYPE;
    provinceInfo: string = "";

    provinces: any[] | undefined;
    isPlanCodeValid: boolean = false;
    isPlaneNameValid: boolean = false;
    isDispatchCodeValid: boolean = false;
    isCustomerTypeValid: boolean = false;
    isSubscriptionFeeValid: boolean = false;
    isSubscriptionTypeValid: boolean = false; // Đã set default
    isPlanScopeValid: boolean = false;
    isProvinceCodeValid: boolean = false;
    isPlanCycleValid: boolean = false;
    isDurationValid: boolean = false;
    isDescriptionValid: boolean = false;
    isFreeDataValid: boolean = false;
    isLimitInsideSMSFreeValid: boolean = false;
    isLimitOutsideSMSFreeValid: boolean = false;
    isFeePerUnitNumberatorValid: boolean = false;
    isFeePerUnitDenominatorValid: boolean = false;
    isSqueezeSpeedNumberatorValid: boolean = false;
    isSqueezeSpeedDenominatorValid: boolean = false;
    isFeePerInsideSMSValid: boolean = false;
    isFeePerOutsideSMSValid: boolean = false;
    isMaxFeeValid: boolean = false;
    isDataMaxValid: boolean = false;

    subPlanCode: Subscription;
    subPlaneName: Subscription;
    subDispatchCode: Subscription;
    subCustomerType: Subscription;
    subSubscriptionFee: Subscription;
    subSubscriptionType: Subscription;
    subPlanScope: Subscription;
    subProvinceCode: Subscription;
    subPlanCycle: Subscription;
    subDuration: Subscription;
    subDescription: Subscription;
    subFreeData: Subscription;
    subLimitInsideSMSFree: Subscription;
    subLimitOutsideSMSFree: Subscription;
    subFeePerUnitNumberator: Subscription;
    subFeePerUnitDenominator: Subscription;
    subSqueezeSpeedNumberator: Subscription;
    subSqueezeSpeedDenominator: Subscription;
    subFeePerInsideSMS: Subscription;
    subFeePerOutsideSMS: Subscription;
    subMaxFee: Subscription;
    subDataMax: Subscription;

    controlComboSelect: ComboLazyControl = new ComboLazyControl();
    customerCode: [];
    isProvince = false
    isCustomer = false
    isRead: boolean = false

    isShowDialogAddCustomerAccount: boolean = false;
    searchInfoUser: {
        username: string | null,
        fullName: string | null,
        email: string | null,
        provinceCode: any | null,
    }
    listProvince: any[] | undefined;
    selectItemsUser: Array<{id: number, provinceCode: string, [key:string]:any}> = [];
    selectItemsUserOld: Array<{id: number, provinceCode: string, [key:string]:any}> = [{id: -1, provinceCode: ""}];
    pageNumberAssign: number;
    pageSizeAssign: number;
    sortAssign: string;
    formSearchUser: any;
    columnsInfoUser: Array<ColumnInfo>;
    dataSetAssignPlan: {
        content: Array<any>,
        total: number
    };
    optionTableAddCustomerAccount: OptionTable;
    sort: string;
    cycles: DropDownValue[] | undefined = [
        {
            id: 1,
            name: this.tranService.translate("ratingPlan.cycle.day")
        },
        {
            id: 3,
            name: this.tranService.translate("ratingPlan.cycle.month")
        },
    ];
    isFlexible: boolean = false;
    isReload: boolean = false;

    paidCategories: any[] = [
        {name: 'Trả trước', key: '1'},
        {name: 'Trả sau', key: '0'},
    ];
    selectedPaidCategory: any = null;

    placeHolderDescription: string = this.tranService.translate("groupSim.placeHolder.description")

    constructor(@Inject(RatingPlanService) public ratingPlanService: RatingPlanService,
                @Inject(AccountService) private accountService: AccountService,private formBuilder: FormBuilder, injector: Injector) {
        super(injector)
    }

    placeHolder = {
        planCode: this.tranService.translate("ratingPlan.placeHolder.planCode"),
        planName: this.tranService.translate("ratingPlan.placeHolder.planeName"),
        dispatchCode: this.tranService.translate("ratingPlan.placeHolder.dispatchCode"),
        customerType: this.tranService.translate("ratingPlan.placeHolder.customerType"),
        description: this.tranService.translate("ratingPlan.placeHolder.description"),
        subscriptionFee: this.tranService.translate("ratingPlan.placeHolder.subscriptionFee"),
        subscriptionType: this.tranService.translate("ratingPlan.placeHolder.subscriptionType"),
        planScope: this.tranService.translate("ratingPlan.placeHolder.planScope"),
        provinceCode: this.tranService.translate("ratingPlan.placeHolder.provinceCode"),
        planCycle: this.tranService.translate("ratingPlan.placeHolder.planCycle"),
        duration: this.tranService.translate("ratingPlan.placeHolder.duration"),
        freeData: this.tranService.translate("ratingPlan.placeHolder.freeData"),
        insideSMSFree: this.tranService.translate("ratingPlan.placeHolder.insideSMSFree"),
        outsideSMSFree: this.tranService.translate("ratingPlan.placeHolder.outsideSMSFree"),
        feePerUnit: this.tranService.translate("ratingPlan.placeHolder.feePerUnit"),
        squeezedSpeed: this.tranService.translate("ratingPlan.placeHolder.squeezeSpeed"),
        feePerInsideSMS: this.tranService.translate("ratingPlan.placeHolder.feePerInsideSMS"),
        feePerOutsideSMS: this.tranService.translate("ratingPlan.placeHolder.feePerOutsideSMS"),
        maxFee: this.tranService.translate("ratingPlan.placeHolder.maxFee"),
        dataMax: this.tranService.translate("ratingPlan.placeHolder.dataMax"),
    }

    checkCodeExisted(query: {}): Observable<number> {
        return new Observable(observer => {
            this.ratingPlanService.checkingPlanCodeExisted(query, (response) => {
                observer.next(response);
                observer.complete();
            });
        });
    }

    checkNameExisted(query: {}): Observable<number> {
        return new Observable(observer => {
            this.ratingPlanService.checkingPlanNameExisted(query, (response) => {
                observer.next(response);
                observer.complete();
            });
        });
    }

    planCodeValidator(): AsyncValidatorFn {
        return (control: AbstractControl): Observable<ValidationErrors | null> => {
            if (!control.valueChanges || control.pristine) {
                return of(null);
            } else
                return control.valueChanges.pipe(
                    debounceTime(500),
                    distinctUntilChanged(),
                    switchMap(value => {
                        this.currentPlanCodeInput = value;
                        return this.checkCodeExisted({code: value})
                    }), take(1),
                    map(result => {
                        if (result === 0 || this.currentPlanCodeInput == this.initialData.code) {
                            this.isPlanCodeExisted = false;
                            return null;
                        } else {
                            this.isPlanCodeExisted = true;
                            return {'exited': true};
                        }
                    }),
                );
        };
    }

    planNameValidator(): AsyncValidatorFn {
        return (control: AbstractControl): Observable<ValidationErrors | null> => {
            if (!control.valueChanges || control.pristine) {
                return of(null);
            } else
                return control.valueChanges.pipe(
                    debounceTime(500),
                    distinctUntilChanged(),
                    switchMap(value => {
                        this.currentPlanNameInput = value;
                        return this.checkNameExisted({name: value})
                    }),
                    take(1),
                    map(result => {
                        if (result === 0 || this.currentPlanNameInput == this.initialData.name) {
                            this.isPlanNameExisted = false
                            return null;
                        } else {
                            this.isPlanNameExisted = true;
                            return {'exited': true};
                        }
                    }),
                );
        };
    }

    customCodeCharacterValidator(): ValidatorFn {
        return (control: AbstractControl): ValidationErrors | null => {
            const value = control.value;
            const isValid = /^[a-zA-Z0-9\-_]*$/.test(value);
            return isValid ? null : {'invalidCharacters': {value}};
        };
    }

    customNameCharacterValidator(): ValidatorFn {
        return (control: AbstractControl): ValidationErrors | null => {
            const value = control.value;
            if (value == '') {
                return null;
            }
            const isValid = /^[a-zA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠàáâãèéêìíòóôõùúăđĩũơỲỴÝỳỵỷỹƯứừữựụợ́̉̃À-ỹ0-9 _-]+$/.test(value);
            return isValid ? null : {'invalidCharacters': {value}};
        };
    }

    blockMinus(event: KeyboardEvent) {
        const invalidChars = ['-', '+', ',', '.', 'e', 'E', 'r', 'R']; // Danh sách các ký tự không cho phép
        if (invalidChars.includes(event.key)) {
            event.preventDefault();
        }
        // if (event.key === '-' || event.key ==='+' || event.key === ',' || event.key === '.') {
        //   event.preventDefault();
        // }
    }

    checkInputValue(event: InputEvent) {
        const input = event.target as HTMLInputElement;
        input.value = input.value.replace(/[^0-9]/g, ''); // Chỉ cho phép nhập số
    }

    onDropdownChange(event) {
        if(event.value==0){
            this.isProvince = false
            this.isCustomer = false
            this.updatePlanForm.get('provinceCode').disable({ emitEvent: false });
            // this.createPlanForm.get('userIds').disable({ emitEvent: false });
        }else if (event.value == 1){
            // gói cước loại khách hàng
            this.isProvince = true
            this.isCustomer = false
            this.updatePlanForm.get('provinceCode').enable({ emitEvent: false });
            this.changeProvince()
            // this.createPlanForm.get('userIds').enable({ emitEvent: false });
        }else if (event.value == 2){
            // gói cước loại tỉnh/thành phố
            this.isProvince = true
            this.isCustomer = false
            this.updatePlanForm.get('provinceCode').enable({ emitEvent: false });
            this.changeProvince()
            // this.createPlanForm.get('userIds').disable({ emitEvent: false });
        }
    }

    onSwitchChange() {
        if (this.isFlexible) {
            this.updatePlanForm.get('feePerDataUnit').enable({emitEvent: false});
            this.updatePlanForm.get('dataRoundUnit').enable({emitEvent: false});
            this.updatePlanForm.get('downSpeed').enable({emitEvent: false});
            this.updatePlanForm.get('squeezedSpeed').enable({emitEvent: false});
            this.updatePlanForm.get('feeSmsInside').enable({emitEvent: false});
            this.updatePlanForm.get('feeSmsOutside').enable({emitEvent: false});
            this.updatePlanForm.get('maximumFee').enable({emitEvent: false});
        } else {
            this.updatePlanForm.get('feePerDataUnit').disable({emitEvent: false});
            this.updatePlanForm.get('dataRoundUnit').disable({emitEvent: false});
            this.updatePlanForm.get('downSpeed').disable({emitEvent: false});
            this.updatePlanForm.get('squeezedSpeed').disable({emitEvent: false});
            this.updatePlanForm.get('feeSmsInside').disable({emitEvent: false});
            this.updatePlanForm.get('feeSmsOutside').disable({emitEvent: false});
            this.updatePlanForm.get('maximumFee').disable({emitEvent: false});
        }
    }

    submitForm() {
        this.messageCommonService.onload();
        let me = this;
        if (this.updatePlanForm.valid) {
            let data = {...this.updatePlanForm.value};
            if (data.reload) {
                data.reload = 1
            } else {
                data.reload = 0;
            }
            if (data.flexible) {
                data.flexible = 1
                data.uploadSpeed = data.downSpeed - data.squeezedSpeed
            } else {
                data.flexible = 0;
            }
            if (this.selectItemsUser.length > 0){
                let provinceSelected = me.updatePlanForm.get("provinceCode").value;
                let currentSelected = me.selectItemsUser.filter((el) => provinceSelected.includes(el.provinceCode)).map(e => e.id);
                data.userIds = currentSelected;
            }
            this.ratingPlanService.editRatingPlan(this.idForEdit, data, (response) => {
                // this.messageCommonService.success("Update okr")
                this.messageCommonService.success(this.tranService.translate('global.message.saveSuccess'))
                this.router.navigate(['/plans'])
            }, null, () => {
                me.messageCommonService.offload();
            })
        }
    }

    onChangeCustomers() {
        this.updatePlanForm.get("userIds").setValue(this.customerCode)
    }

    ngOnInit() {
        let me = this;
        if (!this.checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN.UPDATE])) {
            window.location.hash = "/access";
        }
        this.idForEdit = Number(this.route.snapshot.params["id"]);

        // this.provinces = [
        //   {
        //     id:"1",
        //     name:"Hà Nội"
        //   },
        //   {
        //     id:"2",
        //     name:"Hồ Chí Minh"
        //   },
        //   {
        //     id:"3",
        //     name:"Nghệ Tĩnh"
        //   },
        // ]
        this.optionTableAddCustomerAccount = {
            hasClearSelected: false,
            hasShowIndex: true,
            hasShowChoose: true,
            hasShowToggleColumn: false,
        };
        this.dataSetAssignPlan = {
            content: [],
            total: 0,
        }
        this.searchInfoUser = {
            username: null,
            fullName: null,
            email: null,
            provinceCode: null
        }
        this.formSearchUser = this.formBuilder.group(this.searchInfoUser);
        this.columnsInfoUser = [
            {
                name: this.tranService.translate("ratingPlan.label.username"),
                key: "username",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("ratingPlan.label.fullName"),
                key: "fullName",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("ratingPlan.label.email"),
                key: "email",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("ratingPlan.label.province"),
                key: "provinceName",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
            },
        ]
        this.accountService.getListProvince((data) => {
            this.provinces = data.map(el => {
                return {
                    code: el.code,
                    name: `${el.name} (${el.code})`
                }
            })
        })

        // this.selectedPaidCategory = this.paidCategories[0].key
        // console.log(this.selectedPaidCategory)

        this.ratingPlanService.getById(this.idForEdit, (response) => {
            me.initialData = response;
            this.updatePlanForm = this.initFormGroup();
            this.updatePlanForm.patchValue(response)
            me.customerCode = response.userIds
            this.setupValidate();

            this.selectedPaidCategory = response.paidType

            if (response.flexible == 1) {
                this.isFlexible = true
                this.onSwitchChange()
            }
            if (response.ratingScope == 1) {
                this.isProvince = true;
                this.isCustomer = true;
                this.updatePlanForm.get("provinceCode").enable({emitEvent: false});
                // this.updatePlanForm.get("userIds").enable({emitEvent: false})
                this.updatePlanForm.get('userIds').setValue(response.userIds)
                this.searchInfoUser.provinceCode = response.provinceCode

                me.accountService.getUserAssignedOnRatingPlan({ratingPlanId: response.id}, (resp) => {
                    this.selectItemsUser = resp;
                    this.selectItemsUserOld = this.selectItemsUser
                })


            } else if (response.ratingScope == 2) {
                this.isProvince = true;
                this.isCustomer = false;
                this.updatePlanForm.get("provinceCode").enable({emitEvent: false});
                // this.updatePlanForm.get("userIds").disable({emitEvent: false})
            }
            if (response.reload == 1) {
                this.isReload = true
            }
            if (response.status == 1 || response.status == 5) {
                me.isRead = true;
                // this.updatePlanForm.controls['code'].disable()
                // this.updatePlanForm.controls['dispatchCode'].disable()

                // this.updatePlanForm.get('code').disable({ emitEvent: false });
                // this.updatePlanForm.get('dispatchCode').disable({ emitEvent: false });
            }
            this.updatePlanForm.updateValueAndValidity();
            me.provinces.forEach(el => {
                if (me.initialData.provinceCode.includes(el.code)) {
                    me.provinceInfo += `${el.name}, `;
                }
            })
            if (me.provinceInfo.length > 0) {
                me.provinceInfo = me.provinceInfo.substring(0, me.provinceInfo.length - 2);
            }
        })
    }

    initFormGroup(): FormGroup {
        return new FormGroup({
            status: new FormControl(this.initialData.status),
            createdDate: new FormControl(this.initialData.createdDate),
            createdBy: new FormControl(this.initialData.createdBy),
            code: new FormControl("", [Validators.required, Validators.maxLength(64), this.customCodeCharacterValidator()], [this.planCodeValidator()]),
            name: new FormControl("", [Validators.required, Validators.maxLength(255), this.customNameCharacterValidator()], this.planNameValidator()),
            dispatchCode: new FormControl("", [Validators.required, Validators.maxLength(64), this.customCodeCharacterValidator()]),
            customerType: new FormControl("", [Validators.required]),
            subscriptionFee: new FormControl(0, [Validators.required, Validators.max(9999999999), Validators.min(0)]),
            paidType: new FormControl("", [Validators.required]),
            ratingScope: new FormControl("", [Validators.required]),
            provinceCode: new FormControl({value: "", disabled: !this.isProvince}, [Validators.required]),
            userIds: new FormControl({value: null}),
            cycleTimeUnit: new FormControl("", [Validators.required]),
            cycleInterval: new FormControl(0, [Validators.required, Validators.max(9999999999), Validators.min(0)]),
            reload: new FormControl(0),
            description: new FormControl("", [Validators.maxLength(255)]),//MS

            limitDataUsage: new FormControl(0, [Validators.required, Validators.max(9999999999), Validators.min(0)]),
            limitSmsInside: new FormControl(0, [Validators.max(9999999999), Validators.min(0)]),//insideSMSFree
            limitSmsOutside: new FormControl(0, [Validators.max(9999999999), Validators.min(0)]),//outsideSMSFree
            dataMax: new FormControl(0, [Validators.required, Validators.max(9999999999), Validators.min(0)]),

            flexible: new FormControl(0),
            feePerDataUnit: new FormControl({
                value: 0,
                disabled: !this.isFlexible
            }, [Validators.required, Validators.max(9999999999), Validators.min(0)]),
            dataRoundUnit: new FormControl({
                value: 0,
                disabled: !this.isFlexible
            }, [Validators.required, Validators.max(9999999999), Validators.min(0)]),
            downSpeed: new FormControl({
                value: 0,
                disabled: !this.isFlexible
            }, [Validators.max(9999999999), Validators.min(0)]),
            squeezedSpeed: new FormControl({
                value: 0,
                disabled: !this.isFlexible
            }, [Validators.max(9999999999), Validators.min(0)]),
            feeSmsInside: new FormControl({
                value: 0,
                disabled: !this.isFlexible
            }, [Validators.max(9999999999), Validators.min(0)]),
            feeSmsOutside: new FormControl({
                value: 0,
                disabled: !this.isFlexible
            }, [Validators.max(9999999999), Validators.min(0)]),
            maximumFee: new FormControl({
                value: 0,
                disabled: !this.isFlexible
            }, [Validators.max(9999999999), Validators.min(0)]),
            uploadSpeed: new FormControl({value: 0, disabled: !this.isFlexible}),
        });
    }

    setupValidate() {
        this.subPlanCode = this.updatePlanForm.get('code').statusChanges.subscribe(() => {
            const errors = this.updatePlanForm.get('code').errors;
            if (errors) {
                this.isPlanCodeValid = true;
            } else {
                this.isPlanCodeValid = false;
            }
        });

        this.subPlaneName = this.updatePlanForm.get('name').statusChanges.subscribe(() => {
            const errors = this.updatePlanForm.get('name').errors;
            if (errors) {
                this.isPlaneNameValid = true;
            } else {
                this.isPlaneNameValid = false;
            }
        });

        this.subDispatchCode = this.updatePlanForm.get('dispatchCode').statusChanges.subscribe(() => {
            const errors = this.updatePlanForm.get('dispatchCode').errors;
            if (errors) {
                this.isDispatchCodeValid = true;
            } else {
                this.isDispatchCodeValid = false;
            }
        });

        this.subCustomerType = this.updatePlanForm.get('customerType').statusChanges.subscribe(() => {
            const errors = this.updatePlanForm.get('customerType').errors;
            if (errors) {
                this.isCustomerTypeValid = true;
            } else {
                this.isCustomerTypeValid = false;
            }
        });

        this.subSubscriptionFee = this.updatePlanForm.get('subscriptionFee').statusChanges.subscribe(() => {
            const errors = this.updatePlanForm.get('subscriptionFee').errors;
            if (errors) {
                this.isSubscriptionFeeValid = true;
            } else {
                this.isSubscriptionFeeValid = false;
            }
        });

        this.subSubscriptionType = this.updatePlanForm.get('paidType').statusChanges.subscribe(() => {
            const errors = this.updatePlanForm.get('paidType').errors;
            if (errors) {
                this.isSubscriptionTypeValid = true;
            } else {
                this.isSubscriptionTypeValid = false;
            }
        });

        this.subPlanScope = this.updatePlanForm.get('ratingScope').statusChanges.subscribe(() => {
            const errors = this.updatePlanForm.get('ratingScope').errors;
            if (errors) {
                this.isPlanScopeValid = true;
            } else {
                this.isPlanScopeValid = false;
            }
        });

        this.subProvinceCode = this.updatePlanForm.get('provinceCode').statusChanges.subscribe(() => {
            const errors = this.updatePlanForm.get('provinceCode').errors;
            if (errors) {
                this.isProvinceCodeValid = true;
            } else {
                this.isProvinceCodeValid = false;
            }
        });

        this.subPlanCycle = this.updatePlanForm.get('cycleTimeUnit').statusChanges.subscribe(() => {
            const errors = this.updatePlanForm.get('cycleTimeUnit').errors;
            if (errors) {
                this.isPlanCycleValid = true;
            } else {
                this.isPlanCycleValid = false;
            }
        });

        this.subDuration = this.updatePlanForm.get('cycleInterval').statusChanges.subscribe(() => {
            const errors = this.updatePlanForm.get('cycleInterval').errors;
            if (errors) {
                this.isDurationValid = true;
            } else {
                this.isDurationValid = false;
            }
        });

        this.subDescription = this.updatePlanForm.get('description').statusChanges.subscribe(() => {
            const errors = this.updatePlanForm.get('description').errors;
            if (errors) {
                this.isDescriptionValid = true;
            } else {
                this.isDescriptionValid = false;
            }
        });

        this.subFreeData = this.updatePlanForm.get('limitDataUsage').statusChanges.subscribe(() => {
            const errors = this.updatePlanForm.get('limitDataUsage').errors;
            if (errors) {
                this.isFreeDataValid = true;
            } else {
                this.isFreeDataValid = false;
            }
        });
        this.subDataMax = this.updatePlanForm.get('dataMax').statusChanges.subscribe(() => {
            const errors = this.updatePlanForm.get('dataMax').errors;
            if (errors) {
                this.isDataMaxValid = true;
            } else {
                this.isDataMaxValid = false;
            }
        });

        this.subLimitInsideSMSFree = this.updatePlanForm.get('limitSmsInside').statusChanges.subscribe(() => {
            const errors = this.updatePlanForm.get('limitSmsInside').errors;
            if (errors) {
                this.isLimitInsideSMSFreeValid = true;
            } else {
                this.isLimitInsideSMSFreeValid = false;
            }
        });

        this.subLimitOutsideSMSFree = this.updatePlanForm.get('limitSmsOutside').statusChanges.subscribe(() => {
            const errors = this.updatePlanForm.get('limitSmsOutside').errors;
            if (errors) {
                this.isLimitOutsideSMSFreeValid = true;
            } else {
                this.isLimitOutsideSMSFreeValid = false;
            }
        });

        this.subFeePerUnitNumberator = this.updatePlanForm.get('feePerDataUnit').statusChanges.subscribe(() => {
            const errors = this.updatePlanForm.get('feePerDataUnit').errors;
            if (errors) {
                this.isFeePerUnitNumberatorValid = true;
            } else {
                this.isFeePerUnitNumberatorValid = false;
            }
        });

        this.subFeePerUnitDenominator = this.updatePlanForm.get('dataRoundUnit').statusChanges.subscribe(() => {
            const errors = this.updatePlanForm.get('dataRoundUnit').errors;
            if (errors) {
                this.isFeePerUnitDenominatorValid = true;
            } else {
                this.isFeePerUnitDenominatorValid = false;
            }
        });

        this.subSqueezeSpeedNumberator = this.updatePlanForm.get('downSpeed').statusChanges.subscribe(() => {
            const errors = this.updatePlanForm.get('downSpeed').errors;
            if (errors) {
                this.isSqueezeSpeedNumberatorValid = true;
            } else {
                this.isSqueezeSpeedNumberatorValid = false;
            }
        });

        this.subSqueezeSpeedDenominator = this.updatePlanForm.get('squeezedSpeed').statusChanges.subscribe(() => {
            const errors = this.updatePlanForm.get('squeezedSpeed').errors;
            if (errors) {
                this.isSqueezeSpeedDenominatorValid = true;
            } else {
                this.isSqueezeSpeedDenominatorValid = false;
            }
        });

        this.subFeePerInsideSMS = this.updatePlanForm.get('feeSmsInside').statusChanges.subscribe(() => {
            const errors = this.updatePlanForm.get('feeSmsInside').errors;
            if (errors) {
                this.isFeePerInsideSMSValid = true;
            } else {
                this.isFeePerInsideSMSValid = false;
            }
        });

        this.subFeePerOutsideSMS = this.updatePlanForm.get('feeSmsOutside').statusChanges.subscribe(() => {
            const errors = this.updatePlanForm.get('feeSmsOutside').errors;
            if (errors) {
                this.isFeePerOutsideSMSValid = true;
            } else {
                this.isFeePerOutsideSMSValid = false;
            }
        });

        this.subMaxFee = this.updatePlanForm.get('maximumFee').statusChanges.subscribe(() => {
            const errors = this.updatePlanForm.get('maximumFee').errors;
            if (errors) {
                this.isMaxFeeValid = true;
            } else {
                this.isMaxFeeValid = false;
            }
        });
    }

    ngOnDestroy(): void {
        if (this.subPlanCode && !this.subPlanCode.closed) {
            this.subPlanCode.unsubscribe();
        }
        if (this.subPlaneName && !this.subPlaneName.closed) {
            this.subPlaneName.unsubscribe();
        }
        if (this.subDispatchCode && !this.subDispatchCode.closed) {
            this.subDispatchCode.unsubscribe();
        }
        if (this.subCustomerType && !this.subCustomerType.closed) {
            this.subCustomerType.unsubscribe();
        }
        if (this.subSubscriptionFee && !this.subSubscriptionFee.closed) {
            this.subSubscriptionFee.unsubscribe();
        }
        if (this.subSubscriptionType && !this.subSubscriptionType.closed) {
            this.subSubscriptionType.unsubscribe();
        }
        if (this.subPlanScope && !this.subPlanScope.closed) {
            this.subPlanScope.unsubscribe();
        }
        if (this.subProvinceCode && !this.subProvinceCode.closed) {
            this.subProvinceCode.unsubscribe();
        }
        if (this.subPlanCycle && !this.subPlanCycle.closed) {
            this.subPlanCycle.unsubscribe();
        }
        if (this.subDuration && !this.subDuration.closed) {
            this.subDuration.unsubscribe();
        }
        if (this.subDescription && !this.subDescription.closed) {
            this.subDescription.unsubscribe();
        }
        if (this.subDescription && !this.subDescription.closed) {
            this.subDescription.unsubscribe();
        }
        if (this.subFreeData && !this.subFreeData.closed) {
            this.subFreeData.unsubscribe();
        }
        if (this.subLimitInsideSMSFree && !this.subLimitInsideSMSFree.closed) {
            this.subLimitInsideSMSFree.unsubscribe();
        }
        if (this.subLimitOutsideSMSFree && !this.subLimitOutsideSMSFree.closed) {
            this.subLimitOutsideSMSFree.unsubscribe();
        }
        if (this.subFeePerUnitNumberator && !this.subFeePerUnitNumberator.closed) {
            this.subFeePerUnitNumberator.unsubscribe();
        }
        if (this.subFeePerUnitDenominator && !this.subFeePerUnitDenominator.closed) {
            this.subFeePerUnitDenominator.unsubscribe();
        }
        if (this.subSqueezeSpeedNumberator && !this.subSqueezeSpeedNumberator.closed) {
            this.subSqueezeSpeedNumberator.unsubscribe();
        }
        if (this.subSqueezeSpeedDenominator && !this.subSqueezeSpeedDenominator.closed) {
            this.subSqueezeSpeedDenominator.unsubscribe();
        }
        if (this.subFeePerInsideSMS && !this.subFeePerInsideSMS.closed) {
            this.subFeePerInsideSMS.unsubscribe();
        }
        if (this.subFeePerOutsideSMS && !this.subFeePerOutsideSMS.closed) {
            this.subFeePerOutsideSMS.unsubscribe();
        }
        if (this.subMaxFee && !this.subMaxFee.closed) {
            this.subMaxFee.unsubscribe();
        }
        if (this.subPlaneName && !this.subPlaneName.closed) {
            this.subPlaneName.unsubscribe();
        }
        if (this.subDispatchCode && !this.subDispatchCode.closed) {
            this.subDispatchCode.unsubscribe();
        }
        if (this.subCustomerType && !this.subCustomerType.closed) {
            this.subCustomerType.unsubscribe();
        }
        if (this.subSubscriptionFee && !this.subSubscriptionFee.closed) {
            this.subSubscriptionFee.unsubscribe();
        }
        if (this.subSubscriptionType && !this.subSubscriptionType.closed) {
            this.subSubscriptionType.unsubscribe();
        }
        if (this.subPlanScope && !this.subPlanScope.closed) {
            this.subPlanScope.unsubscribe();
        }
        if (this.subProvinceCode && !this.subProvinceCode.closed) {
            this.subProvinceCode.unsubscribe();
        }
        if (this.subPlanCycle && !this.subPlanCycle.closed) {
            this.subPlanCycle.unsubscribe();
        }
        if (this.subDuration && !this.subDuration.closed) {
            this.subDuration.unsubscribe();
        }
        if (this.subDescription && !this.subDescription.closed) {
            this.subDescription.unsubscribe();
        }
        if (this.subDescription && !this.subDescription.closed) {
            this.subDescription.unsubscribe();
        }
        if (this.subFreeData && !this.subFreeData.closed) {
            this.subFreeData.unsubscribe();
        }
        if (this.subLimitInsideSMSFree && !this.subLimitInsideSMSFree.closed) {
            this.subLimitInsideSMSFree.unsubscribe();
        }
        if (this.subLimitOutsideSMSFree && !this.subLimitOutsideSMSFree.closed) {
            this.subLimitOutsideSMSFree.unsubscribe();
        }
        if (this.subFeePerUnitNumberator && !this.subFeePerUnitNumberator.closed) {
            this.subFeePerUnitNumberator.unsubscribe();
        }
        if (this.subFeePerUnitNumberator && !this.subFeePerUnitDenominator.closed) {
            this.subFeePerUnitDenominator.unsubscribe();
        }
        if (this.subSqueezeSpeedNumberator && !this.subSqueezeSpeedNumberator.closed) {
            this.subSqueezeSpeedNumberator.unsubscribe();
        }
        if (this.subSqueezeSpeedDenominator && !this.subSqueezeSpeedDenominator.closed) {
            this.subSqueezeSpeedDenominator.unsubscribe();
        }
        if (this.subFeePerInsideSMS && !this.subFeePerInsideSMS.closed) {
            this.subFeePerInsideSMS.unsubscribe();
        }
        if (this.subFeePerOutsideSMS && !this.subFeePerOutsideSMS.closed) {
            this.subFeePerOutsideSMS.unsubscribe();
        }
        if (this.subMaxFee && !this.subMaxFee.closed) {
            this.subMaxFee.unsubscribe();
        }
    }
    onSubmitSearchUser(){
        this.pageNumberAssign = 0;
        this.searchUser(this.pageNumberAssign, this.pageSizeAssign, this.sortAssign, this.searchInfoUser);
    }

    searchUser(page, limit, sort, params){
        let me = this;
        this.pageNumberAssign = page;
        this.pageSizeAssign = limit;
        if (sort == null || sort == undefined){
            this.sortAssign = "id,desc";
            sort = "id,desc";
        }else {
            this.sortAssign = sort;
        }
        let dataParams = {
            page,
            size: limit,
            sort,
            provinceCode: this.listProvince.map(e => e.code)
        }
        Object.keys(this.searchInfoUser).forEach(key => {
            if(this.searchInfoUser[key] != null){
                dataParams[key] = this.searchInfoUser[key];
            }
        })
        if (this.searchInfoUser.provinceCode == null){
            dataParams.provinceCode = this.listProvince.map(e => e.code);
        }
        this.selectItemsUserOld = [...this.selectItemsUser]
        this.ratingPlanService.getUserToAddAccount(dataParams, (response) => {
            me.dataSetAssignPlan = {
                content: response.content,
                total: response.totalElements
            }
            this.selectItemsUser = [...this.selectItemsUserOld]
        })

    }

    openDialogAddCustomerAccount() {
        let provincesSelected = this.updatePlanForm.get('provinceCode').value;
        this.listProvince = this.provinces.filter((prov) =>
            provincesSelected.includes(prov.code)
        )
        if (this.pageNumberAssign == null){
            this.pageNumberAssign = 0;
        }
        if (this.pageSizeAssign == null){
            this.pageSizeAssign = 10;
        }
        if (this.sortAssign == null){
            this.sortAssign = "id,desc";
        }
        this.searchInfoUser.provinceCode = this.listProvince.map(e => e.code);
        this.searchUser(this.pageNumberAssign, this.pageSizeAssign, this.sortAssign, this.searchInfoUser)
        this.isShowDialogAddCustomerAccount = true
    }
    changeProvince() {
        let provinceSelected = this.updatePlanForm.get("provinceCode").value
        let ratingScope = this.updatePlanForm.get("ratingScope").value
        if (ratingScope == 1){
            if (provinceSelected.length > 0){
                this.isCustomer = true;
            }else {
                this.isCustomer = false;
            }
        }
    }
}
