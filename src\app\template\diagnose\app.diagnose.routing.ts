import { RouterModule } from '@angular/router';
import { NgModule } from '@angular/core';
import {DiagnoseComponent} from "./app.diagnose.comppnent";
import DataPage from "../../service/data.page";
import {CONSTANTS} from "../../service/comon/constants";


@NgModule({
    imports: [
        RouterModule.forChild([
            { path: '', component: DiagnoseComponent, data: new DataPage("diagnose.titlepage.seardDiagnose", [CONSTANTS.PERMISSIONS.DIAGNOSE.VIEW_LIST])},
        ]),
    ],
    exports: [RouterModule],
})
export class AppSimRoutingModule {}
