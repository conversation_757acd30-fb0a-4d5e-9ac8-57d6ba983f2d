<div style="position: relative; background-image: url('assets/images/login/login_moi.jpg');background-size: cover;" class="surface-ground flex flex-column align-items-center justify-content-center min-h-screen min-w-screen overflow-hidden">
    <div class="flex flex-column align-items-center justify-content-center" style="position: relative">
        <div class="marquee-div" style="display: flex; position: absolute; top: -150px; left: 0px;
    justify-content: center; height: 200px; align-items: center; width: 100%;">

            <marquee width="100%" direction="left" style="
            color: white;
            font-size: 1.3rem;
            align-items: center;">
                Chúng tôi xin thông báo giao diện mới đang được thử nghiệm từ ngày 09/07/2024. <PERSON>u<PERSON> khách hàng vui lòng trải nghiệm và góp ý để chúng tôi hoàn thiện hơn. Xin cảm ơn!
            </marquee>
        </div>
        <!-- <img src="assets/images/m2m.png" alt="ONEIOT Platform logo" class="mb-5 w-20rem flex-shrink-0">                 -->
        <div>
            <div class="w-full surface-card py-8 px-5 sm:px-8" style="border-radius:25px;position: relative;background-color: rgba(225,225,225,85%) !important;;">
                <choose-language style="position: absolute;right: 28px;top: 30px;"></choose-language>
                <div class="mb-5">
                    <div class="text-900 text-2xl font-medium mb-2">{{tranService.translate('login.label.m2mTitle')}}</div>
<!--                    <p>-->
<!--                        <span>Power by VNPT-Technology</span>-->
<!--                    </p>-->
                </div>

                <div>
                    <label for="email1" class="block text-900 text-xl font-medium mb-2">{{tranService.translate("login.label.email")}}</label>
                    <input id="email1" type="text" autocomplete="off webauthn" [(ngModel)]="loginInfo.email" [placeholder]="tranService.translate('login.label.email')" pInputText class="w-full md:w-30rem mb-5" style="padding:1rem">

                    <label for="password1" class="block text-900 font-medium text-xl mb-2">{{tranService.translate("login.label.password")}}</label>
                    <p-password id="password1" autocomplete="off webauthn" [(ngModel)]="loginInfo.password" [feedback]=false [placeholder]="tranService.translate('login.label.password')" [toggleMask]="true" styleClass="mb-5 label-password" inputStyleClass="w-full p-3 md:w-30rem" (keyup.enter)="login(loginInfo)"></p-password>

                    <div class="flex align-items-center justify-content-between mb-5 gap-5">
                        <div class="flex align-items-center">
                            <!-- <p-checkbox id="rememberme1" [binary]="true" styleClass="mr-2"></p-checkbox>
                            <label for="rememberme1">Remember me</label> -->
                            <p>
                                <span>© 2023 Internet of Things Application</span>
                            </p>
                        </div>
                        <a class="font-medium no-underline ml-2 text-right cursor-pointer" (click)="showDialogForgotPass()" style="color: var(--primary-color)">{{tranService.translate("login.label.forgotPass")}}?</a>
                    </div>
                    <button type="submit" pButton pRipple [label]="tranService.translate('login.label.signIn')" class="w-full p-3 text-xl bg-blue-800"  (click)="login(loginInfo)"></button>
                </div>
            </div>
        </div>
    </div>
    <div style="
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: #021c34;
    color: white;
    padding: 12px 16px;
    border-radius: 999px;
    display: flex;
    align-items: center;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    z-index: 10000;
    cursor: pointer;
">
        <i class="fas fa-phone-volume" style="font-size: 20px; margin-right: 8px;"></i>
        <span style="font-size: 15px; font-weight: bold;">Hotline: 1800 1091</span>
    </div>
</div>
<!-- dialog -->
<div class="flex justify-content-center dialog-forgot-pass">
    <p-dialog [header]="tranService.translate('login.label.forgotPass')" [(visible)]="isShowDialogForgotPass" [modal]="true" [style]="{ width: '500px' }" [draggable]="false" [resizable]="false">
        <form [formGroup]="formForgot" (ngSubmit)="resetPasswordEmail(forgotInfo)">
            <div class="field">
                <input id="email1" type="text" [(ngModel)]="forgotInfo.email"
                [placeholder]="tranService.translate('login.label.email')"
                pInputText
                formControlName="email"
                class="w-full mb-5"
                [required]="true"
                [maxLength]="255"
                pattern="^[a-z0-9]+[a-z0-9\-\._]*[a-z0-9]+@([a-z0-9]+[a-z0-9\-\._]*[a-z0-9]+)+(\.[a-z]{2,})$"
                >
            <!-- error email -->
            <div class="w-full field grid text-error-field">
                <div class="col">
                    <small class="text-red-500" *ngIf="formForgot.controls.email.dirty && formForgot.controls.email.errors?.required">{{tranService.translate("global.message.required")}}</small>
                    <small class="text-red-500" *ngIf="formForgot.controls.email.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:255})}}</small>
                    <small class="text-red-500" *ngIf="formForgot.controls.email.errors?.pattern">{{tranService.translate("global.message.invalidEmail")}}</small>
                </div>
            </div>

            <button type="submit" pButton pRipple [label]="tranService.translate('login.label.resetPass')" class="w-full p-3 text-md bg-blue-800"></button>
            </div>
        </form>
    </p-dialog>
</div>

<!-- dialog captcha -->
<div class="flex justify-content-center dialog-forgot-pass" *ngIf="isShowDialogCaptcha">
    <p-dialog header="Captcha" [(visible)]="isShowDialogCaptcha" [modal]="true" [style]="{ width: '500px' }" [draggable]="false" [resizable]="false">
        <div class="flex flex-row justify-content-center" >
            <re-captcha
                siteKey="6LeHexUqAAAAAO-nOLQ7WyF5CyQLars1i9oHE3Hp"
                hl="vi"
                (resolved)="handleCaptchaResolved($event)"
            ></re-captcha>
        </div>
    </p-dialog>
</div>
