<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{tranService.translate("recharge.label.menu")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
    <div class="col-5 flex flex-row justify-content-end align-items-center">
        <p-button [label]="tranService.translate('global.button.import')" (click)="importByFile()" styleClass="p-button-success" ></p-button>
    </div>
</div>
<div class="flex justify-content-center dialog-push-group">
    <p-dialog [header]="tranService.translate('recharge.label.menu')" [(visible)]="isShowDialogRecharge" [modal]="true" [style]="{ width: '700px' }" [draggable]="false" [resizable]="false">
        <div class="w-full field grid">
            <div class="col-10 flex flex-row justify-content-start align-items-center">
                <input-file-vnpt class="w-full" [(fileObject)]="fileObject" [clearFileCallback]="clearFileCallback.bind(this)"
                                 [options]="optionInputFile"
                ></input-file-vnpt>
                <div class="col-3 flex flex-row justify-content-start align-items-center">
                    <p-dropdown [options]="topupValue" [(ngModel)]="rechargeMoney.rechargeAmount" [showClear]="true" placeholder="{{tranService.translate(recharge.label.topupValue)}}"></p-dropdown>
                </div>
            </div>

            <div class="col-2 flex flex-row justify-content-end align-items-center">
                <p-button icon="pi pi-download" [pTooltip]="tranService.translate('global.button.downloadTemp')" styleClass="p-button-outlined p-button-secondary" (click)="downloadTemplate()"></p-button>
            </div>
        </div>
        <p-table
            [paginator]="true"
            [rows]="pageSizeSimImport"
            [first]="rowFirstSimImport"
            [showCurrentPageReport]="true"
            [tableStyle]="{ 'min-width': '100%' }"
            [currentPageReportTemplate]="tranService.translate('global.text.templateTextPagination')"
            (onPage)="pagingResultSimImport($event)"
            [rowsPerPageOptions]="[5,10,20]"
            [styleClass]="'p-datatable-sm'"
            [totalRecords]="simImportsOrigin?.length"
            [lazy]="true"
            #dataTable
            [scrollHeight]="'400px'"
            [value]="simImports"
            dataKey="id"
            [tableStyle]="{ 'min-width': '50rem' }"
            *ngIf="simImportsOrigin">
            <ng-template pTemplate="header">
                <tr>
                    <th style="min-width:150px;max-width:150px">{{tranService.translate("recharge.label.msisdn")}}</th>
                    <th style="min-width:150px;max-width:150px">{{tranService.translate("recharge.label.topupValue")}}</th>
                    <th style="min-width: 250px;max-width: 250px;">{{tranService.translate("recharge.label.content")}}</th>
                    <th style="min-width:70px;max-width:70px; text-align: center;">{{tranService.translate("global.text.action")}}</th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-simImport let-editing="editing" let-i="rowIndex">
                <tr [formGroup]="mapFormSimImports[simImport.keyForm]">
                    <td style="min-width:150px;max-width:150px" [pEditableColumn]="simImport.msisdn" pEditableColumnField="msisdn">
                        <p-cellEditor>
                            <ng-template pTemplate="input">
                                <input formControlName="msisdn"
                                       style="min-width:150px;max-width:150px"
                                       pInputText type="text"
                                       [(ngModel)]="simImport.msisdn"
                                       required
                                       maxlength="20"
                                       pattern="^(\+?84)[1-9][0-9]{8,9}$"
                                       (ngModelChange)="checkValueSimImportChange(simImport)"
                                />
                            </ng-template>
                            <ng-template pTemplate="output">
                                {{ simImport.msisdn }}
                            </ng-template>
                        </p-cellEditor>
                    </td>
                    <td style="min-width:150px;max-width:150px" [pEditableColumn]="simImport.rechargeAmount" pEditableColumnField="rechargeAmount">
                        <p-cellEditor>
                            <ng-template pTemplate="input">
                                <input formControlName="rechargeAmount"
                                       style="min-width:150px;max-width:150px"
                                       pInputText type="text"
                                       [(ngModel)]="simImport.rechargeAmount"
                                       required
                                       maxlength="50"
                                       pattern="^[a-zA-Z0-9\-_]*$"
                                       (ngModelChange)="checkValueSimImportChange(simImport)"
                                       [readonly]="true"
                                />
                            </ng-template>
                            <ng-template pTemplate="output">
                                {{ simImport.rechargeAmount }}
                            </ng-template>
                        </p-cellEditor>
                    </td>
                    <td style="min-width:200px;max-width:200px">
                            <span *ngIf="!mapFormSimImports[simImport.keyForm].invalid"
                            >{{ tranService.translate(simImport.description) }}</span>
                        <!-- ca hai cung trong -->
                        <span *ngIf="(mapFormSimImports[simImport.keyForm].controls.msisdn.hasError('required') && mapFormSimImports[simImport.keyForm].controls.rechargeAmount.hasError('required'))"
                        >
                                {{tranService.translate("global.message.required")}}
                            </span>
                        <!-- format so thue bao -->
                        <span *ngIf="mapFormSimImports[simImport.keyForm].controls.msisdn.errors?.pattern"
                        >
                                {{tranService.translate("global.message.invalidSubsciption")}}
                            </span>
                    </td>
                    <td style="min-width:100px;max-width:100px;text-align: center;">
                        <span [pTooltip]="tranService.translate('global.button.delete')" class="pi pi-trash" (click)="removeItemSimImport(simImport,i)"></span>
                    </td>
                </tr>
            </ng-template>
        </p-table>
        <div class="flex flex-row justify-content-center align-items-center">
            <p-button styleClass="mr-2 p-button-secondary p-button-outlined" [label]="tranService.translate('global.button.cancel')" (click)="isShowDialogRecharge = false"></p-button>
<!--            <p-button [disabled]="!checkValidListImport()" *ngIf="simImports" styleClass="p-button-info" [label]="tranService.translate('global.button.save')" (click)="registerForFile()"></p-button>-->
        </div>
    </p-dialog>
</div>

<form [formGroup]="formSearchRecharge" (ngSubmit)="onSearch()" class="pb-2 pt-3 vnpt-field-set">
    <p-panel [toggleable]="true" [header]="tranService.translate('recharge.label.menu')">
        <div class="grid">
            <!-- Phương thức thanh toán -->
            <div class="col-3">
                <span class="p-float-label">
                    <input pInputText
                           class="w-full"
                           pInputText id="paymentMethod"
                           [(ngModel)]="searchInfo.paymentMethod"
                           formControlName="paymentMethod"
                    />
                    <label htmlFor="paymentMethod">{{tranService.translate("recharge.label.paymentMethod")}}</label>
                </span>
            </div>
            <!-- Tổng thanh toán -->
            <div class="col-3">
                <span class="p-float-label">
                    <input class="w-full"
                           pInputText id="totalAmount"
                           [(ngModel)]="searchInfo.totalAmount"
                           formControlName="totalAmount"
                           type="number"
                    />
                    <label htmlFor="totalAmount">{{tranService.translate("recharge.label.totalAmount")}}</label>
                </span>
            </div>
            <!-- Trạng thái thanh toán -->
            <div class="col-3">
                <span class="p-float-label">
                    <p-dropdown styleClass="w-full" [showClear]="true"
                                id="status" [autoDisplayFirst]="false"
                                [(ngModel)]="searchInfo.status"
                                formControlName="status"
                                [options]="rechargeStatus"
                                optionLabel="name"
                                optionValue="value"
                    ></p-dropdown>
                    <label for="status">{{tranService.translate("recharge.label.status")}}</label>
                </span>
            </div>
            <div class="col-3 pb-0">
                <span class="p-float-label">
                    <p-calendar styleClass="w-full"
                                id="fromDate"
                                [(ngModel)]="searchInfo.fromDate"
                                formControlName="fromDate"
                                [showIcon]="true"
                                [showClear]="true"
                                dateFormat="dd/mm/yy"
                                [maxDate]="maxDateFrom"
                                (onSelect)="onChangeDateFrom(searchInfo.fromDate)"
                                (onInput)="onChangeDateFrom(searchInfo.fromDate)"
                    ></p-calendar>
                    <label htmlFor="fromDate">{{tranService.translate("recharge.label.fromDate")}}</label>
                </span>
            </div>
            <div class="col-3 pb-0">
                <span class="p-float-label">
                    <p-calendar styleClass="w-full"
                                id="toDate"
                                [(ngModel)]="searchInfo.toDate"
                                formControlName="toDate"
                                [showIcon]="true"
                                [showClear]="true"
                                dateFormat="dd/mm/yy"
                                [minDate]="minDateTo"
                                [maxDate]="maxDateTo"
                                (onSelect)="onChangeDateTo(searchInfo.toDate)"
                                (onInput)="onChangeDateTo(searchInfo.toDate)"
                    />
                    <label htmlFor="toDate">{{tranService.translate("recharge.label.toDate")}}</label>
                </span>
            </div>
            <div class="col-3 pb-0">
                <p-button icon="pi pi-search"
                          styleClass="ml-3 p-button-rounded p-button-secondary p-button-text button-search"
                          type="submit"
                ></p-button>
            </div>
        </div>
    </p-panel>
</form>



<table-vnpt
    [fieldId]="'id'"
    [columns]="columns"
    [dataSet]="dataSet"
    [loadData]="search.bind(this)"
    [pageNumber]="pageNumber"
    [pageSize]="pageSize"
    [sort]="sort"
    [params]="searchInfo"
    [labelTable]="this.tranService.translate('recharge.label.menu')"
></table-vnpt>
