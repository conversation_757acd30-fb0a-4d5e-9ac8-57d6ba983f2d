<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{tranService.translate("global.menu.alertList")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
    <div class="col-5 flex flex-row justify-content-end align-items-center">
        <!--        <p-button styleClass="p-button-info" [label]="tranService.translate('global.button.create')" icon="" [routerLink]="['/alert/create']" routerLinkActive="router-link-active" ></p-button>-->
        <div class="flex flex-row justify-content-center gap-3 p-2">
            <button pButton type="submit" class="" style="" [routerLink]="['/alerts/edit/', alertId]"  [label]="tranService.translate('global.button.edit')" icon="pi pi-pencil" *ngIf="checkAuthen([CONSTANTS.PERMISSIONS.ALERT.UPDATE]) && alertInfo.status == CONSTANTS.ALERT_STATUS.INACTIVE"></button>
            <button pButton class="p-button-danger" type="submit" style="" [label]="tranService.translate('global.button.delete')" icon="pi pi-trash"  (click)="deleteAlert()" *ngIf="checkAuthen([CONSTANTS.PERMISSIONS.ALERT.DELETE]) && alertInfo.status == CONSTANTS.ALERT_STATUS.INACTIVE"></button>
        </div>
    </div>
</div>

<p-card class="p-4" >
    <form action="" [formGroup]="formAlert">
        <div class="p-3 pt-0 shadow-2 border-round-md m-1 flex p-fluid p-formgrid grid">
            <!-- ten canh bao -->
            <div class="col-4 flex flex-row justify-content-between align-items-center pb-0">
                <label htmlFor="name" style="width:90px">{{tranService.translate("alert.label.name")}}<span class="text-red-500">*</span></label>
                <div style="width: calc(100% - 90px)" class="relative">
                    <input class="w-full"
                           pInputText id="name"
                           [(ngModel)]="alertInfo.name"
                           formControlName="name"
                           [required]="true"
                           [maxLength]="255"
                           pattern="^[a-zA-Z0-9\-_]*$"
                           [placeholder]="tranService.translate('alert.text.inputName')"
                    />
                </div>
            </div>
            <!-- loai -->
            <div class="col-4 flex flex-row justify-content-between align-items-center pb-0">
                <label for="ruleCategory" style="width:90px">{{tranService.translate("alert.label.rule")}}<span class="text-red-500">*</span></label>
                <div style="width: calc(100% - 90px)">
                    <p-dropdown styleClass="w-full"
                                id="ruleCategory" [autoDisplayFirst]="false"
                                [(ngModel)]="alertInfo.ruleCategory"
                                [required]="true"
                                formControlName="ruleCategory"
                                [options]="ruleOptions"
                                optionLabel="name"
                                optionValue="value"
                                [placeholder]="tranService.translate('alert.text.rule')"
                    ></p-dropdown>
                </div>
            </div>
            <!-- dieu kien kich hoat -->
            <div class="col-4 flex flex-row justify-content-between align-items-center pb-0">
                <label for="eventType" style="width:90px">{{tranService.translate("alert.label.event")}}<span class="text-red-500">*</span></label>
                <div style="width: calc(100% - 90px)">
                    <p-dropdown *ngIf="alertInfo.ruleCategory == CONSTANTS.ALERT_RULE_CATEGORY.MANAGEMENT" styleClass="w-full"
                                class="left-side"
                                id="eventType" [autoDisplayFirst]="false"
                                [(ngModel)]="alertInfo.eventType"
                                [required]="true"
                                formControlName="eventType"
                                [options]="eventOptionManagement"
                                optionLabel="name"
                                optionValue="value"
                                [placeholder]="tranService.translate('alert.text.eventType')"
                                [virtualScroll]="false"
                    ></p-dropdown>
                    <p-dropdown *ngIf="alertInfo.ruleCategory == CONSTANTS.ALERT_RULE_CATEGORY.MONITORING" styleClass="w-full"
                                class="left-side"
                                id="eventType" [autoDisplayFirst]="false"
                                [(ngModel)]="alertInfo.eventType"
                                [required]="true"
                                formControlName="eventType"
                                [options]="eventOptionMonitoring"
                                optionLabel="name"
                                optionValue="value"
                                [placeholder]="tranService.translate('alert.text.eventType')"
                                [virtualScroll]="false"
                    ></p-dropdown>
                </div>
            </div>
            <div class="col-4 flex flex-row p-0 w-full" *ngIf="formAlert.controls.name.invalid || formAlert.controls.severity.invalid || formAlert.controls.statusSim.invalid || isAlertNameExisted">
                <div class="flex-1 py-0 col-4 flex flex-row justify-content-between align-items-center w-full" style="height: fit-content"
                     *ngIf="formAlert.controls.name.invalid || formAlert.controls.severity.invalid || formAlert.controls.statusSim.invalid || isAlertNameExisted">
                    <label htmlFor="name" style="width:130px; height: fit-content"></label>
                    <div style="width: calc(100% - 130px)">
                        <small class="text-red-500" *ngIf="formAlert.controls.name.dirty && formAlert.controls.name.errors?.required">{{tranService.translate("global.message.required")}}</small>
                        <small class="text-red-500" *ngIf="formAlert.controls.name.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:255})}}</small>
                        <small class="text-red-500" *ngIf="formAlert.controls.name.errors?.pattern">{{tranService.translate("global.message.formatCode")}}</small>
                        <small class="text-red-500" *ngIf="isAlertNameExisted">{{tranService.translate("global.message.exists",{type: tranService.translate("alert.label.name").toLowerCase()})}}</small>
                    </div>
                </div>

                <div class="flex-1 py-0 col-4 flex flex-row justify-content-between align-items-center w-full" style="height: fit-content"
                     *ngIf="formAlert.controls.name.invalid || formAlert.controls.severity.invalid || formAlert.controls.statusSim.invalid || isAlertNameExisted">
                    <label htmlFor="severity" style="width:90px; height: fit-content"></label>
                    <div style="width: calc(100% - 90px)">
                        <small class="text-red-500" *ngIf="formAlert.controls.severity.dirty && formAlert.controls.severity.errors?.required">{{tranService.translate("global.message.required")}}</small>
                    </div>
                </div>

                <div class="flex-1 py-0 col-4 flex flex-row justify-content-between align-items-center w-full" style="height: fit-content"
                     *ngIf="formAlert.controls.name.invalid || formAlert.controls.severity.invalid || formAlert.controls.statusSim.invalid || isAlertNameExisted">
                    <label htmlFor="statusSim"  style="width:150px; height: fit-content"></label>
                    <div style="width: calc(100% - 150px)">
                        <small class="text-red-500" *ngIf="formAlert.controls.statusSim.dirty && formAlert.controls.statusSim.errors?.required">{{tranService.translate("global.message.required")}}</small>
                    </div>
                </div>
            </div>
            <!-- muc do -->
            <div class="col-4 flex flex-row justify-content-between align-items-center pb-0 pt-3">
                <label for="severity" style="width:90px">{{tranService.translate("alert.label.level")}}<span class="text-red-500">*</span></label>
                <div style="width: calc(100% - 90px)">
                    <p-dropdown styleClass="w-full"
                                id="severity" [autoDisplayFirst]="false"
                                [(ngModel)]="alertInfo.severity"
                                [required]="true"
                                formControlName="severity"
                                [options]="severityOptions"
                                optionLabel="name"
                                optionValue="value"
                                [placeholder]="tranService.translate('alert.text.inputlevel')"
                    ></p-dropdown>
                </div>
            </div>
            <div class="col-4 flex flex-row p-0 w-full" *ngIf="formAlert.controls.name.invalid || formAlert.controls.severity.invalid || formAlert.controls.statusSim.invalid || isAlertNameExisted">
                <!-- error muc do -->
                <div class="flex-1 py-0 col-4 flex flex-row justify-content-between align-items-center w-full" style="height: fit-content"
                     *ngIf="formAlert.controls.name.invalid || formAlert.controls.severity.invalid || formAlert.controls.statusSim.invalid || isAlertNameExisted">
                    <label htmlFor="severity" style="width:90px; height: fit-content"></label>
                    <div style="width: calc(100% - 90px)">
                        <small class="text-red-500" *ngIf="formAlert.controls.severity.dirty && formAlert.controls.severity.errors?.required">{{tranService.translate("global.message.required")}}</small>
                    </div>
                </div>
            </div>
            <!-- trang thai -->
            <div class="col-4 flex flex-row align-items-center pb-0 pt-3">
                <label for="status" style="width:90px">{{tranService.translate("alert.label.status")}}</label>
                <div style="width: calc(100% - 90px);" class="flex flex-row align-items-center">
                    <span *ngIf="statusAlert.ACTIVE == statusTemp" [class]="['p-2','text-green-800', 'bg-green-100','border-round','inline-block']">{{tranService.translate("alert.status.active")}}</span>
                    <span *ngIf="statusAlert.INACTIVE == statusTemp" [class]="['p-2', 'text-red-700', 'bg-red-100', 'border-round','inline-block']">{{tranService.translate("alert.status.inactive")}}</span>
                    <p-inputSwitch *ngIf="checkAuthen([CONSTANTS.PERMISSIONS.ALERT.UPDATE])" pTooltip="{{alertInfo.status == CONSTANTS.ALERT_STATUS.ACTIVE?tranService.translate('alert.label.inactivePopup') : tranService.translate('alert.label.activePopup')}}" tooltipPosition="right" tooltipStyleClass="absolute"
                                   class="ml-4 mt-2" (onChange)="onChangeStatus($event)"
                                   [trueValue]="statusAlert.ACTIVE" [falseValue]="statusAlert.INACTIVE" [(ngModel)]="alertInfo.status" formControlName="status"/>
                </div>
            </div>
            <!-- mo ta -->
            <div class="col-8 flex flex-row justify-content-between align-items-center pt-3">
                <label htmlFor="description" style="width:90px">{{tranService.translate("alert.label.description")}}</label>
                <div style="width: calc(100% - 90px)">
                    <input class="w-full"
                           pInputText id="description"
                           [(ngModel)]="alertInfo.description"
                           formControlName="description"
                           [maxLength]="255"
                           [placeholder]="tranService.translate('alert.text.inputDescription')"
                    />
                </div>
            </div>

        </div>

        <h4 class="ml-2">{{tranService.translate("alert.text.filterApplieInfo")}}</h4>
        <div *ngIf="alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP" class="p-3 pt-0 shadow-2 border-round-md m-1 flex p-fluid p-formgrid grid">
            <!-- khach hang -->
            <div class="col-4 flex flex-row justify-content-between align-items-center pb-0">
                <label for="customerId"  style="width:130px">{{tranService.translate("alert.label.customer")}}<span class="text-red-500">*</span></label>
                <div style="width: calc(100% - 130px)">
                    {{alertInfo?.customerName + ' - ' + alertInfo?.customerCode}}
                </div>
            </div>
            <!-- nhom thue bao -->
            <div class="col-4 flex flex-row justify-content-between align-items-center pb-0">
                <label for="groupId" style="width:150px">{{tranService.translate("alert.label.group")}}<span class="text-red-500">*</span></label>
                <div style="width: calc(100% - 150px)">
                   {{alertInfo.groupName}}
                </div>
            </div>
            <!--so thue bao -->
            <div class="col-4 flex flex-row justify-content-between align-items-center pb-0">
                <label for="subscriptionNumber" style="width:130px">{{tranService.translate("alert.label.subscriptionNumber")}}<span class="text-red-500">*</span></label>
                <div style="width: calc(100% - 130px)">
                    {{alertInfo.subscriptionNumber}}
                </div>
            </div>
            <div class="col-4 flex flex-row p-0 w-full" *ngIf="comboSelectCustomerControl.error.required || comboSelectSubControl.error.required || comboSelectGroupSubControl.error.required">
                <!-- error khach hang -->
                <div class="flex-1 py-0 col-4 flex flex-row justify-content-between align-items-center w-full" style="height: fit-content">
                    <label htmlFor="customerId" class="col-fixed py-0" style="width:130px"></label>
                    <div style="width: calc(100% - 130px)" class="py-0">
                        <small class="text-red-500" *ngIf="comboSelectCustomerControl.dirty && comboSelectCustomerControl.error.required">{{tranService.translate("global.message.required")}}</small>
                    </div>
                </div>
                <!-- error nhom thue bao -->
                <div class="flex-1 py-0 col-4 flex flex-row justify-content-between align-items-center w-full" style="height: fit-content">
                    <label htmlFor="groupId" class="col-fixed p-0" style="width:130px"></label>
                    <div style="width: calc(100% - 150px)" class="py-0">
                        <small class="text-red-500" *ngIf="comboSelectSubControl.dirty && comboSelectSubControl.error.required">{{tranService.translate("global.message.required")}}</small>
                    </div>
                </div>
                <!-- error so thue bao -->
                <div class="flex-1 py-0 col-4 flex flex-row justify-content-between align-items-center w-full" style="height: fit-content">
                    <label htmlFor="subscriptionNumber" class="col-fixed p-0" style="width:130px"></label>
                    <div style="width: calc(100% - 150px)" class="py-0">
                        <small class="text-red-500" *ngIf="comboSelectGroupSubControl.dirty && comboSelectGroupSubControl.error.required">{{tranService.translate("global.message.required")}}</small>
                    </div>
                </div>
            </div>
            <!-- gia tri -->
            <div class="col-4 flex flex-row gap-3 justify-content-start align-items-center pb-0"
                 [class]="alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_VALUE ||
            alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_PACKAGE ||
            alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_VALUE ||
            alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_PACKAGE? '' : 'hidden'" >
                <label *ngIf="alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_PACKAGE" for="value">{{tranService.translate("alert.label.exceededPakage")}}<span class="text-red-500">*</span></label>
                <label *ngIf="alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_VALUE" for="value">{{tranService.translate("alert.label.exceededValue")}}<span class="text-red-500">*</span></label>
                <label *ngIf="alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_PACKAGE" for="value">{{tranService.translate("alert.label.smsExceededPakage")}}<span class="text-red-500">*</span></label>
                <label *ngIf="alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_VALUE" for="value">{{tranService.translate("alert.label.smsExceededValue")}}<span class="text-red-500">*</span></label>
                <div style="width: 80px">
                   {{alertInfo.value}}
                </div>
            </div>
            <div class="col-4 flex flex-row p-0 w-full" *ngIf="isPlanExisted || formAlert.controls.value.dirty && formAlert.controls.value.errors?.max">
                <div *ngIf="alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_PACKAGE|| alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_PACKAGE" class="flex-1 p-0 col-4 flex flex-row justify-content-between align-items-center w-full" style="height: fit-content">
                    <label htmlFor="groupId" class="col-fixed py-0" style="width:150px"></label>
                    <div class="col py-0">
                        <small class="text-red-500" *ngIf="isPlanExisted">{{tranService.translate("alert.message.existedPlan")}}</small>
                    </div>
                </div>
                <div class="flex-1 p-0 col-4 flex flex-row justify-content-between align-items-center w-full" style="height: fit-content">
                    <label htmlFor="customerId" class="col-fixed py-0"></label>
                    <div style="width: 80" class="py-0">
                        <small class="text-red-500" *ngIf="formAlert.controls.value.dirty && formAlert.controls.value.errors?.max">{{tranService.translate("global.message.twentydigitlength")}}</small>
                    </div>
                </div>
                <div class="flex-1 p-0 col-4 flex flex-row justify-content-between align-items-center w-full" style="height: fit-content" *ngIf="alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_VALUE || alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_PACKAGE"></div>
                <div class="flex-1 p-0 col-4 flex flex-row justify-content-between align-items-center w-full" style="height: fit-content">
                    <label htmlFor="groupId" class="col-fixed py-0" style="width:150px"></label>
                    <div class="col py-0"></div>
                </div>
            </div>
        </div>

        <div *ngIf="alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP" class="pb-3 shadow-2 border-round-md m-1 flex p-fluid p-formgrid grid">
            <!-- goi cuoc dang dung -->
            <div class="col-4 pb-0 flex flex-row justify-content-between align-items-center">
                <label for="appliedPlan"  style="width:150px">{{tranService.translate("alert.label.appliedPlan")}}<span class="text-red-500">*</span></label>
                <div style="width: calc(100% - 150px)">
                    <p-multiSelect styleClass="w-full"
                                   id="appliedPlan" [autoDisplayFirst]="false"
                                   [(ngModel)]="alertInfo.appliedPlan"
                                   formControlName="appliedPlan"
                                   [options]="appliedPlanOptions"
                                   [filter]="true"
                                   filterBy="code"
                                   [placeholder]="tranService.translate('alert.text.appliedPlan')"
                                   optionLabel="code"
                                   optionValue="code"
                                   [required]="true"
                    ></p-multiSelect>
                    <small class="text-red-500" *ngIf="isPlanExisted">{{tranService.translate("alert.message.existedPlan")}}</small>
                    <small class="text-red-500" *ngIf="formAlert.controls.appliedPlan.dirty && formAlert.controls.appliedPlan.errors?.required">{{tranService.translate("global.message.required")}}</small>
                </div>
            </div>
        </div>

        <div class="ml-2 my-4 flex flex-row justify-content-start align-items-center gap-3">
            <h4 for="actionType" class="mb-0">{{tranService.translate("alert.label.action")}}</h4>
            <div>
                <p-dropdown styleClass="w-full"
                            id="actionType" [autoDisplayFirst]="false"
                            [(ngModel)]="alertInfo.actionType"
                            [required]="true"
                            formControlName="actionType"
                            [options]="actionOptions"
                            optionLabel="name"
                            optionValue="value"
                            [placeholder]="tranService.translate('alert.text.actionType')"
                ></p-dropdown>
            </div>
        </div>
        <div [class]="alertInfo.actionType == CONSTANTS.ALERT_ACTION_TYPE.ALERT ? '' : 'hidden'" class="pt-0 shadow-2 border-round-md m-1 flex flex-column p-fluid p-formgrid grid">
            <div class="flex flex-row gap-4">
                <div class="flex-1">
                    <div *ngIf="alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP" class="col-12 flex flex-row justify-content-start align-items-center pt-4 pr-4">
                        <label class="col-fixed" htmlFor="value">{{tranService.translate("alert.text.sendNotifyExpiredData")}}</label>
                        <div>
                            <input  class="w-full" style="resize: none;"
                                    rows="5"
                                    pInputText
                                    [autoResize]="false"
                                    pInputTextarea id="value"
                                    [(ngModel)]="alertInfo.value"
                                    formControlName="value"
                                    type="number"
                            />
                        </div>
                        <label class="col-fixed" htmlFor="value">{{tranService.translate("alert.text.day")}}</label>
                    </div>
                </div>
                <div class="flex-1" *ngIf="alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP">
                    <div class="col-12 flex flex-row justify-content-start align-items-center pb-0">
                        <div>
                            <p-checkbox
                                    [(ngModel)]="repeat"
                                    formControlName="notifyRepeat"
                                    [binary]="true"
                                    inputId="binary" />
                        </div>
                        <label class="col-fixed" htmlFor="notifyRepeat">{{tranService.translate("alert.label.repeat")}}</label>
                        <label class="col-fixed" [style.color]="!repeat ? '#a1a1a1' : '#495057'" htmlFor="notifyInterval">{{tranService.translate("alert.label.frequency")}}</label>
                        <div class="col pl-0 pr-0" style="padding-right: 8px;">
                            <input class="w-full"
                                   pInputText id="notifyInterval"
                                   [(ngModel)]="alertInfo.notifyInterval"
                                   formControlName="notifyInterval"
                                   type="number"
                            />
                        </div>
                        <label class="col-fixed" [style.color]="!repeat ? '#a1a1a1' : '#495057'" for="notifyInterval">{{tranService.translate('alert.text.day')}}</label>
                    </div>
                </div>
            </div>

            <div [class]="alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP? '' : 'hidden'" class="flex flex-row">
                <div style="width: 50px">
                    <div class="col px-4 py-5">
                        <p-checkbox
                                [(ngModel)]="alertInfo.typeAlert"
                                name="Group"
                                formControlName="typeAlert"
                                value="Group"
                                [required]="alertInfo.actionType == CONSTANTS.ALERT_ACTION_TYPE.ALERT && alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP"
                        ></p-checkbox>
                    </div>
                </div>
                <div class="flex-1">
                    <!-- nhom nhan canh bao-->
                    <div class="col-12 flex flex-row justify-content-start align-items-center pb-0">
                        <label for="listAlertReceivingGroupId" class="col-fixed" style="width:180px">{{tranService.translate("alert.label.groupReceiving")}}<span class="text-red-500"></span></label>
                        <div class="col pl-0 pr-0 pb-0">
                            <vnpt-select
                                    class="w-full"
                                    [(value)]="alertInfo.listAlertReceivingGroupId"
                                    [placeholder]="tranService.translate('alert.text.inputgroupReceiving')"
                                    objectKey="receivingGroupAlert"
                                    paramKey="name"
                                    keyReturn="id"
                                    displayPattern="${name}"
                                    typeValue="primitive"
                                    [disabled]="true"
                            ></vnpt-select>
                        </div>
                    </div>
                </div>
                <div style="width: 50px;">

                </div>
                <div class="flex-1">

                </div>
            </div>

            <div [class]="alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP? '' : 'hidden'" class="flex flex-row">
                <div style="width: 50px">
                    <div class="col px-4 py-5">
                        <p-checkbox
                                [(ngModel)]="alertInfo.typeAlert"
                                name="Email"
                                formControlName="typeAlert"
                                value="Email"
                                [required]="alertInfo.actionType == CONSTANTS.ALERT_ACTION_TYPE.ALERT && alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP"
                        />
                    </div>
                </div>
                <div class="flex-1">
                    <!-- email -->
                    <div class="col-12 flex flex-row justify-content-start pb-0">
                        <label class="col-fixed" htmlFor="emailList" style="width:180px; height: fit-content;">{{tranService.translate("alert.label.emails")}}<span class="text-red-500">*</span></label>
                        <div style="width: calc(100% - 180px)">
                            <textarea  class="w-full" style="resize: none;"
                                       rows="5"
                                       [autoResize]="false"
                                       pInputTextarea id="emailList"
                                       [(ngModel)]="alertInfo.emailList"
                                       formControlName="emailList"
                                       [placeholder]="tranService.translate('alert.text.inputemails')"
                                       pattern="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(?:, ?[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})*$"
                                       [required]="true"
                            ></textarea>
                        </div>
                    </div>
                </div>
                <div style="width: 50px">
                    <div class="col px-4 py-5">
                        <p-checkbox
                                [(ngModel)]="alertInfo.typeAlert"
                                name="SMS"
                                formControlName="typeAlert"
                                value="SMS"
                                [required]="alertInfo.actionType == CONSTANTS.ALERT_ACTION_TYPE.ALERT && alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP">
                        </p-checkbox>
                    </div>
                </div>
                <div class="flex-1">
                    <!-- sms -->
                    <div class="col-12 flex flex-row justify-content-start pb-0">
                        <label class="col-fixed" htmlFor="smsList" style="width:180px; height: fit-content;">{{tranService.translate("alert.label.sms")}}<span class="text-red-500">*</span></label>
                        <div style="width: calc(100% - 150px)">
                            <textarea  class="w-full" style="resize: none;"
                                       rows="5"
                                       [autoResize]="false"
                                       pInputTextarea id="smsList"
                                       [(ngModel)]="alertInfo.smsList"
                                       formControlName="smsList"
                                       [placeholder]="tranService.translate('alert.text.inputsms')"
                                       pattern="^(?:0|84)\d{9,10}(?:, ?(?:0|84)\d{9,10})*$"
                                       [required]="true"
                            ></textarea>
                        </div>
                    </div>
                </div>

            </div>

            <div [class]="alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP? '' : 'hidden'" class="flex flex-row">
                <div style="width: 50px">

                </div>
                <div class="flex-1">
                    <!-- noi dung email -->
                    <div class="col-12 flex flex-row justify-content-start pb-0">
                        <label class="col-fixed" htmlFor="emailContent" style="width:180px; height: fit-content;">{{tranService.translate("alert.label.contentEmail")}}<span class="text-red-500">*</span></label>
                        <div style="width: calc(100% - 180px);">
                            <textarea  class="w-full" style="resize: none;"
                                       rows="5"
                                       [autoResize]="false"
                                       pInputTextarea id="emailContent"
                                       [(ngModel)]="alertInfo.emailContent"
                                       formControlName="emailContent"
                                       [maxlength]="255"
                                       [placeholder]="tranService.translate('alert.text.inputcontentEmail')"
                                       [required]="true"
                            ></textarea>
                            <div class="field" *ngIf="formAlert.controls.emailContent.dirty && formAlert.controls.emailContent.errors?.required">
                                <small class="text-red-500" *ngIf="formAlert.controls.emailContent.dirty && formAlert.controls.emailContent.errors?.required">{{tranService.translate("global.message.required")}}</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div style="width: 50px">

                </div>
                <div class="flex-1">
                    <!-- noi dung sms -->
                    <div class="col-12 flex flex-row pb-0">
                        <label class="col-fixed" htmlFor="smsContent" style="width:180px; height: fit-content;">{{tranService.translate("alert.label.contentSms")}}<span class="text-red-500">*</span></label>
                        <div style="width: calc(100% - 180px);">
                            <textarea  class="w-full" style="resize: none;"
                                       rows="5"
                                       [autoResize]="false"
                                       pInputTextarea id="smsContent"
                                       [(ngModel)]="alertInfo.smsContent"
                                       formControlName="smsContent"
                                       [maxlength]="255"
                                       [placeholder]="tranService.translate('alert.text.inputcontentSms')"
                                       [required]="true"
                            ></textarea>
                            <!-- error noi dung sms -->
                            <div class="field"
                                 *ngIf="formAlert.controls.smsContent.dirty && formAlert.controls.smsContent.errors?.required">
                                <small class="text-red-500" *ngIf="formAlert.controls.smsContent.dirty && formAlert.controls.smsContent.errors?.required">{{tranService.translate("global.message.required")}}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--            error checkbox-->
            <div class="col" *ngIf="alertInfo.actionType == CONSTANTS.ALERT_ACTION_TYPE.ALERT && alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP">
                <small class="text-red-500" *ngIf="formAlert.controls.typeAlert.dirty && formAlert.controls.typeAlert.errors?.required">{{tranService.translate("alert.message.checkboxRequired")}}</small>
            </div>

            <div *ngIf="alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP" class="flex flex-row gap-4 p-5 pt-0">
                <div class="text-xl font-bold">{{tranService.translate("alert.text.sendType")}}</div>
            </div>

            <div *ngIf="alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP" class="flex flex-row gap-4 p-5 pt-0">
                <div class="flex-1 flex justify-content-center">
                    <p-checkbox
                            [binary]="true"
                            inputId="binary"
                            formControlName="sendTypeEmail"/>
                    <div>&nbsp;Email</div>
                </div>
                <div class="flex-1 flex justify-content-center">
                    <p-checkbox
                            [binary]="true"
                            inputId="binary"
                            formControlName="sendTypeSMS" />
                    <div>&nbsp;SMS</div>
                </div>
            </div>
        </div>

        <div [class]="alertInfo.actionType == CONSTANTS.ALERT_ACTION_TYPE.API ? '' : 'hidden'" class="pt-0 pb-2 shadow-2 border-round-md m-1 flex p-fluid p-formgrid grid">
            <div class="flex-1">
                <!-- url -->
                <div class="field  px-4 pt-4  flex-row ">
                    <div class="col-12 flex flex-row justify-content-between align-items-center pb-0">
                        <label htmlFor="url" style="width:90px">{{tranService.translate("alert.label.url")}}<span class="text-red-500">*</span></label>
                        <div style="width: calc(100% - 90px)">
                            <input class="w-full"
                                   [required]="alertInfo.actionType == CONSTANTS.ALERT_ACTION_TYPE.API"
                                   pInputText id="url"
                                   [(ngModel)]="alertInfo.url"
                                   formControlName="url"
                                   [maxLength]="255"
                                   pattern="^(https?|ftp):\/\/[^\s/$.?#].[^\s]*$|^www\.[^\s/$.?#].[^\s]*$|^localhost[^\s]*$|^(?:\d{1,3}\.){3}\d{1,3}[^\s]*$"
                                   [placeholder]="tranService.translate('alert.text.inputurl')"
                            />
                        </div>
                    </div>
                    <div class="field grid px-4 flex flex-row flex-nowrap pb-2">
                        <label htmlFor="name" style="width:90px; height: fit-content"></label>
                        <div style="width: calc(100% - 90px);padding-right: 8px;">
                            <small *ngIf="formAlert.controls.url.dirty && formAlert.controls.url.errors?.required" class="text-red-500">{{tranService.translate("global.message.required")}}</small>
                            <small *ngIf="formAlert.controls.url.errors?.pattern" class="text-red-500">{{tranService.translate("global.message.urlNotValid")}}</small>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </form>
</p-card>
