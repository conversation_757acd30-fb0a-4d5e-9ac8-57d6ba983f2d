<div *ngIf="canView">
<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{this.tranService.translate("datapool.label.detailWallet")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
    <div class="col-5 flex flex-row justify-content-end align-items-center">
        <p-button *ngIf="walletDetail.canShare && checkAuthen([PERMISSIONS.DATAPOOL.SHARE_WALLET])" [label]="tranService.translate('datapool.button.shareTraffic')" icon="pi pi-share-alt" styleClass="p-button-info mr-2" (click)="goToShare()"></p-button>
    </div>
</div>

<p-card styleClass="my-3">
    <div class="text-2xl font-bold pb-2">{{subCodeId}}</div>
    <div *ngIf="walletDetail">
        <div class="flex flex-row surface-200 p-4 border-round">
            <div class="flex-1">
                <div class="font-medium text-base">{{tranService.translate('datapool.label.payCode')}}</div>
                <div class="font-semibold text-lg">{{walletDetail.payCode}}</div>
            </div>
            <div class="flex-1">
                <div class="font-medium text-base">{{tranService.translate('datapool.label.packageName')}}</div>
                <div class="font-semibold text-lg">{{walletDetail.packageName}}</div>
            </div>
            <div class="flex-1">
                <div class="font-medium text-base">{{tranService.translate('datapool.label.phoneFull')}}</div>
                <div class="font-semibold text-lg">{{walletDetail.phoneActive}}</div>
            </div>
            <div class="flex-1">
                <div class="font-medium text-base">{{tranService.translate("datapool.label.tax")}}</div>
                <div class="font-semibold text-lg">{{walletDetail.tax}}</div>
            </div>
        </div>
    </div>
    <div class="flex flex-row p-4 border-round">
        <div class="flex-1">
            <div class="font-medium text-base">{{tranService.translate("datapool.label.trafficType")}}</div>
            <div class="font-semibold text-lg">{{walletDetail.trafficType}}</div>
        </div>
        <div class="flex-1">
            <div class="font-medium text-base">{{tranService.translate('datapool.label.remainData')}}/ {{tranService.translate("datapool.label.purchasedData")}}</div>
            <div class="font-semibold text-lg" *ngIf="walletDetail.trafficType == 'Gói Data'" >{{walletDetail.totalRemainingTraffic}}/{{walletDetail.purchasedTraffic}} MB</div>
            <div class="font-semibold text-lg" *ngIf="walletDetail.trafficType == 'Gói thoại'" >{{walletDetail.totalRemainingTraffic}}/{{walletDetail.purchasedTraffic}} {{tranService.translate('alert.label.minutes')}}</div>
            <div class="font-semibold text-lg" *ngIf="((walletDetail.trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase()))">{{walletDetail.totalRemainingTraffic}}/{{walletDetail.purchasedTraffic}} SMS</div>
            <div class="font-semibold text-lg" *ngIf="!((walletDetail.trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase())) && walletDetail.trafficType != 'Gói Data'&&walletDetail.trafficType != 'Gói thoại'" >{{walletDetail.totalRemainingTraffic}}/{{walletDetail.purchasedTraffic}}</div>
        </div>
        <div class="flex-1">
            <div class="font-medium text-base">{{tranService.translate("datapool.label.usedTime")}}</div>
            <div class="font-semibold text-lg">{{walletDetail.timeToUse}}</div>
        </div>
    </div>
</p-card>

<p-card>
    <div class="text-lg font-bold">{{tranService.translate("datapool.label.shareInfo")}}</div>
    <p-table
            #dt2
            [value]="listDetail"
            dataKey="id"
            [rows]="10"
            [rowsPerPageOptions]="[5, 10, 25, 50]"
            [paginator]="true"
            [tableStyle]="{ 'margin-top': '10px' }"
        >
        <ng-template pTemplate="header">
            <tr>
                <th>{{tranService.translate('datapool.label.phoneFull')}}</th>
                <th>{{tranService.translate('datapool.label.fullName')}}</th>
                <th>{{tranService.translate('datapool.label.email')}}</th>
                <th>{{tranService.translate('datapool.label.sharedTime')}}</th>
                <th>{{tranService.translate('datapool.label.usedDate')}}</th>
                <th>{{tranService.translate('datapool.label.shared')}}</th>
                <th >{{tranService.translate('datapool.label.percentage')}}</th>
            </tr>
        </ng-template>
        <ng-template pTemplate="body" let-listDetail>
            <tr>
                <td>
                    {{ listDetail.phoneReceipt }}
                </td>
                <td>
                    {{ listDetail.name }}
                </td>
                <td>
                    {{ listDetail.email }}
                </td>
                <td>
                    {{ getFormattedDate(listDetail.timeUpdate) }}
                </td>
                <td>
                    {{ getFormattedDate(listDetail.timeUpdate, 30) }}
                </td>
                <td *ngIf="listDetail.trafficType == 'Gói Data'">
                    {{ listDetail.trafficShare }} MB
                </td>
                <td *ngIf="listDetail.trafficType == 'Gói thoại'">
                    {{ listDetail.trafficShare }} {{tranService.translate('alert.label.minutes')}}
                </td>
                <td *ngIf="(( listDetail.trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase()))">
                    {{ listDetail.trafficShare }} SMS
                </td>
                <td *ngIf="!(( listDetail.trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase())) && listDetail.trafficType != 'Gói Data' && listDetail.trafficType != 'Gói thoại'">
                    {{ listDetail.trafficShare }}
                </td>
                <td>
                    {{ listDetail.percentTraffic }}%
                </td>
            </tr>
        </ng-template>
    </p-table>
</p-card>
</div>
