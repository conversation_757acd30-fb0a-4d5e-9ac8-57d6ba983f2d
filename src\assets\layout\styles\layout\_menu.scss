.layout-sidebar {
    position: fixed;
    width: 300px;
    // min-height: calc(100vh - 5.5rem);
    height: 100%;
    z-index: 999;
    // overflow-y: auto;
    user-select: none;
    top: 4rem;
    // left: 1rem;
    transition: transform $transitionDuration, left $transitionDuration;
    background-color: var(--surface-overlay);
    // border-radius: $borderRadius;
    // padding: 0.5rem 1.5rem;
    box-shadow: 0px 3px 5px rgba(0, 0, 0, .02), 0px 0px 2px rgba(0, 0, 0, .05), 0px 1px 4px rgba(0, 0, 0, .08);
}

// .layout-sidebar::-webkit-scrollbar {
//     display: none;  /* Safari and Chrome */
// }

.layout-sidebar-small{
    width: 60px;
    overflow-y: visible;
    /* width */
    ::-webkit-scrollbar {
        width: 4px;
        display: none;
    }
}

.button-change-type-menu{
    cursor: pointer;
    width: 5px;
    height: 5px;
    padding: 20px 7px;
    border: 1px solid lightgray;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 0px 10px 10px 0px;
    border-radius: 5px;
    position: fixed;
    top: 50vh;
    left: 0;
    transform: translate(50%, -50%);
    z-index: 100;
    background-color: white;
}

.caret-right{
    border-left: 5px solid black;
    border-top: 5px solid transparent;
    border-bottom: 5px solid transparent;
}

.caret-left{
    border-right: 5px solid black;
    border-top: 5px solid transparent;
    border-bottom: 5px solid transparent;
}

.layout-menu {
    margin: 0;
    padding: 0;
    list-style-type: none;
    max-height: 100%;

    .layout-root-menuitem {
        >.layout-menuitem-root-text {
            font-size: .857rem;
            text-transform: uppercase;
            font-weight: 700;
            color: var(--surface-900);
            margin: .75rem 0;
        }

        >a {
            display: none;
        }
    }

    a {
        user-select: none;

        &.active-menuitem {
            >.layout-submenu-toggler {
                transform: rotate(-180deg);
            }
        }
    }

    li.active-menuitem {
        >a {
            .layout-submenu-toggler {
                transform: rotate(-180deg);
            }
        }
    }

    ul {
        margin: 0;
        padding: 0;
        list-style-type: none;

        a {
            display: flex;
            align-items: center;
            position: relative;
            outline: 0 none;
            color: var(--text-color);
            cursor: pointer;
            padding: .75rem 1rem;
            // border-radius: $borderRadius;
            transition: background-color $transitionDuration, box-shadow $transitionDuration;

            .layout-menuitem-icon {
                margin-right: .75rem;
            }

            .layout-submenu-toggler {
                font-size: 75%;
                margin-left: auto;
                transition: transform $transitionDuration;
            }

            &.active-route {
                // font-weight: 700;
                // color: var(--primary-color);
                color: white;
            }

            &:hover {
                // background-color: var(--surface-hover);
                background-color: #84c1f9;
                color: white;
                .layout-menuitem-icon{
                    color: white;
                    background-color: #0a6fd0;
                }
            }
            &:focus {
                // @include focused-inset();
            }
        }

        ul {
            overflow: hidden;
            // border-radius: $borderRadius;
            // border-left: 1px solid #84c1f9;


            li {
                // border-left: 1px solid #84c1f9;

                a {
                    border-left: 1px solid #6c757d;
                    margin-left: 2rem;
                    .layout-menuitem-icon{
                        display: none;
                    }
                }

                li {
                    a {
                        margin-left: 2rem;
                    }

                    li {
                        a {
                            margin-left: 2.5rem;
                            border-left: 1px solid #6c757d;
                        }

                        li {
                            a {
                                margin-left: 3rem;
                            }

                            li {
                                a {
                                    margin-left: 3.5rem;
                                }

                                li {
                                    a {
                                        margin-left: 4rem;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@media (max-width: 721px) and (max-height: 1281px) {
     .layout-sidebar {
         z-index: 1000 !important;
         top: 4rem !important;
         height: calc(100vh - 4rem) !important;
         width: 250px !important;
    }

    #menuToggleBlock {
        display: none !important;
    }
}


