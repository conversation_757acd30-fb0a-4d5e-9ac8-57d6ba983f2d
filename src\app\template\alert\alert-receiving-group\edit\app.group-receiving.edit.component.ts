import {AfterContentChecked, Component, Inject, Injector, OnInit} from '@angular/core';
import {FormBuilder} from "@angular/forms";
import {MenuItem} from "primeng/api";
import {ColumnInfo, OptionTable} from "../../../common-module/table/table.component";
import {ReceivingGroupService} from "../../../../service/alert/ReceivingGroup";
import {ComponentBase} from "../../../../component.base";

@Component({
  selector: 'app-app.group-receiving.edit',
  templateUrl: './app.group-receiving.edit.component.html',
})
export class AppGroupReceivingEditComponent extends ComponentBase implements OnInit, AfterContentChecked{
    constructor(
                @Inject(ReceivingGroupService) private receivingGroupService: ReceivingGroupService,
                private formBuilder: FormBuilder,
                private injector: Injector
    ) {
        super(injector);
    }
    items: MenuItem[];
    home: MenuItem;
    isRGNameExisted: boolean = false;
    formReceivingGroup : any;
    formMailInput : any;
    receivingGroupInfo: {
        name: string|null,
        description: string|null,
        emails: string|null,
        smsList: string|null,
    };
    myEmails: Array<any>|null;
    mySmsList: Array<any>|null;
    selectItems: Array<any> = [];
    columns: Array<ColumnInfo>;
    dataSet: {
        content: Array<any>,
        total: number
    };
    optionTable: OptionTable;
    email: {}

    formSMSInput : any;
    selectItemsSms: Array<any> = [];
    columnsSms: Array<ColumnInfo>;
    dataSetSms: {
        content: Array<any>,
        total: number
    };
    optionTableSms: OptionTable;
    sms: {}
    rgId = this.route.snapshot.paramMap.get("id");
    isRGEmailExisted: boolean = false;
    isRGSmsExisted: boolean = false;


    ngOnInit(): void {
        let me = this;
        this.items = [{ label: this.tranService.translate("global.menu.groupReceiving") }, { label: this.tranService.translate("global.menu.groupReceivingList"), routerLink:"/alerts/receiving-group"  }, { label: this.tranService.translate("global.button.edit") }];
        this.home = { icon: 'pi pi-home', routerLink: '/' };

        this.receivingGroupInfo = {
            name: "nhom1",
            description: null,
            emails: null,
            smsList: null,
        }
        this.myEmails= []
        this.mySmsList = []
        this.formReceivingGroup = this.formBuilder.group(this.receivingGroupInfo);
        this.formMailInput = this.formBuilder.group({email: ""});
        this.dataSet = {
            content: [],
            total: 0
        }
        this.selectItems = [];
        this.columns = [
            {
                name: this.tranService.translate("alert.receiving.emails"),
                key: "emails",
                size: "90%",
                align: "left",
                isShow: true,
                isSort: false,
            },
        ];
        this.optionTable = {
            hasClearSelected: true,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
            action: [
                {
                    icon: "pi pi-trash",
                    tooltip: this.tranService.translate("alert.text.removeAlert"),
                    func: function(id, item){
                        me.removeEmail(item)
                    },
                }
            ]
        };
        this.dataSet = {
            content: [],
            total: 0
        }

        this.formSMSInput = this.formBuilder.group({sms: ""});
        this.selectItemsSms = [];
        this.columnsSms = [
            {
                name: this.tranService.translate("alert.receiving.sms"),
                key: "smsList",
                size: "90%",
                align: "left",
                isShow: true,
                isSort: false,
            },
        ];
        this.optionTableSms = {
            hasClearSelected: true,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
            action: [
                {
                    icon: "pi pi-trash",
                    tooltip: this.tranService.translate("alert.text.removeSms"),
                    func: function(id, item){
                        me.removeSms(item)
                    },
                }
            ]
        };
        this.getDetail()
        this.search();
        this.searchSms();
    }
    ngAfterContentChecked(): void {
    }
    getDetail(){
        let me = this;
        me.messageCommonService.onload()
        this.receivingGroupService.getById(parseInt(this.rgId), (response)=>{
            me.receivingGroupInfo = response;
            me.receivingGroupInfo.emails = response.emails
            me.receivingGroupInfo.smsList = response.msisdns

            if (response.emails != null){
                for (let i = 0; i <response.emails.split(", ").length; i++) {
                    me.dataSet.content.push({emails :response.emails.split(", ")[i]})
                    me.myEmails.push(response.emails.split(", ")[i])
                }
            }

            if (response.msisdns != null){
                for (let i = 0; i <response.msisdns.split(", ").length; i++) {
                    me.dataSetSms.content.push({smsList :response.msisdns.split(", ")[i]})
                    me.mySmsList.push(response.msisdns.split(", ")[i])
                }
            }
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }
    onSubmitCreate(){
        let dataBody = {
            id: this.rgId,
            name: this.receivingGroupInfo.name,
            description: this.receivingGroupInfo.description,
            emails: this.receivingGroupInfo.emails,
            msisdns: this.receivingGroupInfo.smsList,
        }
        this.messageCommonService.onload();
        let me = this;
        this.receivingGroupService.updateReceivingGroup(this.rgId,dataBody, (response)=>{
            me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"));
            me.router.navigate(['/alerts/receiving-group']);
        })
    }
    closeForm(){
        this.router.navigate(['/alerts/receiving-group'])
    }

    addEmail(val){
        let me = this;
        me.dataSet.content.push({emails :val})
        me.myEmails.push(val)
        me.receivingGroupInfo.emails = me.myEmails.join(', ').toString()
        me.email = ""
    }
    search(){
        let me = this
        me.dataSet = {
            content: [],
            total: 0
        }
    }
    removeEmail(val){
        let me = this
        me.dataSet.content.splice(me.dataSet.content.indexOf(val), 1)
        me.myEmails.splice(me.myEmails.indexOf(val), 1)
        me.receivingGroupInfo.emails = me.myEmails.join(', ').toString()
    }

    addSms(val){
        let me = this;
        me.dataSetSms.content.push({smsList :val})
        me.mySmsList.push(val)
        me.receivingGroupInfo.smsList = me.mySmsList.join(', ').toString()
        me.sms = ""
    }
    searchSms(){
        let me = this
        me.dataSetSms = {
            content: [],
            total: 0
        }
    }
    removeSms(val){
        let me = this
        me.dataSetSms.content.splice(me.dataSetSms.content.indexOf(val), 1)
        me.mySmsList.splice(me.mySmsList.indexOf(val), 1)
        me.receivingGroupInfo.smsList = me.mySmsList.join(', ').toString()
    }

    checkFormInfo(){
        let me = this;
        if ((me.receivingGroupInfo.emails != null && me.receivingGroupInfo.emails != "" || me.myEmails.length > 0 )
            || (me.receivingGroupInfo.smsList != null && me.receivingGroupInfo.smsList != "" || me.mySmsList.length > 0 ))
        {
            return false;
        }
        return true;
    }
    nameChanged(query){
        let me = this
        this.debounceService.set("name",me.receivingGroupService.checkName.bind(me.receivingGroupService),{name:me.receivingGroupInfo.name},(response)=>{
            if (response == 1){
                me.isRGNameExisted = true
            }
            else {
                me.isRGNameExisted = false
            }
        })
    }
    emailChanged(query){
        let me = this;
        for (let i = 0; i < me.myEmails.length; i++) {
            if (me.myEmails[i] == query){
                this.isRGEmailExisted = true
                return
            }
            else {
                this.isRGEmailExisted = false
            }
        }
    }
    smsChanged(query){
        let me = this;
        for (let i = 0; i < me.mySmsList.length; i++) {
            if (me.mySmsList[i] == query){
                this.isRGSmsExisted = true
                return
            }
            else {
                this.isRGSmsExisted = false
            }
        }
    }
}
