import { AfterContentChecked, Component, Injector, Input, OnInit } from "@angular/core";
import { FormBuilder } from "@angular/forms";
import { ComponentBase } from "src/app/component.base";
import { CONSTANTS } from "src/app/service/comon/constants";
import { ReportService } from "src/app/service/report/ReportService";
import { ARRAY_SERVICE } from "src/app/template/common-module/combobox-lazyload/combobox.lazyload";
import { ColumnInfo, OptionTable } from "src/app/template/common-module/table/table.component";
import { ColumnInputInfo, OptionTableInput, TableInputControl } from "src/app/template/common-module/table/table.input.component";
export interface TableReportInfo {
    id: number|null;
    reportConfigId: number | null;
    tableName: string;
    schema: string;
    query: string;
    columnDisplay: string;
    columnQueryResult: string;
    columns?: {id: number, display: string, key: string}[] | null;
}
export interface GeneralInfo {
    id: number | null;
    name: string | null;
    status: number | null;
    enablePreview: number | number[] | null;
    description: string | null;
    reportContents: TableReportInfo[] | null,
    filterParams: string | null;
    filters?: ParameterInfo[];
}

export interface ParameterInfo{
    id?: number | null;
    prKey: string|null;
    prType: number|null;
    prDisplayName: string|null;
    valueList?: {
        id?: number | null;
        display: string | null;
        value: string | number | null;
    }[],
    required: boolean,
    isAutoComplete: boolean,
    isMultiChoice: boolean,
    objectKey?: string,
    input?: string,
    output?: string,
    displayPattern?: string,
    queryParam?: string,
    queryInfo: {
        objectKey: string,
        input: string,
        output: string,
        displayPattern: string,
        queryParam?: string,
    },
    dateType: number;
    paramDefault?: any
    prValue: any;
}
export class TabGeneralDynamicReportControl{
    reload: Function;
}
@Component({
    selector: "tab-report-dynamic-general",
    templateUrl: "./tab.report.dynamic.general.html"
})
export class TabReportDynamicGeneral extends ComponentBase implements OnInit, AfterContentChecked{
    constructor(injector: Injector,
                private formBuilder: FormBuilder,
                private reportService: ReportService) {
        super(injector);
    }

    @Input() generalInfo!: GeneralInfo;
    @Input() control!: TabGeneralDynamicReportControl;
    @Input() modeView!: number;
    @Input() cancel!: Function;
    @Input() saveSuccess!: Function;
    formGeneralInfo: any;
    oldReportName: string = null;
    isReportNameExisted: boolean = false;
    statusReports: any[];
    tableColumns: ColumnInfo[];
    optionTableListTable: OptionTable;
    dataTables: {
        content: any[],
        total: number;
    }
    paramColumns: ColumnInfo[];
    optionTableListParam: OptionTable;
    dataParams: {
        content: any[],
        total: number;
    }
    objectMode = CONSTANTS.MODE_VIEW;
    objectType = CONSTANTS.PARAMETER_TYPE;
    modeTable = CONSTANTS.MODE_VIEW.CREATE;
    modeParameter = CONSTANTS.MODE_VIEW.CREATE;
    isShowDialogTable: boolean = false;
    isShowDialogParameter: boolean = false;
    tableInfo: TableReportInfo;
    parameterInfo: ParameterInfo;
    formTable: any;
    formParameter: any;
    isTableNameExisted: boolean = false;
    isParamKeyExisted: boolean = false;
    isParamDisplayExisted: boolean = false;
    schemas: any[];
    parameterTypes: any[];
    dateTypes: any[];

    columnTableInput: ColumnInputInfo[];
    optionTableInput: OptionTableInput;
    tableInputControl: TableInputControl;

    columnParamInput: ColumnInputInfo[];
    optionParamInput: OptionTableInput;
    paramInputControl: TableInputControl;

    indexFakeTable = -1;
    indexFakeParameter = -1;

    listObjectKey: Array<any>;
    reportPreview = CONSTANTS.REPORT_PREVIEW;
    ngOnInit(): void {
        let me = this;
        this.oldReportName = this.generalInfo ? this.generalInfo.name : null;
        this.statusReports = [
            {value: CONSTANTS.REPORT_STATUS.ACTIVE, name: this.tranService.translate("report.status.active")},
            {value: CONSTANTS.REPORT_STATUS.INACTIVE, name: this.tranService.translate("report.status.inactive")}
        ]

        //table of table content
        this.tableColumns =[{
            align: "left",
            isShow: true,
            isSort: false,
            key: "tableName",
            name: this.tranService.translate("report.label.tableName"),
            size: "750px",
            className: "text-cyan-500 cursor-pointer",
            funcClick: (id, item)=>{
                me.tableInfo = {...item};
                me.formTable = this.formBuilder.group(this.tableInfo);
                me.formTable.get("tableName").disable({onlySelf: true});
                me.formTable.get("schema").disable({onlySelf: true});
                me.formTable.get("query").disable({onlySelf: true});
                me.isTableNameExisted = false;
                me.tableInfo.columns = [];
                let listDisplayName = me.tableInfo.columnDisplay.split(",");
                let listQueryName = me.tableInfo.columnQueryResult.split(",");
                listDisplayName.forEach( (el, index)=>{
                    me.tableInfo.columns.push({
                        id: null,
                        display: el,
                        key: listQueryName[index]
                    })
                })
                me.optionTableInput.mode = CONSTANTS.MODE_VIEW.DETAIL;
                me.tableInputControl.reset();
                me.modeTable = CONSTANTS.MODE_VIEW.DETAIL;
                me.isShowDialogTable = true;
            }
        }];
        this.optionTableListTable = {
            hasClearSelected: true,
            hasShowChoose: false,
            hasShowIndex: false,
            hasShowJumpPage: false,
            paginator: false,
            hasShowToggleColumn: false,
            action: [{
                icon: "pi pi-external-link",
                tooltip: this.tranService.translate("global.button.edit"),
                func: (id, item) => {
                    me.tableInfo = {...item};
                    me.formTable = me.formBuilder.group(me.tableInfo);
                    me.isTableNameExisted = false;
                    me.tableInfo.columns = [];
                    let listDisplayName = me.tableInfo.columnDisplay.split(",");
                    let listQueryName = me.tableInfo.columnQueryResult.split(",");
                    listDisplayName.forEach( (el, index)=>{
                        me.tableInfo.columns.push({
                            id: index + 1,
                            display: el,
                            key: listQueryName[index]
                        })
                    })
                    me.optionTableInput.mode = CONSTANTS.MODE_VIEW.UPDATE;
                    me.tableInputControl.reset();
                    me.modeTable = CONSTANTS.MODE_VIEW.UPDATE;
                    me.isShowDialogTable = true;
                },
                funcAppear(id, item) {
                    return me.modeView != CONSTANTS.MODE_VIEW.DETAIL;
                },
            },{
                icon: "pi pi-trash",
                tooltip: this.tranService.translate("global.button.delete"),
                func: (id, item) => {
                    for(let i = 0; i < me.dataTables.content.length;i++){
                        if(me.dataTables.content[i].id == id){
                            me.dataTables.content.splice(i, 1);
                            me.generalInfo.reportContents = me.dataTables.content;
                            break;
                        }
                    }
                },
                funcAppear(id, item) {
                    return me.modeView != CONSTANTS.MODE_VIEW.DETAIL;
                },
            }]
        }

        this.schemas = [
            {value: CONSTANTS.SCHEMA.BILL, name: this.tranService.translate("report.schema.bill")},
            {value: CONSTANTS.SCHEMA.CORE, name: this.tranService.translate("report.schema.core")},
            {value: CONSTANTS.SCHEMA.LOG, name: this.tranService.translate("report.schema.log")},
            {value: CONSTANTS.SCHEMA.MONITOR, name: this.tranService.translate("report.schema.monitor")},
            {value: CONSTANTS.SCHEMA.REPORT, name: this.tranService.translate("report.schema.report")},
            {value: CONSTANTS.SCHEMA.RULE, name: this.tranService.translate("report.schema.rule")},
            {value: CONSTANTS.SCHEMA.SIM, name: this.tranService.translate("report.schema.sim")},
            {value: CONSTANTS.SCHEMA.ELASTICSEARCH, name: this.tranService.translate("report.schema.elasticsearch")},
        ];
        this.parameterTypes = [
            {value: CONSTANTS.PARAMETER_TYPE.NUMBER, name: this.tranService.translate("report.paramType.number")},
            {value: CONSTANTS.PARAMETER_TYPE.STRING, name: this.tranService.translate("report.paramType.string")},
            {value: CONSTANTS.PARAMETER_TYPE.DATE, name: this.tranService.translate("report.paramType.date")},
            {value: CONSTANTS.PARAMETER_TYPE.LIST_NUMBER, name: this.tranService.translate("report.paramType.listNumber")},
            {value: CONSTANTS.PARAMETER_TYPE.LIST_STRING, name: this.tranService.translate("report.paramType.listString")},
            {value: CONSTANTS.PARAMETER_TYPE.TIMESTAMP, name: this.tranService.translate("report.paramType.timestamp")},
        ]
        this.dateTypes = [
            {value: CONSTANTS.DATE_TYPE.MONTH, name: this.tranService.translate("report.datetype.month")},
            {value: CONSTANTS.DATE_TYPE.DATE, name: this.tranService.translate("report.datetype.date")},
            {value: CONSTANTS.DATE_TYPE.DATETIME, name: this.tranService.translate("report.datetype.datetime")},
        ]

        //valueList tabe of table parameters
        this.paramColumns =[{
            align: "left",
            isShow: true,
            isSort: false,
            key: "prKey",
            name: this.tranService.translate("report.label.paramKey"),
            size: "250px",
            className: "text-cyan-500 cursor-pointer",
            funcClick: (id, item)=>{
                me.isParamKeyExisted = false;
                me.isParamDisplayExisted = false;
                me.parameterInfo = {...item};
                if(me.parameterInfo.required == undefined || me.parameterInfo.required == null){
                    me.parameterInfo.required = false;
                }
                setTimeout(function(){
                    me.parameterInfo.valueList = item.valueList || [];
                    me.optionParamInput.mode = CONSTANTS.MODE_VIEW.DETAIL;
                    me.paramInputControl.reset();
                })
                me.formParameter = me.formBuilder.group(me.parameterInfo);
                Object.keys(me.parameterInfo).forEach(key => {
                    me.formParameter.get(key).disable();
                })
                me.modeParameter = CONSTANTS.MODE_VIEW.DETAIL;
                me.isShowDialogParameter = true;
            }
        },{
            align: "left",
            isShow: true,
            isSort: false,
            key: "prType",
            name: this.tranService.translate("report.label.paramType"),
            size: "150px",
            funcConvertText(value) {
                if(value == CONSTANTS.PARAMETER_TYPE.NUMBER){
                    return me.tranService.translate("report.paramType.number");
                }else if(value == CONSTANTS.PARAMETER_TYPE.STRING){
                    return me.tranService.translate("report.paramType.string");
                }else if(value == CONSTANTS.PARAMETER_TYPE.DATE){
                    return me.tranService.translate("report.paramType.date");
                }else if(value == CONSTANTS.PARAMETER_TYPE.LIST_NUMBER){
                    return me.tranService.translate("report.paramType.listNumber");
                }else if(value == CONSTANTS.PARAMETER_TYPE.LIST_STRING){
                    return me.tranService.translate("report.paramType.listString");
                }else if(value == CONSTANTS.PARAMETER_TYPE.TIMESTAMP){
                    return me.tranService.translate("report.paramType.timestamp");
                } else if (value == CONSTANTS.PARAMETER_TYPE.RECENTLY_DATE_FROM) {
                    return me.tranService.translate("report.paramType.recentlyDateFrom");
                } else if (value == CONSTANTS.PARAMETER_TYPE.RECENTLY_DATE_TO) {
                    return me.tranService.translate("report.paramType.recentlyDateTo");
                }
                return "";
            },
        },{
            align: "left",
            isShow: true,
            isSort: false,
            key: "prDisplayName",
            name: this.tranService.translate("report.label.paramDisplay"),
            size: "350px"
        }];

        this.optionTableListParam = {
            hasClearSelected: true,
            hasShowChoose: false,
            hasShowIndex: false,
            hasShowJumpPage: false,
            paginator: false,
            hasShowToggleColumn: false,
            action: [{
                icon: "pi pi-external-link",
                tooltip: this.tranService.translate("global.button.edit"),
                func: (id, item) => {
                    me.isParamKeyExisted = false;
                    me.isParamDisplayExisted = false;
                    me.parameterInfo = {...item};
                    if(me.parameterInfo.required == undefined || me.parameterInfo.required == null){
                        me.parameterInfo.required = false;
                    }
                    setTimeout(function(){
                        me.parameterInfo.valueList = item.valueList || [];
                        me.optionParamInput.mode = CONSTANTS.MODE_VIEW.UPDATE;
                        me.paramInputControl.reset();
                    })
                    me.formParameter = me.formBuilder.group(me.parameterInfo);
                    me.modeParameter = CONSTANTS.MODE_VIEW.UPDATE;
                    me.optionParamInput.mode = CONSTANTS.MODE_VIEW.UPDATE;
                    me.paramInputControl.reset();
                    me.isShowDialogParameter = true;
                },
                funcAppear(id, item) {
                    return me.modeView != CONSTANTS.MODE_VIEW.DETAIL;
                },
            },{
                icon: "pi pi-trash",
                tooltip: this.tranService.translate("global.button.delete"),
                func: (id, item) => {
                    for(let i = 0;i < me.dataParams.content.length;i++){
                        if(me.dataParams.content[i].id == id){
                            me.dataParams.content.splice(i, 1);
                            me.generalInfo.filterParams = JSON.stringify(me.dataParams.content);
                            break;
                        }
                    }
                },
                funcAppear(id, item) {
                    return me.modeView != CONSTANTS.MODE_VIEW.DETAIL;
                },
            }]
        }

        this.tableInfo = {
            id: null,
            query: null,
            columnDisplay: null,
            columnQueryResult: null,
            reportConfigId: null,
            schema: null,
            tableName: null,
            columns: null
        }
        this.formTable = this.formBuilder.group(this.tableInfo);
        this.parameterInfo = {
            id: null,
            prDisplayName: null,
            prKey: null,
            prType: null,
            valueList: null,
            dateType: null,
            displayPattern: null,
            input: null,
            isAutoComplete: false,
            isMultiChoice: false,
            objectKey: null,
            output: null,
            queryInfo: null,
            required: false,
            queryParam: null,
            prValue: null,
        }
        this.formParameter = this.formBuilder.group(this.parameterInfo);

        //table of fields of table
        this.columnTableInput = [
            {
                align: 'left',
                key: "display",
                name: this.tranService.translate("report.label.fieldDisplay"),
                size: "280px",
                type: CONSTANTS.PARAMETER_TYPE.STRING,
                validate: {
                    required: true,
                    maxLength: 255,
                    pattern: /^[^~`!@#\$%\^&*=\+\[\]\{\}\|\\,<>\/?]*$/,
                    messageErrorPattern: me.tranService.translate("global.message.formatContainVN"),
                    exists: true
                }
            },
            {
                align: 'left',
                key: "key",
                name: this.tranService.translate("report.label.fieldData"),
                size: "280px",
                type: CONSTANTS.PARAMETER_TYPE.STRING,
                validate: {
                    required: true,
                    maxLength: 255,
                    pattern: /^[a-zA-Z0-9\-_]*$/,
                    messageErrorPattern: this.tranService.translate("global.message.formatCode"),
                    exists: true
                }
            }
        ]
        this.optionTableInput = {
            mode: CONSTANTS.MODE_VIEW.CREATE
        };
        this.tableInputControl = new TableInputControl();

        //table of value parameters
        this.columnParamInput = [
            {
                align: 'left',
                key: "display",
                name: this.tranService.translate("report.label.valueDisplay"),
                size: "280px",
                type: CONSTANTS.PARAMETER_TYPE.STRING,
                validate: {
                    required: true,
                    maxLength: 255,
                    exists: true,
                    pattern: /^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]*$/,
                    messageErrorPattern: me.tranService.translate("global.message.formatContainVN")
                }
            },
            {
                align: 'left',
                key: "value",
                name: this.tranService.translate("report.label.valueDB"),
                size: "280px",
                type: this.parameterInfo.prType == CONSTANTS.PARAMETER_TYPE.LIST_STRING ? CONSTANTS.PARAMETER_TYPE.STRING : CONSTANTS.PARAMETER_TYPE.NUMBER,
                validate: {
                    required: true,
                    maxLength: 255,
                    pattern: /^[a-zA-Z0-9\-_]+$/,
                    messageErrorPattern: me.tranService.translate("global.message.formatCode"),
                    exists: true
                }
            }
        ]
        this.optionParamInput = {
            mode: CONSTANTS.MODE_VIEW.CREATE
        };
        this.paramInputControl = new TableInputControl();

        this.listObjectKey = ARRAY_SERVICE.map(el => {
            return {
                value: el.name,
                display: this.tranService.translate(el.display)
            }
        })

        this.control.reload = this.onload.bind(this);
    }

    onload(){
        let me = this;
        this.formGeneralInfo = undefined;
        this.isReportNameExisted = false;
        setTimeout(function(){
            if(me.generalInfo.filterParams){
                me.generalInfo.filters = JSON.parse(me.generalInfo.filterParams);
                let idParam = 1;
                me.generalInfo.filters.forEach(el => {
                    el.id = idParam ++;
                    if(el.dateType == undefined){
                        el.dateType = null
                    }
                    if(!el.isAutoComplete){
                        el.isAutoComplete = false;
                    }
                    if(!el.isMultiChoice){
                        el.isMultiChoice = false;
                    }
                    if(el.queryInfo != undefined && el.queryInfo != null){
                        el.objectKey = el.queryInfo.objectKey;
                        el.input = el.queryInfo.input;
                        el.output = el.queryInfo.output;
                        el.displayPattern = el.queryInfo.displayPattern;
                        el.queryParam = el.queryInfo.queryParam;
                    }else{
                        el.objectKey = null;
                        el.input = null;
                        el.output = null;
                        el.displayPattern = null;
                        el.queryParam = null;
                    }
                })
            }else{
                me.generalInfo.filters = [];
            }
            me.oldReportName = me.generalInfo ? me.generalInfo.name : null;
            me.formGeneralInfo = me.formBuilder.group(me.generalInfo);
            if(me.modeView == CONSTANTS.MODE_VIEW.DETAIL){
                me.formGeneralInfo.get("name").disable();
                me.formGeneralInfo.get("status").disable();
                me.formGeneralInfo.get("enablePreview").disable();
                me.formGeneralInfo.get("description").disable();
            }
            me.dataTables = {
                content: me.generalInfo.reportContents,
                total: me.generalInfo.reportContents.length
            }
            me.dataParams = {
                content: me.generalInfo.filters,
                total: me.generalInfo.filters.length
            }
        })
    }

    ngAfterContentChecked(): void {

    }

    checkExistReportName(){
        if(this.generalInfo.name == this.oldReportName || this.generalInfo.name == null) return;
        let me = this;
        this.debounceService.set("reportDynamicName", this.reportService.checkExistNameReportDynamic.bind(this.reportService), this.generalInfo.name, (reponse)=>{
            if(reponse == 0){
                me.isReportNameExisted = false;
            }else{
                me.isReportNameExisted = true;
            }
        })
    }

    enablePreviewChecked(event){
        if(event.checked.length > 0){
            this.generalInfo.enablePreview = CONSTANTS.REPORT_PREVIEW.ENABLE;
        }else{
            this.generalInfo.enablePreview = CONSTANTS.REPORT_PREVIEW.DISABLE;
        }
    }

    getHeaderParameter(){
        if(this.modeParameter == CONSTANTS.MODE_VIEW.CREATE){
            return this.tranService.translate("report.text.createParameter");
        }else if(this.modeParameter == CONSTANTS.MODE_VIEW.UPDATE){
            return this.tranService.translate("report.text.updateParameter");
        }else{
            return this.tranService.translate("report.text.detailParameter");
        }
    }

    getHeaderTable(){
        if(this.modeTable == CONSTANTS.MODE_VIEW.CREATE){
            return this.tranService.translate("report.text.createTable");
        }else if(this.modeTable == CONSTANTS.MODE_VIEW.UPDATE){
            return this.tranService.translate("report.text.updateTable");
        }else{
            return this.tranService.translate("report.text.detailTable");
        }
    }

    openCreateTable(){
        this.tableInfo = {
            id: null,
            columnDisplay: null,
            columnQueryResult: null,
            query: null,
            reportConfigId: this.generalInfo.id,
            schema: null,
            tableName: null,
            columns: []
        }
        this.formTable = this.formBuilder.group(this.tableInfo);
        this.isTableNameExisted = false;
        this.modeTable = CONSTANTS.MODE_VIEW.CREATE;
        this.optionTableInput.mode = CONSTANTS.MODE_VIEW.CREATE;
        this.isShowDialogTable = true;
        this.tableInputControl.reset();
    }

    openCreateParameter(){
        this.parameterInfo = {
            prDisplayName: null,
            prKey: null,
            prType: null,
            id: null,
            valueList: [],
            required: false,
            dateType: null,
            isAutoComplete: false,
            isMultiChoice: false,
            queryInfo: null,
            displayPattern: null,
            input: null,
            objectKey: null,
            output: null,
            queryParam: null,
            prValue: null,
        }
        this.formParameter = this.formBuilder.group(this.parameterInfo);
        this.isParamDisplayExisted = false;
        this.isParamKeyExisted = false;
        this.optionParamInput.mode = CONSTANTS.MODE_VIEW.CREATE;
        this.modeParameter = CONSTANTS.MODE_VIEW.CREATE;
        this.isShowDialogParameter = true;
        this.paramInputControl.reset();


    }

    onSubmit(){
        let me = this;
        if(this.formGeneralInfo.invalid || this.isReportNameExisted) return;
        let data = {
            ...this.generalInfo
        }
        data.reportContents.forEach(el => {
            if(el.id < 0){
                el.id = null;
            }
        })
        let dataFilter = data.filters || [];
        dataFilter.forEach(el => {
            delete el.id;
            if(el.prType == CONSTANTS.PARAMETER_TYPE.LIST_NUMBER || el.prType == CONSTANTS.PARAMETER_TYPE.LIST_STRING){
                el.valueList.forEach(ele => {
                    delete ele.id;
                    if(ele['mode']){
                        delete ele['mode']
                    }
                })
            }
        })
        data.filterParams = JSON.stringify(dataFilter);
        delete data.filters;
        if(data.enablePreview == null){
            data.enablePreview = CONSTANTS.REPORT_PREVIEW.DISABLE;
        }
        me.messageCommonService.onload();
        if(me.generalInfo.id == null){
            this.reportService.createGeneralReportDynamic(data, (response)=>{
                me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"));
                me.generalInfo = response;
                me.saveSuccess(me.generalInfo.id);
            }, null, ()=>{
                me.messageCommonService.offload();
            })
        }else{
            this.reportService.updateGeneralReportDynamic(me.generalInfo.id, data, (response)=>{
                me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"));
                me.generalInfo = response;
                me.onload();
            }, null, ()=>{
                me.messageCommonService.offload();
            })
        }
    }

    checkExistTableName(){
        for(let i = 0;i< this.generalInfo.reportContents.length;i++){
            if(this.generalInfo.reportContents[i].tableName == this.tableInfo.tableName && this.generalInfo.reportContents[i].id != this.tableInfo.id){
                this.isTableNameExisted = true;
                return;
            }
        }
        this.isTableNameExisted = false;
    }

    checkExistParamKey(){
        for(let i = 0;i< this.generalInfo.filters.length;i++){
            if(this.generalInfo.filters[i].prKey == this.parameterInfo.prKey && this.generalInfo.filters[i].id != this.parameterInfo.id){
                this.isParamKeyExisted = true;
                return;
            }
        }
        this.isParamKeyExisted = false;
    }

    checkExistParamDisplay(){
        for(let i = 0;i< this.generalInfo.filters.length;i++){
            if(this.generalInfo.filters[i].prDisplayName == this.parameterInfo.prDisplayName && this.generalInfo.filters[i].id != this.parameterInfo.id){
                this.isParamDisplayExisted = true;
                return;
            }
        }
        this.isParamDisplayExisted = false;
    }

    getTextSchema(value):string{
        for(let i = 0;i<this.schemas.length;i++){
            if(value == this.schemas[i].value){
                return this.schemas[i].name;
            }
        }
        return "";
    }

    getTextParamType(value): string{
        for(let i = 0;i<this.parameterTypes.length;i++){
            if(value == this.parameterTypes[i].value){
                return this.parameterTypes[i].name;
            }
        }
        return "";
    }

    saveTable(){
        if(this.formTable.invalid || this.tableInfo.columns == null || this.tableInfo.columns == undefined || this.tableInfo.columns.length == 0 || this.tableInputControl.isUpdating == true || this.isTableNameExisted) return;
        let data = {...this.tableInfo};
        data.columnDisplay = data.columns.map(el => el.display).toLocaleString();
        data.columnQueryResult = data.columns.map(el => el.key).toLocaleString();
        this.isShowDialogTable = false;
        delete data.columns;
        if(data.id == null){
            data.id = this.indexFakeTable --;
            this.generalInfo.reportContents.push(data);
        }else{
            for(let i = 0;i < this.generalInfo.reportContents.length; i++){
                if(this.generalInfo.reportContents[i].id == data.id){
                    this.generalInfo.reportContents[i] = data;
                    break;
                }
            }
        }
        this.dataTables.content = this.generalInfo.reportContents;
        this.dataTables.total = this.generalInfo.reportContents.length;
    }

    saveParameter(){
        if(this.checkInvalidFormParameter() == true) return;
        let data = {...this.parameterInfo};
        data.queryInfo = {
            displayPattern: data.displayPattern,
            queryParam: data.queryParam,
            input: data.input,
            output: data.output,
            objectKey: data.objectKey
        }
        this.isShowDialogParameter = false;
        if(data.id == null){
            data.id = this.indexFakeParameter --;
            this.generalInfo.filters.push(data);
        }else{
            for(let i = 0;i < this.generalInfo.filters.length; i++){
                if(this.generalInfo.filters[i].id == data.id){
                    this.generalInfo.filters[i] = data;
                    break;
                }
            }
        }
        this.generalInfo.filterParams = JSON.stringify(this.generalInfo.filters);
    }

    checkInvalidFormParameter():boolean{
        if(this.formParameter.invalid) return true;
        if(this.isParamDisplayExisted || this.isParamKeyExisted) return true;
        if(this.parameterInfo.prType == CONSTANTS.PARAMETER_TYPE.LIST_NUMBER || this.parameterInfo.prType == CONSTANTS.PARAMETER_TYPE.LIST_STRING){
            if(this.parameterInfo.isAutoComplete == false){
                if(this.parameterInfo.valueList == null || this.parameterInfo.valueList == undefined || this.parameterInfo.valueList.length == 0) return true;
            }
        }
        return false;
    }

    changeParamType(){
        let me = this;
        this.columnParamInput = [
            {
                align: 'left',
                key: "display",
                name: this.tranService.translate("report.label.valueDisplay"),
                size: "280px",
                type: CONSTANTS.PARAMETER_TYPE.STRING,
                validate: {
                    required: true,
                    maxLength: 255,
                    exists: true,
                    pattern: /^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]*$/,
                    messageErrorPattern: me.tranService.translate("global.message.formatContainVN")
                }
            },
            {
                align: 'left',
                key: "value",
                name: this.tranService.translate("report.label.valueDB"),
                size: "280px",
                type: this.parameterInfo.prType == CONSTANTS.PARAMETER_TYPE.LIST_STRING ? CONSTANTS.PARAMETER_TYPE.STRING : CONSTANTS.PARAMETER_TYPE.NUMBER,
                validate: {
                    required: true,
                    maxLength: 255,
                    // pattern: /^[a-zA-Z0-9\-_]+$/,
                    // messageErrorPattern: me.tranService.translate("global.message.formatCode"),
                    exists: true
                }
            }
        ]
        if(this.parameterInfo.prType != CONSTANTS.PARAMETER_TYPE.LIST_NUMBER && this.parameterInfo.prType != CONSTANTS.PARAMETER_TYPE.LIST_STRING){
            this.parameterInfo.isMultiChoice = false;
        }
        if(this.parameterInfo.prType != CONSTANTS.PARAMETER_TYPE.STRING && this.parameterInfo.prType != CONSTANTS.PARAMETER_TYPE.LIST_STRING){
            this.parameterInfo.isAutoComplete = false;
        }
        this.parameterInfo.valueList = [];
        if(this.paramInputControl.reset){
            this.paramInputControl.reset();
        }
    }

    changeIsAutoComplete(){

    }

    copyText(event){
        let me = this;
        let value = document.querySelector("textarea#query");
        if(value){
            let text = value["value"];
            me.utilService.copyToClipboard(text,() => {
                me.messageCommonService.success(me.tranService.translate("global.message.copied"));
            });
        }else{
            this.messageCommonService.warning(this.tranService.translate("global.message.error"));
        }
    }

    protected readonly CONSTANTS = CONSTANTS
}
