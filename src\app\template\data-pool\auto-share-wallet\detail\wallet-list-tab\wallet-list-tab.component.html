<ng-template #walletListTemplateRef>
    <div style="box-shadow: 0px 10px 15px -3px rgba(0,0,0,0.1);">
        <div class="flex align-items-center">
            <div class="col-3">
                <input class="w-full"
                       pInputText
                       [(ngModel)]="searchInfo.value"
                       (keydown.enter)="search1()"
                       [placeholder]="tranService.translate('sim.label.quickSearch')"
                />
            </div>
            <div class="col-3">
                <p-button icon="pi pi-search"
                          styleClass="p-button-rounded p-button-secondary p-button-text button-search"
                          (click)="search1()"
                ></p-button>
            </div>
        </div>
        <table-vnpt
                [tableId]="'tableTrafficWallet'"
                [fieldId]="'id'"
                [columns]="columns"
                [dataSet]="dataSet"
                [options]="optionTable"
                [pageNumber]="pageNumber"
                [loadData]="search.bind(this)"
                [pageSize]="pageSize"
                [sort]="sort"
                [params]="searchInfo"
        ></table-vnpt>
    </div>
</ng-template>

<!-- popup detail wallet -->
<div class="flex justify-content-center dialog-vnpt">
    <p-dialog [header]="tranService.translate('global.button.view')" [(visible)]="isShowModalDetail" [modal]="true" [style]="{ width: '980px' }" [draggable]="false" [resizable]="false" *ngIf="isShowModalDetail">
        <p-card styleClass="my-3">
            <div class="text-2xl font-bold pb-2">{{subCodeId}}</div>
            <div *ngIf="walletDetail">
                <div class="flex flex-row surface-200 p-4 border-round">
                    <div class="flex-1">
                        <div class="font-medium text-base">{{tranService.translate('datapool.label.payCode')}}</div>
                        <div class="font-semibold text-lg">{{walletDetail.payCode}}</div>
                    </div>
                    <div class="flex-1">
                        <div class="font-medium text-base">{{tranService.translate('datapool.label.packageName')}}</div>
                        <div class="font-semibold text-lg">{{walletDetail.packageName}}</div>
                    </div>
                    <div class="flex-1">
                        <div class="font-medium text-base">{{tranService.translate('datapool.label.phoneFull')}}</div>
                        <div class="font-semibold text-lg">{{walletDetail.phoneActive}}</div>
                    </div>
                    <div class="flex-1">
                        <div class="font-medium text-base">{{tranService.translate("datapool.label.tax")}}</div>
                        <div class="font-semibold text-lg">{{walletDetail.tax}}</div>
                    </div>
                </div>
            </div>
            <div class="flex flex-row p-4 border-round">
                <div class="flex-1">
                    <div class="font-medium text-base">{{tranService.translate("datapool.label.trafficType")}}</div>
                    <div class="font-semibold text-lg">{{walletDetail.trafficType}}</div>
                </div>
                <div class="flex-1">
                    <div class="font-medium text-base">{{tranService.translate("datapool.label.methodAutoShare")}}</div>
                    <div class="font-semibold text-lg">{{getValueMethodAutoShare(walletDetail.autoType)}}</div>
                </div>
                <div class="flex-1">
                    <div class="font-medium text-base">{{tranService.translate('datapool.label.remainData')}}/ {{tranService.translate("datapool.label.purchasedData")}}</div>
                    <div class="font-semibold text-lg" *ngIf="walletDetail.trafficType == 'Gói Data'" >{{formatNumber(walletDetail.totalRemainingTraffic)}}/ {{formatNumber(walletDetail.purchasedTraffic)}} MB</div>
                    <div class="font-semibold text-lg" *ngIf="walletDetail.trafficType == 'Gói thoại'" >{{formatNumber(walletDetail.totalRemainingTraffic)}}/ {{formatNumber(walletDetail.purchasedTraffic)}} {{tranService.translate('alert.label.minutes')}}</div>
                    <div class="font-semibold text-lg" *ngIf="(( walletDetail.trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase()))" >{{formatNumber(walletDetail.totalRemainingTraffic)}}/ {{formatNumber(walletDetail.purchasedTraffic)}} SMS</div>
                    <div class="font-semibold text-lg" *ngIf="!(( walletDetail.trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase())) && walletDetail.trafficType != 'Gói Data'&&walletDetail.trafficType != 'Gói thoại'" >{{formatNumber(walletDetail.totalRemainingTraffic)}}/ {{formatNumber(walletDetail.purchasedTraffic)}}</div>
                </div>
                <div class="flex-1">
                    <div class="font-medium text-base">{{tranService.translate("datapool.label.usedTime")}}</div>
                    <div class="font-semibold text-lg">{{walletDetail.timeToUse}}</div>
                </div>
            </div>
        </p-card>

        <p-card>
            <div class="text-lg font-bold">{{tranService.translate("datapool.label.shareInfo")}}</div>
<!--            <p-table-->
<!--                #dt2-->
<!--                [value]="listDetail"-->
<!--                dataKey="id"-->
<!--                [rows]="10"-->
<!--                [rowsPerPageOptions]="[5, 10, 25, 50]"-->
<!--                [paginator]="true"-->
<!--                [tableStyle]="{ 'margin-top': '10px' }"-->
<!--            >-->
<!--                <ng-template pTemplate="header">-->
<!--                    <tr>-->
<!--                        <th>{{tranService.translate('datapool.label.phoneFull')}}</th>-->
<!--                        <th>{{tranService.translate('datapool.label.fullName')}}</th>-->
<!--                        <th>{{tranService.translate('datapool.label.email')}}</th>-->
<!--                        <th>{{tranService.translate('datapool.label.sharedTime')}}</th>-->
<!--                        <th>{{tranService.translate('datapool.label.usedDate')}}</th>-->
<!--                        <th>{{tranService.translate('datapool.label.shared')}}</th>-->
<!--                        <th >{{tranService.translate('datapool.label.percentage')}}</th>-->
<!--                    </tr>-->
<!--                </ng-template>-->
<!--                <ng-template pTemplate="body" let-listDetail>-->
<!--                    <tr>-->
<!--                        <td>-->
<!--                            {{ listDetail.phoneReceipt }}-->
<!--                        </td>-->
<!--                        <td>-->
<!--                            {{ listDetail.name }}-->
<!--                        </td>-->
<!--                        <td>-->
<!--                            {{ listDetail.email }}-->
<!--                        </td>-->
<!--                        <td>-->
<!--                            {{ getFormattedDate(listDetail.timeUpdate) }}-->
<!--                        </td>-->
<!--                        <td>-->
<!--                            {{ getFormattedDate(listDetail.timeUpdate, listDetail?.dayExprired) }}-->
<!--                        </td>-->
<!--                        <td *ngIf="listDetail.trafficType == 'Gói Data'">-->
<!--                            {{ formatNumber(listDetail.trafficShare) }} MB-->
<!--                        </td>-->
<!--                        <td *ngIf="listDetail.trafficType == 'Gói thoại'">-->
<!--                            {{formatNumber(listDetail.trafficShare) }} {{tranService.translate('alert.label.minutes')}}-->
<!--                        </td>-->
<!--                        <td *ngIf=" ((listDetail.trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase()))">-->
<!--                            {{ formatNumber(listDetail.trafficShare) }} SMS-->
<!--                        </td>-->
<!--                        <td *ngIf="!((listDetail.trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase())) && listDetail.trafficType != 'Gói Data' && listDetail.trafficType != 'Gói thoại'">-->
<!--                            {{ formatNumber(listDetail.trafficShare) }}-->
<!--                        </td>-->
<!--                        <td>-->
<!--                            {{ listDetail.percentTraffic }}%-->
<!--                        </td>-->
<!--                    </tr>-->
<!--                </ng-template>-->
<!--            </p-table>-->
            <table-vnpt *ngIf="isShowTableInDialogDetail"
                [fieldId]="'id'"
                [columns]="columnsShareWallet"
                [dataSet]="dataSetShareWallet"
                [pageSize]="pageSizeShareWallet"
                [pageNumber]="pageNumberShareWallet"
                [options]="optionTableShareWallet"
                [sort]="sortShareWallet"
                [loadData]="searchShareWallet.bind(this)"
            ></table-vnpt>
        </p-card>
    </p-dialog>
</div>

