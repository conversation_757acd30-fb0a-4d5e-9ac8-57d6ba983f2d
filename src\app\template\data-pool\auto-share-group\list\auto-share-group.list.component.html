<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 mt-3">
    <div class="">
        <div class="text-xl font-bold mb-1">{{tranService.translate("global.menu.autoShareGroup")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
</div>

<p-card styleClass="mt-2 responsive-pcard">
    <div class="flex flex-row gap-3">
        <input style="min-width: 35vw"  type="text" pInputText [placeholder]="tranService.translate('sim.label.quickSearch')" (keydown.enter)="onQuickSearch()" [(ngModel)]="valueSearch" [ngModelOptions]="{standalone: true}">
        <p-button icon="pi pi-search"
                  styleClass="ml-3 p-button-rounded p-button-secondary p-button-text button-search"
                  type="button"
                  (click)="onQuickSearch()"
        ></p-button>
    </div>
</p-card>

<table-vnpt
    [fieldId]="'id'"
    [columns]="columns"
    [dataSet]="dataSet"
    [options]="optionTable"
    [loadData]="search.bind(this)"
    [pageNumber]="pageNumber"
    [pageSize]="pageSize"
    [sort]="sort"
    [params]="searchInfo"
    [labelTable]="this.tranService.translate('global.menu.listGroupSub')"
></table-vnpt>
