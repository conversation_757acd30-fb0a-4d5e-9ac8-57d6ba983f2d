<style>
    /* .col-3{
        padding: 10px;
    } */
</style>

<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{tranService.translate("global.menu.listsim")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
    <div class="responsive-button-container col-5 flex flex-row justify-content-end align-items-center gap-2">
        <p-button (click)="showModalDeleteSim()" [disabled]="selectItems.length === 0" icon="pi pi-minus-circle" label="{{tranService.translate('global.button.delete')}}" styleClass="p-button p-button-danger equal-button"></p-button>
        <p-splitButton styleClass="mr-2 p-button-info" [label]="tranService.translate('global.button.export')" styleClass="equal-button" icon="pi pi-download" [model]="itemExports" (onClick)="helpExport(1)"></p-splitButton>
<!--        <p-splitButton *ngIf="checkAuthen([allPermissions.GROUP_SIM.UPDATE,allPermissions.GROUP_SIM.CREATE])" styleClass="p-button-success" [label]="tranService.translate('global.button.pushGroupSim')" icon="pi pi-file-import" [model]="itemPushGroups" [disabled]="checkInValidPushToGroupSim()"></p-splitButton>-->
    </div>
</div>

<form [formGroup]="formSearchSim" (ngSubmit)="onSubmitSearch()" class="pb-2 pt-3 vnpt-field-set">
    <p-panel [toggleable]="true" (collapsedChange)="onCollapsedChange($event)" [collapsed]="searchPanelCollaps">
        <ng-template pTemplate="icons">

        </ng-template>
        <ng-template pTemplate="header">
            <div *ngIf="searchPanelCollaps"  class="flex flex-row gap-3">
                <input style="min-width: 35vw"  type="text" pInputText [placeholder]="tranService.translate('sim.label.quickSearch')" (keydown.enter)="quickSearch()" [(ngModel)]="quickSearchValue" [ngModelOptions]="{standalone: true}">
                <p-button icon="pi pi-search"
                          styleClass="ml-3 p-button-rounded p-button-secondary p-button-text button-search"
                          type="button"
                          (click)="quickSearch()"
                ></p-button>
            </div>

            <div *ngIf="!searchPanelCollaps" class="font-bold text-lg">{{tranService.translate("global.text.advanceSearch")}}</div>
        </ng-template>
        <div class="grid search-grid-2">
            <!-- so thue bao -->
            <div class="col-3">
                <span class="p-float-label">
                    <input pInputText
                            class="w-full"
                            pInputText id="msisdn"
                            [(ngModel)]="searchInfo.msisdn"
                            formControlName="msisdn"
                    />
                    <label htmlFor="msisdn">{{tranService.translate("sim.label.sothuebao")}}</label>
                </span>
            </div>
            <!-- ismi -->
            <div class="col-3">
                <span class="p-float-label">
                    <input class="w-full"
                            pInputText id="imsi"
                            [(ngModel)]="searchInfo.imsi"
                            formControlName="imsi"
                    />
                    <label htmlFor="imsi">{{tranService.translate("sim.label.imsi")}}</label>
                </span>
            </div>
            <!-- status -->
            <div class="col-3">
                <span class="p-float-label">
                    <p-dropdown styleClass="w-full" [showClear]="true"
                            id="status" [autoDisplayFirst]="false"
                            [(ngModel)]="searchInfo.status"
                            formControlName="status"
                            [options]="statuSims"
                            optionLabel="name"
                            optionValue="value"
                    ></p-dropdown>
                    <label for="status">{{tranService.translate("sim.label.trangthaisim")}}</label>
                </span>
            </div>
            <!-- nhom sim -->
            <div class="col-3">
                <div class="relative">
                    <vnpt-select
                        class="w-full"
                        [(value)]="searchInfo.simGroupId"
                        [placeholder]="tranService.translate('sim.label.nhomsim')"
                        objectKey="dropdownListSim"
                        paramKey="name"
                        keyReturn="id"
                        displayPattern="${name} - ${groupKey}"
                        typeValue="primitive"
                        [floatLabel]="true"
                        [paramDefault]="{type: 'groupSim'}"
                        [stylePositionBoxSelect] = "{top: '40px', right: '0px', 'min-width':'100%'}"
                        [isMultiChoice]="false"
                    ></vnpt-select>
                </div>
            </div>
             <!-- maapn -->
             <div class="col-3">
                <span class="p-float-label">
                    <input class="w-full"
                            pInputText id="apnId"
                            [(ngModel)]="searchInfo.apnId"
                            formControlName="apnId"
                    />
                    <label htmlFor="apnId">{{tranService.translate("sim.label.maapn")}}</label>
                </span>
            </div>
            <!-- goi cuoc -->
            <div class="col-3">
                <div class="relative">
                    <vnpt-select
                        class="w-full"
                        [(value)]="searchInfo.ratingPlanId"
                        [placeholder]="tranService.translate('sim.label.goicuoc')"
                        objectKey="dropdownListSim"
                        paramKey="name"
                        keyReturn="id"
                        displayPattern="${name} - ${code}"
                        typeValue="primitive"
                        [paramDefault]="{type: 'ratingPlan'}"
                        [floatLabel]="true"
                        [isMultiChoice]="false"
                    ></vnpt-select>
                </div>
            </div>
            <!-- ma hop dong -->
            <div class="col-3">
                <div class="relative">
                    <vnpt-select
                        class="w-full"
                        [(value)]="searchInfo.contractCode"
                        [placeholder]="tranService.translate('sim.label.mahopdong')"
                        objectKey="dropdownListSim"
                        paramKey="name"
                        keyReturn="contractCode"
                        displayPattern="${contractCode}"
                        typeValue="primitive"
                        [paramDefault]="{type: 'contract'}"
                        [floatLabel]="true"
                        [isMultiChoice]="false"
                    ></vnpt-select>
                </div>
            </div>
            <!-- nguoi lam hop dong -->
            <div class="col-3">
                <span class="p-float-label">
                    <input class="w-full"
                            pInputText id="contractor"
                            [(ngModel)]="searchInfo.contractor"
                            formControlName="contractor"
                    />
                    <label htmlFor="contractor">{{tranService.translate("sim.label.nguoilamhopdong")}}</label>
                </span>
            </div>
            <!-- khach hang -->
            <div class="col-3">
                <div class="relative">
                    <vnpt-select
                        class="w-full"
                        [(value)]="searchInfo.customer"
                        [placeholder]="tranService.translate('sim.label.khachhang')"
                        objectKey="dropdownListSim"
                        paramKey="name"
                        keyReturn="customerCode"
                        displayPattern="${customerName} - ${customerCode}"
                        typeValue="primitive"
                        [paramDefault]="{type: 'customer'}"
                        [floatLabel]="true"
                        [isMultiChoice]="false"
                    ></vnpt-select>
                </div>
            </div>
            <div class="col-3 pb-0">
                <span class="p-float-label">
                    <p-calendar styleClass="w-full"
                            id="dateFrom"
                            [(ngModel)]="searchInfo.dateFrom"
                            formControlName="dateFrom"
                            [showIcon]="true"
                            [showClear]="true"
                            dateFormat="dd/mm/yy"
                            [maxDate]="maxDateFrom"
                            (onSelect)="onChangeDateFrom(searchInfo.dateFrom)"
                            (onInput)="onChangeDateFrom(searchInfo.dateFrom)"
                    ></p-calendar>
                    <label htmlFor="dateFrom">{{tranService.translate("sim.label.ngaylamhopdongtu")}}</label>
                </span>
            </div>
            <div class="col-3 pb-0">
                <span class="p-float-label">
                    <p-calendar styleClass="w-full"
                            id="dateTo"
                            [(ngModel)]="searchInfo.dateTo"
                            formControlName="dateTo"
                            [showIcon]="true"
                            [showClear]="true"
                            dateFormat="dd/mm/yy"
                            [minDate]="minDateTo"
                            [maxDate]="maxDateTo"
                            (onSelect)="onChangeDateTo(searchInfo.dateTo)"
                            (onInput)="onChangeDateTo(searchInfo.dateTo)"
                    />
                    <label htmlFor="dateTo">{{tranService.translate("sim.label.ngaylamhopdongden")}}</label>
                </span>
            </div>
            <div class="col-3">
                <span class="p-float-label">
                    <p-dropdown styleClass="w-full" [showClear]="true"
                                id="simType" [autoDisplayFirst]="false"
                                [(ngModel)]="searchInfo.simType"
                                formControlName="simType"
                                [options]="typeSims"
                                optionLabel="name"
                                optionValue="value"
                    ></p-dropdown>
                    <label for="status">{{tranService.translate("sim.label.simType")}}</label>
                </span>
            </div>
            <!-- Tinh thanh pho -->
            <div class="col-3" [class]="userType == optionuserType.ADMIN ? '': 'hidden'">
                 <span class="p-float-label">
                <p-dropdown styleClass="w-full"
                            [showClear]="true"
                            id="province" [autoDisplayFirst]="false"
                            [(ngModel)]="searchInfo.provinceCode"
                            formControlName="provinceCode"
                            [options]="listProvince"
                            optionLabel="name"
                            [filter]="true" filterBy="name"
                            optionValue="code"
                            [placeholder]="tranService.translate('account.text.selectProvince')"
                            [emptyFilterMessage]="tranService.translate('global.text.nodata')"
                ></p-dropdown>
                    <label for="provinceCode">{{tranService.translate("account.label.province")}}</label>
                </span>
            </div>
<!--            tai khoan-->
            <div class="col-3">
                <div class="relative">
                    <vnpt-select
                        class="w-full"
                        [(value)]="searchInfo.userId"
                        [placeholder]="tranService.translate('account.text.account')"
                        objectKey="dropdownListUser"
                        paramKey="username"
                        keyReturn="id"
                        displayPattern="${username}"
                        [isMultiChoice]="false"
                        [floatLabel]="true"
                    ></vnpt-select>
                </div>
            </div>

            <div class="col-3 pb-0">
                <p-button icon="pi pi-search"
                            styleClass="ml-3 p-button-rounded p-button-secondary p-button-text button-search"
                            type="submit"
                ></p-button>
            </div>
        </div>
    </p-panel>
</form>

<table-vnpt
    [tableId]="'tableSimList'"
    [fieldId]="'msisdn'"
    [(selectItems)]="selectItems"
    [columns]="columns"
    [dataSet]="dataSet"
    [options]="optionTable"
    [loadData]="search.bind(this)"
    [pageNumber]="pageNumber"
    [pageSize]="pageSize"
    [sort]="sort"
    [params]="searchInfo"
    [labelTable]="this.tranService.translate('global.menu.listsim')"
></table-vnpt>

<div class="flex justify-content-center dialog-push-group">
    <p-dialog [header]="tranService.translate('global.button.pushGroupSim')" [(visible)]="isShowDialogPushGroup" [modal]="true" [style]="{ width: '500px' }" [draggable]="false" [resizable]="false">
        <div class="w-full field grid">
            <label htmlFor="groupSim" class="col-fixed" style="width:100px">{{tranService.translate("sim.label.nhomsim")}}</label>
            <div class="col" style="max-width: calc(100% - 140px);">
                <vnpt-select
                    class="w-full"
                    [(value)]="groupSimSelected"
                    [placeholder]="tranService.translate('sim.text.selectGroupSim')"
                    objectKey="groupSim"
                    paramKey="name"
                    keyReturn="id"
                    displayPattern="${name} - ${groupKey}"
                    typeValue="primitive"
                    [isMultiChoice]="false"
                    [paramDefault]="paramSearchGroupSim"
                ></vnpt-select>
            </div>
            <div style="width:40px">
                <p-button *ngIf="checkAuthen([allPermissions.GROUP_SIM.CREATE])"
                    styleClass="p-button-info" icon="pi pi-plus"
                    [pTooltip]="tranService.translate('global.button.add')" (click)="isShowDialogCreateGroup = true;isShowDialogPushGroup = false;"></p-button>
            </div>
        </div>
        <div class="flex flex-row justify-content-center align-items-center">
            <p-button styleClass="mr-2 p-button-secondary p-button-outlined" [label]="tranService.translate('global.button.cancel')" (click)="isShowDialogPushGroup = false;groupSimSelected=null"></p-button>
            <p-button styleClass="p-button-info" [label]="tranService.translate('global.button.save')" (click)="pushGroupSim(0)" [disabled]="groupSimSelected == null || groupSimSelected == undefined"></p-button>
        </div>
    </p-dialog>
</div>

<!-- chi tiết sim -->
<div class="flex justify-content-center dialog-vnpt">
    <p-dialog [header]="tranService.translate('sim.text.detailSim')" [(visible)]="isShowModalDetailSim" [modal]="true" [style]="{ width: '980px' }" [draggable]="false" [resizable]="false" *ngIf="isShowModalDetailSim">
        <div class="grid grid-1 mt-1 h-auto" style="width: calc(100% + 16px);">
            <div class="col sim-detail pr-0">
                <p-card [header]="tranService.translate('sim.text.simInfo')" *ngIf="Object.keys(detailSim).length > 0">
                    <div class="flex flex-row justify-content-between custom-card">
                        <div class="w-6">
                            <div class="grid">
                                <span style="min-width: 150px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.sothuebao")}}</span>
                                <span class="col">{{detailSim.msisdn}}</span>
                            </div>
                            <div class="sim-status-block mt-1 grid">
                                <span style="min-width: 150px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.trangthaisim")}}</span>
                                <span class="w-auto ml-3" [class]="getClassStatus(detailSim.status)">{{getNameStatus(detailSim.status)}}</span>
                            </div>
                            <div class="mt-1 grid">
                                <span style="min-width: 150px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.imsi")}}</span>
                                <span class="col">{{detailSim.imsi}}</span>
                            </div>
                            <div class="mt-1 grid">
                                <span style="min-width: 150px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.imeiDevice")}}</span>
                                <span class="col">{{detailSim.imei}}</span>
                            </div>
                            <div class="mt-1 grid">
                                <span style="min-width: 150px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.maapn")}}</span>
                                <span class="col">{{detailSim.apnId}}</span>
                            </div>
                            <div class="sim-status-block mt-1 grid">
                                <span style="min-width: 150px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.trangthaiketnoi")}}</span>
                                <span class=" ml-3 p-2 border-round inline-block" [ngClass]="getSimStatus(detailSim.connectionStatus) == 'ON'? 'text-green-800 bg-green-100' : 'text-50 surface-500'" [pTooltip]="getTooltipStatus(detailSim.connectionStatus)">{{getSimStatus(detailSim.connectionStatus)}}</span>
                            </div>
                        </div>
                        <div class="w-6">
                            <div class="grid">
                                <span style="min-width: 150px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.startDate")}}</span>
                                <span class="col">{{detailSim.startDate | date:'dd/MM/yyyy'}}</span>
                            </div>
                            <div class="mt-1 grid">
                                <span style="min-width: 150px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.serviceType")}}</span>
                                <span class="w-auto ml-3">{{getServiceType(detailSim.serviceType)}}</span>
                            </div>
                        </div>
                    </div>
                </p-card>
                <p-card [header]="tranService.translate('sim.text.simInfo')" *ngIf="Object.keys(detailSim).length === 0">
                    <div class="rounded border border-surface-200 dark:border-surface-700 p-6 bg-surface-0 dark:bg-surface-900">
                        <p-skeleton></p-skeleton>
                    </div>
                </p-card>
                <p-card [header]="tranService.translate('sim.text.simStatusInfo')" styleClass="mt-3 sim-status" *ngIf="Object.keys(detailStatusSim).length > 0">
                    <div class="grid">
                        <div class="col-4 text-center">
                            <p-toggleButton [(ngModel)]="detailStatusSim.statusData" [disabled]="true" onLabel="ON" offLabel="OFF"></p-toggleButton>
                            <div>{{tranService.translate("sim.status.service.data")}}</div>
                        </div>
                        <div class="col-4 text-center">
                            <p-toggleButton [(ngModel)]="detailStatusSim.statusReceiveCall" [disabled]="true" onLabel="ON" offLabel="OFF"></p-toggleButton>
                            <div>{{tranService.translate("sim.status.service.callReceived")}}</div>
                        </div>
                        <div class="col-4 text-center">
                            <p-toggleButton [(ngModel)]="detailStatusSim.statusSendCall" [disabled]="true" onLabel="ON" offLabel="OFF"></p-toggleButton>
                            <div>{{tranService.translate("sim.status.service.callSent")}}</div>
                        </div>
                    </div>
                    <div class="grid">
                        <div class="col-4 text-center">
                            <p-toggleButton [(ngModel)]="detailStatusSim.statusWorldCall" [disabled]="true" onLabel="ON" offLabel="OFF"></p-toggleButton>
                            <div>{{tranService.translate("sim.status.service.callWorld")}}</div>
                        </div>
                        <div class="col-4 text-center">
                            <p-toggleButton [(ngModel)]="detailStatusSim.statusReceiveSms" [disabled]="true" onLabel="ON" offLabel="OFF"></p-toggleButton>
                            <div>{{tranService.translate("sim.status.service.smsReceived")}}</div>
                        </div>
                        <div class="col-4 text-center">
                            <p-toggleButton [(ngModel)]="detailStatusSim.statusSendSms" [disabled]="true" onLabel="ON" offLabel="OFF"></p-toggleButton>
                            <div>{{tranService.translate("sim.status.service.smsSent")}}</div>
                        </div>
                    </div>
                </p-card>
                <p-card [header]="tranService.translate('sim.text.simStatusInfo')" styleClass="mt-3" *ngIf="Object.keys(detailStatusSim).length === 0">
                    <div class="rounded border border-surface-200 dark:border-surface-700 p-6 bg-surface-0 dark:bg-surface-900">
                        <p-skeleton></p-skeleton>
                    </div>
<!--                    <p-progressSpinner></p-progressSpinner>-->
                </p-card>
                <!-- goi cuoc -->
                <p-card [header]="tranService.translate('sim.text.ratingPlanInfo')" styleClass="mt-3"  *ngIf="Object.keys(detailRatingPlan).length > 0">
                    <div class="grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.tengoicuoc")}}</span>
                        <span class="col">{{detailSim.ratingPlanName}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.dataUseInMonth")}}</span>
                        <span class="col">{{this.utilService.bytesToMegabytes(detailRatingPlan.dataUseInMonth) | number }} {{detailRatingPlan.unit?detailRatingPlan.unit:"MB"}}</span>
                    </div>
                </p-card>
                <p-card [header]="tranService.translate('sim.text.ratingPlanInfo')" styleClass="mt-3" *ngIf="Object.keys(detailRatingPlan).length === 0">
                    <div class="rounded border border-surface-200 dark:border-surface-700 p-6 bg-surface-0 dark:bg-surface-900">
                        <p-skeleton></p-skeleton>
                    </div>
                </p-card>
            </div>
            <div class="col sim-detail pr-0">
                <!-- hop dong -->
                <p-card [header]="tranService.translate('sim.text.contractInfo')" *ngIf="Object.keys(detailContract).length > 0">
                    <div class="grid mt-0">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.mahopdong")}}</span>
                        <span class="col">{{detailContract.contractCode}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.ngaylamhopdong")}}</span>
                        <span class="col">{{detailContract.contractDate | date:'dd/MM/yyyy'}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.nguoilamhopdong")}}</span>
                        <span class="col uppercase">{{detailContract.contractorInfo}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.matrungtam")}}</span>
                        <span class="col">{{detailContract.centerCode}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.dienthoailienhe")}}</span>
                        <span class="col">{{detailContract.contactPhone}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.diachilienhe")}}</span>
                        <span class="col">{{detailContract.contactAddress}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.paymentName")}}</span>
                        <span class="col uppercase">{{detailContract.paymentName}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.paymentAddress")}}</span>
                        <span class="col">{{detailContract.paymentAddress}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.routeCode")}}</span>
                        <span class="col">{{detailContract.routeCode}}</span>
                    </div>
                </p-card>
                <p-card [header]="tranService.translate('sim.text.contractInfo')" *ngIf="Object.keys(detailContract).length === 0">
                    <div class="rounded border border-surface-200 dark:border-surface-700 p-6 bg-surface-0 dark:bg-surface-900">
                        <p-skeleton></p-skeleton>
                    </div>
                </p-card>
                <!-- customer -->
                <p-card [header]="tranService.translate('sim.text.customerInfo')" styleClass="mt-3" *ngIf="Object.keys(detailCustomer).length > 0">
                    <div class="grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.khachhang")}}</span>
                        <span class="col">{{detailCustomer.name}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.customerCode")}}</span>
                        <span class="col">{{detailCustomer.code}}</span>
                    </div>
                </p-card>
                <p-card [header]="tranService.translate('sim.text.customerInfo')" styleClass="mt-3" *ngIf="Object.keys(detailCustomer).length === 0">
                    <div class="rounded border border-surface-200 dark:border-surface-700 p-6 bg-surface-0 dark:bg-surface-900">
                        <p-skeleton></p-skeleton>
                    </div>
                </p-card>
            </div>
        </div>
    </p-dialog>
</div>

<div class="flex justify-content-center dialog-create-group">
    <p-dialog [header]="tranService.translate('global.text.createGroupSimAndPushSimToGroup')" [(visible)]="isShowDialogCreateGroup" [modal]="true" [style]="{ width: '500px' }" [draggable]="false" [resizable]="false">
        <form [formGroup]="formCreateGroupSim" (ngSubmit)="pushGroupSim(1)">
            <!-- group scope -->
            <div class="w-full field grid">
                <label htmlFor="customer" class="col-fixed" style="width:140px">{{tranService.translate("groupSim.label.groupScope")}}</label>
                <div class="col">
                    <span *ngIf="groupScope == groupScopeObjects.GROUP_ADMIN">{{tranService.translate("groupSim.scope.admin")}}</span>
                    <span *ngIf="groupScope == groupScopeObjects.GROUP_PROVINCE">{{tranService.translate("groupSim.scope.province")}}</span>
                    <span *ngIf="groupScope == groupScopeObjects.GROUP_CUSTOMER">{{tranService.translate("groupSim.scope.customer")}}</span>
                </div>
            </div>
            <!-- group key -->
            <div class="w-full field grid">
                <label htmlFor="groupKey" class="col-fixed" style="width:140px">{{tranService.translate("sim.label.groupKey")}}<span class="text-red-500">*</span></label>
                <div class="col">
                    <input class="w-full"
                            pInputText id="groupKey"
                            [(ngModel)]="dataCreateGroupSim.groupKey"
                            formControlName="groupKey"
                            [required]="true"
                            [maxLength]="16"
                            pattern="[a-zA-Z0-9\-_]*"
                            [placeholder]="tranService.translate('sim.text.inputGroupKey')"
                            (keyup)="checkExistGroupKey()"
                    />
                </div>
            </div>
            <div class="w-full field grid text-error-field">
                <label htmlFor="groupName" class="col-fixed" style="width:140px"></label>
                <div class="col">
                    <small class="text-red-500" *ngIf="formCreateGroupSim.controls.groupKey.dirty && formCreateGroupSim.controls.groupKey.errors?.required">{{tranService.translate("global.message.required")}}</small>
                    <small class="text-red-500" *ngIf="formCreateGroupSim.controls.groupKey.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:16})}}</small>
                    <small class="text-red-500" *ngIf="formCreateGroupSim.controls.groupKey.errors?.pattern">{{tranService.translate("global.message.formatCode")}}</small>
                    <small class="text-red-500" *ngIf="!formCreateGroupSim.controls.groupKey.errors?.required && isExistsGroupKey">{{tranService.translate("global.message.exists",{type:tranService.translate("sim.label.groupKey").toLowerCase()})}}</small>
                </div>
            </div>
            <!-- group name -->
            <div class="w-full field grid">
                <label htmlFor="name" class="col-fixed" style="width:140px">{{tranService.translate("sim.label.groupName")}}<span class="text-red-500">*</span></label>
                <div class="col">
                    <input class="w-full"
                            pInputText id="name"
                            [(ngModel)]="dataCreateGroupSim.name"
                            formControlName="name"
                            [required]="true"
                            [maxLength]="255"
                            pattern="[^~`!@#\$%^&*()=\+\[\]\{\}\|\\,<>/?]*"
                            [placeholder]="tranService.translate('sim.text.inputGroupName')"
                    />
                </div>
            </div>
            <div class="w-full field grid text-error-field">
                <label htmlFor="name" class="col-fixed" style="width:140px"></label>
                <div class="col">
                    <small class="text-red-500" *ngIf="formCreateGroupSim.controls.name.dirty && formCreateGroupSim.controls.name.errors?.required">{{tranService.translate("global.message.required")}}</small>
                    <small class="text-red-500" *ngIf="formCreateGroupSim.controls.name.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:255})}}</small>
                    <small class="text-red-500" *ngIf="formCreateGroupSim.controls.name.errors?.pattern">{{tranService.translate("global.message.formatContainVN")}}</small>
                </div>
            </div>
            <!-- customer -->
            <div class="w-full field grid" *ngIf="groupScope == groupScopeObjects.GROUP_CUSTOMER">
                <label htmlFor="customer" class="col-fixed" style="width:140px">{{tranService.translate("sim.label.khachhang")}}</label>
                <div class="col">
                    {{customerName}} - {{customerCode}}
                </div>
            </div>
            <!-- province -->
            <div class="w-full field grid" *ngIf="groupScope == groupScopeObjects.GROUP_PROVINCE">
                <label htmlFor="province" class="col-fixed" style="width:140px">{{tranService.translate("account.label.province")}}</label>
                <div class="col">
                    {{provinceName}} ({{provinceCode}})
                </div>
            </div>
            <!-- description -->
            <div class="w-full field grid">
                <label htmlFor="description" class="col-fixed" style="width:140px">{{tranService.translate("sim.label.description")}}</label>
                <div class="col">
                    <textarea  class="w-full" style="resize: none;"
                            rows="3"
                            [autoResize]="false"
                            pInputTextarea id="description"
                            [(ngModel)]="dataCreateGroupSim.description"
                            formControlName="description"
                            [placeholder]="tranService.translate('sim.text.inputDescription')"
                            [maxLength]="255"
                    ></textarea>
                </div>
            </div>
            <div class="w-full field grid text-error-field">
                <label htmlFor="groupName" class="col-fixed" style="width:140px"></label>
                <div class="col">
                    <small class="text-red-500" *ngIf="formCreateGroupSim.controls.description.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:255})}}</small>
                </div>
            </div>
            <div class="flex flex-row justify-content-center align-items-center">
                <p-button styleClass="mr-2 p-button-secondary p-button-outlined" [label]="tranService.translate('global.button.cancel')" (click)="isShowDialogCreateGroup = false"></p-button>
                <p-button styleClass="p-button-info" [label]="tranService.translate('global.button.save')" type="submit" [disabled]="formCreateGroupSim.invalid || isExistsGroupKey"></p-button>
            </div>
        </form>
    </p-dialog>
<!--    dialog xóa sim-->
    <div class="flex justify-content-center">
        <p-dialog [header]="tranService.translate('sim.label.deleteSim')" [(visible)]="isShowModalDeleteSim" [modal]="true" [style]="{ width: '500px' }" [draggable]="false" [resizable]="false">
            <div class="flex flex-row justify-content-center align-items-center">
                <p>{{tranService.translate("sim.text.deleteSim")}}</p>
            </div>
            <div class="flex flex-row justify-content-center align-items-center mt-3">
                <p-button styleClass="mr-2 p-button-secondary" [label]="tranService.translate('global.button.cancel')" (click)="isShowModalDeleteSim = false"></p-button>
                <p-button styleClass="p-button-info" [label]="tranService.translate('global.button.agree')" (click)="deleteSim()" ></p-button>
            </div>
        </p-dialog>
    </div>
</div>
