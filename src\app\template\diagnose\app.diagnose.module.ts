import {NgModule} from "@angular/core";
import {CommonModule} from "@angular/common";
import {BreadcrumbModule} from 'primeng/breadcrumb';
import {AppSimRoutingModule} from "./app.diagnose.routing";
import {FieldsetModule} from 'primeng/fieldset';
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {InputTextModule} from 'primeng/inputtext';
import {ButtonModule} from 'primeng/button';
import {CommonVnptModule} from "../common-module/common.module";
import {SplitButtonModule} from 'primeng/splitbutton';
import {DropdownModule} from 'primeng/dropdown';
import {AutoCompleteModule} from 'primeng/autocomplete';
import {CalendarModule} from 'primeng/calendar';
import {DialogModule} from 'primeng/dialog';
import {CardModule} from 'primeng/card';
import {SplitterModule} from 'primeng/splitter';
import {ToggleButtonModule} from 'primeng/togglebutton';
import {RadioButtonModule} from 'primeng/radiobutton';
import {InputTextareaModule} from 'primeng/inputtextarea';
import {MultiSelectModule} from 'primeng/multiselect';
import {PanelModule} from 'primeng/panel';
import {TableModule} from "primeng/table";
import {DiagnoseComponent} from "./app.diagnose.comppnent";
import {DiagnoseService} from "../../service/diagnose/DiagnoseService";
import {SelectButtonModule} from "primeng/selectbutton";

@NgModule({
    imports: [
        CommonModule,
        AppSimRoutingModule,
        BreadcrumbModule,
        FieldsetModule,
        FormsModule,
        ReactiveFormsModule,
        InputTextModule,
        ButtonModule,
        CommonVnptModule,
        SplitButtonModule,
        DropdownModule,
        AutoCompleteModule,
        CalendarModule,
        DialogModule,
        CardModule,
        SplitterModule,
        ToggleButtonModule,
        RadioButtonModule,
        MultiSelectModule,
        InputTextareaModule,
        PanelModule,
        TableModule,
        SelectButtonModule
    ],
    declarations: [
        DiagnoseComponent,
    ],
    providers: [
        DiagnoseService,
    ]
})
export class AppDiagnoseModule {
}
