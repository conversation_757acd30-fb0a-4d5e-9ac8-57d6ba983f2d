export default {
    label: {
        name: "Name",
        customer: "Customer",
        contractCode: "Contract Code",
        statusSIM: "Status SIM",
        subscriptionNumber: "Subscription number",
        group: "Group SIM",
        status: "Status",
        afterTime: "After time",
        afterCount: "After count",
        unit: "Unit",
        unitValue: "Value",
        description: "Description",
        level: "Severity",
        groupReceiving: "Alert Group Receiving",
        url: "URL:",
        emails: "Email to receive alerts",
        topic: "Topic Email",
        contentEmail: "Content Email",
        contentSms: "Content SMS",
        sms: "Phone number to receive SMS",
        alertreceivinggroup: "Alert receiving group",
        simstatus: "Sim Status",
        time: "Time",
        fromdate: "Time from",
        todate: "Time to",
        minutes: "Minutes",
        news: "News",
        rule: "Rule category",
        event: "Rule trigger",
        action: "Action Type",
        appliedPlan: "Rate plan apply",
        frequency:"Repeat Frequency",
        repeat:"Repeat",
        exceededPakage:"Package threshold (%)",
        exceededValue: "Threshold value (MB)",
        smsExceededPakage:"SMS threshold (%)",
        smsExceededValue:"Threshold value (Unit)",
        inactivePopup: "Click to stop working" ,
        activePopup :"Click to activate",
        wallet: "Wallet",
        thresholdValue: "Threshold Value",
        walletEmail: "Email to receive alerts",
        walletPhone: "Phone number to receive SMS",
    },
    text: {
        inputName: "Alert Name",
        inputStatusSIM: "Select Status SIM",
        inputCustomer: "Select Customer",
        inputContractCode: "Select Contract Code",
        inputSubscriptionNumber: "Select Subscription Number",
        inputGroup: "Select Group",
        inputafterTime: "After time",
        inputafterCount: "After count",
        inputunit: "Select Unit",
        inputunitValue: "Value",
        inputDescription: "Input Description",
        inputlevel: "Select Severity",
        inputgroupReceiving: "Select Alert Group Receiving",
        inputurl: "Input URL",
        inputemails: "Enter email to receive alerts",
        inputtopic: "Input Topic Email",
        inputcontentEmail: "Enter content email",
        inputcontentSms: "Enter content sms",
        inputsms: "Enter phone number to receive alerts",
        headerAPI: "Receive Alert via API",
        headerEmail: "Receive Alert via Email",
        headerSMS: "Receive Alert via SMS",
        labelAlert: "Alert Information",
        inputNameReceiving: "Input Name Alert Receiving Group",
        removeAlert: "Remove Email",
        removeSms: "Remove SMS",
        rule: "Select Rule",
        eventType: "Select Event Type",
        appliedPlan: "Enter Applied Plan",
        actionType :"Select Action Type",
        filterApplieInfo: "Filter",
        sendNotifyExpiredData : "Send notification when the package is about to expire in advance",
        hour: "hour",
        sendType: "Form of notification"
    },
    status: {
        active: "Active",
        inactive: "Inactive",
    },
    type: {
        admin: "Admin",
        customer: "Customer",
        province: "Province",
        district: "District",
        agency: "Agency",
    },
    statusSim: {
        outPlan: "Data exceed the packet threshold",
        outLine: "Data exceeds the threshold value",
        disconnected: "Disconnected",
        newConnection: "New Connections"
    },

    receiving: {
        name: "Name Alert Receiving Group",
        description: "Description",
        emails: "Email",
        sms: "SMS",
    },
    severity: {
        critical: "CRITICAL",
        major: "MAJOR",
        minor: "MINOR",
        info: "INFO",
    },
    eventType:{
        exceededPakage:"Data threshold",
        exceededValue: "Data value threshold",
        sessionEnd : "Session end",
        sessionStart : "Session start",
        smsExceededPakage:"SMS threshold",
        smsExceededValue:"SMS value threshold",
        owLock:"One-way lock",
        twLock:"Two-way lock",
        noConection:"No connection",
        simExp:"Packet expiration",
        datapoolExp:"Datapool Expiration",
        subExp:"Subscription Expired",
        dataWalletExp:"Data wallet expiration",
        owtwlock: "One-way lock - Two-way lock",
        walletThreshold: "Wallet Traffic Reached Threshold",
    },
    actionType:{
        alert:"Alert",
        api:"API"
    },
    ruleCategory:{
        monitoring:"Usage monitoring",
        management:"Subscription management",
    },
    message:{
        existedPlan:"Package Code Used",
        checkboxRequired : "Please select at least 1 checkbox",
        exceededPakage: "Warning for subscriber [msisdn] of data usage reaching threshold [value] % of plan [plan_name]",
        smsExceededPakage: "Warning for subscriber [msisdn] using SMS reaching threshold [value] % of plan [plan_name]",
        exceededValue: "Warning for subscriber [msisdn] using data reaching the threshold value [value] MB",
        smsExceededValue: "Warning subscriber [msisdn] using number of SMS reaching threshold value [value]",
        status: "Warning that subscriber [msisdn] has [status]",
    }
}
