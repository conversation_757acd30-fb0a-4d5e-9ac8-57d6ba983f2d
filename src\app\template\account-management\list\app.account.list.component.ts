import { AfterContentChecked, Component, Inject, Injector, OnInit } from "@angular/core";
import { FormBuilder } from "@angular/forms";
import { MenuItem } from "primeng/api";
import { AccountService } from "src/app/service/account/AccountService";
import { ColumnInfo, OptionTable } from "../../common-module/table/table.component";
import { CONSTANTS } from "src/app/service/comon/constants";
import { AutoCompleteCompleteEvent } from "primeng/autocomplete";
import { ComponentBase } from "src/app/component.base";
import { ComboLazyControl } from "../../common-module/combobox-lazyload/combobox.lazyload";
import { CustomerService } from "src/app/service/customer/CustomerService";
import {ContractService} from "../../../service/contract/ContractService";
@Component({
    selector: "app-account-list",
    templateUrl: './app.account.list.component.html'
})
export class AppAccountListComponent extends ComponentBase implements OnInit, AfterContentChecked{
    userType: number;
    searchInfo: {
        username: string | null,
        fullName: string | null,
        type: number | null,
        email: string | null,
        provinceCode: object | null,
        status: number | null,
        loggable : boolean | null,
    }
    statusAccounts: Array<any>;
    items: MenuItem[];
    home: MenuItem;
    selectItems: Array<any>;
    columns: Array<ColumnInfo>;
    dataSet: {
        content: Array<any>,
        total: number
    };
    optionTable: OptionTable;
    pageNumber: number;
    pageSize: number;
    sort: string;
    isShowDialogChangeManageLevel: boolean = false;
    accountId: number | string;
    listAccounts: Array<any>;
    accountSelected: any;
    listProvince: Array<any>;
    listStatus: Array<any>;
    allPermissions = CONSTANTS.PERMISSIONS;

    isShowDialogChangeManageData: boolean = false;
    formChangeManageData: any;
    changeManageDataInfo: {
        userId: number | null,
        accountCustomerIds: Array<any> | null,
        typeSelect: 0 | 1
    };
    paramSearchTeller: {
        type: number | null
        provinceCode: string | null,
        status: number | null
    } = {
        status: CONSTANTS.USER_STATUS.ACTIVE,
        type: CONSTANTS.USER_TYPE.DISTRICT,
        provinceCode: this.sessionService.userInfo.provinceCode
    }
    tellerSelected: any;
    listTellerExcludes: Array<any> | null = [];
    searchUserTellerController: ComboLazyControl = new ComboLazyControl();
    searchUserCustomerController: ComboLazyControl = new ComboLazyControl();
    isShowModalDetail: boolean = false;
    accountResponse: any;
    accountInfo: {
        accountName: string| null,
        fullName: string|null,
        email: string|null,
        phone: string|null,
        userType: number| null,
        province: any,
        roles: Array<any>,
        description: string|null,
        manager: any,
        customers: Array<any>
    };
    optionUserType: any;
    listRole: Array<any>;
    constructor(@Inject(AccountService) private accountService: AccountService,
                private customerService: CustomerService,
                private contractService: ContractService,
                private formBuilder: FormBuilder,
                private injector: Injector) {
        super(injector);
    }
    formSearchAccount: any;
    paramQuickSearchCustomer: {
        keyword: string|null,
        accountRootId: number| null,
    }
    columnInfoCustomer: Array<ColumnInfo>;
    optionTableCustomer: OptionTable;
    dataSetCustomer: {
        content: Array<any>,
        total: number,
    }
    paginationCustomer: {
        page: number|null,
        size: number|null,
        sortBy: string|null,
    }
    paramQuickSearchContract: {
        keyword: string|null,
        customerIds: Array<{ id: number }>|null,
        //search contract theo id này
        accountRootId: number| null,
    }
    columnInfoContract: Array<ColumnInfo>;
    optionTableContract: OptionTable;
    dataSetContract: {
        content: Array<any>,
        total: number,
    }
    paginationContract: {
        page: number|null,
        size: number|null,
        sortBy: string|null,
    }

    isShowSecretKey = true
    listModule = []
    //sẽ lưu lại list api sau khi đã chọn
    selectItemGrantApi: Array<any> = []
    paginationGrantApi: {
        page: number|null,
        size: number|null,
        sortBy: string|null,
    }
    columnInfoGrantApi: Array<ColumnInfo>;

    dataSetGrantApi: {
        content: Array<any>,
        total: number,
    }
    optionTableGrantApi: OptionTable;

    paramsSearchGrantApi = {api : null, module : null}

    genGrantApi = {clientId: null, secretKey: null}

    statusGrantApi : any = null;

    userInfo = this.sessionService.userInfo;
    ngOnInit(): void {
        let me = this;
        this.optionUserType = CONSTANTS.USER_TYPE;
        this.userType = this.sessionService.userInfo.type;
        this.items = [{ label: this.tranService.translate("global.menu.accountmgmt") }, { label: this.tranService.translate("global.menu.listaccount") }];
        this.home = { icon: 'pi pi-home', routerLink: '/' };
        this.searchInfo = {
            username: null,
            type: null,
            email: null,
            provinceCode: null,
            fullName: null,
            status: null,
            loggable : null
        }
        this.accountInfo = {
            accountName: null,
            fullName: null,
            email: null,
            phone: null,
            userType: null,
            province: null,
            roles: null,
            description: null,
            manager: null,
            customers: null
        };
        if(this.userType == CONSTANTS.USER_TYPE.ADMIN){
            this.statusAccounts = [
                {name: this.tranService.translate("account.usertype.admin"),value:CONSTANTS.USER_TYPE.ADMIN},
                {name: this.tranService.translate("account.usertype.customer"),value:CONSTANTS.USER_TYPE.CUSTOMER},
                {name: this.tranService.translate("account.usertype.province"),value:CONSTANTS.USER_TYPE.PROVINCE},
                {name: this.tranService.translate("account.usertype.district"),value:CONSTANTS.USER_TYPE.DISTRICT},
                // {name: this.tranService.translate("account.usertype.agency"),value:CONSTANTS.USER_TYPE.AGENCY},
            ]
        }else if(this.userType == CONSTANTS.USER_TYPE.PROVINCE){
            this.statusAccounts = [
                {name: this.tranService.translate("account.usertype.customer"),value:CONSTANTS.USER_TYPE.CUSTOMER},
                // {name: this.tranService.translate("account.usertype.province"),value:CONSTANTS.USER_TYPE.PROVINCE},
                {name: this.tranService.translate("account.usertype.district"),value:CONSTANTS.USER_TYPE.DISTRICT},
                // {name: this.tranService.translate("account.usertype.agency"),value:CONSTANTS.USER_TYPE.AGENCY},
            ]
        }else if(this.userType == CONSTANTS.USER_TYPE.DISTRICT){
            this.statusAccounts = [
                {name: this.tranService.translate("account.usertype.customer"),value:CONSTANTS.USER_TYPE.CUSTOMER},
                // {name: this.tranService.translate("account.usertype.district"),value:CONSTANTS.USER_TYPE.DISTRICT},
                // {name: this.tranService.translate("account.usertype.agency"),value:CONSTANTS.USER_TYPE.AGENCY},
            ]
        }else if(this.userType == CONSTANTS.USER_TYPE.CUSTOMER){
            this.statusAccounts = [
                {name: this.tranService.translate("account.usertype.customer"),value:CONSTANTS.USER_TYPE.CUSTOMER},
            ]
        }else if(this.userType == CONSTANTS.USER_TYPE.AGENCY){
            this.statusAccounts = [
                {name: this.tranService.translate("account.usertype.customer"),value:CONSTANTS.USER_TYPE.AGENCY},
            ]
        }
        this.listStatus = [
            {name: this.tranService.translate("account.userstatus.active"),value:CONSTANTS.USER_STATUS.ACTIVE },
            {name: this.tranService.translate("account.userstatus.inactive"),value:CONSTANTS.USER_STATUS.INACTIVE }
        ]
        this.paginationGrantApi = {
            page: 0,
            size: 10,
            sortBy: "id,desc",
        }
        this.columnInfoGrantApi = [
            {
                name: "API",
                key: "name",
                size: "30%",
                align: "left",
                isShow: true,
                isSort: true,
            },
            {
                name: "Module",
                key: "module",
                size: "50%",
                align: "left",
                isShow: true,
                isSort: true,
            }
        ]

        this.dataSetGrantApi = {
            content: [],
            total: 0,
        }

        this.optionTableGrantApi = {
            hasClearSelected: false,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
        }
        this.formSearchAccount = this.formBuilder.group(this.searchInfo);
        this.selectItems = [];
        this.pageNumber = 0;
        this.pageSize = 10;
        this.sort = "createdDate,asc";

        this.optionTable = {
            hasClearSelected: true,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
            action: [
                {
                    icon: "pi pi-user-edit",
                    tooltip: this.tranService.translate("global.button.edit"),
                    func: function(id, item){
                        me.router.navigate([`/accounts/edit/${id}`]);
                    },
                    funcAppear: function(id, item) {
                        return me.checkPermission([CONSTANTS.PERMISSIONS.ACCOUNT.UPDATE]);
                    }
                },
                {
                    icon: "pi pi-lock",
                    tooltip: this.tranService.translate("global.button.changeStatus"),
                    func: function(id, item){
                        me.messageCommonService.confirm(
                            me.tranService.translate("global.message.titleConfirmChangeStatusAccount"),
                            me.tranService.translate("global.message.confirmChangeStatusAccount"),
                            {
                                ok:()=>{
                                    me.accountService.changeStatus(id, (response)=>{
                                        me.messageCommonService.success(me.tranService.translate("global.message.changeStatusSuccess"));
                                        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);
                                    })
                                },
                                cancel: ()=>{
                                    // me.messageCommonService.error(me.tranService.translate("global.message.changeStatusFail"));
                                }
                            }
                        )
                    },
                    funcAppear: function(id, item) {
                        return item.status == CONSTANTS.USER_STATUS.ACTIVE && me.checkPermission([CONSTANTS.PERMISSIONS.ACCOUNT.CHANGE_STATUS]);;
                    }
                },
                {
                    icon: "pi pi-lock-open",
                    tooltip: this.tranService.translate("global.button.changeStatus"),
                    func: function(id, item){
                        me.messageCommonService.confirm(
                            me.tranService.translate("global.message.titleConfirmChangeStatusAccount"),
                            me.tranService.translate("global.message.confirmChangeStatusAccount"),
                            {
                                ok:()=>{
                                    me.accountService.changeStatus(id, (response)=>{
                                        me.messageCommonService.success(me.tranService.translate("global.message.changeStatusSuccess"));
                                        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);
                                    })
                                },
                                cancel: ()=>{
                                    // me.messageCommonService.error(me.tranService.translate("global.message.changeStatusFail"));
                                }
                            }
                        )
                    },
                    funcAppear: function(id, item) {
                        return item.status == CONSTANTS.USER_STATUS.INACTIVE && me.checkPermission([CONSTANTS.PERMISSIONS.ACCOUNT.CHANGE_STATUS]);;
                    }
                },
                // {
                //     icon: "pi pi-sync",
                //     func: function(id, item){
                //         me.isShowDialogChangeManageLevel = true;
                //         me.accountId = parseInt(id);
                //     },
                //     funcAppear: function(id, item) {
                //         return item.isHasChild === true;
                //     }
                // },
                {
                    icon: "pi pi-sync",
                    tooltip: this.tranService.translate("global.button.changeManageData"),
                    func: function(id, item){
                        me.isShowDialogChangeManageData = true;
                        me.changeManageDataInfo = {
                            userId: null,
                            accountCustomerIds: [],
                            typeSelect: 0
                        }
                        me.tellerSelected = item;
                        me.listTellerExcludes = [id];
                        me.paramSearchTeller.provinceCode = item.provinceCode;
                        me.formChangeManageData = me.formBuilder.group(me.changeManageDataInfo);
                        me.searchUserTellerController.reload();
                        me.searchUserCustomerController.reload();
                    },
                    funcAppear: function(id, item) {
                        return item.type == CONSTANTS.USER_TYPE.DISTRICT && item.status == CONSTANTS.USER_STATUS.INACTIVE && me.checkPermission([CONSTANTS.PERMISSIONS.ACCOUNT.CHANGE_MANAGER_DATA]);;
                    }
                },
                {
                    icon: "pi pi-trash",
                    tooltip: this.tranService.translate("global.button.delete"),
                    func: function(id, item){
                        me.messageCommonService.confirm(
                            me.tranService.translate("global.message.titleConfirmDeleteAccount"),
                            me.tranService.translate("global.message.confirmDeleteAccount"),
                            {
                                ok:()=>{
                                    me.accountService.deleleUser(id, (response)=>{
                                        me.messageCommonService.success(me.tranService.translate("global.message.deleteSuccess"));
                                        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);
                                    })
                                },
                                cancel: ()=>{
                                    // me.messageCommonService.error(me.tranService.translate("global.message.deleteFail"));
                                }
                            }
                        )
                    },
                    funcAppear: function(id, item) {
                        if (item.isRootCustomer !== true && me.checkPermission([CONSTANTS.PERMISSIONS.ACCOUNT.DELETE])) {
                            return true;
                        } else if (item.isRootCustomer == true && item.isHasChild !== true && me.checkPermission([CONSTANTS.PERMISSIONS.ACCOUNT.DELETE])) {
                            return true;
                        } else {
                            return false;
                        }
                    }
                },
            ]
        },
        this.columns = [
            {
                name: this.tranService.translate("account.label.username"),
                key: "username",
                size: "250px",
                align: "left",
                isShow: true,
                isSort: true,
                style:{
                    cursor: "pointer",
             color: "var(--mainColorText)"
                },
                funcClick(id, item) {
                    me.accountId = id;
                    me.getDetail();
                    me.isShowModalDetail = true;
                },
            },
            {
                name: this.tranService.translate("account.label.fullname"),
                key: "fullName",
                size: "300px",
                align: "left",
                isShow: true,
                isSort: true,
                isShowTooltip:true,
                funcGetClassname(){
                    return ["max-w-13rem", "text-overflow-ellipsis", "inline-block", "overflow-hidden"]
                }
            },
            {
                name: this.tranService.translate("account.label.userType"),
                key: "type",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: true,
                funcConvertText(value) {
                    if(value == CONSTANTS.USER_TYPE.ADMIN){
                        return me.tranService.translate("account.usertype.admin");
                    }else if(value == CONSTANTS.USER_TYPE.CUSTOMER){
                        return me.tranService.translate("account.usertype.customer");
                    }else if(value == CONSTANTS.USER_TYPE.PROVINCE){
                        return me.tranService.translate("account.usertype.province");
                    }else if(value == CONSTANTS.USER_TYPE.DISTRICT){
                        return me.tranService.translate("account.usertype.district");
                    }else if(value == CONSTANTS.USER_TYPE.AGENCY){
                        return me.tranService.translate("account.usertype.agency");
                    }else{
                        return "";
                    }
                },
            },
            {
                name: this.tranService.translate("account.label.email"),
                key: "email",
                size: "300px",
                align: "left",
                isShow: true,
                isSort: true,
            },
            {
                name: this.tranService.translate("account.label.province"),
                key: "provinceCode",
                size: "175px",
                align: "left",
                isShow: true,
                isSort: true,
                funcConvertText(value) {
                    if(!me.listProvince) return value;
                    for(let i = 0; i < me.listProvince.length; i++){
                        if(me.listProvince[i].code == value){
                            return `${me.listProvince[i].name} (${value})`
                        }
                    }
                    return "";
                },
            },
            {
                name: this.tranService.translate("account.label.time"),
                key: "createdDate",
                size: "125px",
                align: "left",
                isShow: true,
                isSort: true,
            },
            {
                name: this.tranService.translate("account.label.status"),
                key: "status",
                size: "175px",
                align: "left",
                isShow: true,
                isSort: true,
                funcConvertText(value) {
                    if(value == CONSTANTS.USER_STATUS.ACTIVE){
                        return me.tranService.translate("account.userstatus.active");
                    }else if(value == CONSTANTS.USER_STATUS.INACTIVE){
                        return me.tranService.translate("account.userstatus.inactive");
                    }else{
                        return "";
                    }
                },
            },
        ]
        this.getListProvince();
        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
        this.paramQuickSearchCustomer = {
            keyword: null,
            accountRootId: Number(this.accountId),
        }
        this.columnInfoCustomer = [
            {
                name: this.tranService.translate("customer.label.customerCode"),
                key: "code",
                size: "30%",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("customer.label.customerName"),
                key: "name",
                size: "50%",
                align: "left",
                isShow: true,
                isSort: false,
            },
        ]
        this.dataSetCustomer = {
            content: [],
            total: 0,
        }
        this.paginationCustomer = {
            page: 0,
            size: 10,
            sortBy: "name,asc;id,asc",
        }
        this.optionTableCustomer = {
            hasClearSelected: false,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
        }
        this.paramQuickSearchContract = {
            keyword: null,
            customerIds: [],
            accountRootId: -1,
        }
        this.columnInfoContract = [
            {
                name: this.tranService.translate("customer.label.customerCode"),
                key: "customerCode",
                size: "30%",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("customer.label.customerName"),
                key: "customerName",
                size: "50%",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("contract.label.contractCode"),
                key: "contractCode",
                size: "50%",
                align: "left",
                isShow: true,
                isSort: false,
            },
        ]
        this.dataSetContract = {
            content: [],
            total: 0,
        }
        this.paginationContract = {
            page: 0,
            size: 10,
            sortBy: "customerName,asc;id,asc",
        }
        this.optionTableContract = {
            hasClearSelected: false,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
        }
    }

    ngAfterContentChecked(): void {
        if(this.isShowDialogChangeManageLevel == false){
            this.accountSelected = null;
        }
    }

    onSubmitSearch(){
        this.pageNumber = 0;
        this.searchInfo.loggable = true;
        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
    }

    search(page, limit, sort, params){
        let me = this;
        this.pageNumber = page;
        this.pageSize = limit;
        this.sort = sort;
        let dataParams = {
            page,
            size: limit,
            sort
        }
        Object.keys(this.searchInfo).forEach(key => {
            if(this.searchInfo[key] != null){
                dataParams[key] = this.searchInfo[key];
            }
        })
        me.messageCommonService.onload();
        this.accountService.search(dataParams, (response)=>{
            me.dataSet = {
                content: response.content,
                total: response.totalElements
            }
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }

    filterListAccount(event: AutoCompleteCompleteEvent){
        let valueFilter = event.query;
        this.listAccounts = [
            {
                id: 1,
                name: "Tai khoan 1",
            },
            {
                id: 2,
                name: "Tai khoan 2",
            },
            {
                id: 3,
                name: "Tai khoan 3",
            },
            {
                id: 4,
                name: "Tai khoan 4",
            }
        ].filter(el => el.name.indexOf(valueFilter) >= 0);
    }

    getListProvince(){
        this.accountService.getListProvince((response)=>{
            this.listProvince = response.map(el => {
                return {
                    ...el,
                    display: `${el.code} - ${el.name}`
                }
            })
        })
    }

    checkPermission(permissions: string[]): boolean{
        return this.checkAuthen(permissions);
    }

    changeManageLevel(){

    }

    loadListUserCustomerOfTeller(params, callback){
        if(this.tellerSelected){
            let p = {
                fullname: params.fullName,
                userManageId: this.tellerSelected.id,
                page: params.page,
                size: params.size,
                sort: params.sort
            }
            this.accountService.searchAccountUserOfUser(p, callback)
        }
    }

    changeManageData(){
        let data = {
            newUserId: this.changeManageDataInfo.userId,
            oldUserId: this.tellerSelected.id,
            accountCustomerIds: this.changeManageDataInfo.typeSelect == 0 ? null : this.changeManageDataInfo.accountCustomerIds
        }
        let me = this;
        me.messageCommonService.onload();
        this.accountService.changeManageData(data, (response)=>{
            me.isShowDialogChangeManageData = false;
            me.messageCommonService.success(me.tranService.translate("global.message.success"));
        }, null, ()=>{
            me.messageCommonService.offload();
        })

    }

    getDetail(){
        let me = this;
        me.messageCommonService.onload();
        this.accountService.getById(Number(me.accountId), (response)=>{
            me.accountResponse = response;
            me.accountInfo.accountName = response.username;
            me.accountInfo.fullName = response.fullName;
            me.accountInfo.email = response.email;
            me.accountInfo.description = response.description;
            me.accountInfo.phone = response.phone;
            me.accountInfo.province = response.provinceCode;
            me.accountInfo.userType = response.type;
            me.getListRole(false);
            if (me.accountInfo.userType == CONSTANTS.USER_TYPE.CUSTOMER) {
                me.resetPaginationCustomerAndContract()
                me.paramQuickSearchCustomer.accountRootId = Number(me.accountId)
                me.paramQuickSearchContract.customerIds = (me.accountResponse.customers|| []).map(customer => customer.customerId)
                me.paramQuickSearchContract.accountRootId = Number(me.accountId)
            }
            me.statusGrantApi = String(response.statusApi)
            me.selectItemGrantApi = response.listApiId? response.listApiId.map(el=> ({id: el})) : [{id:-99}]
            me.genGrantApi.secretKey = response.secretId
            me.genGrantApi.clientId = response.username
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }

    getListRole(isClear){
        this.accountService.getListRole(this.accountInfo.userType, (response)=>{
            this.listRole = response.map(el => {
                return {
                    id: el.id,
                    name: el.name
                }
            });
            if(isClear){
                this.accountInfo.roles = null;
            }else{
                this.accountInfo.roles = this.listRole.filter(el => (this.accountResponse.roles||[]).includes(el.id));
            }
        })
    }

    getStringUserStatus(value) {
        if(value == CONSTANTS.USER_STATUS.ACTIVE){
            return this.tranService.translate("account.userstatus.active");
        }else if(value == CONSTANTS.USER_STATUS.INACTIVE){
            return this.tranService.translate("account.userstatus.inactive");
        }else{
            return "";
        }
    }

    getStringUserType(value) {
        if(value == CONSTANTS.USER_TYPE.ADMIN){
            return this.tranService.translate("account.usertype.admin");
        }else if(value == CONSTANTS.USER_TYPE.CUSTOMER){
            return this.tranService.translate("account.usertype.customer");
        }else if(value == CONSTANTS.USER_TYPE.PROVINCE){
            return this.tranService.translate("account.usertype.province");
        }else if(value == CONSTANTS.USER_TYPE.DISTRICT){
            return this.tranService.translate("account.usertype.district");
        }else if(value == CONSTANTS.USER_TYPE.AGENCY){
            return this.tranService.translate("account.usertype.agency");
        }else{
            return "";
        }
    }
    onTabChange(event) {
        const tabName = event.originalEvent.target.innerText;
        let me = this;
        if (event && tabName.includes(this.tranService.translate('account.text.grantApi'))) {
            me.onSearchGrantApi()
        } else if (event && tabName.includes(this.tranService.translate('global.menu.listbill'))) {
            me.onSearchContract()
        } else if (event && tabName.includes(this.tranService.translate('global.menu.listcustomer'))) {
            me.onSearchCustomer()
        }
    }
    onSearchCustomer(back?) {
        let me = this;
        if(back) {
            me.paginationCustomer.page = 0;
        }
        me.searchCustomer(me.paginationCustomer.page, me.paginationCustomer.size, me.paginationCustomer.sortBy, me.paramQuickSearchCustomer);
    }
    searchCustomer(page, limit, sort, params){
        let me = this;
        this.paginationCustomer.page = page;
        this.paginationCustomer.size = limit;
        this.paginationCustomer.sortBy = sort;
        let dataParams = {
            page,
            size: limit,
            sort
        }
        Object.keys(this.paramQuickSearchCustomer).forEach(key => {
            if(this.paramQuickSearchCustomer[key] != null){
                dataParams[key] = this.paramQuickSearchCustomer[key];
            }
        })
        me.messageCommonService.onload();
        this.customerService.quickSearchCustomer(dataParams, this.paramQuickSearchCustomer,(response)=>{
            me.dataSetCustomer = {
                content: response.content,
                total: response.totalElements
            }
        }, null, ()=>{
            me.messageCommonService.offload();
        })
        // console.log(this.selectItemCustomer)
    }

    onSearchContract(back?) {
        let me = this;
        if(back) {
            me.paginationContract.page = 0;
        }
        me.searchContract(me.paginationContract.page, me.paginationContract.size, me.paginationContract.sortBy, me.paramQuickSearchContract);
    }
    searchContract(page, limit, sort, params){
        let me = this;
        this.paginationContract.page = page;
        this.paginationContract.size = limit;
        this.paginationContract.sortBy = sort;
        let dataParams = {
            page,
            size: limit,
            sort
        }
        // Object.keys(this.paramQuickSearchContract).forEach(key => {
        //     if(this.paramQuickSearchContract[key] != null){
        //         dataParams[key] = this.paramQuickSearchContract[key];
        //     }
        // })
        me.messageCommonService.onload();
        this.contractService.quickSearchContract(dataParams, this.paramQuickSearchContract,(response)=>{
            me.dataSetContract = {
                content: response.content,
                total: response.totalElements
            }
            // console.log(response)
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }

    resetPaginationCustomerAndContract() {
        this.paginationCustomer = {
            page: 0,
            size: 10,
            sortBy: "name,asc;id,asc",
        }
        this.paginationContract = {
            page: 0,
            size: 10,
            sortBy: "customerName,asc;id,asc",
        }
    }

    searchGrantApi(page, limit, sort, params){
        let me = this;
        this.paginationGrantApi.page = page;
        this.paginationGrantApi.size = limit;
        this.paginationGrantApi.sortBy = sort;
        let dataParams = {
            page,
            size: limit,
            sort,
            selectedApiIds: this.selectItemGrantApi.map(el=>el.id).join(',')
        }
        Object.keys(this.paramsSearchGrantApi).forEach(key => {
            if(this.paramsSearchGrantApi[key] != null){
                dataParams[key] = this.paramsSearchGrantApi[key];
            }
        })
        console.log(dataParams)
        me.messageCommonService.onload();
        this.accountService.searchGrantApi(dataParams,(response)=>{
            me.dataSetGrantApi = {
                content: response.content,
                total: response.totalElements
            }
        }, null, ()=>{
            me.messageCommonService.offload();
        })
        let copyParam = {...dataParams};
        copyParam.size = *********;
        this.accountService.searchGrantApi(copyParam,(response)=>{
            me.listModule = [...new Set(response.content.map(el=>el.module))]
            me.listModule = me.listModule.map(el=>({
                name : el,
                value : el
            }))
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }


    generateToken(n) {
        var chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        var token = '';
        for(var i = 0; i < n; i++) {
            token += chars[Math.floor(Math.random() * chars.length)];
        }
        return token;
    }

    genToken(){
        this.genGrantApi.secretKey = this.generateToken(20);
    }

    onSearchGrantApi(back?) {
        let me = this;
        console.log(me.paramsSearchGrantApi)
        if(back) {
            me.paginationGrantApi.page = 0;
        }
        me.searchGrantApi(me.paginationGrantApi.page, me.paginationGrantApi.size, me.paginationGrantApi.sortBy, me.paramsSearchGrantApi);
    }
    protected readonly CONSTANTS = CONSTANTS;
}
