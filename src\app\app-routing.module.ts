import { RouterModule } from '@angular/router';
import { NgModule } from '@angular/core';
import { AppLayoutComponent } from "./template/layout/app.layout.component";
import DataPage from './service/data.page';
import { AppDashboardComponent } from './template/dashboard/app.dashboard.component';
import { AppGuideComponent } from './template/pages/guide/app.guide.component';

@NgModule({
    imports: [
        RouterModule.forRoot([
            {
                path: '', component: AppLayoutComponent,
                children: [
                    {path: "", redirectTo: "/sims", pathMatch: "full"},
                    {path: "dashboard", loadChildren: () => import("./template/dashboard/app.dashboard.module").then(m => m.AppDashboardModule)},
                    {path: "sims", loadChildren: () => import("./template/sim-management/app.sim.module").then(m => m.AppSimModule)},
                    {path: "accounts", loadChildren: () => import("./template/account-management/app.account.module").then(m => m.AppAccountModule)},
                    {path: "policies", loadChildren: () => import("./template/term-policy/app.term.policy.module").then(m => m.TermPolicyModule)},
                    {path: "roles", loadChildren: () => import("./template/account-management/roles/app.roles.module").then(m => m.AppRolesModule)},
                    {path: "permissions", loadChildren: () => import("./template/permission/app.permission.module").then(m => m.PermissionListModule)},
                    {path: "plans", loadChildren: () => import("./template/rating-plan-management/app.ratingplan.module").then(m => m.AppRatingPlanModule)},
                    {path: "apnsim", loadChildren: () => import("./template/apn-sim/app.apnsim.module").then(m => m.AppApnSimModule)},
                    {path: "devices", loadChildren: () => import("./template/device-management/app.device.module").then(m => m.AppDeviceModule)},
                    {path: "profile", loadChildren: () => import("./template/profile/app.profile.module").then(m => m.AppProfileModule)},
                    {path: "alerts", loadChildren: () => import("./template/alert/app.alert.module").then(m => m.AppAlertModule)},
                    {path: "customers", loadChildren: ()=>import('./template/customer-management/customer-management.module').then(m => m.CustomerManagementModule)},
                    {path: "reports", loadChildren: ()=> import('./template/reporting/app.report.module').then(m => m.ReportModule)},
                    {path: "config-chart", loadChildren: ()=> import('./template/charts/app.config.chart.module').then(m => m.AppConfigChartModule)},
                    {path: "test", loadChildren: ()=>import('./template/test/test.module').then(m => m.TestModule)},
                    {path: "ticket", loadChildren: ()=>import('./template/ticket/app.ticket.module').then(m => m.AppTicketModule)},
                    {path: "data-pool", loadChildren: () => import("./template/data-pool/data-pool.module").then(m => m.DataPoolModule)},
                    {path: "ticket", loadChildren: ()=>import('./template/ticket/app.ticket.module').then(m => m.AppTicketModule)},
                    {path: "history-activity", loadChildren: ()=>import('./template/history_activity/app.history.module').then(m => m.AppHistoryModule)},
                    {path: "diagnose", loadChildren: ()=>import('./template/diagnose/app.diagnose.module').then(m => m.AppDiagnoseModule)},
                ],
                data: new DataPage("", [])
            },
            { path: 'docs', loadChildren: () => import('./template/pages/guide/app.guide.module').then(m => m.GuideModule) },
            { path: 'error', loadChildren: () => import('./template/pages/error/error.module').then(m => m.ErrorModule) },
            { path: 'access', loadChildren: () => import('./template/pages/access/access.module').then(m => m.AccessModule) },
            { path: 'login', loadChildren: () => import('./template/pages/login/login.module').then(m => m.LoginModule) },
            { path: 'reset-password', loadChildren: () => import('./template/pages/reset-password/reset-password.module').then(m => m.ResetPasswordModule) },
            { path: 'notfound', loadChildren: () => import('./template/pages/notfound/notfound.module').then(m => m.NotfoundModule) },
            { path: 'landing', loadChildren: () => import('./template/pages/landing/landing.module').then(m => m.LandingModule) },
            { path: '**', redirectTo: '/notfound' },
        ], { scrollPositionRestoration: 'enabled', anchorScrolling: 'enabled', onSameUrlNavigation: 'reload' })
    ],
    exports: [RouterModule]
})
export class AppRoutingModule {
}
