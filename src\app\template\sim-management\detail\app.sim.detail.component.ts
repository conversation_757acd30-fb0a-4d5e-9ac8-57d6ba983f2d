import {Component, Inject, Injector, Input, OnInit} from "@angular/core";
import {MenuItem} from "primeng/api";
import {SimService} from "src/app/service/sim/SimService";
import {CONSTANTS} from "src/app/service/comon/constants";
import {ComponentBase} from "src/app/component.base";

@Component({
    selector: "sim-detail",
    templateUrl: "./app.sim.detail.component.html"
})
export class SimDetailComponent extends ComponentBase implements OnInit{
    constructor(@Inject(SimService) private simService: SimService, injector: Injector) {
        super(injector);
    }
    simId: string;
    customerCode: string;
    items: Array<MenuItem>;
    home: MenuItem;
    detailSim:any = {};
    detailStatusSim: any={};
    detailCustomer:any={};
    detailRatingPlan: any={};
    detailContract: any={};
    detailAPN: any={};
    itemActionStatuses: Array<MenuItem>;
    itemExecuteRatePlans: Array<MenuItem>;
    ngOnInit(): void {
        let me = this;
        this.simId = this.route.snapshot.paramMap.get("id");
        this.items = [
            { label: this.tranService.translate("global.menu.simmgmt") },
            { label: this.tranService.translate("global.menu.listsim"), routerLink:"/sims" },
            { label: this.tranService.translate("sim.text.detailSim") },];
        this.home = { icon: 'pi pi-home', routerLink: '/' };
        this.detailSim = {};
        this.getDetailSim();

        this.itemActionStatuses = [
            {
                label: this.tranService.translate("global.button.exportSelect"),
                // icon: "pi pi-refresh",
                command: ()=>{
                    alert("export select");
                },
                disabled: true,
            },
            {
                label: this.tranService.translate("global.button.exportFilter"),
                // icon: "pi pi-times",
                command: ()=>{
                    alert("export filter");
                },
                disabled: true
            }
        ];
        this.itemExecuteRatePlans = [
            {
                label: this.tranService.translate("global.button.registerRatingPlan"),
                // icon: "pi pi-refresh",
                command: ()=>{
                    me.messageCommonService.success(me.tranService.translate("global.message.success"));
                }
            },
            {
                label: this.tranService.translate("global.button.changeRatingPlan"),
                // icon: "pi pi-times",
                command: ()=>{
                    me.messageCommonService.success(me.tranService.translate("global.message.message"));
                }
            },
            {
                label: this.tranService.translate("global.button.cancelRatingPlan"),
                // icon: "pi pi-times",
                command: ()=>{
                    me.messageCommonService.success(me.tranService.translate("global.message.message"));
                }
            }
        ]
    }

    getDetailSim(){
        let me = this;
        // this.messageCommonService.onload();
        this.simService.getById(this.simId, (response)=>{
            me.detailSim = {
                ...response
            }
            me.getStatusSim();
            me.getDetailCustomer();
            me.getDetailRatingPlan();
            me.getDetailContract();
            me.getDetailApn();
            me.simService.getConnectionStatus([me.simId], (resp)=>{
                me.detailSim.connectionStatus = resp[0].userstate
            }, ()=>{})
        }, null,()=>{
            this.messageCommonService.offload();
        })
    }

    getStatusSim(){
        let me = this;
        this.simService.getDetailStatus(this.detailSim.msisdn, (response)=>{
            me.detailStatusSim =  {
                statusData: response.gprsStatus == 1,
                statusReceiveCall: response.icStatus == 1,
                statusSendCall: response.ocStatus == 1,
                statusWorldCall: response.iddStatus == 1,
                statusReceiveSms: response.smtStatus == 1,
                statusSendSms: response.smoStatus == 1
            };
        },()=>{})
    }

    getDetailCustomer(){
        this.detailCustomer = {
            name: this.detailSim.customerName,
            code: this.detailSim.customerCode
        }
    }

    getDetailRatingPlan(){
        this.simService.getDetailPlanSim(this.detailSim.msisdn, (response)=>{
            this.detailRatingPlan = {
                ...response
            }
        }, ()=>{})

    }

    getDetailContract(){
        this.simService.getDetailContract(this.utilService.stringToStrBase64(this.detailSim.contractCode), (response)=>{
            this.detailContract = response;
        }, ()=>{})
    }

    getDetailApn(){
        this.detailAPN = {
            code: this.detailSim.apnCode,
            type: "Kết nối bằng 3G",
            ip: 0,
            rangeIp: this.detailSim.ip
        }
    }

    getNameStatus(value){
        if(value == 0){
            return this.tranService.translate("sim.status.inventory");
        }else if(value == CONSTANTS.SIM_STATUS.READY){
            // return this.tranService.translate("sim.status.ready");
            return this.tranService.translate("sim.status.activated");
        }else if(value == CONSTANTS.SIM_STATUS.ACTIVATED){
            return this.tranService.translate("sim.status.activated");
        }else if(value == CONSTANTS.SIM_STATUS.DEACTIVATED){
            return this.tranService.translate("sim.status.deactivated");
        }else if(value == CONSTANTS.SIM_STATUS.PURGED){
            return this.tranService.translate("sim.status.purged");
        }else if(value == CONSTANTS.SIM_STATUS.INACTIVED){
            return this.tranService.translate("sim.status.inactivated");
        }else if(value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY){
            return this.tranService.translate("sim.status.processingChangePlan");
        }else if(value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY){
            return this.tranService.translate("sim.status.processingRegisterPlan");
        }else if(value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY){
            return this.tranService.translate("sim.status.waitingCancelPlan");
        }
        return "";
    }

    getClassStatus(value){
        if(value == 0){
            return ['p-1' , "border-round", "border-400", "text-color","inline-block"];
        }else if(value == CONSTANTS.SIM_STATUS.READY){
            // return ['p-1', "bg-blue-600", "border-round","inline-block"];
            return ['p-2', "text-green-800", "bg-green-100","border-round","inline-block"];
        }else if(value == CONSTANTS.SIM_STATUS.ACTIVATED){
            return ['p-2', 'text-green-800', "bg-green-100","border-round","inline-block"];
        }else if(value == CONSTANTS.SIM_STATUS.INACTIVED){
            return ['p-2', 'text-yellow-800', "bg-yellow-100", "border-round","inline-block"];
        }else if(value == CONSTANTS.SIM_STATUS.DEACTIVATED){
            return ['p-2', 'text-indigo-600', "bg-indigo-100", "border-round","inline-block"];
        }else if(value == CONSTANTS.SIM_STATUS.PURGED){
            return ['p-2', 'text-red-700', "bg-red-100", "border-round","inline-block"];
        }else if(value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY){
            return ['p-2', 'text-cyan-800', "bg-cyan-100", "border-round","inline-block"];
        }else if(value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY){
            return ['p-2', 'text-teal-800', "bg-teal-100", "border-round","inline-block"];
        }else if(value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY){
            return ['p-2', 'text-orange-700', "bg-orange-100", "border-round","inline-block"];
        }
        return [];
    }

    getServiceType(value) {
        if(value == CONSTANTS.SERVICE_TYPE.PREPAID) return this.tranService.translate("sim.serviceType.prepaid")
        else if(value == CONSTANTS.SERVICE_TYPE.POSTPAID) return this.tranService.translate("sim.serviceType.postpaid")
        else return ""
    }
}
