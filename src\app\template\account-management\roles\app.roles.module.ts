import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AppRolesRoutingModule } from './app.roles-routing.module';
import { AppRolesListComponent } from './list/app.roles.list.component';
import { BreadcrumbModule } from 'primeng/breadcrumb';
import { FieldsetModule } from 'primeng/fieldset';
import { InputTextModule } from 'primeng/inputtext';
import { DropdownModule } from 'primeng/dropdown';
import { ToggleButtonModule } from 'primeng/togglebutton';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonVnptModule } from '../../common-module/common.module';
import { SplitterModule } from 'primeng/splitter';
import { ButtonModule } from 'primeng/button';
import { SplitButtonModule } from 'primeng/splitbutton';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { DialogModule } from 'primeng/dialog';
import { CardModule } from 'primeng/card';
import { RolesService } from 'src/app/service/account/RolesService';
import { TreeSelectModule } from 'primeng/treeselect';
import { AppRolesCreateComponent } from './create/app.roles.create.component';
import {AppRolesDetailComponent} from "./detail/app.roles.detail.component";
import {AppRolesEditComponent} from "./edit/app.roles.edit.component";
import { PanelModule } from 'primeng/panel';

@NgModule({
  declarations: [
    AppRolesListComponent,
    AppRolesCreateComponent,
    AppRolesDetailComponent,
    AppRolesEditComponent
  ],
    imports: [
        CommonModule,
        AppRolesRoutingModule,
        BreadcrumbModule,
        FieldsetModule,
        FormsModule,
        ReactiveFormsModule,
        InputTextModule,
        ButtonModule,
        CommonVnptModule,
        SplitButtonModule,
        DropdownModule,
        AutoCompleteModule,
        DialogModule,
        SplitterModule,
        ToggleButtonModule,
        TreeSelectModule,
        CardModule,
        PanelModule
    ],
  providers:[
    RolesService
  ]
})
export class AppRolesModule { }
