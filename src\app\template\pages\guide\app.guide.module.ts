import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonModule } from 'primeng/button';
import { GuideRoutingModule } from './app.guide.routing.module';
import { AppGuideComponent } from './app.guide.component';
import { CommonVnptModule } from '../../common-module/common.module';
import { RippleModule } from 'primeng/ripple';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { TooltipModule } from 'primeng/tooltip';
import { LayoutService } from 'src/app/service/app.layout.service';
import { GuideService } from 'src/app/service/guide/GuideService';
import { PanelMenuModule } from 'primeng/panelmenu';
import { DividerModule } from 'primeng/divider';
import { TieredMenuModule } from 'primeng/tieredmenu';
import { BreadcrumbModule } from 'primeng/breadcrumb';
import {AppGuideIntegrationComponent} from "./integration/app.guide.integration.component";

@NgModule({
    imports: [
        CommonModule,
        GuideRoutingModule,
        ButtonModule,
        CommonVnptModule,
        RippleModule,
        CommonVnptModule,
        OverlayPanelModule,
        TooltipModule,
        PanelMenuModule,
        DividerModule,
        TieredMenuModule,
        BreadcrumbModule,
    ],
    declarations: [
        AppGuideComponent,
        AppGuideIntegrationComponent,
    ],
    providers: [LayoutService, GuideService]
})
export class GuideModule { }
