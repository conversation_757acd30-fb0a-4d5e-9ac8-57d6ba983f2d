import {AfterContentChecked, Component, Inject, Injector, OnInit} from '@angular/core';
import {FormBuilder} from "@angular/forms";
import {MenuItem} from "primeng/api";
import {ColumnInfo, OptionTable} from "../../../common-module/table/table.component";
import {ReceivingGroupService} from "../../../../service/alert/ReceivingGroup";
import {ComponentBase} from "../../../../component.base";
import {CONSTANTS} from "../../../../service/comon/constants";

@Component({
  selector: 'app-app.group-receiving.detail',
  templateUrl: './app.group-receiving.detail.component.html',
})
export class AppGroupReceivingDetailComponent extends ComponentBase implements OnInit, AfterContentChecked{
    constructor(
                @Inject(ReceivingGroupService) private receivingGroupService: ReceivingGroupService,
                private formBuilder: FormBuilder,
                private injector: Injector
    ) {
        super(injector);
    }
    items: MenuItem[];
    home: MenuItem;
    formReceivingGroup : any;
    formMailInput : any;

    receivingGroupInfo: {
        name: string|null,
        description: string|null,
        emails: Array<any>|null,
        smsList: Array<any>|null,
    };
    selectItems: Array<any> = [];
    columns: Array<ColumnInfo>;
    dataSet: {
        content: Array<any>,
        total: number
    };
    optionTable: OptionTable;
    email: {}

    formSMSInput : any;
    selectItemsSms: Array<any> = [];
    columnsSms: Array<ColumnInfo>;
    dataSetSms: {
        content: Array<any>,
        total: number
    };
    optionTableSms: OptionTable;
    sms: {}
    rgId = parseInt(this.route.snapshot.paramMap.get("id"));


    ngOnInit(): void {
        let me = this;
        this.items = [{ label: this.tranService.translate("global.menu.groupReceiving") }, { label: this.tranService.translate("global.menu.groupReceivingList"), routerLink:"/alerts/receiving-group"  }, { label: this.tranService.translate("global.button.view") }];
        this.home = { icon: 'pi pi-home', routerLink: '/' };

        this.receivingGroupInfo = {
            name: "nhom1",
            description: null,
            emails: [],
            smsList: [],
        }

        this.formReceivingGroup = this.formBuilder.group(this.receivingGroupInfo);
        this.formReceivingGroup.controls['name'].disable()
        this.formReceivingGroup.controls['description'].disable()

        this.formMailInput = this.formBuilder.group({email: ""});
        this.dataSet = {
            content: [],
            total: 0
        }
        this.selectItems = [];
        this.columns = [
            {
                name: this.tranService.translate("alert.receiving.emails"),
                key: "emails",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
            },
        ];
        this.optionTable = {
            hasClearSelected: false,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
        };
        this.dataSet = {
            content: [],
            total: 0
        }

        this.formSMSInput = this.formBuilder.group({sms: ""});
        this.selectItemsSms = [];
        this.columnsSms = [
            {
                name: this.tranService.translate("alert.receiving.sms"),
                key: "smsList",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
            },
        ];
        this.optionTableSms = {
            hasClearSelected: true,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
        };
        this.getDetail()
        this.search();
        this.searchSms();
    }
    getDetail(){
        let me = this;
        me.messageCommonService.onload()
        this.receivingGroupService.getById(this.rgId, (response)=>{
            me.receivingGroupInfo = response;
            me.receivingGroupInfo.emails = response.emails
            me.receivingGroupInfo.smsList = response.msisdns

            if (response.emails != null){
                for (let i = 0; i <response.emails.split(", ").length; i++) {
                    me.dataSet.content.push({emails :response.emails.split(", ")[i]})
                    // me.myEmails.push(response.emails.split(", ")[i])
                }
            }

            if (response.msisdns != null){
                for (let i = 0; i <response.msisdns.split(", ").length; i++) {
                    me.dataSetSms.content.push({smsList :response.msisdns.split(", ")[i]})
                    // me.mySmsList.push(response.msisdns.split(", ")[i])
                }
            }
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }
    ngAfterContentChecked(): void {
    }
    onSubmitCreate(){

    }
    closeForm(){
        this.router.navigate(['/alerts/receiving-group'])
    }

    addEmail(val){
        let me = this;


        me.dataSet.content.push({emails :val})
        me.receivingGroupInfo.emails.push({emails :val})
        // me.dataSet.content.push(me.receivingGroupInfo)
    }
    search(){
        let me = this
        me.dataSet = {
            content: [],
            total: 0
        }
    }
    removeEmail(val){
        let me = this
        me.dataSet.content.splice(me.dataSet.content.indexOf(val), 1)
        me.receivingGroupInfo.emails.splice(me.receivingGroupInfo.emails.indexOf(val), 1)
    }

    addSms(val){
        let me = this;

        me.dataSetSms.content.push({smsList :val})
        me.receivingGroupInfo.smsList.push({smsList :val})
        // me.dataSet.content.push(me.receivingGroupInfo)
    }
    searchSms(){
        let me = this
        me.dataSetSms = {
            content: [],
            total: 0
        }
    }
    removeSms(val){
        let me = this
        me.dataSetSms.content.splice(me.dataSetSms.content.indexOf(val), 1)
        me.receivingGroupInfo.smsList.splice(me.receivingGroupInfo.smsList.indexOf(val), 1)
    }

    deleteReceivingGroup(){
        let me = this;
        me.messageCommonService.confirm(
            me.tranService.translate("global.message.titleConfirmDeleteAlertReceivingGroup"),
            me.tranService.translate("global.message.confirmDeleteAlertReceivingGroup"),
            {
                ok:()=>{
                    me.receivingGroupService.deleteById(this.rgId,(response)=>{
                        me.messageCommonService.success(me.tranService.translate("global.message.deleteSuccess"));
                        me.router.navigate(['/alerts/receiving-group']);
                    })
                },
                cancel: ()=>{
                    // me.messageCommonService.error(me.tranService.translate("global.message.deleteFail"));
                }
            }
        )
    }

    onEdit(){
        let me = this;
        me.router.navigate([`/alerts/receiving-group/edit/${this.rgId}`]);
    }

    protected readonly CONSTANTS = CONSTANTS;
}
