import {ex} from "@fullcalendar/core/internal-common";

export interface Wallet {
    id: number;
    purchasedTraffic: number;
    subCode: string;
    payCode: string;
    subName: string;
    packageName: string;
    packageCode: string;
    customerName: string;
    customerCode: string;
    phoneActive: string;
    trafficType: string,
    totalRemainingTraffic: number;
    totalTrafficShared: number;
    time?: Date | null;
    startDate?: Date | null;
    endDate?: Date | null;
    autoType?: number | null;
};

export interface ShareDetail {
    id?: number;
    idGroup?: number;
    phoneReceipt?: number;
    name?: string;
    email?: string;
    data?: number;
    percent?: number;
    locked?: boolean;
    isAuto?: boolean;
};

export interface PhoneInfo {
    id?: number;
    phoneReceipt?: number;
    name?: string;
    email?: string;
    percent ?: number;
    locked?:boolean;
};

export interface GroupInfo {
    id?: number;
    groupName?: string;
    groupCode?: string;
    description?: string;
}

export interface PhoneInGroup {
    idGroup?: number;
    phoneReceipt?: number;
    name?: string;
    email?: string;
    groupName?: string
}
