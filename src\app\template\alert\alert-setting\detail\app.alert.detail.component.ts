import {AfterContentChecked, Component, Inject, Injector, OnInit} from '@angular/core';
import {AccountService} from "../../../../service/account/AccountService";
import {FormBuilder, FormGroup} from "@angular/forms";
import {MenuItem} from "primeng/api";
import {CONSTANTS} from "../../../../service/comon/constants";
import {ComponentBase} from "../../../../component.base";
import {AlertService} from "../../../../service/alert/AlertService";
import {CustomerService} from "../../../../service/customer/CustomerService";
import {SimService} from "../../../../service/sim/SimService";
import {GroupSimService} from "../../../../service/group-sim/GroupSimService";
import { TrafficWalletService } from 'src/app/service/datapool/TrafficWalletService';
import { ComboLazyControl } from 'src/app/template/common-module/combobox-lazyload/combobox.lazyload';
import {RatingPlanService} from "../../../../service/rating-plan/RatingPlanService";

@Component({
  selector: 'app-app.alert.detail',
  templateUrl: './app.alert.detail.component.html',
})
export class AppAlertDetailComponent extends ComponentBase  implements OnInit{
    constructor(
                @Inject(AccountService) private accountService: AccountService,
                @Inject(CustomerService) private customerService: CustomerService,
                @Inject(AlertService) private alertService: AlertService,
                @Inject(TrafficWalletService) private trafficWalletService: TrafficWalletService,
                @Inject(SimService) private simService: SimService,
                @Inject(GroupSimService) private groupSimService: GroupSimService,
                @Inject(RatingPlanService) private ratingPlanService: RatingPlanService,
                private formBuilder: FormBuilder,
                private injector: Injector
    ) {
        super(injector);
    }
    items: MenuItem[];
    home: MenuItem;
    formAlert: any;
    alertInfo: {
        name: string|null,
        customerId: any,
        statusSim: number|null,
        subscriptionNumber: string|null,
        groupId: string|null,
        interval: number|null,
        count: number|null,
        unit: number|null,
        value: string|null,
        description: string|null,
        severity: string|null,
        listAlertReceivingGroupId: Array<any>|null,
        url: string|null,
        emailList: string|null,
        emailSubject: string|null,
        emailContent: string|null,
        smsList: string|null
        smsContent: string|null,
        ruleCategory: number | null,
        eventType: number | null,
        appliedPlan: Array<any>,
        actionType:number|null,
        walletName: string|null,
        notifyInterval : number | null,
        notifyRepeat: number | null;
        typeAlert: Array<any> | null;
        sendTypeEmail: boolean;
        sendTypeSMS: boolean;
        status : number | null;
        groupName : string | null
        customerName : string | null
        customerCode : string | null
    };
    paramSearchSim = {};
    paramSearchGroupSim = {};
    ruleOptions: Array<any>;
    eventOptions: Array<any>;
    actionOptions: Array<any>;
    statusSimOptions: Array<any>;
    comboSelectCustomerControl: ComboLazyControl = new ComboLazyControl();
    comboSelectSubControl: ComboLazyControl = new ComboLazyControl();
    comboSelectGroupSubControl: ComboLazyControl = new ComboLazyControl();
    optionStatusSim: any;
    unitOptions: Array<any>;
    severityOptions: Array<any>;
    customerNameOptions: Array<{ name: any, value: any, id: any }>;
    groupOptions: Array<any>;
    listGroupByCustomer: Array<any>;
    listSimByCustomer: Array<any>;
    subscriptionNumberOptions: Array<any>;
    groupReceivingOptions: Array<any>;
    isShowDialogActive: boolean;
    statuSims: Array<{name: string, value: any}>
    statusAlert: any;
    alertId = this.route.snapshot.paramMap.get("id");
    appliedPlanOptions: Array<any>;
    isAlertNameExisted: boolean = false;
    isPlanExisted: boolean = false;
    repeat: boolean;
    eventOptionManagement: Array<any>;
    eventOptionMonitoring: Array<any>;
    statusTemp : any
    alertResponse : any

    ngOnInit(): void {
        let me = this;
        this.items = [{ label: this.tranService.translate("global.menu.alertSettings") }, { label: this.tranService.translate("global.menu.alertList"), routerLink:"/alerts"  }, { label: this.tranService.translate("global.button.view") }];
        this.home = { icon: 'pi pi-home', routerLink: '/' };
        this.optionStatusSim = CONSTANTS.ALERT_STATUS_SIM;
        this.statusAlert = CONSTANTS.ALERT_STATUS;
        this.alertInfo = {
            name: null,
            customerId: null,
            statusSim: null,
            subscriptionNumber: null,
            groupId: null,
            interval: null,
            count: null,
            unit: null,
            value: null,
            description: null,
            severity: null,
            listAlertReceivingGroupId: [],
            url: null,
            emailList: null,
            emailSubject: null,
            emailContent: null,
            smsList: null,
            smsContent: null,
            ruleCategory : 1,
            eventType :  null,
            appliedPlan: null,
            actionType:0,
            walletName:null,
            notifyInterval:null,
            notifyRepeat: null,
            typeAlert: null,
            sendTypeEmail: true,
            sendTypeSMS: null,
            status : null,
            groupName : null,
            customerName : null,
            customerCode : null
        }
        this.formAlert = this.formBuilder.group(this.alertInfo);
        this.formAlert.controls['name'].disable()
        this.formAlert.controls['severity'].disable()
        this.formAlert.controls['statusSim'].disable()
        this.formAlert.controls['description'].disable()
        this.formAlert.controls['customerId'].disable()
        this.formAlert.controls['groupId'].disable()
        this.formAlert.controls['subscriptionNumber'].disable()
        this.formAlert.controls['unit'].disable()
        this.formAlert.controls['count'].disable()
        this.formAlert.controls['interval'].disable()
        this.formAlert.controls['value'].disable()
        this.formAlert.controls['listAlertReceivingGroupId'].disable()
        this.formAlert.controls['url'].disable()
        this.formAlert.controls['emailList'].disable()
        this.formAlert.controls['emailSubject'].disable()
        this.formAlert.controls['emailContent'].disable()
        this.formAlert.controls['smsList'].disable()
        this.formAlert.controls['smsContent'].disable()
        this.formAlert.controls['ruleCategory'].disable()
        this.formAlert.controls['eventType'].disable()
        this.formAlert.controls['appliedPlan'].disable()
        this.formAlert.controls['actionType'].disable()
        this.formAlert.controls['notifyInterval'].disable()
        this.formAlert.controls['notifyRepeat'].disable()

        this.statuSims = [
            {
                value: [CONSTANTS.SIM_STATUS.ACTIVATED],
                name: this.tranService.translate("sim.status.activated")
            },
            {
                value: [CONSTANTS.SIM_STATUS.INACTIVED],
                name: this.tranService.translate("sim.status.inactivated")
            },
            {
                value: [CONSTANTS.SIM_STATUS.DEACTIVATED],
                name: this.tranService.translate("sim.status.deactivated")
            },
            {
                value: [CONSTANTS.SIM_STATUS.PURGED],
                name: this.tranService.translate("sim.status.purged")
            },
            {
                value: [15 + CONSTANTS.SIM_STATUS.ACTIVATED, 15 + CONSTANTS.SIM_STATUS.READY],
                name: this.tranService.translate("sim.status.processingChangePlan")
            },
            {
                value: [10 + CONSTANTS.SIM_STATUS.ACTIVATED, 10 + CONSTANTS.SIM_STATUS.READY],
                name: this.tranService.translate("sim.status.processingRegisterPlan")
            },
            {
                value: [20 + CONSTANTS.SIM_STATUS.ACTIVATED, 20 + CONSTANTS.SIM_STATUS.READY],
                name: this.tranService.translate("sim.status.waitingCancelPlan")
            },
        ]


        this.statusSimOptions = [
            {name: this.tranService.translate("alert.statusSim.outPlan"),value:1},
            {name: this.tranService.translate("alert.statusSim.outLine"),value:2},
            {name: me.tranService.translate("alert.eventType.subExp"),value:12},
            {name: me.tranService.translate("alert.eventType.dataWalletExp"),value:13},
        ]
        this.unitOptions = [
            {name: "KB", value: 1},
            {name: "Mb", value: 2},
            {name: "Gb", value: 3}
        ]
        this.severityOptions = [
            {name: this.tranService.translate("alert.severity.critical"),value:CONSTANTS.ALERT_SEVERITY.CRITICAL},
            {name: this.tranService.translate("alert.severity.major"),value:CONSTANTS.ALERT_SEVERITY.MAJOR},
            {name: this.tranService.translate("alert.severity.minor"),value:CONSTANTS.ALERT_SEVERITY.MINOR},
            {name: this.tranService.translate("alert.severity.info"),value:CONSTANTS.ALERT_SEVERITY.INFO}
        ]
        this.customerNameOptions = []

        this.groupOptions = []

        this.subscriptionNumberOptions = []

        this.groupReceivingOptions = []
        this.getListReceivingGroup()

        this.getDetail()

        this.eventOptions = [
            {
                name: me.tranService.translate("alert.eventType.exceededPakage"),
                value: CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_PACKAGE
            },
            {
                name: me.tranService.translate("alert.eventType.exceededValue"),
                value: CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_VALUE
            },
            {
                name: me.tranService.translate("alert.eventType.sessionEnd"),
                value: CONSTANTS.ALERT_EVENT_TYPE.SESSION_END
            },
            {
                name: me.tranService.translate("alert.eventType.sessionStart"),
                value: CONSTANTS.ALERT_EVENT_TYPE.SESSION_START
            },
            {
                name: me.tranService.translate("alert.eventType.smsExceededPakage"),
                value: CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_PACKAGE
            },
            {
                name: me.tranService.translate("alert.eventType.smsExceededValue"),
                value: CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_VALUE
            },
            {name: me.tranService.translate("alert.eventType.owLock"), value: CONSTANTS.ALERT_EVENT_TYPE.ONE_WAY_LOCK},
            {name: me.tranService.translate("alert.eventType.twLock"), value: CONSTANTS.ALERT_EVENT_TYPE.TWO_WAY_LOCK},
            {
                name: me.tranService.translate("alert.eventType.noConection"),
                value: CONSTANTS.ALERT_EVENT_TYPE.NO_CONECTION
            },
            {name: me.tranService.translate("alert.eventType.simExp"), value: CONSTANTS.ALERT_EVENT_TYPE.SIM_EXP},
            {
                name: me.tranService.translate("alert.eventType.dataWalletExp"),
                value: CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP
            },
            {
                name: me.tranService.translate("alert.eventType.owtwlock"),
                value: CONSTANTS.ALERT_EVENT_TYPE.ONE_WAY_TWO_WAY_LOCK
            },
        ]
        this.ruleOptions = [
            {name:this.tranService.translate("alert.ruleCategory.monitoring"), value: CONSTANTS.ALERT_RULE_CATEGORY.MONITORING},
            {name:this.tranService.translate("alert.ruleCategory.management"), value: CONSTANTS.ALERT_RULE_CATEGORY.MANAGEMENT}
        ]
        this.actionOptions = [
            {name:this.tranService.translate("alert.actionType.alert"), value:CONSTANTS.ALERT_ACTION_TYPE.ALERT},
            {name:this.tranService.translate("alert.actionType.api"), value:CONSTANTS.ALERT_ACTION_TYPE.API}
        ]

        this.eventOptionManagement = this.eventOptions.filter(item =>
            item.value == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_PACKAGE ||
            item.value == CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_PACKAGE||
            item.value == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_VALUE ||
            item.value == CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_VALUE ||
            item.value == CONSTANTS.ALERT_EVENT_TYPE.SIM_EXP ||
            item.value == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP )

        this.eventOptionMonitoring = this.eventOptions.filter(item =>
            item.value == CONSTANTS.ALERT_EVENT_TYPE.ONE_WAY_LOCK ||
            item.value == CONSTANTS.ALERT_EVENT_TYPE.TWO_WAY_LOCK ||
            item.value == CONSTANTS.ALERT_EVENT_TYPE.ONE_WAY_TWO_WAY_LOCK ||
            item.value == CONSTANTS.ALERT_EVENT_TYPE.NO_CONECTION ||
            item.value == CONSTANTS.ALERT_EVENT_TYPE.SESSION_START ||
            item.value == CONSTANTS.ALERT_EVENT_TYPE.SESSION_END );

        this.formAlert.controls["typeAlert"].disable();
        this.formAlert.controls["listAlertReceivingGroupId"].disable();

        this.formAlert.get("sendTypeEmail").disable({emitEvent:false});
        this.formAlert.get("sendTypeSMS").disable({emitEvent:false});
    }

    restoreTypeAlert(response: any): any {
        this.alertInfo.typeAlert = []
        if (response.listAlertReceivingGroupId != null && response.listAlertReceivingGroupId.length > 0) {
            this.alertInfo.typeAlert.push("Group")
        }
        if (response.emailList != null) {
            this.alertInfo.typeAlert.push("Email")
        }
        if (response.smsList != null) {
            this.alertInfo.typeAlert.push("SMS")
        }
    }

    getDetail(){
        let me = this;
        let alertId = this.route.snapshot.paramMap.get("id");
        me.messageCommonService.onload()
        this.alertService.getById(parseInt(alertId), (response)=>{
            me.alertResponse = {...response};
            me.alertInfo = response;
            me.alertInfo.name = response.name;
            me.alertInfo.customerId = {id: response.customerId};
            // me.alertInfo.customerCode = response.customerCode;
            me.alertInfo.subscriptionNumber = response.subscriptionNumber;
            me.alertInfo.description = response.description;
            me.alertInfo.groupId = response.groupId;
            me.alertInfo.listAlertReceivingGroupId = response.listAlertReceivingGroup;
            me.alertInfo.emailList = response.emailList;
            me.alertInfo.emailSubject = response.emailSubject;
            me.alertInfo.emailContent = response.emailContent;
            me.alertInfo.smsList = response.smsList;
            me.alertInfo.smsContent = response.smsContent;
            me.alertInfo.url = response.url;
            me.alertInfo.interval = response.interval;
            me.alertInfo.count = response.count;
            me.alertInfo.unit = response.unit;
            me.alertInfo.value = response.eventType == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP ? response.value / 24 : response.value,
            me.alertInfo.severity = response.severity;
            me.alertInfo.actionType = response.actionType;
            me.alertInfo.ruleCategory = response.ruleCategory;
            me.alertInfo.eventType = response.eventType;
            me.alertInfo.appliedPlan = response.dataPackCode;
            me.alertInfo.status = response.status;
            me.statusTemp = response.status;
            me.alertInfo.notifyInterval = response.notifyInterval / 24;
            if(response.notifyRepeat == 1){
                this.repeat = true
            }else if (response.notifyRepeat == 0){
                this.repeat = false
            }
            me.getListRatingPlan();
            me.restoreTypeAlert(response);
            me.alertInfo.notifyRepeat = response.notifyRepeat
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }

    closeForm(){
        this.router.navigate(['/alerts'])
    }

    deleteAlert(){
        let me = this;
        me.messageCommonService.confirm(
            me.tranService.translate("global.message.titleConfirmDeleteAlert"),
            me.tranService.translate("global.message.confirmDeleteAlert"),
            {
                ok:()=>{
                    me.alertService.deleteById(parseInt(me.alertId),(response)=>{
                        me.messageCommonService.success(me.tranService.translate("global.message.deleteSuccess"));
                        me.router.navigate(['/alerts']);
                    })
                },
                cancel: ()=>{

                }
            }
        )
    }

    changeStatus(value){
        let me = this;

        me.messageCommonService.confirm(
            me.tranService.translate("global.message.titleConfirmChangeStatusAlert"),
            me.tranService.translate("global.message.confirmChangeStatusAlert"),
            {
                ok:()=>{
                    let dataBody = {
                        id : me.alertId,
                        status: value
                    }
                    me.alertService.changeStatus(dataBody,(response)=>{
                    me.messageCommonService.success(me.tranService.translate("global.message.changeStatusSuccess"));
                      me.alertInfo.status = value;
                      me.statusTemp = value;
                    })
                },
                cancel: ()=>{

                }
            }
        )
    }

    onEdit(){
        let me = this;
        me.router.navigate([`/alerts/edit/${me.alertId}`]);
    }

    getListReceivingGroup() {
        let me = this;
        this.alertService.getAllReceivingGroup({},(response)=>{
            me.groupReceivingOptions = (response || []).map(el => {
                return {
                    ...el,
                    name: `${el.name||'unknown'}`,
                    value: el.id||'unknown'
                }
            });
        })
    }
    filerGroupByCustomer(customerCode) {
        if(this.alertInfo.customerId != null){
            this.paramSearchGroupSim = {customerCode: this.alertInfo.customerId.customerCode}
            this.paramSearchSim = {customer: this.alertInfo.customerId.customerCode};
            this.alertInfo.groupId = null;
            this.alertInfo.subscriptionNumber = null;
        }
    }

    protected readonly CONSTANTS = CONSTANTS;

    onChangeEventOption(event){
        if(event.value == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP){
            this.formAlert.get("unit").disable({emitEvent : false})
            this.formAlert.get("value").enable({emitEvent : false})
            this.formAlert.get("customerId").disable({emitEvent : false})
            this.formAlert.get("groupId").disable({emitEvent : false})
            this.formAlert.get("subscriptionNumber").disable({emitEvent : false})
            this.formAlert.get("statusSim").disable({emitEvent : false})

            this.formAlert.get("emailSubject").enable({emitEvent : false})
            this.formAlert.get("emailContent").enable({emitEvent : false})
            this.formAlert.get("smsContent").enable({emitEvent : false})

            this.alertInfo.actionType = CONSTANTS.ALERT_ACTION_TYPE.ALERT;
            this.formAlert.get("actionType").disable({emitEvent : false})
        }else if(event.value == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_VALUE){
            this.formAlert.get("unit").enable({emitEvent : false})
            this.formAlert.get("value").enable({emitEvent : false})
            this.formAlert.get("customerId").enable({emitEvent : false})
            this.formAlert.get("groupId").enable({emitEvent : false})
            this.formAlert.get("subscriptionNumber").enable({emitEvent : false})
            this.formAlert.get("statusSim").enable({emitEvent : false})
        }else{
            this.formAlert.get("customerId").enable({emitEvent : false})
            this.formAlert.get("groupId").enable({emitEvent : false})
            this.formAlert.get("subscriptionNumber").enable({emitEvent : false})
            this.formAlert.get("statusSim").enable({emitEvent : false})

            this.formAlert.get("actionType").enable({emitEvent : false})
        }

        if(this.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP){
            this.formAlert.get("emailSubject").enable({emitEvent : false})
            this.formAlert.get("emailContent").enable({emitEvent : false})
            this.formAlert.get("smsContent").enable({emitEvent : false})
        }
    }
    onChangeStatus(event) {
        let me = this;
        setTimeout(function(){
            if(event.checked == CONSTANTS.ALERT_STATUS.ACTIVE) {
                me.alertInfo.status = CONSTANTS.ALERT_STATUS.INACTIVE;
            }else {
                me.alertInfo.status = CONSTANTS.ALERT_STATUS.ACTIVE;
            }
            me.changeStatus(event.checked)
        })
    }

    getListRatingPlan() {
        let me = this;
        if (me.alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP) {
            this.trafficWalletService.searchPakageCode({}, (response) => {
                me.appliedPlanOptions = (response || []).map(el => ({code: el}))
                if(me.alertResponse.dataPackCode != null && me.alertResponse.dataPackCode.length > 0) {
                    me.appliedPlanOptions.push(...me.alertResponse.dataPackCode.map(el=> ({code : el})))
                }
            })
        }
    }

}
