import {Component, Inject, Injector, OnInit} from "@angular/core";
import {FormBuilder} from "@angular/forms";
import {MenuItem} from "primeng/api";
import {DomSanitizer} from "@angular/platform-browser";
import {CONSTANTS} from "src/app/service/comon/constants";
import {GroupSubWalletService} from "../../../../service/group-sub-wallet/GroupSubWalletService";
import {ComponentBase} from "../../../../component.base";
import {ColumnInfo, OptionTable} from "../../../common-module/table/table.component";
import {PhoneInfo, PhoneInGroup, ShareDetail} from "../../data-pool.type-data";
import {ShareManagementService} from "../../../../service/datapool/ShareManagementService";
import {TrafficWalletService} from "../../../../service/datapool/TrafficWalletService";

@Component({
    selector: "app-auto-share-group",
    templateUrl: "./auto-share-group.list.component.html"
})
export class AutoShareGroupListComponent extends ComponentBase implements OnInit{
    constructor(
        @Inject(GroupSubWalletService) private groupSubWalletService: GroupSubWalletService,
        @Inject(TrafficWalletService) private walletService: TrafficWalletService,
        private sanitizer: DomSanitizer,
        private formBuilder: FormBuilder,
        injector: Injector) {
        super(injector);
    }

    items: MenuItem[];
    home: MenuItem;
    groupInfo: {
        groupCode: string | null,
        groupName: string | null,
        description: string | null,
        listSub: ShareDetail[];
    };
    columns: Array<ColumnInfo>;
    dataSet: {
        content: Array<any>,
        total: number
    };
    optionTable: OptionTable;
    pageNumber: number;
    pageSize: number;
    sort: string;
    searchInfo: {
        keySearch?: string,
    };
    valueSearch: string;
    ngOnInit(): void {
        let me = this;
        me.home = { icon: 'pi pi-home', routerLink: '/' };
        me.items = [{ label: me.tranService.translate("global.menu.trafficManagement"), routerLink: '' }, { label: me.tranService.translate("global.menu.autoShareGroup") },];
        me.searchInfo = {
            keySearch: ""
        };
        me.columns = [
            {
                name: me.tranService.translate("datapool.label.groupCode"),
                key: "code",
                size: "450px",
                align: "left",
                isShow: true,
                isSort: false,
                style:{
                    cursor: "pointer",
                    color: "var(--mainColorText)"
                },
                funcClick(id, item) {
                    if (me.checkAuthen([CONSTANTS.PERMISSIONS.AUTO_SHARE_GROUP.VIEW_DETAIL])) {
                        me.router.navigate([`/data-pool/auto-share-group/detail/${item.id}`]);
                    } else {
                        window.location.hash = "/access";
                    }
                },
            },
            {
                name: me.tranService.translate("datapool.label.groupName"),
                key: "name",
                size: "450px",
                align: "left",
                isShow: true,
                isSort: false,
                isShowTooltip: true,
                style: {
                    display: 'inline-block',
                    maxWidth: '350px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                }
            },
            {
                name: me.tranService.translate("datapool.label.description"),
                key: "description",
                size: "450px",
                align: "left",
                isShow: true,
                isSort: false,
                isShowTooltip: true,
                style: {
                    display: 'inline-block',
                    maxWidth: '350px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                }
            },
        ]
        me.optionTable = {
            hasClearSelected: true,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
        }
        me.pageNumber = 0;
        me.pageSize = 10;
        me.sort = "id,desc";
        me.groupInfo = {
            groupCode: "",
            groupName: "",
            description: "",
            listSub: []
        };

        me.dataSet = {
            content: [
            ],
            total: 0
        }
        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);
    }

    search(page, limit, sort, params) {
        this.pageNumber = page;
        this.pageSize = limit;
        this.sort = sort;
        let me = this;
        let dataParams = {
            ...params,
            page,
            size: limit,
            sort
        }
        me.messageCommonService.onload();
        this.walletService.searchAutoGroupWallet(dataParams, (response)=>{
            me.dataSet = {
                content: response.content,
                total: response.totalElements
            }
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }

    createGroupSub() {
        // this.router.navigate(['/data-pool/auto-share-group/create']);
    }

    onQuickSearch() {
        event.preventDefault();
        let me = this;
        if (me.valueSearch || me.valueSearch === "") {
            me.searchInfo = {
                keySearch: me.valueSearch.trim()
            }
        }
        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);
    }
    protected readonly CONSTANTS = CONSTANTS;
}
