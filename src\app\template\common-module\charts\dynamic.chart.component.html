<div [style]="getStyleChart()">
    <div class="flex flex-row justify-content-between align-items-center">
        <div class="uppercase font-bold text-xl flex-1" [style]="width >= 400 ? {} : {
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis'
        }" [pTooltip]="width >= 400 ? '' : chartConfig.name" tooltipStyleClass="min-w-max">{{chartConfig.name}}</div>
        <div style="width: fit-content; min-width: 100px;" class="flex flex-row justify-content-end align-items-center" *ngIf="mode != objectMode.DETAIL">
            <p-button [style]="{width: '24px',height: '24px'}" *ngIf="(chartConfig.subTypes || []).includes('threshold')" (click)="openSetting()" icon="pi pi-cog" styleClass="mr-2 p-ripple p-element p-button p-component p-button-icon-only p-button-info p-button-raised p-button-rounded p-button-text"></p-button>
            <p-button [style]="{width: '24px',height: '24px'}" *ngIf="(chartConfig.subTypes || []).includes('sliderThreshold')" (click)="openSetting()" icon="pi pi-sliders-h" styleClass="mr-2 p-ripple p-element p-button p-component p-button-icon-only p-button-info p-button-raised p-button-rounded p-button-text"></p-button>
            <p-button [style]="{width: '24px',height: '24px'}" *ngIf="isShowButtonExtra && handleOpenSizing" (click)="openSizing()" icon="pi pi-arrows-alt" styleClass="p-ripple p-element p-button p-component p-button-icon-only p-button-info p-button-raised p-button-rounded p-button-text"></p-button>
        </div>
        <div style="width: fit-content; min-width: 100px;" class="flex flex-row justify-content-end align-items-center" *ngIf="mode == objectMode.DETAIL">
            <p-button [style]="{width: '24px',height: '24px'}" (click)="refresh()" icon="pi pi-refresh" styleClass="mr-2 p-ripple p-element p-button p-component p-button-icon-only p-button-info p-button-raised p-button-rounded p-button-text"></p-button>
            <p-button [style]="{width: '24px',height: '24px'}" (click)="panelHelp.toggle($event)"  icon="pi pi-info-circle" styleClass="p-ripple p-element p-button p-component p-button-icon-only p-button-info p-button-raised p-button-rounded p-button-text"></p-button>
        </div>
    </div>
    <form *ngIf="formSearch && (listParameters || []).length > 0" [formGroup]="formSearch" (ngSubmit)="onSubmitSearch()" class="pb-2 pt-3 vnpt-field-set">
        <!-- <p-panel [toggleable]="true" [header]="tranService.translate('global.text.filter')"> -->
        <div class=" flex flex-row justify-content-center align-items-end">
            <div [class]="listParameters.length % numberElementPerRow == 0 ? 'w-11' : 'w-full'">
                <div style="margin-left: 0;" class="grid chart-grid w-full" *ngIf="listParameters && listParameters.length > 0">
                    <div style="padding: 4px;" [class]="width >= 1200 ? 'col-2 mt-3'
                                    : (width >= 900 ? 'col-3 mt-3'
                                    : (width >= 600 ? 'col-4 mt-3'
                                    : (width >= 300 ? 'col-6 mt-3' : 'col-12 mt-3')))" *ngFor="let param of listParameters">
                        <span class="p-float-label relative"
                              [class]="formSearch.controls[param.prKey].dirty && formSearch.controls[param.prKey].errors?.required ? 'report-param-required-error' : ''"
                              *ngIf="param.prType == paramTypes.NUMBER "
                        >
                            <input class="w-full" pInputText (keydown)="preventCharacter($event)" [(ngModel)]="searchInfo[param.prKey]" [formControlName]="param.prKey" [required]="param.required" (ngModelChange)="checkIsNumberOrNull(param.prKey)"/>
                            <!-- <p-inputNumber [useGrouping]="false" mode="decimal" class="w-full" [(ngModel)]="searchInfo[param.prKey]" [required]="param.required" [formControlName]="param.prKey"></p-inputNumber> -->
                            <label [htmlFor]="param.prKey">{{param.prDisplayName}}<span *ngIf="param.required" class="text-red-500">*</span></label>
                            <small class="text-red-500" *ngIf="formSearch.controls[param.prKey].dirty && formSearch.controls[param.prKey].errors?.required">
                                {{tranService.translate("global.message.required")}}
                            </small>
                        </span>
                        <span
                            class="p-float-label relative"
                            *ngIf="param.prType == paramTypes.STRING && param.isAutoComplete == false "
                            [class]="formSearch.controls[param.prKey].dirty && formSearch.controls[param.prKey].errors?.required ? 'report-param-required-error' : ''"
                        >
                            <input class="w-full" pInputText  [(ngModel)]="searchInfo[param.prKey]" [formControlName]="param.prKey" [required]="param.required"/>
                            <label [htmlFor]="param.prKey">{{param.prDisplayName}}<span *ngIf="param.required" class="text-red-500">*</span></label>
                            <small class="text-red-500" *ngIf="formSearch.controls[param.prKey].dirty && formSearch.controls[param.prKey].errors?.required">
                                {{tranService.translate("global.message.required")}}
                            </small>
                        </span>
                        <span
                            class="p-float-label relative"
                            *ngIf="param.prType == paramTypes.DATE "
                            [class]="formSearch.controls[param.prKey].dirty && formSearch.controls[param.prKey].errors?.required ? 'report-param-required-error' : ''"
                        >
                            <p-calendar styleClass="w-full"
                                        [(ngModel)]="searchInfo[param.prKey]"
                                        [showClear]="param.isAutoComplete == true ? false : true"
                                        [dateFormat]="param.dateType == dateTypes.MONTH ? 'mm/yy' : 'dd/mm/yy'"
                                        [hourFormat]="param.dateType == dateTypes.DATETIME ? 'hh:mm:ss' : ''"
                                        [view]="param.dateType == dateTypes.MONTH ? 'month' : 'date'"
                                        [showTime]="param.dateType == dateTypes.DATETIME" [showSeconds]="param.dateType == dateTypes.DATETIME"
                                        appendTo="body"
                                        [formControlName]="param.prKey"
                                        [showOnFocus]="param.isAutoComplete == true ? false : true"
                                        [readonlyInput]="param.isAutoComplete == true ? true : false"
                                        [placeholder]="tranService.translate('global.text.inputDate')"
                                        [required]="param.required"
                                        (ngModelChange)="onDateChange($event)"
                            ></p-calendar>
                            <label [htmlFor]="param.prKey">{{param.prDisplayName}}<span *ngIf="param.required" class="text-red-500">*</span></label>
                            <small class="text-red-500" *ngIf="formSearch.controls[param.prKey].dirty && formSearch.controls[param.prKey].errors?.required">
                                {{tranService.translate("global.message.required")}}
                            </small>
                        </span>
                        <span
                            class="p-float-label relative"
                            *ngIf="param.prType == paramTypes.RECENTLY_DATE_FROM "
                            [class]="formSearch.controls[param.prKey].dirty && formSearch.controls[param.prKey].errors?.required ? 'report-param-required-error' : ''"
                        >
                            <p-calendar styleClass="w-full" [ngModelOptions]="{standalone: true}"
                                        [dateFormat]="param.dateType == dateTypes.MONTH ? 'mm/yy' : 'dd/mm/yy'"
                                        [hourFormat]="param.dateType == dateTypes.DATETIME ? 'hh:mm:ss' : ''"
                                        [view]="param.dateType == dateTypes.MONTH ? 'month' : 'date'"
                                        [showTime]="param.dateType == dateTypes.DATETIME"
                                        [showSeconds]="param.dateType == dateTypes.DATETIME"
                                        [showClear]="true" appendTo="body"
                                        [placeholder]="tranService.translate('global.text.inputDate')"
                                        [required]="param.required"
                                        [maxDate]="inputRecentlyDate.maxDateFrom"
                                        [minDate]="inputRecentlyDate.minDateFrom"
                                        [ngModel]="inputRecentlyDate.inputDateFrom"
                                        (onSelect)="onChangeRecentlyDateFrom($event, param.prKey, true)"
                                        (ngModelChange)="onChangeRecentlyDateFrom($event,  param.prKey, true)"
                                        (onClear)="onClearRecentlyDateFrom(param.prKey)"
                            ></p-calendar>
                            <label [htmlFor]="param.prKey">{{param.prDisplayName}}<span *ngIf="param.required" class="text-red-500">*</span></label>
                            <small *ngIf="param.required && (!inputRecentlyDate.inputDateFrom || !searchInfo[param.prKey])"
                                   class="text-red-500"> {{ tranService.translate('global.message.required') }} </small>
                        </span>

                        <span
                            class="p-float-label relative"
                            *ngIf="param.prType == paramTypes.RECENTLY_DATE_TO "
                            [class]="formSearch.controls[param.prKey].dirty && formSearch.controls[param.prKey].errors?.required ? 'report-param-required-error' : ''"
                        >
                            <p-calendar styleClass="w-full" [ngModelOptions]="{standalone: true}"
                                        [dateFormat]="param.dateType == dateTypes.MONTH ? 'mm/yy' : 'dd/mm/yy'"
                                        [hourFormat]="param.dateType == dateTypes.DATETIME ? 'hh:mm:ss' : ''"
                                        [view]="param.dateType == dateTypes.MONTH ? 'month' : 'date'"
                                        [showTime]="param.dateType == dateTypes.DATETIME"
                                        [showSeconds]="param.dateType == dateTypes.DATETIME"
                                        [showClear]="true" appendTo="body"
                                        [placeholder]="tranService.translate('global.text.inputDate')"
                                        [required]="param.required"
                                        [minDate]="inputRecentlyDate.minDateTo"
                                        [maxDate]="inputRecentlyDate.maxDateTo"
                                        [ngModel]="inputRecentlyDate.inputDateTo"
                                        (onSelect)="onChangeRecentlyDateTo($event,  param.prKey, true)"
                                        (ngModelChange)="onChangeRecentlyDateTo($event,  param.prKey, true)"
                                        (onClear)="onClearRecentlyDateTo(param.prKey)"
                            ></p-calendar>
                            <label [htmlFor]="param.prKey">{{param.prDisplayName}}<span *ngIf="param.required" class="text-red-500">*</span></label>
                            <small *ngIf="param.required && (!inputRecentlyDate.inputDateTo || !searchInfo[param.prKey])"
                                   class="text-red-500"> {{ tranService.translate('global.message.required') }} </small>
                        </span>
                        <span
                            class="p-float-label relative"
                            *ngIf="param.prType == paramTypes.TIMESTAMP "
                            [class]="formSearch.controls[param.prKey].dirty && formSearch.controls[param.prKey].errors?.required ? 'report-param-required-error' : ''"
                        >
                            <p-calendar styleClass="w-full"
                                        [(ngModel)]="searchInfo[param.prKey]"
                                        [showClear]="true"
                                        [showIcon]="true"
                                        dateFormat="dd/mm/yy"
                                        hourFormat="hh:mm:ss"
                                        [showTime]="true" [showSeconds]="true"
                                        [showClear]="true" [showIcon]="true" appendTo="body"
                                        [formControlName]="param.prKey"
                                        [placeholder]="tranService.translate('global.text.inputDate')"
                                        [required]="param.required"
                                        (ngModelChange)="onDateChange($event)"
                            ></p-calendar>
                            <label [htmlFor]="param.prKey">{{param.prDisplayName}}<span *ngIf="param.required" class="text-red-500">*</span></label>
                            <small class="text-red-500" *ngIf="formSearch.controls[param.prKey].dirty && formSearch.controls[param.prKey].errors?.required">
                                {{tranService.translate("global.message.required")}}
                            </small>
                        </span>
                        <div class="relative" *ngIf="(param.prType == paramTypes.LIST_NUMBER || param.prType == paramTypes.LIST_STRING || (param.prType == paramTypes.STRING && param.isAutoComplete == true)) " >
                            <vnpt-select
                                [control]="param.control"
                                class="w-full"
                                [(value)]="searchInfo[param.prKey]"
                                [placeholder]="param.prDisplayName"
                                [isAutoComplete]="param.prType == paramTypes.STRING"
                                [isMultiChoice]="param.isMultiChoice"
                                [options]="param.valueList"
                                [objectKey]="param.queryInfo.objectKey"
                                [paramKey]="(param.isAutoComplete ? param.queryInfo.input : 'display')"
                                [keyReturn]="(param.isAutoComplete ? param.queryInfo.output : 'value')"
                                [displayPattern]="(param.isAutoComplete ? param.queryInfo.displayPattern : '${display}')"
                                [lazyLoad]="param.isAutoComplete"
                                [paramDefault]="(param.isAutoComplete ? getParamDefault(param) : {})"
                                typeValue="primitive"
                                [floatLabel]="true"
                                [required]="param.required"
                                [showTextRequired]="param.required"
                                elementLength="300px"
                                tooltipBreakpoint=200
                                (onchange)="changeSelect($event, param)"
                            ></vnpt-select>
                            <small class="text-red-500" *ngIf="param.control.dirty && param.control.error.required">
                                {{tranService.translate("global.message.required")}}
                            </small>
                            <small class="text-red-500" *ngIf="!(param.control.dirty && param.control.error.required) && param.required && param.prType == paramTypes.STRING && searchInfo[param.prKey]?.length == 0">
                                {{tranService.translate("global.message.required")}}
                            </small>
                        </div>
                    </div>
                    <div class="col-1 mt-2 pl-0" *ngIf="listParameters.length % numberElementPerRow !== 0">
<!--                        <p-button icon="pi pi-search"-->
<!--                                    styleClass="p-button-rounded p-button-secondary p-button-text button-search"-->
<!--                                    type="submit"-->
<!--                        ></p-button>-->
                        <p-button styleClass="p-button-info" [disabled]="checkDisabledBtn()" [label]="tranService.translate('global.button.save')" type="submit"></p-button>
                    </div>
                </div>
            </div>
            <div *ngIf="listParameters.length % numberElementPerRow === 0" class="w-1">
                <div style="margin-left: 0;" class="grid w-full mb-1">
                    <div class="align-items-center">
<!--                        <p-button icon="pi pi-search"-->
<!--                                    styleClass="p-button-rounded p-button-secondary p-button-text button-search"-->
<!--                                    type="submit"-->
<!--                        ></p-button>-->
                        <p-button styleClass="p-button-info" [disabled]="checkDisabledBtn()" [label]="tranService.translate('global.button.save')" type="submit"></p-button>
                    </div>
                </div>
            </div>
        </div>
        <!-- </p-panel> -->
    </form>
    <div class="flex flex-row justify-content-center align-items-center relative" [style]="{
        width: width+'px',
        height: (height+40)+'px',
        marginLeft: 'auto',
        marginRight: 'auto',
        border: mode === objectMode.DETAIL ? 'none' : '1px dashed gray'
    }">
        <p-chart *ngIf="mode == objectMode.DETAIL && isValidChart && !isErrorQuery"
            #chart
            [type]="chartConfig.type"
            [data]="datasets"
            [options]="options"
            [width]="width"
            [height]="height+40"
            [responsive]="false"
            class="flex flex-row justify-content-center align-items-center"
            [ngClass]="{'surface-200': isLoadingChart}"
        ></p-chart>
        <div *ngIf="mode == objectMode.DETAIL && isLoadingChart && !isErrorQuery" class="absolute top-50 right-50" style="transform: translate(50%, -50%);">
            <p-progressSpinner
            styleClass="w-4rem h-4rem"
            strokeWidth="8"
            fill="var(--surface-ground)"
            animationDuration=".5s" />
        </div>
        <div *ngIf="!isValidChart" class="absolute top-50 right-50" style="transform: translate(50%, -50%);">
            {{tranService.translate('global.message.notChartData')}}
        </div>
        <div *ngIf="isErrorQuery" class="absolute top-50 right-50" style="transform: translate(50%, -50%);">
            {{tranService.translate('global.message.isErrorQuery')}}
        </div>
        <div *ngIf="mode != objectMode.DETAIL">Content</div>
    </div>
</div>

<p-overlayPanel #panelHelp>
    <div [innerHTML]="htmlContentDescription" style="width: 250px;">
    </div>
</p-overlayPanel>
