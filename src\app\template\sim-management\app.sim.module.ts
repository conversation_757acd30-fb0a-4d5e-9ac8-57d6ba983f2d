import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { BreadcrumbModule } from 'primeng/breadcrumb';
import { AppSimRoutingModule } from "./app.sim-routing";
import { AppSimListComponent } from "./list/app.sim.list.component";
import { AppSimCreateComponent } from "./create/app.sim.create.component";
import { FieldsetModule } from 'primeng/fieldset';
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { InputTextModule } from 'primeng/inputtext';
import { ButtonModule } from 'primeng/button';
import { CommonVnptModule } from "../common-module/common.module";
import { SimService } from "src/app/service/sim/SimService";
import { GroupSimComponent } from './group-sim/group-sim.list.component';
import { CreateGroupSimComponent } from './group-sim/create-group-sim/create-group-sim.component';
import { UpdateGroupSimComponent } from './group-sim/update-group-sim/update-group-sim.component';
import { SplitButtonModule } from 'primeng/splitbutton';
import { DropdownModule } from 'primeng/dropdown';
import { AutoCompleteModule } from 'primeng/autocomplete';
import { CalendarModule } from 'primeng/calendar';
import { DialogModule } from 'primeng/dialog';
import { SimDetailComponent } from "./detail/app.sim.detail.component";
import { CardModule } from 'primeng/card';
import { SplitterModule } from 'primeng/splitter';
import { ToggleButtonModule } from 'primeng/togglebutton';
import { RadioButtonModule } from 'primeng/radiobutton';
import { ListGroupComponent } from './group-sim/detail-group/group.detail.component';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { CustomerService } from "src/app/service/customer/CustomerService";
import { GroupSimService } from "src/app/service/group-sim/GroupSimService";
import { ContractService } from "src/app/service/contract/ContractService";
import { RatingPlanService } from "src/app/service/rating-plan/RatingPlanService";
import { ContractManagementComponent } from './contract-management/contract-management.component';
import { MultiSelectModule } from 'primeng/multiselect';
import { AccountService } from "src/app/service/account/AccountService";
import { PanelModule } from 'primeng/panel';
import { ProvinceService } from "src/app/service/account/ProvinceService";
import {AppRechargeMoneyComponent} from "./recharge-money/app.sim.recharge-money-history.list.component";
import {RechargeMoneyService} from "../../service/recharge-money/RechargeMoneyService";
import {TableModule} from "primeng/table";
import {SkeletonModule} from "primeng/skeleton";
import {ProgressSpinnerModule} from "primeng/progressspinner";
@NgModule({
    imports: [
        CommonModule,
        AppSimRoutingModule,
        BreadcrumbModule,
        FieldsetModule,
        FormsModule,
        ReactiveFormsModule,
        InputTextModule,
        ButtonModule,
        CommonVnptModule,
        SplitButtonModule,
        DropdownModule,
        AutoCompleteModule,
        CalendarModule,
        DialogModule,
        CardModule,
        SplitterModule,
        ToggleButtonModule,
        RadioButtonModule,
        MultiSelectModule,
        InputTextareaModule,
        PanelModule,
        TableModule,
        SkeletonModule,
        ProgressSpinnerModule
    ],
    declarations:[
        AppSimListComponent,
        AppSimCreateComponent,
        GroupSimComponent,
        CreateGroupSimComponent,
        UpdateGroupSimComponent,
        SimDetailComponent,
        ListGroupComponent,
        ContractManagementComponent,
        AppRechargeMoneyComponent,
    ],
    providers:[
        SimService,
        CustomerService,
        GroupSimService,
        ContractService,
        RatingPlanService,
        AccountService,
        ProvinceService,
        RechargeMoneyService,
    ]
})
export class AppSimModule{}
