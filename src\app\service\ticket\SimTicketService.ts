import { Inject, Injectable } from "@angular/core";
import { HttpService } from "../comon/http.service";

@Injectable()
export class SimTicketService{
    private prefixApi: string;
    constructor(@Inject(HttpService) private httpService:HttpService) {
        this.prefixApi = "/sim-ticket";
    }

    public search(params:{[key:string]:any}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(`${this.prefixApi}/search`,{}, params,callback, errorCallBack, finallyCallback);
    }
    public create(body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.post(`${this.prefixApi}/create`, {},body,{}, callback, errorCallback, finallyCallback);
    }
}
