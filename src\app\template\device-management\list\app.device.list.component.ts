import {Component, Inject, Injector, OnInit} from "@angular/core";
import {FormBuilder, FormControl, FormGroup} from "@angular/forms";
import {MenuItem} from "primeng/api";
import {DeviceService} from "src/app/service/device/DeviceService";
import {ColumnInfo, OptionTable} from "../../common-module/table/table.component";
import {CONSTANTS} from "../../../service/comon/constants";
import {OptionInputFile} from "../../common-module/input-file/input.file.component";
import {ComponentBase} from "../../../component.base";
import * as XLSX from 'xlsx';
import {DomSanitizer, SafeResourceUrl} from "@angular/platform-browser";
import {SimService} from "../../../service/sim/SimService";

@Component({
    selector: "app-device-list",
    templateUrl: "./app.device.list.component.html"
})
export class DeviceListComponent extends ComponentBase implements OnInit{
    constructor(
        @Inject(DeviceService) private deviceService: DeviceService,
        @Inject(SimService) private simService: SimService,
        private sanitizer: DomSanitizer,
        private formBuilder: FormBuilder,
        injector: Injector) {
        super(injector);
    }

    items: MenuItem[];
    home: MenuItem
    searchInfo: {
        imei: string|null,
        msisdn: string|null,//so thue bao
        imsi: string|null,//imsi
        // location:string|null,//vi tri
        country: string|null,//xuat su
        deviceType: string|null,//chung loai
        contractDateFrom: Date|null,
        contractDateTo: Date|null,
    };
    safeUrl: SafeResourceUrl;
    deviceInfo: {
        imei: string | null,
        location: string | null,
        msisdn: number | null,
        country: string | null,
        category: string | null,
        expiredDate: Date | string | null,
        deviceType: string | null,
        iotLink: number | null,
    }
    findCellIDDto: any;
    msisdn: number;
    formDetailDevice: any;
    isShowPopupDetailSim: boolean = false;
    isShowPopupDetailDevice: boolean = false;
    simId: string;
    detailSim:any = {};
    detailStatusSim: any={};
    detailCustomer:any={};
    detailRatingPlan: any={};
    detailContract: any={};
    detailAPN: any={};
    columns: Array<ColumnInfo>;
    dataSet: {
        content: Array<any>,
        total: number
    };
    dataStore: Array<any>;
    selectItems: Array<{id:number,[key:string]:any}>;
    optionTable: OptionTable;
    pageNumber: number;
    pageSize: number;
    sort: string;
    formSearch: any;
    maxDateFrom: Date|number|string|null = null;
    minDateTo: Date|number|string|null = null;
    maxDateTo: Date|number|string|null = null;
    isShowDialogImportByFile: boolean = false;
    simImportsOrigin: Array<any>;
    isShowErrorUpload: boolean = false;
    fileObject: any;
    optionInputFile: OptionInputFile;
    messageErrorUpload: string| null;
    ngOnInit(): void {
        let me = this;
        this.home = { icon: 'pi pi-home', routerLink: '/' };
        this.items = [{ label: this.tranService.translate("global.menu.devicemgmt") }, { label: this.tranService.translate("global.menu.listdevice") },];
        this.searchInfo = {
            imei: null,
            imsi: null,
            msisdn: null,
            // location: null,
            deviceType: null,
            country: null,
            contractDateFrom: null,
            contractDateTo: null
        }
        this.detailSim = {};
        this.deviceInfo = {
            imei: null,
            location: null,
            msisdn: null,
            country: null,
            category: null,
            expiredDate: null,
            deviceType: null,
            iotLink: null,
        }
        this.detailStatusSim = {
            statusData: null,
            statusReceiveCall: null,
            statusSendCall: null,
            statusWorldCall: null,
            statusReceiveSms: null,
            statusSendSms: null
        }
        const url = `https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d42135.60828498614!2d105.78743105312334!3d21.020807357074563!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3135ab9bd9861ca1%3A0xe7887f7b72ca17a9!2sHanoi%2C%20Vietnam!5e0!3m2!1sen!2s!4v1713255802111!5m2!1sen!2s`;
        this.safeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(url);
        this.safeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(url);
        this.formSearch = this.formBuilder.group(this.searchInfo);
        this.columns = [
            {
                name: this.tranService.translate("device.label.imei"),
                key: "imei",
                size: "250px",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("device.label.deviceType"),
                key: "deviceType",
                size: "250px",
                align: "left",
                isShow: true,
                isSort: false
            },
            {
                name: this.tranService.translate("device.label.subcriber"),
                key: "msisdn",
                size: "200px",
                align: "left",
                isShow: true,
                isSort: false,
                style:{
                    cursor: "pointer",
             color: "var(--mainColorText)"
                },
                funcClick(id, item) {
                    me.isShowPopupDetailSim = true;
                    me.simId = id.toString();
                    me.simService.getById(me.simId, (response)=>{
                        me.detailSim = {
                            ...response
                        }
                        me.simService.getDetailStatus(me.simId, (response)=>{
                            me.detailStatusSim =  {
                                statusData: response.gprsStatus == 1,
                                statusReceiveCall: response.icStatus == 1,
                                statusSendCall: response.ocStatus == 1,
                                statusWorldCall: response.iddStatus == 1,
                                statusReceiveSms: response.smtStatus == 1,
                                statusSendSms: response.smoStatus == 1
                            };
                        },()=>{})
                        me.detailCustomer = {
                            name: me.detailSim.customerName,
                            code: me.detailSim.customerCode
                        };
                        me.detailCustomer = {
                            name: me.detailSim.customerName,
                            code: me.detailSim.customerCode
                        };
                        me.simService.getDetailPlanSim(me.simId, (response)=>{
                            me.detailRatingPlan = {
                                ...response
                            }
                        }, ()=>{})
                        me.simService.getDetailContract(me.utilService.stringToStrBase64(me.detailSim.contractCode), (response)=>{
                            me.detailContract = response;
                        }, ()=>{});
                        me.detailAPN = {
                            code: me.detailSim.apnCode,
                            type: "Kết nối bằng 3G",
                            ip: 0,
                            rangeIp: me.detailSim.ip
                        };
                        me.simService.getConnectionStatus([me.simId], (resp)=>{
                            me.detailSim.connectionStatus = resp[0].userstate
                        }, ()=>{})
                    }, null,()=>{
                        this.messageCommonService.offload();
                    })
                },
            },
            {
                name: this.tranService.translate("device.label.country"),
                key: "country",
                size: "175px",
                align: "left",
                isShow: true,
                isSort: false
            },
            {
                name: this.tranService.translate("device.label.expireDate"),
                key: "expiredDate",
                size: "175px",
                align: "left",
                isShow: true,
                isSort: false,
                funcConvertText(value){
                    if(value == null) return "";
                    return me.utilService.convertDateToString(new Date(value));
                }
            }
        ]
        this.selectItems = [];
        this.optionTable = {
            hasClearSelected: true,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
            action: [
                {
                    icon: "pi pi-eye",
                    tooltip: this.tranService.translate("global.button.view"),
                    func: function(id, item){
                        me.msisdn = id;
                        me.messageCommonService.onload()
                        me.deviceService.detailDevice(Number(me.msisdn), (response) => {
                            me.deviceInfo = {
                                ...response
                            }
                            if (response.expiredDate != null && response.expiredDate != "") {
                                me.deviceInfo.expiredDate = new Date(response.expiredDate);
                                me.minDateTo = me.deviceInfo.expiredDate;
                                me.maxDateTo = me.deviceInfo.expiredDate;
                            } else {
                                me.deviceInfo.expiredDate = null;
                            }
                            me.initForm();
                        }, null, () => {
                            me.messageCommonService.offload();
                        })

                        me.deviceService.getLocation(Number(me.msisdn), (response) => {
                            if (response != null) {
                                me.findCellId(response.mediaDtoResp.cell_lac);
                            } else {
                                me.deviceInfo.location = " "
                                const url = `https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d42135.60828498614!2d105.78743105312334!3d21.020807357074563!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3135ab9bd9861ca1%3A0xe7887f7b72ca17a9!2sHanoi%2C%20Vietnam!5e0!3m2!1sen!2s!4v1713255802111!5m2!1sen!2s`;
                                me.safeUrl = me.sanitizer.bypassSecurityTrustResourceUrl(url);
                            }
                        })
                        me.isShowPopupDetailDevice = true;
                    },
                    funcAppear: function (id, item) {
                        return me.checkAuthen([CONSTANTS.PERMISSIONS.DEVICE.VIEW_DETAIL]);
                    }
                },
                {
                    icon: "pi pi-pencil",
                    tooltip: this.tranService.translate("global.button.edit"),
                    func: function(id, item){
                        me.router.navigate([`/devices/edit/${item.msisdn}`]);
                    },
                    funcAppear: function (id, item) {
                        return me.checkAuthen([CONSTANTS.PERMISSIONS.DEVICE.UPDATE]);
                    }
                },
                {
                    icon: "pi pi-trash",
                    tooltip: this.tranService.translate("global.button.delete"),
                    func: function(id, item){
                        me.messageCommonService.confirm(
                            me.tranService.translate("global.message.titleConfirmDeleteDevice"),
                            me.tranService.translate("global.message.confirmDeleteDevice"),
                            {
                                ok:()=>{
                                    me.messageCommonService.onload();
                                    me.deviceService.deleleDevice(item.msisdn, (response) => {
                                        me.messageCommonService.success(me.tranService.translate("global.message.deleteSuccess"));
                                        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);
                                        }, null, ()=>{
                                        me.messageCommonService.offload();
                                    })
                                },
                                cancel: ()=>{
                                    // me.messageCommonService.error(me.tranService.translate("global.message.deleteFail"));
                                }
                            }
                        )
                    },
                    funcAppear: function (id, item) {
                        return me.checkAuthen([CONSTANTS.PERMISSIONS.DEVICE.DELETE]);
                    }
                }
            ]
        }
        this.pageNumber = 0;
        this.pageSize = 10;
        this.sort = "imei,asc";

        this.dataSet = {
            content: [
            ],
            total: 0
        }
        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
        this.optionInputFile = {
            type: ['xls','xlsx'],
            messageErrorType: this.tranService.translate("global.message.wrongFileExcel"),
            maxSize: 10,
            unit: "MB",
            required: true,
            isShowButtonUpload: true,
            actionUpload: this.uploadFile.bind(this),
            disabled: false
        }
    }

    onSubmitSearch(){
        let me = this;
        me.pageNumber = 0;
        me.search(0, this.pageSize, this.sort, this.searchInfo);
    }

    updateParams(dataParams){
        Object.keys(this.searchInfo).forEach(key => {
            if(this.searchInfo[key] != null){
                if(key == "contractDateFrom"){
                    dataParams["contractDateFrom"] = this.searchInfo.contractDateFrom.getTime();
                }else if(key == "contractDateTo"){
                    dataParams["contractDateTo"] = this.searchInfo.contractDateTo.getTime();
                }else{
                    dataParams[key] = this.searchInfo[key];
                }
            }
        })
    }

    search(page, limit, sort, params){
        this.pageNumber = page;
        this.pageSize = limit;
        this.sort = sort;
        let me = this;
        let dataParams = {
            page,
            size: limit,
            sort
        }
        this.updateParams(dataParams);
        me.messageCommonService.onload();
        this.deviceService.search(dataParams, (response)=>{
            me.dataSet = {
                content: response.content,
                total: response.totalElements
            }
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }

    onChangeDateFrom(value){
        if(value){
            this.minDateTo = value;
        }else{
            this.minDateTo = null
        }
    }

    onChangeDateTo(value){
        if(value){
            this.maxDateFrom = value;
        }else{
            this.maxDateFrom = null;
        }
    }
    navigateToCreateDevice() {
        this.router.navigate(['/devices/create']);
    }
    clearFileCallback(){
        this.isShowErrorUpload = false;
    }
    uploadFile(objectFile: any) {
        let me = this;
        me.messageCommonService.onload();
        this.deviceService.uploadRegisterByFile(objectFile, (response) => {
            me.messageCommonService.offload();
            console.log(response)
            me.excuteResponseImportFile(response);
        })
    }
    excuteResponseImportFile(response) {
        let me = this;
        me.simImportsOrigin = undefined;
        me.isShowErrorUpload = false;
        this.optionInputFile.disabled = true;
        if (response.errorCode == CONSTANTS.DEVICE.SUCCESS) {
            me.messageCommonService.success(me.tranService.translate("device.text.messageSuccess"));
            me.isShowDialogImportByFile = false;
            me.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
            return;
        } else if (response.errorCode == CONSTANTS.DEVICE.WRONG_FOMAT){
            me.messageCommonService.error(me.tranService.translate("device.text.wrongFormat"));
            me.isShowDialogImportByFile = false;
            return;
        }
        else if (response.errorCode == CONSTANTS.DEVICE.FILE_TO_BIG){
            me.messageCommonService.error(me.tranService.translate("device.text.tooBig"));
            me.isShowDialogImportByFile = false;
            return;
        }else if (response.errorCode == CONSTANTS.DEVICE.COLUMN_INVALID) {
            me.messageCommonService.error(me.tranService.translate("device.text.columnInvalid"));
            me.isShowDialogImportByFile = false;
            return;
        } if (response.errorCode == CONSTANTS.DEVICE.MAX_ROW_FILE_IMPORT) {
            me.messageCommonService.error(me.tranService.translate("device.text.maxRowImport"));
            me.isShowDialogImportByFile = false;
            return;
        }else {
            let data = [];
            response.errorList.forEach(item => {
                data.push({
                    imei: item.imei,
                    msisdn: item.msisdn,
                    // location: item.location,
                    deviceType: item.deviceType,
                    expiredDate: item.expiredDate,
                    country: item.country,
                    error: this.getErrorContent(item.errorCode),
                })
            })
            if (data.length === 0) {
                me.messageCommonService.success(me.tranService.translate("device.text.messageSuccess"));
                me.isShowDialogImportByFile = false;
                return;
            }
            me.isShowDialogImportByFile = false;
            const header = [me.tranService.translate("device.label.imei"),
                me.tranService.translate("device.label.msisdn"),
                // me.tranService.translate("device.label.location"),
                me.tranService.translate("device.label.deviceType"),
                me.tranService.translate("device.label.expireDate"),
                me.tranService.translate("device.label.country"),
                me.tranService.translate("device.label.note")]
            const ws: XLSX.WorkSheet = XLSX.utils.json_to_sheet(data);
            XLSX.utils.sheet_add_aoa(ws, [header], { origin: 'A1' });
            const columnWidths = { A: { wch: 20 }, B: { wch: 20 }, C: { wch: 15 }, D: { wch: 15 }, E: { wch: 15 }, F: { wch: 15 }, G: { wch: 50 } };
            ws['!cols'] = Object.keys(columnWidths).map(col => ({ ...{ width: columnWidths[col].wch }, ...columnWidths[col] }));
            const wb: XLSX.WorkBook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'Sheet 1');
            XLSX.writeFile(wb, 'error.xlsx');
            me.messageCommonService.warning(me.tranService.translate("device.text.textResultImportByFile"))
        }
    }
    downloadTemplate(){
        this.deviceService.downloadTemplate();
    }
    importByFile() {
        let me = this;
        me.isShowDialogImportByFile = true;
        me.observableService.next(CONSTANTS.OBSERVABLE.KEY_INPUT_FILE_VNPT, {});
        me.simImportsOrigin = undefined;
        me.isShowErrorUpload = false;
    }
    getErrorContent(code) {
        let me = this;
        if (code == CONSTANTS.DEVICE.MSISDN_IS_EMPTY) {
            return me.tranService.translate('device.text.msisdnEmpty')
        } else if (code == CONSTANTS.DEVICE.MSISDN_NOTEXITS) {
            return me.tranService.translate('device.text.msisdnNotExists')
        } else if (code == CONSTANTS.DEVICE.MSISDN_ASSIGN) {
            return me.tranService.translate('device.text.msisdnAssign')
        } else if (code == CONSTANTS.DEVICE.MSISDN_INVALD) {
            return me.tranService.translate('device.text.msisdnInvalid')
        } else if (code == CONSTANTS.DEVICE.MSISDN_IS_EMPTY) {
            return me.tranService.translate('device.text.msisdnIsEmptly')
        } else if (code == CONSTANTS.DEVICE.MSISDN_IS_DUPLICATE) {
            return me.tranService.translate('device.text.msisdnIsDuplicate')
        } else if (code == CONSTANTS.DEVICE.EXPRIRED_DATE_INVALID) {
            return me.tranService.translate('device.text.expriredDateInvalid')
        } else if (code == CONSTANTS.DEVICE.MSISDN_NOT_PERMISSION) {
            return me.tranService.translate('device.text.msisdnNotPermission')
        } else if (code == CONSTANTS.DEVICE.IMEI_IS_EXSIT) {
            return me.tranService.translate('device.text.imeiIsExist')
        } else if (code == CONSTANTS.DEVICE.IMEI_IS_DUPLITE) {
            return me.tranService.translate('device.text.imeiIsDuplicate')
        } else if (code == CONSTANTS.DEVICE.IMEI_LEN) {
            return me.tranService.translate('device.text.imeiLen')
        } else if (code == CONSTANTS.DEVICE.DEVICE_TYPE_LEN) {
            return me.tranService.translate('device.text.deviceTypeLen')
        } else if (code == CONSTANTS.DEVICE.COUNTRY_LEN) {
            return me.tranService.translate('device.text.countryLen');
        }
        return ""
    }
    convertDateString(dateString: string): string {
        // Create a new Date object from the string
        const date = new Date(dateString);

        // Extract day, month, and year
        const day = date.getDate();
        const month = date.getMonth() + 1; // Months are zero-based
        const year = date.getFullYear();

        // Format day and month to two digits
        const formattedDay = day < 10 ? `0${day}` : `${day}`;
        const formattedMonth = month < 10 ? `0${month}` : `${month}`;

        // Return the formatted date string
        return `${formattedDay}/${formattedMonth}/${year}`;
    };

    findCellId(cell_lac){
        let me = this;
        this.deviceService.findCellId({loc: cell_lac.split(":")[1], cell: cell_lac.split(":")[0]}, (response)=>{
                me.findCellIDDto = {
                    lat: response.lat,
                    lng: response.lng
                }
                me.findAddress(response.lat, response.lng)
        }, null, null)
    };

    findAddress(lat, lon){
        let me = this;
        me.deviceService.findAddress(lat, lon, (response)=>{
            me.deviceInfo.location = response.display_name
            const url = `https://www.google.com/maps?q=${me.findCellIDDto.lat},${me.findCellIDDto.lng}&output=embed`;
            me.safeUrl = me.sanitizer.bypassSecurityTrustResourceUrl(url);
        }, null, null)
    }

    initForm() {
        // debugger
        let me = this;
        me.formDetailDevice = me.formBuilder.group({
            imei: [me.deviceInfo.imei],
            location: [me.deviceInfo.location],
            msisdn: [me.deviceInfo.msisdn],
            country: [me.deviceInfo.country],
            expiredDate: [me.utilService.convertDateToString(new Date(me.deviceInfo.expiredDate))],
            deviceType: [me.deviceInfo.deviceType],
            iotLink: [me.deviceInfo.iotLink],
        });
        if (me.deviceInfo.iotLink === 1) {
            me.formDetailDevice.controls["iotLink"].setValue(true);
        }
    };

    goUpdate() {
        this.router.navigate([`/devices/edit/${this.msisdn}`]);
    };

    getNameStatus(value){
        if(value == 0){
            return this.tranService.translate("sim.status.inventory");
        }else if(value == CONSTANTS.SIM_STATUS.READY){
            // return this.tranService.translate("sim.status.ready");
            return this.tranService.translate("sim.status.activated");
        }else if(value == CONSTANTS.SIM_STATUS.ACTIVATED){
            return this.tranService.translate("sim.status.activated");
        }else if(value == CONSTANTS.SIM_STATUS.DEACTIVATED){
            return this.tranService.translate("sim.status.deactivated");
        }else if(value == CONSTANTS.SIM_STATUS.PURGED){
            return this.tranService.translate("sim.status.purged");
        }else if(value == CONSTANTS.SIM_STATUS.INACTIVED){
            return this.tranService.translate("sim.status.inactivated");
        }else if(value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY){
            return this.tranService.translate("sim.status.processingChangePlan");
        }else if(value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY){
            return this.tranService.translate("sim.status.processingRegisterPlan");
        }else if(value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY){
            return this.tranService.translate("sim.status.waitingCancelPlan");
        }
        return "";
    }

    getClassStatus(value){
        if(value == 0){
            return ['p-1' , "border-round", "border-400", "text-color","inline-block"];
        }else if(value == CONSTANTS.SIM_STATUS.READY){
            // return ['p-1', "bg-blue-600", "border-round","inline-block"];
            return ['p-2', "text-green-800", "bg-green-100","border-round","inline-block"];
        }else if(value == CONSTANTS.SIM_STATUS.ACTIVATED){
            return ['p-2', 'text-green-800', "bg-green-100","border-round","inline-block"];
        }else if(value == CONSTANTS.SIM_STATUS.INACTIVED){
            return ['p-2', 'text-yellow-800', "bg-yellow-100", "border-round","inline-block"];
        }else if(value == CONSTANTS.SIM_STATUS.DEACTIVATED){
            return ['p-2', 'text-indigo-600', "bg-indigo-100", "border-round","inline-block"];
        }else if(value == CONSTANTS.SIM_STATUS.PURGED){
            return ['p-2', 'text-red-700', "bg-red-100", "border-round","inline-block"];
        }else if(value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY){
            return ['p-2', 'text-cyan-800', "bg-cyan-100", "border-round","inline-block"];
        }else if(value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY){
            return ['p-2', 'text-teal-800', "bg-teal-100", "border-round","inline-block"];
        }else if(value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY){
            return ['p-2', 'text-orange-700', "bg-orange-100", "border-round","inline-block"];
        }
        return [];
    }

    getServiceType(value) {
        if(value == CONSTANTS.SERVICE_TYPE.PREPAID) return this.tranService.translate("sim.serviceType.prepaid")
        else if(value == CONSTANTS.SERVICE_TYPE.POSTPAID) return this.tranService.translate("sim.serviceType.postpaid")
        else return ""
    }

    protected readonly CONSTANTS = CONSTANTS;
}
