import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import {AppAlertListComponent} from "./alert-setting/list/app.alert.list.component";
import {AppAlertCreateComponent} from "./alert-setting/create/app.alert.create.component";
import {AppAlertDetailComponent} from "./alert-setting/detail/app.alert.detail.component";
import {AppAlertEditComponent} from "./alert-setting/edit/app.alert.edit.component";
import {AppGroupReceivingCreateComponent} from "./alert-receiving-group/create/app.group-receiving.create.component";
import {AppGroupReceivingDetailComponent} from "./alert-receiving-group/detail/app.group-receiving.detail.component";
import {AppGroupReceivingEditComponent} from "./alert-receiving-group/edit/app.group-receiving.edit.component";
import {AppAlertsAlertHistoryComponent} from "../alert/alert-history/app.alerts.alert.history";
import {
    AppAlertsAlertReceivingGroupComponent
} from "./alert-receiving-group/list/app.alerts.alert.receiving.group.component";
import DataPage from "../../service/data.page";
import {CONSTANTS} from "../../service/comon/constants";

@NgModule({
    imports:[
        RouterModule.forChild([
            {path: "", component: AppAlertListComponent, data: new DataPage("global.menu.alertList", [CONSTANTS.PERMISSIONS.ALERT.VIEW_LIST])},
            {path: "create", component: AppAlertCreateComponent, data: new DataPage("global.titlepage.createAlarm", [CONSTANTS.PERMISSIONS.ALERT.CREATE, CONSTANTS.PERMISSIONS.ALERT.CREATE_WALLET_EXPIRY, CONSTANTS.PERMISSIONS.ALERT.CREATE_WALLET_THRESHOLD])},
            {path: "detail/:id", component: AppAlertDetailComponent, data: new DataPage("global.titlepage.detailAlarm", [CONSTANTS.PERMISSIONS.ALERT.VIEW_DETAIL])},
            {path: "edit/:id", component: AppAlertEditComponent, data: new DataPage("global.titlepage.editAlarm", [CONSTANTS.PERMISSIONS.ALERT.UPDATE])},
            {path: "wallet-threshold/edit/:id", component: AppAlertEditComponent, data: new DataPage("global.titlepage.editAlarm", [CONSTANTS.PERMISSIONS.ALERT.UPDATE_WALLET_THRESHOLD])},
            {path: "wallet-expiry/edit/:id", component: AppAlertEditComponent, data: new DataPage("global.titlepage.editAlarm", [CONSTANTS.PERMISSIONS.ALERT.UPDATE_WALLET_EXPIRY])},
            {path: "receiving-group/create", component: AppGroupReceivingCreateComponent, data: new DataPage("global.titlepage.createAlertReceivingGroup", [CONSTANTS.PERMISSIONS.ALERT_RECEIVING_GROUP.CREATE])},
            {path: "receiving-group/detail/:id", component: AppGroupReceivingDetailComponent, data: new DataPage("global.titlepage.detailAlertReceivingGroup", [CONSTANTS.PERMISSIONS.ALERT_RECEIVING_GROUP.VIEW_DETAIL])},
            {path: "receiving-group/edit/:id", component: AppGroupReceivingEditComponent, data: new DataPage("global.titlepage.editAlertReceivingGroup", [CONSTANTS.PERMISSIONS.ALERT_RECEIVING_GROUP.UPDATE])},
            {path: "receiving-group", component: AppAlertsAlertReceivingGroupComponent, data: new DataPage("global.titlepage.listAlertReceivingGroup", [CONSTANTS.PERMISSIONS.ALERT_RECEIVING_GROUP.VIEW_LIST])},
            {path: "history", component: AppAlertsAlertHistoryComponent, data: new DataPage("global.titlepage.listAlertHistory", [CONSTANTS.PERMISSIONS.ALERT_HISTORY.VIEW_LIST])}
        ])
    ],
    exports: [RouterModule]
})
export class AppAlertRoutingModule { }
