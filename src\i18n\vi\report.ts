export default {
    label: {
        tabGeneral: "Thông tin chung",
        tabCrontab: "Lập lịch tổng hợp",
        tabSend: "<PERSON><PERSON>i báo cáo",
        reportName: "Tên báo cáo",
        reportStatus: "Trạng thái báo cáo",
        reportEnablePreview: "Xem trước báo cáo",
        description: "Mô tả",
        tableList: "<PERSON>h sách các bảng",
        paramList: "<PERSON><PERSON> sách các mục tìm kiếm",
        tableName: "Tên bảng",
        paramKey: "Key",
        paramType: "Kiểu dữ liệu",
        paramDisplay: "Tên hiển thị",
        query: "<PERSON><PERSON>u truy vấn",
        schema: "Schema",
        updateDate: "<PERSON><PERSON><PERSON> cập nhật",
        fromDate: "Từ ngày",
        toDate: "Đến ngày",
        reportreceivinggroup: "Tên nhóm nhận báo cáo động",
        fieldDisplay: "Tên cột hiển thị",
        fieldData: "Tên trường dữ liệu",
        valueDisplay: "Giá trị hiển thị",
        valueDB: "Giá trị trong database",
        timeOnce: "Giờ tổng hợp",
        startTime: "Giờ bắt đầu",
        endTime: "Giờ kết thúc",
        typeSchedule: "Kiểu báo cáo",
        runOne: "Chạy 1 lần",
        runRepeat: "Chạy lặp",
        numberRepeatHour: "Số giờ lặp",
        dayInMonth: "Ngày trong tháng",
        dayInWeek: "Ngày trong tuần",
        monthInYear: "Tháng trong năm",
        monday: "Thứ hai",
        tuesday: "Thứ ba",
        wednesday: "Thứ tư",
        thursday: "Thứ năm",
        friday: "Thứ sáu",
        saturday: "Thứ 7",
        sunday: "Chủ nhật",
        january: "Tháng 1",
        february: "Tháng 2",
        march: "Tháng 3",
        april: "Tháng 4",
        may: "Tháng 5",
        june: "Tháng 6",
        july: "Tháng 7",
        august: "Tháng 8",
        september: "Tháng 9",
        october: "Tháng 10",
        november: "Tháng 11",
        december: "Tháng 12",
        emailSubject: "Tiêu đề email",
        emailGroups: "Nhóm nhận báo cáo",
        emailReceive: "Email nhận báo cáo",
        hourSend: "Giờ gửi báo cáo",
        dateType: "Kiểu hiển thị",
        isAutoComplete: "Gọi truy vấn",
        isMultiChoice: "Lựa chọn nhiều",
        objectKey: "Đối tượng truy vấn",
        input: "Trường truy vấn",
        output: "Trường lấy giá trị",
        displayPattern: "Mẫu hiển thị",
        queryParams:"Tìm kiếm tùy chọn",
        required: "Bắt buộc",
        sampleQueryParam:"Mẫu dữ liệu: customerCode=$customerCode&userType=1 (customerCode,userType: Trường query; $customerCode: trường customerCode có thể cho người dùng lựa chọn; userType=1: trường tìm kiếm cố định)",
        recentlyDateFrom: "Recently Date From",
        recentlyDateTo: "Recently Date To",
        showDateDefault: "Hiển thị thời gian hiện tại",
    },
    status: {
        active: "Hoạt động",
        inactive: "Không hoạt động",
    },
    text: {
        inputReportName: "Nhập tên báo cáo",
        createTable: "Tạo bảng",
        updateTable: "Cập nhật bảng",
        detailTable: "Chi tiết bảng",
        createParameter: "Tạo mục tìm kiếm",
        updateParameter: "Cập nhật mục tìm kiếm",
        detailParameter: "Chi tiết mục tìm kiếm",
        inputQuery: "Nhập truy vấn",
        inputTableName: "Nhập tên bảng",
        selectSchema: "Chọn schema",
        inputParamKey: "Nhập key tìm kiếm",
        inputDisplayName: "Nhập tên hiển thị",
        selectParamType: "Chọn kiểu dữ liệu",
        inputDescription: "Nhập mô tả",
        inputemails: "Nhập Emails",
        inputsms: "Nhập SMS",
        inputNameReceiving: "Nhập tên nhóm nhận báo cáo động",
        selectCycle: "Chọn số giờ lặp",
        selectHourSummary: "Chọn giờ tổng hợp",
        selectStartTime: "Chọn giờ bắt đầu",
        selectEndTime: "Chọn giờ kết thúc",
        selectEmailGroup: "Chọn nhóm nhận báo cáo",
        selectHourSend: "Chọn giờ gửi báo cáo",
        inputEmailSubject: "Nhập tiêu đề email",
        selectDateType: "Chọn kiểu hiển thị",
        inputObjectKey: "Nhập đối tượng truy vấn",
        inputInput: "Nhập trường truy vấn",
        inputOutput: "Nhập trường lấy giá trị",
        inputDisplayPattern: "Nhập mẫu hiển thị",
        errorExportLimit:"Dữ liệu xuất file vượt quá 1 triệu dòng",
        inputQueryParam:"Nhập Query Param",
        recentlyDateFrom: "Recently Date From",
        recentlyDateTo: "Recently Date To",
    },
    button: {
        addTable: "Thêm bảng",
        addParam: "Thêm mục tìm kiếm",
    },
    paramType: {
        number: "Number",
        string: "String",
        date: "Date",
        listNumber: "Enum Number",
        listString: "Enum String",
        timestamp: "Timestamp",
        recentlyDateFrom: "Recently Date From",
        recentlyDateTo: "Recently Date To",
    },
    schema: {
        core: "COREMGMT",
        sim: "SIMMGMT",
        rule: "RULEMGMT",
        bill: "BILLING",
        log: "LOGGING",
        monitor: "MONITORING",
        report: "REPORTING",
        elasticsearch: "ELASTICSEARCH"
    },
    receiving: {
        name: "Tên nhóm nhận báo cáo động",
        description: "Mô tả",
        emails: "Email",
        sms: "SMS",
    },
    datetype: {
        month: "Tháng",
        date: "Ngày",
        datetime: "Ngày giờ"
    },
    message: {
        wrongQueryParamFormat: "Sai định dạng query param. Vui lòng kiểm tra hướng dẫn bên cạnh label"
    }
}
