<div *ngIf="canShare && canView">
<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{this.tranService.translate("datapool.label.shareData")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
</div>

<style>
    input[type=number]::-webkit-outer-spin-button,
    input[type=number]::-webkit-inner-spin-button {
        /* Ẩn nút */
        display: none;
    }
</style>

<upload-file-vnpt
    [shareList]="shareList"
    [remainDataTotal]="remainDataTotal"
    [isShowDialogImportByFile]="isShowDialogImportByFile"
    [isShowErrorUpload]="isShowErrorUpload"
    (onHideImport)="onHideImport()"
    [totalRemain]="originRemain"
    [trafficType]="typeOfTraffic"
></upload-file-vnpt>

<form action="" [formGroup]="shareWalletDataForm" (submit)="submitForm()">

    <div class="mt-3 shareData">
        <p-card>
            <div class="font-bold text-xl">{{walletName}}</div>
            <div class="mt-5 flex flex-row gap-3 justify-content-between">
                <div class="flex flex-row gap-3">
                    <div class="relative flex">
                        <vnpt-select
                            [control]="phoneReceiptSelectControl"
                            [(value)]="phoneReceiptSelect"
                            (onchange)="checkValidAdd()"
                            (onSelectItem)="addPhone(phoneReceiptSelect, true)"
                            [isAutoComplete]="true"
                            [isMultiChoice]="false"
                            paramKey="phoneReceipt"
                            keyReturn="phoneReceipt"
                            [lazyLoad]="false"
                            [placeholder]="tranService.translate('datapool.label.receiverPhone')"
                            displayPattern="${phoneReceipt}"
                            [loadData]="getListShareInfoCbb.bind(this)"
                        ></vnpt-select>
                    </div>
                    <button [disabled]="isClickCreate || !isValidPhone" type="button" pButton [label]="tranService.translate('datapool.button.add')" (click)="addPhone(phoneReceiptSelect)"></button>
                </div>
                <p-button icon="pi pi-file-excel" [label]="tranService.translate('datapool.button.importFile')" (click)="importByFile()" styleClass="p-button-success"></p-button>
            </div>
            <div class="mb-5 flex flex-row gap-3 justify-content-between text-red-500 px-1">
                <div *ngIf="!isValidPhone">
                    {{tranService.translate("global.message.invalidPhone")}}
                </div>
            </div>
            <div class="flex flex-row justify-content-between mb-2">
                <div class="font-semibold text-lg">{{tranService.translate('datapool.label.remainData')}}: {{shareMethod == 1 ? formatNumber(dataShareGroupTotal) : formatNumber(remainDataTotal)}}/{{formatNumber(totalData)}}</div>
                <div class="flex flex-row gap-2">
                    <button [disabled]="shareList.length <= 0" pButton class="p-button-outlined" type="button" (click)="shareData()">{{tranService.translate("datapool.button.equalSharing")}}</button>
                    <button [disabled]="shareList.length <= 0" pButton class="p-button-outlined" type="button" (click)="defineData()">{{tranService.translate("datapool.button.fixedAllocation")}}</button>
                    <button [disabled]="shareList.length <= 0" pButton class="p-button-outlined" type="button" (click)="resetData()">{{tranService.translate("datapool.button.revokeSharing")}}</button>
                </div>
            </div>
            <p-table
                [value]="shareList" [tableStyle]="{ 'min-width': '50rem' }"
                [(selection)]="selectedTableData"
                dataKey="phoneReceipt"
                [paginator]="true"
                [rows]="10"
                [showCurrentPageReport]="true"
                [totalRecords]="shareList.length"
                currentPageReportTemplate="Hiển thị từ {first} đến {last} trong số {totalRecords} các bản ghi"
                [rowsPerPageOptions]="[10, 25, 50]"
            >
                <ng-template pTemplate="header">
                    <tr>
                        <th class="flex flex-row gap-2">
                            <p-tableHeaderCheckbox [disabled]="!isAutoWallet" (click)="onClickSelection()"></p-tableHeaderCheckbox>
                            <div class="align-self-auto">{{tranService.translate('datapool.label.autoSharing')}}</div>
                        </th>
                        <th>{{tranService.translate("global.text.stt")}}</th>
                        <th>{{tranService.translate("datapool.label.phone")}}</th>
                        <th>{{tranService.translate('datapool.label.fullName')}}</th>
                        <th>{{tranService.translate('datapool.label.email')}}</th>
                        <th *ngIf="typeOfTraffic == 'Gói Data'">{{tranService.translate('datapool.label.sharingDataNotType')}} (MB)</th>
                        <th *ngIf="typeOfTraffic == 'Gói thoại'">{{tranService.translate('datapool.label.sharingDataNotType')}} ({{tranService.translate('alert.label.minutes')}})</th>
                        <th *ngIf="(typeOfTraffic || '').toUpperCase().includes('Gói SMS'.toUpperCase())">{{tranService.translate('datapool.label.sharingDataNotType')}} (SMS)</th>
                        <th *ngIf="typeOfTraffic != 'Gói Data' && typeOfTraffic != 'Gói thoại' && !(typeOfTraffic || '').toUpperCase().includes('Gói SMS'.toUpperCase())">{{tranService.translate('datapool.label.sharingData')}}</th>
                        <th>{{tranService.translate('datapool.label.percentage')}}</th>
                        <th></th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-list let-index="rowIndex">
                    <tr>
                        <td>
                            <p-tableCheckbox [value]="list" [disabled]="!isAutoWallet" (click)="onClickSelection()"></p-tableCheckbox>
                        </td>
                        <td>{{ index + 1 }}</td>
                        <td>{{ list.phoneReceipt }}</td>
                        <td>
                            <input type="text" (input)="changeDataName($event, index)" pInputText [value]="list.name">
                            <div class="text-red-500" *ngIf="list.name?.length >=50">
                                {{tranService.translate("global.message.maxLength",{len:50})}}
                            </div>
                            <div *ngIf="!utilService.checkValidCharacterVietnamese(list.name)" class="text-red-500">
                                {{tranService.translate("global.message.wrongFormatName",{len:150})}}
                            </div>
                        </td>
                        <td>
                            <input type="text" (input)="changeDataMail($event, index)" pInputText [value]="list.email">
                            <div class="text-red-500" *ngIf="list.email?.length >=100">
                                {{tranService.translate("global.message.maxLength",{len:100})}}
                            </div>
                            <div class="text-red-500" *ngIf="isMailInvalid(list.email)">
                                {{tranService.translate("global.message.formatEmail")}}
                            </div>
                        </td>
                        <td>
                            <div class="flex flex-row align-items-center">
                                <input [ngClass]="{'surface-200':list.locked}" (input)="changeData($event, index)" (keydown)="onKeyDown($event, index)" [disabled]="list.locked" class="border-noround-right" pInputText type="number" [value]="list.data">
                                <div pInputText class="border-round-right border-noround-left  cursor-pointer surface-300" styleClass="cursor-pointer" (click)="list.locked=!list.locked">
                                    <i *ngIf="list.locked" class="pi pi-lock"></i>
                                    <i *ngIf="!list.locked" class="pi pi-lock-open"></i>
                                </div>
                            </div>
                            <div class="text-red-500" *ngIf="((typeOfTraffic || '').toUpperCase().includes('Gói SMS'.toUpperCase())) && (list.data < 10 || !checkDataCondition(index))">
                                {{tranService.translate("datapool.message.smsError")}}
                            </div>
                            <div class="text-red-500" *ngIf="typeOfTraffic == 'Gói Data' && (list.data < 100 || !checkDataCondition(index))">
                                {{tranService.translate("datapool.message.dataError")}}
                            </div>
                        </td>
                        <td><div class="flex flex-row ">{{ list.percent }} <div *ngIf="list.percent||list.percent==0">%</div> </div></td>
                        <td><button type="button" pButton class="p-button-outlined" (click)="deleteItem(index)"><i class="pi pi-trash"></i></button></td>
                    </tr>
                </ng-template>
            </p-table>

        </p-card>
    </div>

    <div class="mt-3">
        <p-card>
            <div class="w-full mt-3 px-2">
                <div class="flex flex-column gap-2 flex-1">
                    <label htmlFor="description">{{tranService.translate("datapool.label.description")}}</label>
                    <textarea
                        class="w-full" style="resize: none;"
                        rows="5"
                        [autoResize]="false"
                        pInputTextarea id="description"
                        formControlName="description"
                        [placeholder]="tranService.translate('sim.text.inputDescription')"
                    ></textarea>
                </div>
            </div>

            <div class="flex flex-row justify-content-center gap-3 p-2 mt-5">
                <a routerLink="/data-pool/walletMgmt/list"><button pButton [label]="tranService.translate('global.button.cancel')" class="p-button-secondary p-button-outlined" type="button"></button></a>
                <button pButton [disabled]="!(shareWalletDataForm.controls.walletValue.valid && shareWalletDataForm.controls.description.valid) || checkValidData()" [label]="tranService.translate('global.button.save')" class="p-button-info" type="button" (click)="handleShareData()"></button>
                <!-- <button pButton (click)="testClick()">Test</button> -->
            </div>
        </p-card>
    </div>

    <p-dialog (onHide)="onHideDefine()" [header]="tranService.translate('datapool.button.fixedAllocation')" [(visible)]="isShowDefined" [modal]="true" [style]="{ width: '50vw' }" [draggable]="false" [resizable]="false">
        <div class="flex flex-column gap-2 flex-1">
            <label htmlFor="">{{tranService.translate("datapool.label.revokeData", {data: dataForLabel})}}</label>
            <input type="number" (input)="checkValidDefine($event)" formControlName="definedData" pInputText>
            <div class="text-red-500" *ngIf="((typeOfTraffic || '').toUpperCase().includes('Gói SMS'.toUpperCase())) && (shareWalletDataForm.controls['definedData'].value < 10 || isValidDataFixed) && shareWalletDataForm.controls['definedData'].dirty">
                {{tranService.translate("datapool.message.smsError")}}
            </div>
            <div class="text-red-500" *ngIf="typeOfTraffic == 'Gói Data' && (shareWalletDataForm.controls['definedData'].value < 100 || isValidDataFixed) && shareWalletDataForm.controls['definedData'].dirty">
                {{tranService.translate("datapool.message.dataError")}}
            </div>
            <div class="text-red-500" *ngIf="isDefineValueError">{{tranService.translate("datapool.message.exceededData")}}</div>
            <div class="m-auto">
                <button class="m-auto mr-2" [disabled]="(((typeOfTraffic || '').toUpperCase().includes('Gói SMS'.toUpperCase())) && shareWalletDataForm.controls['definedData'].value < 10) || isValidDataFixed ||(typeOfTraffic == 'Gói Data' && shareWalletDataForm.controls['definedData'].value < 100)||isDefineValueError || shareWalletDataForm.controls['definedData'].value == null || shareWalletDataForm.controls['definedData'].value == undefined || isValidDataFixed" pButton type="button" (click)="definedDataChange()">{{tranService.translate("global.button.confirm")}}</button>
                <button class="m-auto ml-2 p-button-outlined" type="button" pButton (click)="isShowDefined = false">{{tranService.translate("global.button.cancel")}}</button>
            </div>
        </div>
    </p-dialog>

    <p-dialog [header]="tranService.translate('datapool.label.otpCode')" [(visible)]="isSubmit" [modal]="true" [style]="{ width: '30vw' }" [draggable]="false" [resizable]="false">
        <div class="flex flex-column gap-2 flex-1">
            <p-inputOtp formControlName="otp" class="mx-auto my-3" [integerOnly]="true" length="6"></p-inputOtp>
            <button
                type="button"
                class="border-none mb-4 cursor-pointer flex flex-row justify-content-center font-semibold"
                style="background-color: transparent;" [disabled]="countdown>0"
                (click)="resetTimer()"
            >{{tranService.translate("datapool.message.resendOtp")}}&nbsp;<div *ngIf="countdown>0"> {{tranService.translate("datapool.message.in")}} {{countdown}} {{tranService.translate("datapool.message.sec")}} </div>
            </button>
            <div class="m-auto">
                <button [disabled]="shareWalletDataForm.invalid" class="m-auto mr-2" pButton>{{tranService.translate("global.button.save")}}</button>
                <button class="m-auto ml-2 p-button-outlined" pButton (click)="isSubmit = false">{{tranService.translate("global.button.cancel")}}</button>
            </div>
        </div>
    </p-dialog>

    <p-dialog [header]="tranService.translate('datapool.label.listShareError')" [(visible)]="isError" [modal]="true" [style]="{ width: '60vw' }" [draggable]="false" [resizable]="false" (onHide)="onHideError()">
        <p-button styleClass="mr-2 p-button-outlined" style="position: absolute;top: 30px;right: 45px;z-index: 5;font-size: 10px"
                  tooltipPosition="right"
                  [pTooltip]="tranService.translate('datapool.label.downloadErrorFile')"
                  icon="pi pi-download" (onClick)="downloadErrorFile()"></p-button>
        <table-vnpt
                [tableId]="'tbSubShareByGroup'"
                [fieldId]="'id'"
                [columns]="columnsError"
                [dataSet]="dataSetError"
                [options]="optionTableError"
                [pageNumber]="pageNumberError"
                [pageSize]="pageSizeError"
                [sort]="sortError"
                [loadData]="pagingDataError.bind(this)"
                [params]="searchInfoError"
                [labelTable]=""
        ></table-vnpt>
    </p-dialog>
</form>

</div>
