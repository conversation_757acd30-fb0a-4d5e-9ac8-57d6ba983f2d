<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{tranService.translate("global.menu.listdevice")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
    <div class="col-5 flex flex-row justify-content-end align-items-center">
        <p-button [label]="tranService.translate('global.button.add')" (click)="navigateToCreateDevice()" styleClass="p-button-info mr-2" *ngIf="checkAuthen([CONSTANTS.PERMISSIONS.DEVICE.CREATE])"></p-button>
        <p-button [label]="tranService.translate('global.button.import')" (click)="importByFile()" styleClass="p-button-success" *ngIf="checkAuthen([CONSTANTS.PERMISSIONS.DEVICE.CREATE])"></p-button>
    </div>
</div>

<form [formGroup]="formSearch" (ngSubmit)="onSubmitSearch()" class="pb-2 pt-3 vnpt-field-set">
    <p-panel [toggleable]="true" [header]="tranService.translate('global.text.filter')">
        <div class="grid search-grid-4">
            <!-- imei -->
            <div class="col-3">
                <span class="p-float-label">
                    <input pInputText
                           class="w-full"
                           pInputText id="imei"
                           [(ngModel)]="searchInfo.imei"
                           formControlName="imei"
                    />
                    <label htmlFor="imei">{{tranService.translate("device.label.imei")}}</label>
                </span>
            </div>
            <div class="col-3">
                <span class="p-float-label">
                    <input class="w-full"
                           pInputText id="msisdn"
                           [(ngModel)]="searchInfo.msisdn"
                           formControlName="msisdn"
                    />
                    <label htmlFor="msisdn">{{tranService.translate("device.label.subcriber")}}</label>
                </span>
            </div>
            <!-- xuat xu -->
            <div class="col-3">
                <span class="p-float-label">
                    <input class="w-full"
                           pInputText id="country"
                           [(ngModel)]="searchInfo.country"
                           formControlName="country"
                    />
                    <label htmlFor="country">{{tranService.translate("device.label.country")}}</label>
                </span>
            </div>
            <!-- chung loai -->
            <div class="col-3">
                <span class="p-float-label">
                    <input class="w-full"
                           pInputText id="deviceType"
                           [(ngModel)]="searchInfo.deviceType"
                           formControlName="deviceType"
                    />
                    <label htmlFor="deviceType">{{tranService.translate("device.label.deviceType")}}</label>
                </span>
            </div>

            <div class="col-3 pb-0">
                <span class="p-float-label">
                    <p-calendar styleClass="w-full"
                            id="contractDateFrom"
                            [(ngModel)]="searchInfo.contractDateFrom"
                            formControlName="contractDateFrom"
                            [showIcon]="true"
                            [showClear]="true"
                            dateFormat="dd/mm/yy"
                            [maxDate]="maxDateFrom"
                            (onSelect)="onChangeDateFrom(searchInfo.contractDateFrom)"
                            (onInput)="onChangeDateFrom(searchInfo.contractDateFrom)"
                    ></p-calendar>
                    <label class="label-calendar" htmlFor="contractDateFrom">{{tranService.translate("device.label.expireFrom")}}</label>
                </span>
            </div>
            <div class="col-3 pb-0">
                <span class="p-float-label">
                    <p-calendar styleClass="w-full"
                            id="contractDateTo"
                            [(ngModel)]="searchInfo.contractDateTo"
                            formControlName="contractDateTo"
                            [showIcon]="true"
                            [showClear]="true"
                            dateFormat="dd/mm/yy"
                            [minDate]="minDateTo"
                            [maxDate]="maxDateTo"
                            (onSelect)="onChangeDateTo(searchInfo.contractDateTo)"
                            (onInput)="onChangeDateTo(searchInfo.contractDateTo)"
                    />
                    <label class="label-calendar" htmlFor="contractDateTo">{{tranService.translate("device.label.expireTo")}}</label>
                </span>
            </div>
            <div class="col-3 pb-0">
                <p-button icon="pi pi-search"
                          styleClass="p-button-rounded p-button-secondary p-button-text button-search"
                          type="submit"
                ></p-button>
            </div>
        </div>
    </p-panel>
</form>
<div class="flex justify-content-center dialog-push-group">
    <p-dialog [header]="tranService.translate('device.label.importByFile')" [(visible)]="isShowDialogImportByFile" [modal]="true" [style]="{ width: '700px' }" [draggable]="false" [resizable]="false" styleClass="dialog-upload-device">
        <div class="w-full field grid">
            <div class="col-10 flex flex-row justify-content-start align-items-center">
                <input-file-vnpt class="w-full upload-device-file" [(fileObject)]="fileObject" [clearFileCallback]="clearFileCallback.bind(this)"
                                 [options]="optionInputFile"
                ></input-file-vnpt>
            </div>
            <div class="col-2 flex flex-row justify-content-end align-items-center">
                <p-button icon="pi pi-download" [pTooltip]="tranService.translate('global.button.downloadTemp')" styleClass="p-button-outlined p-button-secondary" (click)="downloadTemplate()"></p-button>
            </div>
        </div>
        <div class="grid"><div class="col pt-0"><small class="text-red-500" *ngIf="isShowErrorUpload">{{messageErrorUpload}}</small></div></div>
    </p-dialog>
</div>
<div class="flex justify-content-center dialog-vnpt">
    <p-dialog [header]="tranService.translate('sim.text.detailSim')" [(visible)]="isShowPopupDetailSim" [modal]="true" [style]="{ width: '980px' }" [draggable]="false" [resizable]="false">
        <div class="grid grid-1 mt-1 h-auto" style="width: calc(100% + 16px);">
            <div class="col sim-detail pr-0">
                <p-card [header]="tranService.translate('sim.text.simInfo')">
                    <div class="flex flex-row justify-content-between custom-card">
                        <div class="w-6">
                            <div class="grid">
                                <span style="min-width: 150px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.sothuebao")}}</span>
                                <span class="col">{{detailSim.msisdn}}</span>
                            </div>
                            <div class="mt-1 grid">
                                <span style="min-width: 150px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.trangthaisim")}}</span>
                                <span class="w-auto ml-3" [class]="getClassStatus(detailSim.status)">{{getNameStatus(detailSim.status)}}</span>
                            </div>
                            <div class="mt-1 grid">
                                <span style="min-width: 150px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.imsi")}}</span>
                                <span class="col">{{detailSim.imsi}}</span>
                            </div>
                            <div class="mt-1 grid">
                                <span style="min-width: 150px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.imeiDevice")}}</span>
                                <span class="col">{{detailSim.imei}}</span>
                            </div>
                            <div class="mt-1 grid">
                                <span style="min-width: 150px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.maapn")}}</span>
                                <span class="col">{{detailSim.apnId}}</span>
                            </div>
                            <div class="mt-1 grid">
                                <span style="min-width: 150px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.trangthaiketnoi")}}</span>
                                <span *ngIf="detailSim.connectionStatus!==undefined && detailSim.connectionStatus!==null && detailSim.connectionStatus!=='0' " class="ml-3 p-2 text-green-800 bg-green-100 border-round inline-block">ON</span>
                                <span *ngIf="detailSim.connectionStatus==='0'" class="ml-3 p-2 text-50 surface-500 border-round inline-block">OFF</span>
                                <span *ngIf="detailSim.connectionStatus===undefined || detailSim.connectionStatus=== null " class="ml-3 p-2 text-50 surface-500 border-round inline-block">NOT FOUND</span>
                            </div>
                        </div>
                        <div class="w-6">
                            <div class="grid">
                                <span style="min-width: 150px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.startDate")}}</span>
                                <span class="col">{{detailSim.startDate | date:'dd/MM/yyyy'}}</span>
                            </div>
                            <div class="mt-1 grid">
                                <span style="min-width: 150px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.serviceType")}}</span>
                                <span class="w-auto ml-3">{{getServiceType(detailSim.serviceType)}}</span>
                            </div>
                        </div>
                    </div>
                </p-card>
                <p-card [header]="tranService.translate('sim.text.simStatusInfo')" styleClass="mt-3 sim-status">
                    <div class="grid">
                        <div class="col-4 text-center">
                            <p-toggleButton [(ngModel)]="detailStatusSim.statusData" [disabled]="true" onLabel="ON" offLabel="OFF"></p-toggleButton>
                            <div>{{tranService.translate("sim.status.service.data")}}</div>
                        </div>
                        <div class="col-4 text-center">
                            <p-toggleButton [(ngModel)]="detailStatusSim.statusReceiveCall" [disabled]="true" onLabel="ON" offLabel="OFF"></p-toggleButton>
                            <div>{{tranService.translate("sim.status.service.callReceived")}}</div>
                        </div>
                        <div class="col-4 text-center">
                            <p-toggleButton [(ngModel)]="detailStatusSim.statusSendCall" [disabled]="true" onLabel="ON" offLabel="OFF"></p-toggleButton>
                            <div>{{tranService.translate("sim.status.service.callSent")}}</div>
                        </div>
                    </div>
                    <div class="grid">
                        <div class="col-4 text-center">
                            <p-toggleButton [(ngModel)]="detailStatusSim.statusWorldCall" [disabled]="true" onLabel="ON" offLabel="OFF"></p-toggleButton>
                            <div>{{tranService.translate("sim.status.service.callWorld")}}</div>
                        </div>
                        <div class="col-4 text-center">
                            <p-toggleButton [(ngModel)]="detailStatusSim.statusReceiveSms" [disabled]="true" onLabel="ON" offLabel="OFF"></p-toggleButton>
                            <div>{{tranService.translate("sim.status.service.smsReceived")}}</div>
                        </div>
                        <div class="col-4 text-center">
                            <p-toggleButton [(ngModel)]="detailStatusSim.statusSendSms" [disabled]="true" onLabel="ON" offLabel="OFF"></p-toggleButton>
                            <div>{{tranService.translate("sim.status.service.smsSent")}}</div>
                        </div>
                    </div>
                </p-card>
                <!-- goi cuoc -->
                <p-card [header]="tranService.translate('sim.text.ratingPlanInfo')" styleClass="mt-3">
                    <div class="grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.tengoicuoc")}}</span>
                        <span class="col">{{detailSim.ratingPlanName}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.dataUseInMonth")}}</span>
                        <span class="col">{{this.utilService.bytesToMegabytes(detailRatingPlan.dataUseInMonth) | number }} {{detailRatingPlan.unit?detailRatingPlan.unit:"MB"}}</span>
                    </div>
                </p-card>
            </div>
            <div class="col sim-detail pr-0">
                <!-- hop dong -->
                <p-card [header]="tranService.translate('sim.text.contractInfo')">
                    <div class="grid mt-0">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.mahopdong")}}</span>
                        <span class="col">{{detailContract.contractCode}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.ngaylamhopdong")}}</span>
                        <span class="col">{{detailContract.contractDate | date:'dd/MM/yyyy'}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.nguoilamhopdong")}}</span>
                        <span class="col uppercase">{{detailContract.contractorInfo}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.matrungtam")}}</span>
                        <span class="col">{{detailContract.centerCode}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.dienthoailienhe")}}</span>
                        <span class="col">{{detailContract.contactPhone}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.diachilienhe")}}</span>
                        <span class="col">{{detailContract.contactAddress}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.paymentName")}}</span>
                        <span class="col uppercase">{{detailContract.paymentName}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.paymentAddress")}}</span>
                        <span class="col">{{detailContract.paymentAddress}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.routeCode")}}</span>
                        <span class="col">{{detailContract.routeCode}}</span>
                    </div>
                </p-card>
                <!-- customer -->
                <p-card [header]="tranService.translate('sim.text.customerInfo')" styleClass="mt-3">
                    <div class="grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.khachhang")}}</span>
                        <span class="col">{{detailCustomer.name}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.customerCode")}}</span>
                        <span class="col">{{detailCustomer.code}}</span>
                    </div>
                </p-card>
            </div>
        </div>
    </p-dialog>
</div>
<!-- detail device -->
<div class="flex justify-content-center dialog-vnpt">
    <p-dialog [header]="tranService.translate('global.menu.devicedetail')" [(visible)]="isShowPopupDetailDevice" [modal]="true" [style]="{ width: '980px' }" [draggable]="false" [resizable]="false">
        <p-card styleClass="mt-3">
            <form [formGroup]="formDetailDevice" *ngIf="isShowPopupDetailDevice">
                <div class="grid mx-4 my-3">
                    <!--            imei-->
                    <div class="col-3 ">
                        <span class="p-float-label">
                            <input class="w-full"
                                   pInputText id="imei"
                                   [(ngModel)]="deviceInfo.imei"
                                   formControlName="imei"
                                   pattern="^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]{2,255}$" readonly
                            >
                            <label htmlFor="imei">{{ tranService.translate("device.label.imei") }}</label>
                        </span>
                    </div>
                    <div class="col-3">
                        <span class="p-float-label">
                            <input class="w-full"
                                   pInputText id="deviceType"
                                   [(ngModel)]="deviceInfo.deviceType"
                                   formControlName="deviceType"
                                   pattern="^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]{2,255}$" [readonly]="true"
                                   autofocus
                            >
                            <label htmlFor="deviceType">{{ tranService.translate("device.label.deviceType") }}</label>
                        </span>
                    </div>
                    <div class="col-3">
                        <span class="p-float-label">
                                <input class="w-full"
                                   pInputText id="msisdn"
                                   [(ngModel)]="deviceInfo.msisdn"
                                   formControlName="msisdn"
                                   readonly
                                >
                            <label htmlFor="msisdn">{{ tranService.translate("device.label.msisdn") }}</label>
                        </span>
                    </div>
                    <div class="col-3">
                        <span class="p-float-label">
                            <input class="w-full"
                                   pInputText id="country"
                                   [(ngModel)]="deviceInfo.country"
                                   formControlName="country"
                                   pattern="^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]{2,255}$" [readonly]="true"
                                   [disabled]="true">
                            <label htmlFor="country">{{ tranService.translate("device.label.country") }}</label>
                        </span>
                    </div>
                    <div class="col-3 ">
                        <span class="p-float-label" disabled="true">
                            <p-calendar styleClass="w-full"
                                        id="expiredDate"
                                        [(ngModel)]="deviceInfo.expiredDate"
                                        formControlName="expiredDate"
                                        [disabled]="true"
                                        disabledDays="1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31"
                                        [readonlyInput]="true"
                                        [minDate]="minDateTo"
                                        [maxDate]="maxDateTo"
                            ></p-calendar>
                            <label htmlFor="expiredDate">{{ tranService.translate("device.label.expireDate") }}</label>
                        </span>
                    </div>

                    <!--            vị trí-->
                    <div class="col-3">
                        <span class="p-float-label">
                            <input class="w-full"
                                   pInputText id="location"
                                   [(ngModel)]="deviceInfo.location"
                                   formControlName="location"
                                   pattern="^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]{2,255}$" readonly>

                            <label htmlFor="location">{{ tranService.translate("device.label.location") }}</label>
                        </span>
                    </div>

                    <div class="col-3">
                        <label class="flex align-items-center">
                            <p-checkbox [disabled]="true" [readonly]="true" formControlName="iotLink" id="iotLink"
                                        inputId="iotLink" [binary]="true" [ngStyle]="{'margin': '6px'}"></p-checkbox>
                            <label for="iotLink">{{ tranService.translate("device.label.iotLink") }}</label>
                        </label>
                    </div>
                </div>
                <div class="col-offset-2 col-8 devices-map">
                    <iframe [src]="safeUrl" class="w-full" style="border:0;" allowfullscreen="true" loading="lazy" height="500"
                            referrerpolicy="no-referrer-when-downgrade"></iframe>
                </div>

<!--                <div class="flex flex-row justify-content-center align-items-center mt-3">-->
<!--                    <p-button styleClass="p-button-info "-->
<!--                              (click)="goUpdate()">{{ tranService.translate("global.button.update") }}-->
<!--                    </p-button>-->
<!--                </div>-->
            </form>
        </p-card>
    </p-dialog>
</div>
<table-vnpt
    [fieldId]="'msisdn'"
    [(selectItems)]="selectItems"
    [columns]="columns"
    [dataSet]="dataSet"
    [options]="optionTable"
    [loadData]="search.bind(this)"
    [pageNumber]="pageNumber"
    [pageSize]="pageSize"
    [sort]="sort"
    [params]="searchInfo"
    [labelTable]="this.tranService.translate('global.menu.listdevice')"
></table-vnpt>
