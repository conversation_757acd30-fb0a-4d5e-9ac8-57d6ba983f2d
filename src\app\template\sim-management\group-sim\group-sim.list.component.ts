import { ComponentBase } from 'src/app/component.base';
import { Inject, Injector } from '@angular/core';
import { Component } from '@angular/core';
import { MenuItem } from 'primeng/api';
import { ColumnInfo, OptionTable } from '../../common-module/table/table.component';
import { TranslateService } from 'src/app/service/comon/translate.service';
import { MessageCommonService } from 'src/app/service/comon/message-common.service';
import { Router } from '@angular/router';
import { GroupSimService } from 'src/app/service/group-sim/GroupSimService';
import { CONSTANTS } from 'src/app/service/comon/constants';
import {AccountService} from "../../../service/account/AccountService";
import {CustomerService} from "../../../service/customer/CustomerService";
import {ComboLazyControl} from "../../common-module/combobox-lazyload/combobox.lazyload";
import {SimService} from "../../../service/sim/SimService";



@Component({
  selector: 'app-group-sim',
  templateUrl: './group-sim.list.component.html',
//   styleUrls: ['./group-sim.component.scss']
})
export class GroupSimComponent extends ComponentBase {
    buttonAdd: string =this.tranService.translate("groupSim.label.buttonAdd");
    tableLabel=this.tranService.translate("groupSim.breadCrumb.group")
    items: MenuItem[];
    home: MenuItem
    searchInfo: {
        groupKey?:string,
        name?: string,
        customer?: string,
        time?: string,
        scope?: string
    };
    columns: Array<ColumnInfo>;
    columnsDetail: Array<ColumnInfo>;
    dataSet: {
        content: Array<any>,
        total: number
    };
    dataSetDetail: {
        content: Array<any>,
        total: number
    };
    // dataStore: Array<any>;
    selectItems: Array<any>;
    selectItemsDetail: Array<any>;
    optionTable: OptionTable;
    optionTableDetail: OptionTable;
    pageNumber: number;
    pageSize: number;
    pageNumberDetail: number;
    pageSizeDetail: number;
    sortDetail: string;
    sort: string;
    groupScopes: Array<any>;
    userType: number;
    isShowModalDetailGroupSim: boolean = false;
    groupKey: string;
    groupScope: number;
    groupScopeObjects = CONSTANTS.GROUP_SCOPE;
    groupName: string;
    description: string;
    idForEdit:string;
    customer: string;
    customerCode: string;
    contractCode: string;
    provinceCode: string;
    province: string;
    paramSearchSim = {};
    boxSimAddController: ComboLazyControl = new ComboLazyControl();
    idForDelete:string[];
    displayAddSim: boolean = false;
    selectedSimItems:Array<any> = [];
    headerSim: string = this.tranService.translate("groupSim.label.buttonAddSim");
    placeholderSIM= this.tranService.translate("groupSim.placeHolder.addSim");
    buttonSaveSimToGroup = this.tranService.translate("global.button.save");
    allPermissions = CONSTANTS.PERMISSIONS;
    constructor( @Inject(GroupSimService) private groupSimService: GroupSimService,
                 @Inject(CustomerService) private customerService: CustomerService,
                 @Inject(SimService) private simService: SimService,
                 private accountService: AccountService,
                injector: Injector) {super(injector);}

    convertToDDMMYYYY(input: string): string {
        const date = new Date(input);
        const day = date.getUTCDate().toString().padStart(2, '0'); // Thêm số 0 phía trước nếu là số đơn vị
        const month = (date.getUTCMonth() + 1).toString().padStart(2, '0'); // Thêm số 0 phía trước nếu là số đơn vị và +1 vì getMonth() trả về từ 0-11
        const year = date.getUTCFullYear().toString();

        return `${day}/${month}/${year}`;
    }

    isShowGroupScope: boolean;
    itemAddGroups: MenuItem[];
  ngOnInit(){

    let me = this;
        this.userType = this.sessionService.userInfo.type;
        this.isShowGroupScope = [CONSTANTS.USER_TYPE.ADMIN, CONSTANTS.USER_TYPE.PROVINCE].includes(this.userType);
        this.items = [{ label: this.tranService.translate(`global.menu.simmgmt`) }, { label: this.tranService.translate("groupSim.breadCrumb.group") },];

        this.home = { icon: 'pi pi-home', routerLink: '/' };

        this.searchInfo = {};
        this.optionTableDetail = {
            hasClearSelected: false,
            hasShowChoose: true,
            hasShowIndex: true,
            hasShowToggleColumn: false,
            action: [
                {
                    icon: 'pi pi-fw pi-trash',
                    tooltip: this.tranService.translate('global.button.delete'),
                    func: (id: string) => {  let ids:string[] = [id];this.removeSim(ids)
                    },
                },
            ],
        };
        this.pageNumberDetail = 0;
        this.pageSizeDetail = 10;
        this.sortDetail = 'msisdn,asc';

        this.selectItemsDetail = [];
        this.dataSetDetail = {
          content: [],
          total: 0,
        };

      this.columnsDetail = [
          {
              name: this.tranService.translate('groupSim.detail.subNumber'),
              key: 'msisdn',
              size: '20%',
              align: 'left',
              isShow: true,
              isSort: true,
              style: {
                  color: 'var(--mainColorText)',
              },
              funcGetRouting(item) {
                  return ["/sims/detail/"+item.msisdn];
              },
          },
          {
              name: "IMSI",
              key: 'imsi',
              size: '20%',
              align: 'left',
              isShow: true,
              isSort: true,
          },
          {
              name: this.tranService.translate("sim.label.trangthaisim"),
              key: "status",
              size: "20%",
              align: "left",
              isShow: true,
              isSort: true,
              funcGetClassname: (value) => {
                  if(value == 0){
                      return ['p-1' , "border-round", "border-400", "text-color","inline-block"];
                  }else if(value == CONSTANTS.SIM_STATUS.READY){
                      // return ['p-1', "bg-blue-600", "border-round","inline-block"];
                      return ['p-2', "text-green-800", "bg-green-100","border-round","inline-block"];
                  }else if(value == CONSTANTS.SIM_STATUS.ACTIVATED){
                      return ['p-2', 'text-green-800', "bg-green-100","border-round","inline-block"];
                  }else if(value == CONSTANTS.SIM_STATUS.INACTIVED){
                      return ['p-2', 'text-yellow-800', "bg-yellow-100", "border-round","inline-block"];
                  }else if(value == CONSTANTS.SIM_STATUS.DEACTIVATED){
                      return ['p-2', 'text-indigo-600', "bg-indigo-100", "border-round","inline-block"];
                  }else if(value == CONSTANTS.SIM_STATUS.PURGED){
                      return ['p-2', 'text-red-700', "bg-red-100", "border-round","inline-block"];
                  }else if(value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY){
                      return ['p-2', 'text-cyan-800', "bg-cyan-100", "border-round","inline-block"];
                  }else if(value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY){
                      return ['p-2', 'text-teal-800', "bg-teal-100", "border-round","inline-block"];
                  }else if(value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY){
                      return ['p-2', 'text-orange-700', "bg-orange-100", "border-round","inline-block"];
                  }
                  return [];
              },
              funcConvertText: (value)=>{
                  if(value == 0){
                      return me.tranService.translate("sim.status.inventory");
                  }else if(value == CONSTANTS.SIM_STATUS.READY){
                      // return me.tranService.translate("sim.status.ready");
                      return me.tranService.translate("sim.status.activated");
                  }else if(value == CONSTANTS.SIM_STATUS.ACTIVATED){
                      return me.tranService.translate("sim.status.activated");
                  }else if(value == CONSTANTS.SIM_STATUS.DEACTIVATED){
                      return me.tranService.translate("sim.status.deactivated");
                  }else if(value == CONSTANTS.SIM_STATUS.PURGED){
                      return me.tranService.translate("sim.status.purged");
                  }else if(value == CONSTANTS.SIM_STATUS.INACTIVED){
                      return me.tranService.translate("sim.status.inactivated");
                  }else if(value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY){
                      return this.tranService.translate("sim.status.processingChangePlan");
                  }else if(value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY){
                      return this.tranService.translate("sim.status.processingRegisterPlan");
                  }else if(value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY){
                      return this.tranService.translate("sim.status.waitingCancelPlan");
                  }
                  return "";
              },
              style:{
                  color: "white"
              }
          },
          {
              name: this.tranService.translate('groupSim.detail.planName'),
              key: 'ratingPlanName',
              size: '20%',
              align: 'left',
              isShow: true,
              isSort: false,
          },
      ];

        this.groupScopes = [
            {
                value: CONSTANTS.GROUP_SCOPE.GROUP_CUSTOMER,
                name: this.tranService.translate("groupSim.scope.customer")
            }
        ];
        this.itemAddGroups = [
            {
                label: this.tranService.translate("groupSim.scope.customer"),
                command: ()=>{
                    me.create.apply(me, [2]);
                }
            }
        ];
        let groupScopeForAdmin = [
            {
                value: CONSTANTS.GROUP_SCOPE.GROUP_ADMIN,
                name: this.tranService.translate("groupSim.scope.admin")
            },
            {
                value: CONSTANTS.GROUP_SCOPE.GROUP_PROVINCE,
                name: this.tranService.translate("groupSim.scope.province")
            },
            {
                value: CONSTANTS.GROUP_SCOPE.GROUP_CUSTOMER,
                name: this.tranService.translate("groupSim.scope.customer")
            }
        ];

        let groupScopeForProvince = [
            {
                value: CONSTANTS.GROUP_SCOPE.GROUP_PROVINCE,
                name: this.tranService.translate("groupSim.scope.province")
            },
            {
                value: CONSTANTS.GROUP_SCOPE.GROUP_CUSTOMER,
                name: this.tranService.translate("groupSim.scope.customer")
            }
        ]

        let groupScopeForManager = [
            {
                value: CONSTANTS.GROUP_SCOPE.GROUP_CUSTOMER,
                name: this.tranService.translate("groupSim.scope.customer")
            }
        ]
        if(this.userType == CONSTANTS.USER_TYPE.ADMIN){
            this.groupScopes = groupScopeForAdmin;
            this.itemAddGroups = [
                {
                    label: this.tranService.translate("groupSim.scope.admin"),
                    command: ()=>{
                        me.create.apply(me, [0]);
                    },
                },
                {
                    label: this.tranService.translate("groupSim.scope.province"),
                    command: ()=>{
                        me.create.apply(me, [1]);
                    }
                },
                {
                    label: this.tranService.translate("groupSim.scope.customer"),
                    command: ()=>{
                        me.create.apply(me, [2]);
                    }
                }
            ];
        }else if(this.userType == CONSTANTS.USER_TYPE.PROVINCE){
            this.groupScopes = groupScopeForProvince;
            this.itemAddGroups = [
                {
                    label: this.tranService.translate("groupSim.scope.province"),
                    command: ()=>{
                        me.create.apply(me, [1]);
                    }
                },
                {
                    label: this.tranService.translate("groupSim.scope.customer"),
                    command: ()=>{
                        me.create.apply(me, [2]);
                    }
                }
            ];
        } else if (this.userType == CONSTANTS.USER_TYPE.DISTRICT) {
            this.groupScopes = groupScopeForManager;
            this.itemAddGroups = [
                {
                    label: this.tranService.translate("groupSim.scope.customer"),
                    command: ()=>{
                        me.create.apply(me, [2]);
                    }
                }
            ];
        }

        this.columns = [{
            name: this.tranService.translate("groupSim.label.groupKey"),
            key: "groupKey",
            size: "25%",
            align: "left",
            isShow: true,
            isSort: true,
            style:{
                cursor: "pointer",
                color: "var(--mainColorText)",
                'min-width': '300px'
            },
            funcClick(id, item) {
                me.isShowModalDetailGroupSim = true;
                me.selectItemsDetail = [];
                me.idForEdit = id;
                me.searchDetail(0, me.pageSizeDetail,me.sortDetail, null)
                me.groupSimService.getSimGroupById(me.idForEdit,{},{},(response)=>{
                    me.groupKey = response.groupKey
                    me.customer = response.customer;
                    me.groupName = response.name;
                    me.description = response.description;
                    me.groupScope = response.scope;
                    me.customerCode = response.customerCode;
                    me.contractCode = response.contractCode;
                    me.provinceCode = response.provinceCode;
                    if(me.groupScope == CONSTANTS.GROUP_SCOPE.GROUP_CUSTOMER){
                        me.paramSearchSim = {
                            customer: me.customerCode,
                            contractCode: me.contractCode ? me.contractCode : " ",
                        }
                        me.customerService.getByKey("customerCode", me.customerCode,(res)=>{
                            me.customer = `${res[0].customerName} - ${res[0].customerCode}`;
                        })
                    }
                    if(me.groupScope == CONSTANTS.GROUP_SCOPE.GROUP_PROVINCE){
                        me.paramSearchSim = {
                            provinceCode: me.provinceCode
                        }
                        me.accountService.getListProvince((res)=>{
                            (res || []).forEach(el => {
                                if(el.code == response.provinceCode){
                                    me.province = `${el.name} (${el.code})`
                                }
                            })
                        })
                    }
                }, null, ()=>{
                    me.messageCommonService.offload();
                })
            },
        },
        {
            name: this.tranService.translate("groupSim.label.groupName"),
            key: "name",
            size: "25%",
            align: "left",
            isShow: true,
            isSort: true,
            style:{
                'min-width': '300px'
            },
        },
        {
            name: this.tranService.translate("groupSim.label.groupScope"),
            key: "scope",
            size: "25%",
            align: "left",
            isShow: true,
            isSort: true,
            funcConvertText(value){
                if(value == CONSTANTS.GROUP_SCOPE.GROUP_ADMIN){
                    return me.tranService.translate("groupSim.scope.admin");
                }else if(value == CONSTANTS.GROUP_SCOPE.GROUP_PROVINCE){
                    return me.tranService.translate("groupSim.scope.province");
                }else if(value == CONSTANTS.GROUP_SCOPE.GROUP_CUSTOMER){
                    return me.tranService.translate("groupSim.scope.customer");
                }
                return "";
            },
            style:{
                'min-width': '300px'
            },
        },
        {
            name: this.tranService.translate("groupSim.label.time"),
            key: "createdDate",
            size: "200px",
            align: "left",
            isShow: true,
            isSort: true
        }];

        this.optionTable = {
            hasClearSelected:false,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
            action: [
                {
                    icon: "pi pi-fw pi-pencil",
                    tooltip: this.tranService.translate('global.button.edit'),
                    func: (id: string)=>{
                        // console.log(id)
                        this.router.navigate(['/sims/group/update', id]);
                    },
                    funcAppear: (id:string, item)=>{
                        return me.checkAuthen([CONSTANTS.PERMISSIONS.GROUP_SIM.UPDATE]);
                    }
                },
                {
                    icon: "pi pi-fw pi-trash",
                    tooltip: this.tranService.translate("global.button.delete"),
                    func: (id: string)=>{
                        me.messageCommonService.confirm(this.tranService.translate("groupSim.label.confirmDelete"),this.tranService.translate("groupSim.label.deleteTextGroup"),{ok: ()=> {
                            this.onDelete(id);
                        }, cancel: ()=>{}});
                    },
                    funcAppear: (id:string, item)=>{
                        return me.checkAuthen([CONSTANTS.PERMISSIONS.GROUP_SIM.DELETE]);
                    }
                }
            ]
        }
        this.pageNumber = 0;
        this.pageSize= 10;
        this.sort = "createdDate,desc"

        this.selectItems = [];
        this.dataSet ={
            content: [],
            total: 0
        }
        me.messageCommonService.onload();
        this.groupSimService.searchSimGroup({},{...this.searchInfo,sort:this.sort},(response)=>{
            this.dataSet.content=response.content.map((item:any)=>{
                item.createdDate=this.convertToDDMMYYYY(item.createdDate)
                return item;
            });
            this.dataSet.total = response.totalElements;
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }

    onSearch(){
        // console.log(this.searchInfo);
        this.search(0,this.pageSize, this.sort, this.searchInfo)
    }

    onDelete(id:string){
        let me = this;
        // console.log(id)
        this.groupSimService.deleteSimGroup(id,{},{},(response)=>{
            me.messageCommonService.success(this.tranService.translate("global.message.deleteSuccess"));
            // me.selectItems = me.selectItems.filter(el => el.id != id);
            this.search(0, this.pageSize,this.sort, this.searchInfo)
        },(error)=>{console.error(error)},()=>{})
    }

    search(page, limit, sort,params){
        let me = this;
        this.pageNumber = page;
        this.pageSize = limit;
        this.sort = sort;
        if(this.searchInfo.scope==null)
            this.searchInfo.scope=""
        let dataParam = {
            ...params,
            page,
            size:limit,
            sort
        }
        me.messageCommonService.onload();
        this.groupSimService.searchSimGroup({},dataParam,(response)=>{
            this.dataSet.content=response.content.map((item:any)=>{
                item.createdDate=this.convertToDDMMYYYY(item.createdDate)
                return item;
            });
            this.dataSet.total = response.totalElements;
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }

    searchDetail(page, limit, sort, params){
        let me = this;
        this.pageNumberDetail = page;
        this.pageSizeDetail = limit;
        this.sortDetail = sort;
        let dataParam = {
            page,
            size:limit,
            sort
        }
        this.groupSimService.getListDetailGroupSim(this.idForEdit,{},dataParam,(response)=>{
            me.dataSetDetail.content=response.content;
            me.dataSetDetail.total = response.totalElements;
        })
    }

    create(type){
        this.router.navigate(['/sims/group/create'],{queryParams: {type}})
    }

    showOverLayAddSim(){
        this.boxSimAddController.reload();
        this.displayAddSim=true;
        this.selectedSimItems = []
    }

    showOverLayEdit(){
        this.router.navigate(["/sims/group/update", this.idForEdit]);
    }

    removeMany(){
        // console.log(this.selectItems)
        if(this.selectItemsDetail.length==0)
            return null
        this.idForDelete=[];
        this.selectItemsDetail.map((item)=>{
            this.idForDelete.push(item.msisdn);
        })
        this.removeSim(this.idForDelete)
    }

    removeSim(ids:string[]){
        this.messageCommonService.confirm(this.tranService.translate("groupSim.label.confirmDelete"),this.tranService.translate("groupSim.label.deleteTextSim"), {
            ok: () => {
                this.simService.removeSIMFromGroup(ids,parseInt(this.idForEdit),(response)=>{
                    // console.log(response);
                    this.selectItemsDetail=[];
                    this.messageCommonService.success(this.tranService.translate("global.message.deleteSuccess"))
                    this.searchDetail(0, this.pageSizeDetail,this.sortDetail, null)
                })
            },
            cancel: () => {
            },
        });
    }

    deleteGroupSim(){
        let me = this;
        this.messageCommonService.confirm(this.tranService.translate("groupSim.label.deleteTextGroup"), this.tranService.translate("groupSim.label.confirmDelete"),{
            ok: ()=>{
                me.groupSimService.deleteSimGroup(me.idForEdit, {}, {}, (response)=>{
                    me.messageCommonService.success(me.tranService.translate("global.message.success"));
                    me.router.navigate(['/sims/group'])
                })
            }
        })
    }

    loadSimNotInGroup(data, callback){
        this.simService.searchNotInGroup(data, callback);
    }

    handleModelClose(){
        this.displayAddSim=false;
    }

    handleSavetoGroup(){
        if (this.selectedSimItems.length > 0) {
            this.simService.pushSimToGroup(this.selectedSimItems,{id:parseInt(this.idForEdit)},
                (response)=>{
                    this.messageCommonService.success(this.tranService.translate("global.message.addGroupSuccess"))
                    this.searchDetail(0, this.pageSize,this.sort, null)

                })
            this.displayAddSim=false;
        }
    }

    protected readonly CONSTANTS = CONSTANTS

}
