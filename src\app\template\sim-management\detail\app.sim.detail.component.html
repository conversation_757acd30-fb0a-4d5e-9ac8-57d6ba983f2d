
<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{tranService.translate("global.menu.listsim")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
<!--        <div class="col-5 flex flex-row justify-content-end align-items-center">-->
<!--            &lt;!&ndash; <p-splitButton [disabled]="true" styleClass="mr-2 p-button-info" [label]="tranService.translate('sim.label.trangthaisim')" icon="" [model]="itemActionStatuses"></p-splitButton>-->
<!--            <p-splitButton styleClass="p-button-success" [label]="tranService.translate('sim.label.goicuoc')" icon="" [model]="itemExecuteRatePlans"></p-splitButton> &ndash;&gt;-->
<!--        </div>-->
</div>

<div class="grid grid-1 mt-1 h-auto" style="width: calc(100% + 16px);">
    <div class="col sim-detail pr-0">
        <p-card [header]="tranService.translate('sim.text.simInfo')">
            <div class="flex flex-row justify-content-between custom-card">
                <div class="w-6">
                    <div class="grid">
                        <span style="min-width: 150px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.sothuebao")}}</span>
                        <span class="col">{{detailSim.msisdn}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 150px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.trangthaisim")}}</span>
                        <span class="w-auto ml-3" [class]="getClassStatus(detailSim.status)">{{getNameStatus(detailSim.status)}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 150px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.imsi")}}</span>
                        <span class="col">{{detailSim.imsi}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 150px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.imeiDevice")}}</span>
                        <span class="col">{{detailSim.imei}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 150px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.maapn")}}</span>
                        <span class="col">{{detailSim.apnId}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 150px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.trangthaiketnoi")}}</span>
                        <span *ngIf="detailSim.connectionStatus!==undefined && detailSim.connectionStatus!==null && detailSim.connectionStatus!=='0' " class="ml-3 p-2 text-green-800 bg-green-100 border-round inline-block">ON</span>
                        <span *ngIf="detailSim.connectionStatus==='0'" class="ml-3 p-2 text-50 surface-500 border-round inline-block">OFF</span>
                        <span *ngIf="detailSim.connectionStatus===undefined || detailSim.connectionStatus=== null " class="ml-3 p-2 text-50 surface-500 border-round inline-block">NOT FOUND</span>
                    </div>
                </div>
                <div class="w-6">
                    <div class="grid">
                        <span style="min-width: 150px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.startDate")}}</span>
                        <span class="col">{{detailSim.startDate | date:'dd/MM/yyyy'}}</span>
                    </div>
                    <div class="mt-1 grid">
                        <span style="min-width: 150px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.serviceType")}}</span>
                        <span class="w-auto ml-3">{{getServiceType(detailSim.serviceType)}}</span>
                    </div>
                </div>
            </div>
        </p-card>
        <p-card [header]="tranService.translate('sim.text.simStatusInfo')" styleClass="mt-3 sim-status">
            <div class="grid">
                <div class="col-4 text-center">
                    <p-toggleButton [(ngModel)]="detailStatusSim.statusData" [disabled]="true" onLabel="ON" offLabel="OFF"></p-toggleButton>
                    <div>{{tranService.translate("sim.status.service.data")}}</div>
                </div>
                <div class="col-4 text-center">
                    <p-toggleButton [(ngModel)]="detailStatusSim.statusReceiveCall" [disabled]="true" onLabel="ON" offLabel="OFF"></p-toggleButton>
                    <div>{{tranService.translate("sim.status.service.callReceived")}}</div>
                </div>
                <div class="col-4 text-center">
                    <p-toggleButton [(ngModel)]="detailStatusSim.statusSendCall" [disabled]="true" onLabel="ON" offLabel="OFF"></p-toggleButton>
                    <div>{{tranService.translate("sim.status.service.callSent")}}</div>
                </div>
            </div>
            <div class="grid">
                <div class="col-4 text-center">
                    <p-toggleButton [(ngModel)]="detailStatusSim.statusWorldCall" [disabled]="true" onLabel="ON" offLabel="OFF"></p-toggleButton>
                    <div>{{tranService.translate("sim.status.service.callWorld")}}</div>
                </div>
                <div class="col-4 text-center">
                    <p-toggleButton [(ngModel)]="detailStatusSim.statusReceiveSms" [disabled]="true" onLabel="ON" offLabel="OFF"></p-toggleButton>
                    <div>{{tranService.translate("sim.status.service.smsReceived")}}</div>
                </div>
                <div class="col-4 text-center">
                    <p-toggleButton [(ngModel)]="detailStatusSim.statusSendSms" [disabled]="true" onLabel="ON" offLabel="OFF"></p-toggleButton>
                    <div>{{tranService.translate("sim.status.service.smsSent")}}</div>
                </div>
            </div>
        </p-card>
        <!-- goi cuoc -->
        <p-card [header]="tranService.translate('sim.text.ratingPlanInfo')" styleClass="mt-3">
            <div class="grid">
                <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.tengoicuoc")}}</span>
                <span class="col">{{detailSim.ratingPlanName}}</span>
            </div>
            <!-- <div class="mt-1 grid">
                <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.dataUseMax")}}</span>
                <span class="col">{{detailRatingPlan.limitDataUsage | number}}&nbsp;{{detailRatingPlan.unit?detailRatingPlan.unit:"KB"}}</span>
            </div>
            <div class="mt-1 grid">
                <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.dataUse")}}</span>
                <span class="col">{{detailRatingPlan.dataUsage | number}}&nbsp;{{detailRatingPlan.unit?detailRatingPlan.unit:"KB"}}</span>
            </div> -->
            <div class="mt-1 grid">
                <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.dataUseInMonth")}}</span>
                <span class="col">{{this.utilService.bytesToMegabytes(detailRatingPlan.dataUseInMonth) | number }} {{detailRatingPlan.unit?detailRatingPlan.unit:"MB"}}</span>
            </div>
            <!-- <div class="mt-1 grid ">
                <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.overData")}}</span>
                <span class="col" *ngIf="detailRatingPlan.overData > 0">{{detailRatingPlan.overData | number}}&nbsp;{{detailRatingPlan.unit?detailRatingPlan.unit:"KB"}}</span>
            </div>
            <div class="mt-1 grid">
                <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.dataUseOnRatingPlan")}}</span>
                <span class="col">{{detailRatingPlan.dataUseOnRatingPlan | number}}&nbsp;{{detailRatingPlan.unit?detailRatingPlan.unit:"MB"}}</span>
            </div> -->
            <!-- <div class="mt-1 grid">
                <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.dataRemainOnRatingPlan")}}</span>
                <span class="col">{{detailRatingPlan.dataRemainOnRatingPlan | number}}&nbsp;{{detailRatingPlan.unit?detailRatingPlan.unit:"MB"}}</span>
            </div>
            <div class="mt-1 grid">
                <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.smsIntra")}}</span>
                <span class="col">{{detailRatingPlan.smsIntra | number}}&nbsp;{{tranService.translate("sim.label.smsUnit")}}</span>
            </div>
            <div class="mt-1 grid">
                <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.smsInter")}}</span>
                <span class="col">{{detailRatingPlan.smsInter | number}}&nbsp;{{tranService.translate("sim.label.smsUnit")}}</span>
            </div>
            <div class="grid mt-0">
                <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.chargesIncurred")}}</span>
                <span class="col">{{detailRatingPlan.chargesIncurred}}</span>
            </div> -->
        </p-card>
    </div>
    <div class="col sim-detail pr-0">
        <!-- hop dong -->
        <p-card [header]="tranService.translate('sim.text.contractInfo')">
            <div class="grid mt-0">
                <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.mahopdong")}}</span>
                <span class="col">{{detailContract.contractCode}}</span>
            </div>
            <div class="mt-1 grid">
                <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.ngaylamhopdong")}}</span>
                <span class="col">{{detailContract.contractDate | date:'dd/MM/yyyy'}}</span>
            </div>
            <div class="mt-1 grid">
                <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.nguoilamhopdong")}}</span>
                <span class="col uppercase">{{detailContract.contractorInfo}}</span>
            </div>
            <div class="mt-1 grid">
                <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.matrungtam")}}</span>
                <span class="col">{{detailContract.centerCode}}</span>
            </div>
            <div class="mt-1 grid">
                <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.dienthoailienhe")}}</span>
                <span class="col">{{detailContract.contactPhone}}</span>
            </div>
            <div class="mt-1 grid">
                <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.diachilienhe")}}</span>
                <span class="col">{{detailContract.contactAddress}}</span>
            </div>
            <div class="mt-1 grid">
                <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.paymentName")}}</span>
                <span class="col uppercase">{{detailContract.paymentName}}</span>
            </div>
            <div class="mt-1 grid">
                <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.paymentAddress")}}</span>
                <span class="col">{{detailContract.paymentAddress}}</span>
            </div>
            <div class="mt-1 grid">
                <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.routeCode")}}</span>
                <span class="col">{{detailContract.routeCode}}</span>
            </div>
        </p-card>
       <!-- customer -->
       <p-card [header]="tranService.translate('sim.text.customerInfo')" styleClass="mt-3">
            <div class="grid">
                <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.khachhang")}}</span>
                <span class="col">{{detailCustomer.name}}</span>
            </div>
            <div class="mt-1 grid">
                <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.customerCode")}}</span>
                <span class="col">{{detailCustomer.code}}</span>
            </div>
        </p-card>
    </div>
</div>

   <!-- apn sim -->
        <!-- <p-card [header]="tranService.translate('sim.text.apnInfo')" styleClass="mt-3">
            <div class="grid">
                <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.maapn")}}</span>
                <span class="col">{{detailAPN.code}}</span>
            </div>
            <div class="mt-1 grid">
                <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.typeConnection")}}</span>
                <span class="col">{{detailAPN.type}}</span>
            </div>
            <div class="mt-1 grid">
                <span class="col">
                    <span>
                        <p-radioButton [disabled]="true" name="typeIp" [value]="0" [(ngModel)]="detailAPN.ip" inputId="typeIp1"></p-radioButton>
                        &nbsp;
                        <span>{{tranService.translate("sim.label.staticIp")}}</span>
                    </span>
                </span>
                <span class="col">
                    <span>
                        <p-radioButton [disabled]="true" name="typeIp" [value]="1" [(ngModel)]="detailAPN.ip" inputId="typeIp2"></p-radioButton>
                        &nbsp;
                        <span>{{tranService.translate("sim.label.dynamicIp")}}</span>
                    </span>
                </span>
            </div>
            <div class="mt-1 grid">
                <span style="min-width: 200px;max-width: 200px;" class="inline-block col-fixed">{{tranService.translate("sim.label.rangeIp")}}</span>
                <span class="col">{{detailAPN.rangeIp}}</span>
            </div>
        </p-card> -->

