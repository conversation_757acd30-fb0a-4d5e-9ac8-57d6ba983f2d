<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{this.tranService.translate("global.menu.walletConfig")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
</div>

<form action="" [formGroup]="walletConfigForm" (submit)="submitForm()">
    <div class="mt-3">
        <p-card>
            <div class="text-lg font-bold mb-2">{{tranService.translate("datapool.label.sendType")}}</div>
            <div class="flex flex-row justify-content-around">
                <div class="flex align-items-center">
                    <p-checkbox label="Email" name="sendType" value="Email" formControlName="sendTypeEmail" [(ngModel)]="sendType"></p-checkbox>
                </div>
                <div class="flex align-items-center">
                    <p-checkbox disabled="true" label="Sms" name="sendType" value="SMS" formControlName="sendTypeSMS" [(ngModel)]="sendType"></p-checkbox>
                </div>
            </div>
        </p-card>
    </div>

    <div class="mt-3">
        <p-card>
            <div class="text-lg font-bold mb-2">{{tranService.translate("datapool.label.appliedSubscription")}}</div>
            <div class="field px-2 flex flex-row flex-nowrap">
                <label htmlFor="code"  style="min-width: 140px;" class="col-12 mb-2 md:col-2 md:mb-0">{{tranService.translate("datapool.label.packageName")}}</label>
                <input class="flex-1" pInputText id="code" type="text" formControlName="subscription" [placeholder]="tranService.translate('')"/>
            </div>
            <div>Thông tin gói cước</div>
            <p-table [value]="subscriptionDetail" [tableStyle]="{ 'min-width': '50rem' }">
                <ng-template pTemplate="header">
                    <tr>
                        <th>{{tranService.translate('global.text.stt')}}</th>
                        <th>{{tranService.translate("datapool.label.packageName")}}</th>
                        <th>{{tranService.translate('datapool.label.cycle')}}</th>
                        <th>{{tranService.translate('datapool.label.trafficType')}}</th>
                        <th></th>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-list let-index="rowIndex">
                    <tr>
                        <td>{{ index+1 }}</td>
                        <td>{{ list.packageName }}</td>
                        <td>{{ list.cycle }}</td>
                        <td>{{ list.trafficType }}</td>
                    </tr>
                </ng-template>
            </p-table>
        </p-card>
    </div>

    <div class="mt-3">
        <p-card>
            <div class="text-lg font-bold mb-2">{{tranService.translate("datapool.label.sendNoticeExpired")}}</div>
            <div class="px-2 flex flex-row flex-nowrap align-items-center mt-4">
                <label class="w-4 mb-2">{{tranService.translate("datapool.label.sendNoticeExpiredBefore")}}</label>
                <input pInputText id="code" type="number" formControlName="subscription" [placeholder]="tranService.translate('')"/>
                <label  style="min-width: 140px;" class="ml-2">{{tranService.translate("datapool.label.day")}}</label>
            </div>

            <div class="px-2 flex flex-row flex-nowrap align-items-center mt-4">
                <label class="w-4 mb-2">{{tranService.translate("datapool.label.noticeFrequency")}}</label>
                <input pInputText id="code" type="number" formControlName="noticeFrequencyDay" [placeholder]="tranService.translate('')"/>
                <label  style="min-width: 140px;" class="ml-2">{{tranService.translate("datapool.label.day")}}</label>
                <input disabled="true" pInputText id="code" type="number" formControlName="noticeFrequencyNumber" [placeholder]="tranService.translate('')"/>
                <label  style="min-width: 140px;" class="ml-2">{{tranService.translate("datapool.label.time")}}</label>
            </div>

            <div class="flex flex-row justify-content-center gap-3 p-2 mt-4">
                <a routerLink="/"><button pButton [label]="tranService.translate('global.button.cancel')" class="p-button-secondary p-button-outlined" type="button"></button></a>
                <button pButton [label]="tranService.translate('global.button.save')" class="p-button-info" type="submit"></button>
            </div>
        </p-card>
    </div>
</form>
