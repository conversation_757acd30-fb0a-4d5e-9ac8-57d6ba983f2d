import {ChangeDetectorRef, Component, Inject, Injector, OnInit, ViewChild} from "@angular/core";
import {MenuItem} from "primeng/api";
import {TicketService} from "src/app/service/ticket/TicketService";
import {ColumnInfo, OptionTable} from "../../../common-module/table/table.component";
import {CONSTANTS} from "src/app/service/comon/constants";
import {ComponentBase} from "src/app/component.base";
import {AccountService} from "../../../../service/account/AccountService";
import {FormBuilder} from "@angular/forms";
import {InputFileVnptComponent, OptionInputFile} from "../../../common-module/input-file/input.file.component";
import * as XLSX from 'xlsx';
import {elementAt} from "rxjs";
import {SimTicketService} from "../../../../service/ticket/SimTicketService";
import {da} from "suneditor/src/lang";

@Component({
    selector: "list-active-sim-ticket",
    templateUrl: './app.list.active-sim.component.html'
})
export class ListActiveSimTicketComponent extends ComponentBase implements OnInit {
    @ViewChild(InputFileVnptComponent) inputFileComponent: InputFileVnptComponent;

    items: MenuItem[];
    home: MenuItem
    searchInfo: {
        provinceCode: string | null,
        email: string | null,
        contactPhone: string | null,
        contactEmail: string | null,
        type: number | null,
        status: number | null,
        dateFrom: Date | null,
        dateTo: Date | null,
        contactName: string | null,
    };
    maxDateFrom: Date | number | string | null = new Date();
    minDateTo: Date | number | string | null = null;
    maxDateTo: Date | number | string | null = new Date();
    columns: Array<ColumnInfo>;
    listImsis = [];
    listImsisSelected = []
    dataSet: {
        content: Array<any>,
        total: number
    };
    selectItems: Array<any>;
    optionTable: OptionTable;
    pageNumber: number;
    pageSize: number;
    sort: string;
    formSearchTicket: any;
    listProvince: Array<any>;
    listTicketType: Array<any>;
    listTicketStatus: Array<any>;
    mapTicketStatus: any;
    listEmail: Array<any>;
    isShowCreateRequest: boolean;
    isShowDownload: boolean;
    isShowUpload: boolean
    formActiveSim: any;
    ticket: {
        id: number
        contactName: string | null,
        contactEmail: string | null,
        contactPhone: string | null,
        content: string | null,
        note: string | null,
        cause: string | null,
        type: number | null, // 0: thay thế sim, 1: test sim
        status: number | null,
        statusOld?: number | null,
        assigneeId: number | null,
        provinceCode: string | null,
    };
    typeRequest: string
    userInfo: any
    userType: any
    isValidActiveSim: boolean
    fileObject: any;
    fileName: string | null;
    errorRecords: any[] = [];
    optionInputFile: OptionInputFile;

    constructor(
        @Inject(TicketService) private ticketService: TicketService,
        @Inject(AccountService) private accountService: AccountService,
        @Inject(SimTicketService) private simTicketService: SimTicketService,
        private cdr: ChangeDetectorRef,
        private formBuilder: FormBuilder,
        private injector: Injector) {
        super(injector);
    }

    ngOnInit() {
        let me = this;
        this.userInfo = this.sessionService.userInfo;
        this.isShowCreateRequest = false;
        this.isShowDownload = false;
        this.isShowUpload = false;
        this.typeRequest = 'create'
        this.userType = CONSTANTS.USER_TYPE;
        this.isValidActiveSim = true;
        this.ticket = {
            id: null,
            contactName: null,
            contactEmail: null,
            contactPhone: null,
            content: null,
            note: null,
            cause: null,
            type: CONSTANTS.REQUEST_TYPE.ACTIVE_SIM, // 0: thay thế sim, 1: test sim
            status: null,
            statusOld: null,
            assigneeId: null,
            provinceCode: null,
        };
        this.fileName = "";
        this.listTicketType = [
            {
                label: this.tranService.translate('ticket.type.activeSim'),
                value: 1
            }
        ]
        this.mapTicketStatus = {
            0: [{
                label: me.tranService.translate('ticket.status.received'),
                value: 1
            }],
            1: [
                {
                    label: me.tranService.translate('ticket.status.inProgress'),
                    value: 2
                },
                {
                    label: me.tranService.translate('ticket.status.reject'),
                    value: 3
                }
            ],
            2: [
                {
                    label: me.tranService.translate('ticket.status.done'),
                    value: 4
                }
            ]
        }
        this.listTicketStatus = [
            {
                label: me.tranService.translate('ticket.status.new'),
                value: 0
            },
            {
                label: me.tranService.translate('ticket.status.received'),
                value: 1
            },
            {
                label: me.tranService.translate('ticket.status.inProgress'),
                value: 2
            },
            {
                label: me.tranService.translate('ticket.status.reject'),
                value: 3
            },
            {
                label: me.tranService.translate('ticket.status.done'),
                value: 4
            }
        ]
        this.searchInfo = {
            provinceCode: null,
            email: null,
            contactPhone: null,
            contactEmail: null,
            type: CONSTANTS.REQUEST_TYPE.ACTIVE_SIM,
            status: null,
            dateFrom: null,
            dateTo: null,
            contactName: null
        }
        this.columns = [
            {
                name: this.tranService.translate("ticket.label.province"),
                key: "provinceName",
                size: "150px",
                align: "left",
                isShow: this.userInfo.type == CONSTANTS.USER_TYPE.ADMIN,
                isSort: true
            },
            {
                name: this.tranService.translate("ticket.label.customerName"),
                key: "contactName",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: true,
                isShowTooltip: true,
                style: {
                    display: 'inline-block',
                    maxWidth: '350px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                }
            }, {
                name: this.tranService.translate("ticket.label.email"),
                key: "contactEmail",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: true,
                isShowTooltip: true,
                style: {
                    display: 'inline-block',
                    maxWidth: '350px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                }
            }, {
                name: this.tranService.translate("ticket.label.phone"),
                key: "contactPhone",
                size: "fit-content",
                align: "left",
                isShow: true,
                isSort: true
            }, {
                name: this.tranService.translate("ticket.label.content"),
                key: "content",
                size: "fit-content",
                align: "left",
                isShow: true,
                isSort: true,
                isShowTooltip: true,
                style: {
                    display: 'inline-block',
                    maxWidth: '350px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                }
            },
            {
                name: this.tranService.translate("ticket.label.createdDate"),
                key: "createdDate",
                size: "fit-content",
                align: "left",
                isShow: true,
                isSort: true,
                funcConvertText(value) {
                    if (value == null) return null;
                    return me.utilService.convertDateToString(new Date(value))
                },
            },
            // {
            //     name: this.tranService.translate("ticket.label.updatedDate"),
            //     key: "updatedDate",
            //     size: "fit-content",
            //     align: "left",
            //     isShow: true,
            //     isSort: true,
            //     funcConvertText(value) {
            //         if (value == null) return null;
            //         return me.utilService.convertDateToString(new Date(value))
            //     },
            // },
            // {
            //     name: this.tranService.translate("ticket.label.updateBy"),
            //     key: "updatedByName",
            //     size: "fit-content",
            //     align: "left",
            //     isShow: true,
            //     isSort: true
            // },
            // {
            //     name: this.tranService.translate("ticket.label.status"),
            //     key: "status",
            //     size: "fit-content",
            //     align: "left",
            //     isShow: true,
            //     isSort: true,
            //     funcGetClassname: (value) => {
            //         if (value == CONSTANTS.REQUEST_STATUS.NEW) {
            //             return ['p-2', 'text-white', "bg-cyan-300", "border-round", "inline-block"];
            //         } else if (value == CONSTANTS.REQUEST_STATUS.RECEIVED) {
            //             return ['p-2', 'text-white', "bg-bluegray-500", "border-round", "inline-block"];
            //         } else if (value == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {
            //             return ['p-2', 'text-white', "bg-orange-400", "border-round", "inline-block"];
            //         } else if (value == CONSTANTS.REQUEST_STATUS.REJECT) {
            //             return ['p-2', 'text-white', "bg-red-500", "border-round", "inline-block"];
            //         } else if (value == CONSTANTS.REQUEST_STATUS.DONE) {
            //             return ['p-2', 'text-white', "bg-green-500", "border-round", "inline-block"];
            //         }
            //         return '';
            //     },
            //     funcConvertText: function (value) {
            //         if (value == CONSTANTS.REQUEST_STATUS.NEW) {
            //             return me.tranService.translate("ticket.status.new");
            //         } else if (value == CONSTANTS.REQUEST_STATUS.RECEIVED) {
            //             return me.tranService.translate("ticket.status.received");
            //         } else if (value == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {
            //             return me.tranService.translate("ticket.status.inProgress");
            //         } else if (value == CONSTANTS.REQUEST_STATUS.REJECT) {
            //             return me.tranService.translate("ticket.status.reject");
            //         } else if (value == CONSTANTS.REQUEST_STATUS.DONE) {
            //             return me.tranService.translate("ticket.status.done");
            //         }
            //         return "";
            //     }
            // }
        ];

        this.optionTable = {
            hasClearSelected: false,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
            action: [
                {
                    icon: "pi pi-info-circle",
                    tooltip: this.tranService.translate("global.button.view"),
                    func: function (id, item) {
                        me.handleRequest(id, item, 'view')
                    },
                },]
        }
        this.pageNumber = 0;
        this.pageSize = 10;
        this.sort = "createdDate,desc"
        this.dataSet = {
            content: [],
            total: 0
        }
        this.formSearchTicket = this.formBuilder.group(this.searchInfo);
        this.formActiveSim = this.formBuilder.group(this.ticket);
        this.optionInputFile = {
            type: ['xls', 'xlsx'],
            messageErrorType: this.tranService.translate("global.message.wrongFileExcel"),
            maxSize: 100,
            unit: "MB",
            required: false,
            isShowButtonUpload: true,
            actionUpload: this.uploadFile.bind(this),
            disabled: false
        }
        this.getListProvince();
        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
    }

    search(page, limit, sort, params) {
        let me = this;
        this.pageNumber = page;
        this.pageSize = limit;
        this.sort = sort;
        let dataParams = {
            page,
            size: limit,
            sort
        }
        Object.keys(this.searchInfo).forEach(key => {
            if (this.searchInfo[key] != null) {
                if (key == "dateFrom") {
                    dataParams["dateFrom"] = this.searchInfo.dateFrom.getTime();
                } else if (key == "dateTo") {
                    dataParams["dateTo"] = this.searchInfo.dateTo.getTime();
                } else {
                    dataParams[key] = this.searchInfo[key];
                }
            }
        })
        this.dataSet = {
            content: [],
            total: 0
        }
        // me.messageCommonService.onload();
        this.ticketService.searchTicket(dataParams, (response) => {
            me.dataSet = {
                content: response.content,
                total: response.totalElements
            }
        }, null, () => {
            me.messageCommonService.offload();
        })
    }

    resetTicket() {
        this.ticket = {
            id: null,
            contactName: null,
            contactEmail: null,
            contactPhone: null,
            content: null,
            note: null,
            cause: null,
            type: CONSTANTS.REQUEST_TYPE.ACTIVE_SIM, // 0: thay thế sim, 1: test sim
            status: null,
            statusOld: null,
            assigneeId: null,
            provinceCode: null,
        };
        this.listImsisSelected = [];
        this.errorRecords = [];
        this.isShowDownload = false;
    }

    onSubmitSearch() {
        this.pageNumber = 0;
        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
    }

    getListProvince() {
        this.accountService.getListProvince((response) => {
            this.listProvince = response.map(el => {
                return {
                    ...el,
                    display: `${el.code} - ${el.name}`
                }
            })
        })
    }

    // tạo sửa yêu cầu
    createRequest() {
        let me = this;
        console.log("create")
        me.messageCommonService.onload()
        let bodySend = {
            contactName: this.ticket.contactName,
            contactEmail: this.ticket.contactEmail,
            contactPhone: this.ticket.contactPhone,
            content: this.ticket.content,
            note: this.ticket.note,
            type: this.ticket.type,
        }
        this.ticketService.createTicket(bodySend, (resp) => {
            // console.log(resp)
            let createSimTicketBody = {
                ticketId: resp.id,
                userCustomerId: resp.createdBy,
                userHandleId: resp.assigneeId,
                imsis: me.listImsisSelected,
            }
            me.simTicketService.create(createSimTicketBody, (res) => {
                console.log(res)
                me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"));
                me.isShowCreateRequest = false
                me.messageCommonService.offload();
                me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo)
            })
            // get mail admin tinh
            // me.ticketService.getListAssignee({email : '', provinceCode : this.userInfo.provinceCode, page : 0, size: 99999999}, (respAssignee)=>{
            //     let listProvinceConfig = respAssignee.content;
            //     let array = []
            //     for (let user of listProvinceConfig) {
            //         array.push({
            //             userId: user.id,
            //             ticketId: resp.id
            //         })
            //     }
            //     me.ticketService.sendMailNotify(array);
            // })
            me.ticketService.getDetailTicketConfig(me.userInfo.provinceCode, (resp1) => {
                let array = []
                for (let info of resp1.emailInfos) {
                    array.push({
                        userId: info.userId,
                        ticketId: resp.id
                    })
                }
                if (resp?.assigneeId) {
                    array.push({
                        userId: resp.assigneeId,
                        ticketId: resp.id
                    })
                }
                me.ticketService.sendMailNotify(array);
            })
        }, null, () => {
            me.messageCommonService.offload()
        })
    }

    showModalCreate() {
        this.isShowCreateRequest = true
        this.typeRequest = 'create'
        this.resetTicket()
        // auto fill thong tin khi tao
        if (this.userInfo.type === CONSTANTS.USER_TYPE.CUSTOMER) {
            this.ticket.contactName = this.userInfo.fullName;
            this.ticket.contactPhone = this.userInfo.phone;
            this.ticket.contactEmail = this.userInfo.email;
        }
        this.formActiveSim = this.formBuilder.group(this.ticket)
    }

    handleRequest(id, item, typeRequest: string) {
        if (typeRequest == 'view') {
            this.typeRequest = typeRequest
            this.isShowCreateRequest = true;
        }
        let me = this
        this.resetTicket()
        this.formActiveSim.reset()
        this.ticketService.getDetailTicket(item.id, (resp) => {
            this.ticket = {
                id: resp.id,
                contactName: resp.contactName,
                contactEmail: resp.contactEmail,
                contactPhone: resp.contactPhone,
                content: resp.content,
                note: resp.note,
                cause: resp.cause,
                type: resp.type, // 0: thay thế sim, 1: test sim, 2: order sim, 3: active sim
                status: null,
                statusOld: resp.status,
                assigneeId: resp.assigneeId,
                provinceCode: resp.provinceCode,
            }
            this.simTicketService.search({ticketId: this.ticket.id, size: 1000}, (res) => {
                    let imsis: number[] = [];
                    res.content.forEach(item => {
                        imsis.push(item.imsi);
                    })
                    this.listImsisSelected = imsis

                }
            )
        })

        this.ticketService.getDetailTicketConfig(me.userInfo.provinceCode, (resp) => {
            this.listEmail = resp.emailInfos;
        })
    }

    preventCharacter(event) {
        if (event.ctrlKey) {
            return;
        }
        if (event.keyCode == 8 || event.keyCode == 13 || event.keyCode == 37 || event.keyCode == 39) {
            return;
        }
        if (event.keyCode < 48 || event.keyCode > 57) {
            event.preventDefault();
        }
        // Chặn ký tự 'e', 'E' và dấu '+'
        if (event.keyCode == 69 || event.keyCode == 101 || event.keyCode == 107 || event.keyCode == 187) {
            event.preventDefault();
        }
    }

    downloadTemplate() {
        this.ticketService.downloadTemplate();
    }

    uploadFile(file: any) {
        let me = this;
        me.isShowUpload = false;
        this.fileName = this.removeFileExtension(file.name);
        me.messageCommonService.onload();
        if (file.size > 1024 * 1024) { // 1MB
            me.messageCommonService.offload();
            me.messageCommonService.warning(me.tranService.translate("ticket.message.largeFile"));
            return;
        }
        if (file) {
            // me.listImsisSelected = [];
            me.errorRecords = [];
            me.listImsis = [];
            const reader = new FileReader();
            reader.onload = (e: any) => {
                const binaryStr = e.target.result;
                const workbook = XLSX.read(binaryStr, {type: 'binary'});
                const sheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[sheetName];
                const jsonData = XLSX.utils.sheet_to_json(worksheet, {header: 1});

                // Bỏ qua dòng trống và chỉ lấy dòng có dữ liệu
                let headers = jsonData[0];
                let data = jsonData.slice(1).filter((row: any) => row.some((cell: any) => cell !== null && cell !== undefined && cell !== ''));
                this.validateFile(headers, data);
            };
            reader.readAsBinaryString(file);
        }
    }

    validateFile(headers, data: any[]) {
        let me = this;
        me.errorRecords = [];
        me.inputFileComponent.resetFile();
        const requiredHeaders = ['STT', 'IMSI (Bắt buộc)'];
        const extraColumns = headers.length > requiredHeaders.length;
        const missingColumns = headers.length < requiredHeaders.length;

        for (let i = 0; i < requiredHeaders.length; i++) {
            if (headers[i] !== requiredHeaders[i]) {
                me.messageCommonService.offload()
                me.messageCommonService.error(me.tranService.translate('ticket.message.wrongSample'));
                return;
            }
        }

        if (extraColumns || missingColumns) {
            if (extraColumns) {
                me.messageCommonService.error(me.tranService.translate('ticket.message.redundantColumns'))
            }
            if (missingColumns) {
                me.messageCommonService.error(me.tranService.translate('ticket.message.missingColumns'))
            }
            me.messageCommonService.offload()
            return;
        }

        if (data.length === 0) {
            me.messageCommonService.offload()
            me.messageCommonService.error(me.tranService.translate('ticket.message.emptyFile'));
            return;
        }

        const filteredRecords = data.filter(record =>
            record.length === requiredHeaders.length &&
            (record[0] !== undefined && record[0].toString().trim() !== '' || record[1].trim() !== '')
        );

        if (filteredRecords.length === 0) {
            me.messageCommonService.offload()
            me.messageCommonService.error(me.tranService.translate('ticket.message.emptyFile'));
            return;
        }

        data.forEach((record, index) => {
            let errors = [];
            if (!record[1] || record[1].trim() === '') {
                errors.push(me.tranService.translate('ticket.message.missingImsiInfo'));
            } else if (typeof record[1] !== 'string' || !/^\d+$/.test(record[1]) || record[1].length > 18) {
                errors.push(me.tranService.translate('ticket.message.wrongImsiFormat'));
            } else {
                this.listImsis.push(record[1])
            }
            if (errors.length > 0) {
                this.errorRecords.push([...record, errors.join(', ')]);
            }
        });

        this.listImsis = this.listImsis.map(imsi => Number(imsi.trim()))

        if (this.listImsis.length > 0) {
            me.simTicketService.search({
                listImsi: me.listImsis.join(','),
                ticketType: CONSTANTS.REQUEST_TYPE.ORDER_SIM
            }, (res) => {
                let index = 0;
                for (const imsi of me.listImsis) {
                    let found = false;
                    for (const item of res.content) {
                        if (imsi === item.imsi) {
                            if (item.status == CONSTANTS.SIM_TICKET_STATUS.ACTIVATED) {
                                me.errorRecords.push(['0', imsi + '', me.tranService.translate('ticket.message.imsiIsActivated')])
                            } else {
                                if (me.listImsisSelected.includes(imsi)) {
                                    me.errorRecords.push(['0', imsi + '', me.tranService.translate("ticket.message.imsiIsExist")])
                                } else {
                                    me.listImsisSelected.push(imsi);
                                }
                            }
                            found = true;
                            break;
                        }
                    }
                    // Nếu không tìm thấy IMSI trong
                    if (!found) {
                        me.errorRecords.push(['0', imsi + '', me.tranService.translate("ticket.message.imsiNotExist")])
                    }
                }
                me.messageCommonService.offload();
                if (this.errorRecords.length > 0) {
                    me.isShowDownload = true;
                } else {
                    me.messageCommonService.success((me.tranService.translate("global.message.saveSuccess")))
                }
            })
        } else {
            me.messageCommonService.offload();
            if (this.errorRecords.length > 0) {
                me.isShowDownload = true;
            } else {
                me.messageCommonService.success((me.tranService.translate("global.message.saveSuccess")))
            }
        }

    }

    downloadErrorFile() {
        let me = this;
        for (let i = 0; i < me.errorRecords.length; i++) {
            me.errorRecords[i][0] = (i + 1).toString();
        }
        console.log(this.errorRecords)
        const ws: XLSX.WorkSheet = XLSX.utils.aoa_to_sheet([['STT', 'IMSI', 'Nội dung lỗi'], ...this.errorRecords]);
        const wb: XLSX.WorkBook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'Errors');

        const wbout = XLSX.write(wb, {bookType: 'xlsx', type: 'binary'});
        const buf = new ArrayBuffer(wbout.length);
        const view = new Uint8Array(buf);

        for (let i = 0; i < wbout.length; ++i) {
            view[i] = wbout.charCodeAt(i) & 0xFF;
        }

        const blob = new Blob([buf], {type: 'application/octet-stream'});
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${this.fileName}_Danh_sách_lỗi_${new Date().toISOString().replace(/[-:T.]/g, '').substring(0, 14)}.xlsx`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        this.isShowDownload = false;
    }

    onChangeDateFrom(value) {
        if (value) {
            this.minDateTo = value;
        } else {
            this.minDateTo = null
        }
    }

    onChangeDateTo(value) {
        if (value) {
            this.maxDateFrom = value;
        } else {
            this.maxDateFrom = new Date();
        }
    }

    removeImsi(i: number) {
        this.listImsisSelected.splice(i, 1);
    }

    isHideUpload() {
        this.isShowUpload = false;
    }
    removeFileExtension(fileName: string): string {
        const lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex === -1) return fileName;
        return fileName.substring(0, lastDotIndex);
    }
    onKeyDownNote(event): void {
        if (event.key === ' ' && (this.ticket.note == null || this.ticket.note != null && this.ticket.note.trim() === '')) {
            event.preventDefault();
        }

        if (this.ticket.note != null && this.ticket.note.trim() != '') {
            this.ticket.note = this.ticket.note.trimStart().replace(/\s{2,}/g, ' ');
            return;
        }
    }
    onKeyDownContent(event) {
        if (event.key === ' ' && (this.ticket.content == null || this.ticket.content != null && this.ticket.content.trim() === '')) {
            event.preventDefault();
        }

        if (this.ticket.content != null && this.ticket.content.trim() != '') {
            this.ticket.content = this.ticket.content.trimStart().replace(/\s{2,}/g, ' ');
            return;
        }
    }
    onKeyDownNoteContent(event: KeyboardEvent, note: any): void {
        if (event.key === ' ' && (!note.content || note.content.trim() === '')) {
            event.preventDefault();
        }

        if (note.content && note.content.trim() !== '') {
            note.content = note.content.trimStart().replace(/\s{2,}/g, ' ');
            return;
        }
    }
    onKeyDownCause(event) {
        if (event.key === ' ' && (this.ticket.cause == null || this.ticket.cause != null && this.ticket.cause.trim() === '')) {
            event.preventDefault();
        }

        if (this.ticket.cause != null && this.ticket.cause.trim() != '') {
            this.ticket.cause = this.ticket.cause.trimStart().replace(/\s{2,}/g, ' ');
            return;
        }
    }
    validateInput(event) {
        const invalidChars = ['e', 'E', '+', '-'];
        const inputField = event.target;
        let value = inputField.value;

        // Remove invalid characters from the value
        invalidChars.forEach(char => {
            value = value.split(char).join('');
        });

        // Update the input field value if it contains invalid characters
        if (inputField.value !== value) {
            inputField.value = value;
        }
    }

    protected readonly CONSTANTS = CONSTANTS;
}
