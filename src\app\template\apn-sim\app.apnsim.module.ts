import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { BreadcrumbModule } from 'primeng/breadcrumb';
import { FieldsetModule } from 'primeng/fieldset';
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { InputTextModule } from 'primeng/inputtext';
import { ButtonModule } from 'primeng/button';
import { AppApnSimListComponent} from "./list/app.apnsim.list.component";
import {SplitButtonModule} from "primeng/splitbutton";
import {AppApnSimRoutingModule} from "./app.apnsim.routing";
import {SimService} from "../../service/sim/SimService";
import {CustomerService} from "../../service/customer/CustomerService";
import {CalendarModule} from "primeng/calendar";
import {DropdownModule} from "primeng/dropdown";
import {CommonVnptModule} from "../common-module/common.module";
import {AppApnSimDetailComponent} from "./detail/app.apnsim.detail.component";
import {CardModule} from "primeng/card";
import {ApnSimService} from "../../service/apn/ApnSimService";
import {PanelModule} from "primeng/panel";
import {DialogModule} from "primeng/dialog";

@NgModule({
    imports: [
        CommonModule,
        AppApnSimRoutingModule,
        BreadcrumbModule,
        FieldsetModule,
        FormsModule, ReactiveFormsModule,
        InputTextModule,
        ButtonModule, SplitButtonModule, CalendarModule, DropdownModule, CommonVnptModule, CardModule, PanelModule, DialogModule,
    ],
    declarations: [
        AppApnSimListComponent,
        AppApnSimDetailComponent
    ],

    providers:[
        CustomerService,
        ApnSimService,
    ]
})
export class AppApnSimModule{}
