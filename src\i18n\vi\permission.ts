export default {
    User: {
        User: "<PERSON><PERSON><PERSON> khoản",
        createUser: "Tạo tài khoản",
        deleteUser: "Xóa tài khoản",
        getUser: "Xem chi tiết tài khoản",
        searchUser: "Xem danh sách tài khoản",
        updateUser: "Cập nhật thông tin tài khoản",
        getProfileUser: "Xem chi tiết thông tin cá nhân",
        updateProfile: "Cập nhật thông tin cá nhân",
        changeManageData: "Chuyển quyền quản lý dữ liệu"
    },
    SimGroup: {
        SimGroup: "Nhóm thuê bao",
        createSimGroup: "Tạo nhóm thuê bao",
        deleteSimGroup: "Xóa nhóm thuê bao",
        getSimGroup: "Xem chi tiết nhóm thuê bao",
        searchSimGroup: "Xem danh sách nhóm thuê bao",
        updateSimGroup: "<PERSON>ậ<PERSON> nhật nhóm thuê bao"
    },
    Sim: {
        Sim: "Thuê bao",
        createSim: "Tạo thuê bao",
        deleteSim: "Xóa thuê bao",
        getSim: "Xem chi tiết thuê bao",
        searchSim: "Xem danh sách thuê bao",
        updateSim: "Cập nhật thuê bao"
    },
    RatingPlanSim:{
        RatingPlanSim: "Gói cước thuê bao",
        setRateSim: "Đăng ký gói cước cho thuê bao",
        cancelRateSim: "Hủy gói cước cho thuê bao",
        registerByFile: "Đăng ký gói cước theo file",
        registerByList: "Đăng ký gói cước theo nhóm"
    },
    Role: {
        Role: "Nhóm quyền",
        createRole: "Tạo nhóm quyền",
        deleteRole: "Xóa nhóm quyền",
        getRole: "Xem chi tiết nhóm quyền",
        searchRole: "Xem danh sách nhóm quyền",
        updateRole: "Cập nhật nhóm quyền"
    },
    Report: {
        Report: "Báo cáo",
        getListReportSimStatus: "Get List Report Subcriber Status",
        exportReportSimRatingPLan: "Export Report Subcriber Rating Plan",
        getListReportRequestApiLog: "Get List Report Report Api Log",
        searchReportRequestApi: "Search Report Request Api",
        getListReportMonthly: "Get List Report Monthly",
        getListReportManageSim: "Get List Report Manage Subcriber",
        getListReportHistorySim: "Get List Report History SSubcriberim",
        getListReportDetailSim: "Get List Report Detail Subcriber",
        getListReportCheckPostage: "Get List Report Check Postage",
        getListReportContract: "Get List Report Contract",
        getListReportBillingCustomer: "Get List Report Billing Customer"
    },
    RatingPlan: {
        RatingPlan: "Gói cước",
        changeStatusRatingPlan: "Kích hoạt/Tạm dừng gói cước",
        approveRatingPlan: "Phê duyệt gói cước",
        createRatingPlan: "Tạo gói cước",
        deleteRatingPlan: "Xóa gói cước",
        getRatingPlan: "Xem chi tiết gói cước",
        issueRatingPlan: "Gán gói cước cho cấp dưới",
        searchRatingPlan: "Xem danh sách gói cước",
        updateRatingPlan: "Cập nhật gói cước",
    },
    Permission:{
        Permission: "Quyền",
        getPermission: "Xem chi tiết quyền",
        searchPermission: "Xem danh sách quyền"
    },
    Device: {
        Device: "Thiết bị",
        createDevice: "Tạo thiết bị",
        deleteDevice: "Xóa thiết bị",
        getDevice: "Xem chi tiết thiết bị",
        searchDevice: "Xem danh sách thiết bị",
        updateDevice: "Cập nhật thiết bị",
    },
    Customer: {
        Customer: "Khách hàng",
        changeStatusCustomer: "Kích hoạt khách hàng",
        getCustomer: "Xem chi tiết khách hàng",
        searchCustomer: "Xem danh sách khách hàng",
        updateCustomer: "Cập nhật khách hàng",
        deleteCustomer: "Xóa khách hàng",
        createCustomer: "Tạo khách hàng"
    },
    CustomAlert: {
        CustomAlert: "Cấu hình cảnh báo",
        createAlertConfig: "Tạo cấu hình cảnh báo",
        deleteAlertConfig: "Xóa cấu hình cảnh báo",
        getAlertConfig: "Xem chi tiết cấu hình cảnh báo",
        searchAlertConfig: "Xem danh sách cầu hình cảnh báo",
        updateAlertConfig: "Cập nhật cấu hình cảnh báo"
    },
    AlertRecvGrp: {
        AlertRecvGrp: "Nhóm nhận cảnh báo",
        createAlertRecvGrp: "Tạo nhóm nhận cảnh báo",
        updateAlertRecvGrp: "Cập nhật nhóm nhận cảnh báo",
        getAlertRecvGrp: "Xem chi tiết nhóm nhận cảnh báo",
        deleteAlertRecvGrp: "Xóa nhóm nhận cảnh báo",
        searchAlertRecvGrp: "Danh sách nhóm nhận cảnh báo"
    },
    RptCfg: {
        RptCfg: "Cấu hình báo cáo động",
        createRptCfg: "Tạo cấu hình báo cáo động",
        updateRptCfg: "Cập nhật cấu hình báo cáo động",
        getRptCfg: "Xem chi tiết cấu hình báo cáo động",
        deleteRptCfg: "Xóa cấu hình báo cáo động",
        searchRptCfg: "Danh sách cấu hình báo cáo động"
    },
    RptRecvGrp: {
        RptRecvGrp: "Nhóm nhận báo cáo động",
        createRptRecvGrp: "Tạo nhóm nhận báo cáo động",
        updateRptRecvGrp: "Cập nhật nhóm nhận báo cáo động",
        getRptRecvGrp: "Xem chi tiết nhóm nhận báo cáo động",
        deleteRptRecvGrp: "Xóa nhóm nhận báo cáo động",
        searchRptRecvGrp: "Danh sách nhóm nhận báo cáo động"
    },
    RptSend: {
        RptSend: "Cấu hình gửi mail báo cáo",
        updateRptSend: "Cập nhật cấu hình gửi mail báo cáo"
    },
    Contract: {
        Contract: "Hợp đồng",
        getContract: "Xem chi tiết hợp đồng",
        searchContract: "Xem danh sách hợp đồng"
    },
    Configuration: {
        Configuration: "Cấu hình hệ thống",
        getConfiguration: "Xem chi tiết thông số cấu hình hệ thống",
        searchConfiguration: "Tìm kiếm thông số cấu hình hệ thống",
        updateConfiguration: "Cập nhật thông số cấu hình hệ thống"
    },
    ApnSim: {
        ApnSim: "APN Sim",
        issueApnSim: "Issue APN Sim",
        searchApnSim: "Danh sách APN thuê bao",
        getApnSim: "Chi tiết APN thuê bao"
    },
    Apn: {
        Apn: "Apn",
        activeApn: "Active APN",
        cancelApn: "Cancel APN",
        completeApn: "Complete APN",
        createApn: "Tạo APN",
        deactiveApn: "Deactive APN",
        getApn: "Xem chi tiết APN",
        issueApn: "Issue APN",
        searchApn: "Danh sách APN",
        sentEmailApn: "Send Email APN",
        updateApn: "Cập nhật APN"
    },
    AlertLog: {
        AlertLog: "Lịch sử cảnh báo",
        getAlertLog: "Xem chi tiết lịch sử cảnh báo",
        searchAlertLog: "Xem danh sách lịch sử cảnh báo",
    },
    Alert: {
        Alert: "Cảnh báo",
        ackAlert: "Xác nhận cảnh báo",
        getAlert: "Xem chi tiết cảnh báo",
        searchAlert: "Xem danh sách quy tắc",
        changeStatusAlert: "Đổi trạng thái cảnh báo",
        createAlert: "Tạo cảnh báo",
        updateAlert: "Cập nhật cảnh báo",
        deleteAlert: "Xóa cảnh báo",
        createAlertWalletThreshold: "Tạo cảnh báo ví vượt ngưỡng",
        createAlertWalletExpiry: "Tạo cảnh báo ví hết hạn",
        updateAlertWalletThreshold: "Cập nhật cảnh báo ví vượt ngưỡng",
        updateAlertWalletExpiry: "Cập nhật cảnh báo ví hết hạn",
    },
    RptContent: {
        RptContent: "Dữ liệu báo cáo động"
    },
    DynamicChart: {
        DynamicChart: "Biểu đồ động",
        getDashBoardContent: "Xem Nội dung Dashboard",
    },
    CnfDynamicChart: {
        CnfDynamicChart: "Cấu hình biểu đồ động",
        searchCnfDynamicChart: "Tìm kiếm cấu hình biểu đồ động",
        getCnfDynamicChart: "Xem chi tiết cấu hình biểu đồ động",
        updateCnfDynamicChart: "Cập nhật cấu hình biểu đồ động",
        createCnfDynamicChart: "Tạo cấu hình biểu đồ động",
        deleteCnfDynamicChart: "Xóa cấu hình biểu đồ động"
    },Log : {
        Log : "Nhật ký hoạt động"

    },
    Ticket: {
        Ticket: "Quản lý yêu cầu",
        getTicket: "Xem danh sách yêu cầu",
        createTicket: "Tạo yêu cầu",
        updateTicket: "Cập nhật yêu cầu"
    },
    Policy: {
        Policy: "Điều khoản và chính sách",
        getPersonalDataPolicy: "Chính sách bảo vệ dữ liệu cá nhân"
    },
    Wallet:{
        Wallet: "Quản lý lưu lượng",
        searchWallet: "Tìm kiếm và liệt kê toàn bộ danh sách ví",
        accuracyWallet: "Thêm ví (Xác thực ví)",
        shareWallet: "Chia sẻ",
        createShareInfo: "Thêm người nhận chia sẻ (Cho phép thêm trên web và file)",
        searchShareInfo: "Tìm kiếm và liệt kê toàn bộ danh sách chia sẻ",
        walletHistory: "Xem lịch sử chia sẻ",
        // alertWalletThreshold: "Cảnh báo ví lưu lượng vượt ngưỡng giá trị",
        // alertWalletExpiry: "Cảnh báo hết hạn ví",
    },
    ShareGroup:{
        ShareGroup: "Nhóm chia sẻ",
        searchShareGroup: "Hiển thị danh sách nhóm chia sẻ",
        createShareGroup: "Tạo nhóm chia sẻ",
        editShareGroup: "Chỉnh sửa nhóm chia sẻ",
        detailShareGroup: "Xem chi tiết nhóm chia sẻ",
        deleteShareGroup: "Xóa nhóm chia sẻ",
    },
    Diagnose: {
        Diagnose: "Chẩn đoán",
        searchDiagnose: "Tra cứu chẩn đoán",
    },
    'API Partner': {
        'API Partner': "Cấp quyền API",
    },
    Guide: {
        Guide: "Hướng dẫn sử dụng",
        guideIntegration: "Xem hướng dẫn tích hợp",
    }
}
