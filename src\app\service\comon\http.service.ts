import { Http<PERSON>lient, HttpErrorResponse, HttpEvent, <PERSON>ttp<PERSON><PERSON><PERSON>, HttpInterceptor, HttpRequest } from "@angular/common/http";
import { Inject, Injectable,InjectionToken } from "@angular/core";
import { MessageCommonService } from "./message-common.service";

import { Observable, TimeoutError } from 'rxjs';
import { timeout } from 'rxjs/operators';
import { TranslateService } from "./translate.service";
import { DomSanitizer } from "@angular/platform-browser";
import {SessionService} from "../session/SessionService";
import { CONSTANTS } from "./constants";
import { environment } from "src/environments/environment";

export const DEFAULT_TIMEOUT = new InjectionToken<number>('defaultTimeout');

@Injectable({ providedIn: 'root'})
export class TimeoutInterceptor implements HttpInterceptor {
  constructor(@Inject(DEFAULT_TIMEOUT) protected defaultTimeout: number) {
  }

  intercept(req: HttpRequest<any>, next: <PERSON>ttp<PERSON><PERSON><PERSON>): Observable<HttpEvent<any>> {
    const timeoutValue = req.headers.get('timeout') || this.defaultTimeout;
    const timeoutValueNumeric = Number(timeoutValue);

    return next.handle(req).pipe(timeout(timeoutValueNumeric));
  }
}

@Injectable({ providedIn: 'root'})
export class HttpService{
    headers: object;
    baseUrl: string;
    baseUrlFake: string;
    constructor(@Inject(HttpClient) private httpClient: HttpClient,
                        private messageCommonService: MessageCommonService,
                        private tranService: TranslateService,
                        private sanitizer: DomSanitizer) {
        this.headers = {
            Authorization: 'Bearer ' + localStorage.getItem("token"),
            timeout: CONSTANTS.MAX_TIME_HTTP_WAIT,
            "ngrok-skip-browser-warning": "69420"
        }
        this.baseUrl = environment.baseUrl;
        // this.baseUrl = "http://**********:8080/api"
        this.baseUrlFake = environment.baseUrlFake;
    }

    handleCommonNext(response){
        // this.messageCommonService.offload();
    }

    updateHeader(token) {
        this.headers['Authorization'] = 'Bearer ' + token
    }

    handleBodyError(bodyError, error){
        if(bodyError){
            let statusError = error.status || 500;
            let errorCode = bodyError.errorCode || "global.message.error";
            let field = bodyError.field || "";
            field = field.substring(1, field.length - 1).split(",");
            let objectKey = bodyError.object;
            let timestamp = bodyError.timestamp;
            let dataMessage = {};
            for(let i = 0;i < field.length;i++){
                dataMessage[i] = field[i].trim();
            }
            this.messageCommonService.error(this.tranService.translate(errorCode, dataMessage), this.tranService.translate(`error.status.${statusError}`));
            if(statusError == CONSTANTS.HTTP_STATUS.FORBIDDEN){
                window.location.hash = "/access";
            }else if(statusError == CONSTANTS.HTTP_STATUS.NOT_FOUND){
                if (errorCode == "error.emailIncorect" ||
                    errorCode ==  "error.passwordIncorect" ||
                    errorCode == "error.accountInactive" ||
                    errorCode == "error.role.not.working") {
                    // Sai thông tin đăng nhập hoặc tài khoản/nhóm quyền không hoạt động thì chỉ đưa ra thông báo,
                    // không chuyển tới /notfound
                } else {
                    window.location.hash = "/notfound";
                }
            }
        }else{
            this.messageCommonService.error(this.tranService.translate("global.message.error"), this.tranService.translate("ERROR"));
        }
    }

    handleCommonError(error){
        console.log(error);
        if(error instanceof TimeoutError){
            this.messageCommonService.error(this.tranService.translate("global.message.timeout"));
        }else if(error instanceof HttpErrorResponse){
            if(error.error){
                let me = this;
                if(error.error instanceof Blob){
                    error.error.text().then(function(resultText){
                        let bodyError = JSON.parse(resultText).error;
                        me.handleBodyError(bodyError, error);
                    })
                }else{
                    let bodyError = error.error.error;
                    this.handleBodyError(bodyError, error);
                }
            }else{
                if(error.status == CONSTANTS.HTTP_STATUS.UNAUTHENTICATION){
                    localStorage.clear();
                    window.location.href = "/#/login"
                } else {
                    this.messageCommonService.error(error.status+"");
                }
            }
        }else{
            this.messageCommonService.error(this.tranService.translate("global.message.error"))
        }
        // this.messageCommonService.offload();
    }

    handleCommonFinally(){
        // this.messageCommonService.offload();
    }

    public findCellId(lac, cell, callback?:Function, errorCallback?:Function, finallyCallback?:Function):void{
        let obs = this.httpClient.get(`https://api.findcellid.com/api/look_up?mnc=04&mcc=452&lac=${lac}&cid=${cell}`);
        let me = this;
        obs.subscribe({
            next: (response)=>{
                if(callback){
                    callback(response);
                }else{
                    me.handleCommonNext(response);
                }
            },
            error: (error)=>{
                if(errorCallback){
                    errorCallback(error);
                }else{
                    me.handleCommonError(error);
                }
                if(finallyCallback){
                    finallyCallback("error");
                }else{
                    me.handleCommonFinally();
                }
            },
            complete: ()=>{
                if(finallyCallback){
                    finallyCallback();
                }else{
                    me.handleCommonFinally();
                }
            }
        })
    }

    public findAddress(lat, lon, callback?:Function, errorCallback?:Function, finallyCallback?:Function):void{
        let obs = this.httpClient.get(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lon}`);
        let me = this;
        obs.subscribe({
            next: (response)=>{
                if(callback){
                    callback(response);
                }else{
                    me.handleCommonNext(response);
                }
            },
            error: (error)=>{
                if(errorCallback){
                    errorCallback(error);
                }else{
                    me.handleCommonError(error);
                }
                if(finallyCallback){
                    finallyCallback("error");
                }else{
                    me.handleCommonFinally();
                }
            },
            complete: ()=>{
                if(finallyCallback){
                    finallyCallback();
                }else{
                    me.handleCommonFinally();
                }
            }
        })
    }

    public get(url:string, headers:{[key:string]:any}, params:{[key:string]:any}, callback?:Function, errorCallback?:Function, finallyCallback?:Function):void{
        let obs = this.httpClient.get(`${this.baseUrl}${url}`, {
            headers: {
                ...this.headers,
                ...headers,
            },
            params
        });
        let me = this;
        obs.subscribe({
            next: (response)=>{
                if(callback){
                    callback(response);
                }else{
                    me.handleCommonNext(response);
                }
            },
            error: (error)=>{
                if(errorCallback){
                    errorCallback(error);
                }else{
                    me.handleCommonError(error);
                }
                if(finallyCallback){
                    finallyCallback("error");
                }else{
                    me.handleCommonFinally();
                }
            },
            complete: ()=>{
                if(finallyCallback){
                    finallyCallback();
                }else{
                    me.handleCommonFinally();
                }
            }
        })
    }

    public getFake(url:string, headers:{[key:string]:any}, params:{[key:string]:any}, callback?:Function, errorCallback?:Function, finallyCallback?:Function):void{
        let obs = this.httpClient.get(`${this.baseUrlFake}${url}`, {
            headers: {
                ...this.headers,
                ...headers,
            },
            params
        });
        let me = this;
        obs.subscribe({
            next: (response)=>{
                if(callback){
                    callback(response);
                }else{
                    me.handleCommonNext(response);
                }
            },
            error: (error)=>{
                if(errorCallback){
                    errorCallback(error);
                }else{
                    me.handleCommonError(error);
                }
                if(finallyCallback){
                    finallyCallback();
                }else{
                    me.handleCommonFinally();
                }
            },
            complete: ()=>{
                if(finallyCallback){
                    finallyCallback();
                }else{
                    me.handleCommonFinally();
                }
            }
        })
    }

    public post(url:string, headers:{[key:string]:any}, data: any,params?:{[key:string]:any}, callback?:Function, errorCallback?:Function, finallyCallback?:Function):void{
        let obs = this.httpClient.post(`${this.baseUrl}${url}`,data, {
            headers: {
                ...this.headers,
                ...headers
            },
            params
        });
        let me = this;
        obs.subscribe({
            next: (response)=>{
                if(callback){
                    callback(response);
                }else{
                    me.handleCommonNext(response);
                }
            },
            error: (error)=>{
                if(errorCallback){
                    errorCallback(error);
                }else{
                    me.handleCommonError(error);
                }
                if(finallyCallback){
                    finallyCallback();
                }else{
                    me.handleCommonFinally();
                }
            },
            complete: ()=>{
                if(finallyCallback){
                    finallyCallback();
                }else{
                    me.handleCommonFinally();
                }
            }
        })
    }
    public postNoError(url: string, headers: { [key: string]: any }, data: any, params?: { [key: string]: any }, callback?: Function, errorCallback?:Function, finallyCallback?: Function): void {
    let obs = this.httpClient.post(`${this.baseUrl}${url}`, data, {
        headers: {
            ...this.headers,
            ...headers
        },
        params
    });
    let me = this;
    obs.subscribe({
        next: (response) => {
            if (callback) {
                callback(response);
            } else {
                me.handleCommonNext(response);
            }
        },
        error: () => {
            // Không làm gì khi gặp lỗi
        },
        complete: () => {
            if (finallyCallback) {
                finallyCallback();
            } else {
                me.handleCommonFinally();
            }
        }
    });
    }


    public postFake(url:string, headers:{[key:string]:any}, data: any,params?:{[key:string]:any}, callback?:Function, errorCallback?:Function, finallyCallback?:Function):void{
        let obs = this.httpClient.post(`${this.baseUrlFake}${url}`,data, {
            headers: {
                ...this.headers,
                ...headers
            },
            params
        });
        let me = this;
        obs.subscribe({
            next: (response)=>{
                if(callback){
                    callback(response);
                }else{
                    me.handleCommonNext(response);
                }
            },
            error: (error)=>{
                if(errorCallback){
                    errorCallback(error);
                }else{
                    me.handleCommonError(error);
                }
                if(finallyCallback){
                    finallyCallback();
                }else{
                    me.handleCommonFinally();
                }
            },
            complete: ()=>{
                if(finallyCallback){
                    finallyCallback();
                }else{
                    me.handleCommonFinally();
                }
            }
        })
    }

    public postNoHeader(url:string, headers:{[key:string]:any}, data: any,params?:{[key:string]:any}, callback?:Function, errorCallback?:Function, finallyCallback?:Function):void{
        let obs = this.httpClient.post(`${this.baseUrl}${url}`,data, {
            headers: {
                ...headers
            },
            params
        });
        let me = this;
        obs.subscribe({
            next: (response)=>{
                if(callback){
                    callback(response);
                }else{
                    me.handleCommonNext(response);
                }
            },
            error: (error)=>{
                if(errorCallback){
                    errorCallback(error);
                }else{
                    me.handleCommonError(error);
                }
                if(finallyCallback){
                    finallyCallback();
                }else{
                    me.handleCommonFinally();
                }
            },
            complete: ()=>{
                if(finallyCallback){
                    finallyCallback();
                }else{
                    me.handleCommonFinally();
                }
            }
        })
    }

    public put(url:string, headers:{[key:string]:any}, data: any,params?:{[key:string]:any}, callback?:Function, errorCallback?:Function, finallyCallback?:Function):void{
        let obs = this.httpClient.put(`${this.baseUrl}${url}`,data, {
            headers: {
                ...this.headers,
                ...headers
            },
            params

        });
        let me = this;
        obs.subscribe({
            next: (response)=>{
                if(callback){
                    callback(response);
                }else{
                    me.handleCommonNext(response);
                }
            },
            error: (error)=>{
                if(errorCallback){
                    errorCallback(error);
                }else{
                    me.handleCommonError(error);
                }
                if(finallyCallback){
                    finallyCallback();
                }else{
                    me.handleCommonFinally();
                }
            },
            complete: ()=>{
                if(finallyCallback){
                    finallyCallback();
                }else{
                    me.handleCommonFinally();
                }
            }
        })
    }

    public delete(url:string, headers:{[key:string]:any}, params:{[key:string]:any}, callback?:Function, errorCallback?:Function, finallyCallback?:Function):void{
        let obs = this.httpClient.delete(`${this.baseUrl}${url}`, {
            headers: {
                ...this.headers,
                ...headers
            },
            params
        });
        let me = this;
        obs.subscribe({
            next: (response)=>{
                if(callback){
                    callback(response);
                }else{
                    me.handleCommonNext(response);
                }
            },
            error: (error)=>{
                if(errorCallback){
                    errorCallback(error);
                }else{
                    me.handleCommonError(error);
                }
                if(finallyCallback){
                    finallyCallback();
                }else{
                    me.handleCommonFinally();
                }
            },
            complete: ()=>{
                if(finallyCallback){
                    finallyCallback();
                }else{
                    me.handleCommonFinally();
                }
            }
        })
    }

    public upload(url: string, objectFile, headers:{[key:string]:any}, params:{[key:string]:any}, callback?:Function, errorCallback?: Function, finallyCallback?: Function){
        let data = new FormData();
        data.append("file", objectFile);
        let obs = this.httpClient.post(`${this.baseUrl}${url}`, data, {
            headers: {
                ...this.headers,
                ...headers
            },
            observe: 'response',
            responseType: 'blob',
            params
        });
        let me = this;
        obs.subscribe({
            next: (response)=>{
                if(callback){
                    callback(response);
                }else{
                    me.handleCommonNext(response);
                    me.messageCommonService.offload()
                }
            },
            error: (error)=>{
                if(errorCallback){
                    errorCallback(error);
                }else{
                    me.handleCommonError(error);
                    me.messageCommonService.offload()
                }
                if(finallyCallback){
                    finallyCallback();
                }else{
                    me.handleCommonFinally();
                    me.messageCommonService.offload()
                }
            },
            complete: ()=>{
                if(finallyCallback){
                    finallyCallback();
                }else{
                    me.handleCommonFinally();
                    me.messageCommonService.offload()
                }
            }
        })
    }
    public uploadContainBody(url: string, objectFile,body , headers:{[key:string]:any}, params:{[key:string]:any}, callback?:Function, errorCallback?: Function, finallyCallback?: Function){
        let data = new FormData();
        data.append("file", objectFile);
        data.append("data", new Blob([JSON.stringify(body)], { type: "application/json" }))
        let obs = this.httpClient.post(`${this.baseUrl}${url}`, data, {
            headers: {
                ...this.headers,
                ...headers
            },
            observe: 'response',
            responseType: 'blob',
            params
        });
        let me = this;
        obs.subscribe({
            next: (response)=>{
                if(callback){
                    callback(response);
                }else{
                    me.handleCommonNext(response);
                    me.messageCommonService.offload()
                }
            },
            error: (error)=>{
                if(errorCallback){
                    errorCallback(error);
                }else{
                    me.handleCommonError(error);
                    me.messageCommonService.offload()
                }
                if(finallyCallback){
                    finallyCallback();
                }else{
                    me.handleCommonFinally();
                    me.messageCommonService.offload()
                }
            },
            complete: ()=>{
                if(finallyCallback){
                    finallyCallback();
                }else{
                    me.handleCommonFinally();
                    me.messageCommonService.offload()
                }
            }
        })
    }
    public uploadFile(url: string, objectFile, headers:{[key:string]:any}, params:{[key:string]:any}, callback?:Function, errorCallback?: Function, finallyCallback?: Function){
        let data = new FormData();
        data.append("file", objectFile);
        let obs = this.httpClient.post(`${this.baseUrl}${url}`, data, {
            headers: {
                ...this.headers,
                ...headers
            },
            params
        });
        let me = this;
        obs.subscribe({
            next: (response)=>{
                if(callback){
                    callback(response);
                }else{
                    me.handleCommonNext(response);
                    me.messageCommonService.offload()
                }
            },
            error: (error)=>{
                if(errorCallback){
                    errorCallback(error);
                }else{
                    me.handleCommonError(error);
                    me.messageCommonService.offload()
                }
                if(finallyCallback){
                    finallyCallback();
                }else{
                    me.handleCommonFinally();
                    me.messageCommonService.offload()
                }
            },
            complete: ()=>{
                if(finallyCallback){
                    finallyCallback();
                }else{
                    me.handleCommonFinally();
                    me.messageCommonService.offload()
                }
            }
        })
    }

    public uploadFake(url: string, objectFile, headers:{[key:string]:any}, params:{[key:string]:any}, callback?:Function, errorCallback?: Function, finallyCallback?: Function){
        let data = new FormData();
        data.append("file", objectFile);
        let obs = this.httpClient.post(`${this.baseUrlFake}${url}`, data, {
            headers: {
                ...this.headers,
                ...headers
            },
            params
        });
        let me = this;
        obs.subscribe({
            next: (response)=>{
                if(callback){
                    callback(response);
                }else{
                    me.handleCommonNext(response);
                }
            },
            error: (error)=>{
                if(errorCallback){
                    errorCallback(error);
                }else{
                    me.handleCommonError(error);
                }
                if(finallyCallback){
                    finallyCallback();
                }else{
                    me.handleCommonFinally();
                }
            },
            complete: ()=>{
                if(finallyCallback){
                    finallyCallback();
                }else{
                    me.handleCommonFinally();
                }
            }
        })
    }

    public download(url:string, headers:{[key:string]:any}, params:{[key:string]:any}):void{
        let obs = this.httpClient.get(`${this.baseUrl}${url}`, {
            headers: {
                ...this.headers,
                ...headers,
            },
            responseType: 'blob' as 'json',
            params,
            observe: 'response'
        });
        let me = this;
        obs.subscribe({
            next: (response)=>{
                me.downloadFile(response);
            },
            error: (error)=>{
                me.handleCommonError(error);
                me.handleCommonFinally();
                me.messageCommonService.offload()
            },
            complete: ()=>{
                me.handleCommonFinally();
                me.messageCommonService.offload()
            }
        })
    }

    public downloadPost(url:string, headers:{[key:string]:any}, data:{[key:string]:any}, params:{[key:string]:any}):void{
        let obs = this.httpClient.post(`${this.baseUrl}${url}`, data,{
            headers: {
                ...this.headers,
                ...headers,
            },
            responseType: 'blob' as 'json',
            params,
            observe: 'response'
        });
        let me = this;
        obs.subscribe({
            next: (response)=>{
                me.downloadFile(response);
            },
            error: (error)=>{
                me.handleCommonError(error);
                me.handleCommonFinally();
                me.messageCommonService.offload()
            },
            complete: ()=>{
                me.handleCommonFinally();
                me.messageCommonService.offload()
            }
        })
    }

    public downloadPostForReporting(url:string, headers:{[key:string]:any}, data:{[key:string]:any}, params:{[key:string]:any}, callback?:Function, errorCallback?: Function, finallyCallback?: Function):void{
        let obs = this.httpClient.post(`${this.baseUrl}${url}`, data,{
            headers: {
                ...this.headers,
                ...headers,
            },
            responseType: 'blob' as 'json',
            params,
            observe: 'response'
        });
        let me = this;
        obs.subscribe({
            next: (response)=>{
                me.downloadFile(response);
            },
            error: (error)=>{
                if(errorCallback){
                    errorCallback(error);
                }else{
                    me.handleCommonError(error);
                }
                if(finallyCallback){
                    finallyCallback();
                }else{
                    me.handleCommonFinally();
                }
                me.messageCommonService.offload()
            },
            complete: ()=>{
                if(finallyCallback){
                    finallyCallback();
                }else{
                    me.handleCommonFinally();
                }
                me.messageCommonService.offload()
            }
        })
    }

    public downloadLocal(url:string, filename: string):void{
        let obs = this.httpClient.get(`${url}`, {
            headers: {
                ...this.headers,
            },
            responseType: 'blob' as 'json',
            observe: 'response'
        });
        let me = this;
        obs.subscribe({
            next: (response)=>{
                me.downloadFile(response, filename);
            },
            error: (error)=>{
                me.handleCommonError(error);
                me.handleCommonFinally();
                me.messageCommonService.offload()
            },
            complete: ()=>{
                me.handleCommonFinally();
                me.messageCommonService.offload()
            }
        })
    }

    public downloadFake(url:string, headers:{[key:string]:any}, params:{[key:string]:any}):void{
        let obs = this.httpClient.get(`${this.baseUrlFake}${url}`, {
            headers: {
                ...this.headers,
                ...headers,
            },
            responseType: 'blob' as 'json',
            params,
            observe: 'response'
        });
        let me = this;
        obs.subscribe({
            next: (response)=>{
                me.downloadFile(response);
            },
            error: (error)=>{
                me.handleCommonError(error);
                me.handleCommonFinally();
                me.messageCommonService.offload()
            },
            complete: ()=>{
                me.handleCommonFinally();
                me.messageCommonService.offload()
            }
        })
    }

    private downloadFile(response, filename?:string){
        let contentDisposition = response.headers.get('content-disposition') || 'attachment; filename=default.csv';
        if(!filename){
            filename = contentDisposition.split(";")[1].split("=")[1];
        }
        let blob = new Blob([response.body], { type: 'application/octet-stream' });
        let url = window.URL.createObjectURL(blob);
        let a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.click();
        a.remove();
        this.messageCommonService.offload()
    }
}
