import {Component, Injector, OnInit} from "@angular/core";
import {FormBuilder} from "@angular/forms";
import {MenuItem} from "primeng/api";
import {CONSTANTS} from "src/app/service/comon/constants";
import {RatingPlanService} from "src/app/service/rating-plan/RatingPlanService";
import {ColumnInfo, OptionTable} from "../../common-module/table/table.component";
import {ComponentBase} from "src/app/component.base";
import {AccountService} from "../../../service/account/AccountService";

@Component({
    selector: "app-rating-plan-list",
    templateUrl: "./app.ratingplan.list.component.html",
})
export class AppRatingPlanListComponent extends ComponentBase implements OnInit{
    searchInfo: {
        id: number | null,
        name: string | null,
        status: number | null,
        cycleTimeUnit: number | null,
        paidType: number | null,
        customerType: string | null,
        ratingScope: number | null
    }
    searchInfoUser: {
        username: string | null,
        fullName: string | null,
        email: string | null,
        provinceCode: string | null,
    }
    planSelected: any = {};
    listStatus: Array<any>;
    listCycle: Array<any>;
    listPaidType: Array<any>;
    listCustomerType: Array<any>;
    listRatingScope: Array<any>;
    items: MenuItem[];
    home: MenuItem;
    selectItems: Array<any> = [];
    columns: Array<ColumnInfo>;

    dataSet: {
        content: Array<any>,
        total: number
    };
    optionTable: OptionTable;
    pageNumber: number;
    pageSize: number;
    sort: string;
    formSearch: any;

    selectItemsUser: Array<{id: number, provinceCode: string, [key:string]:any}> = [];
    selectItemsUserOld: Array<{id: number, provinceCode: string, [key:string]:any}> = [{id: -1, provinceCode: ""}];
    columnsInfoUser: Array<ColumnInfo>;
    dataSetAssignPlan: {
        content: Array<any>,
        total: number
    };
    optionTableAssignPLan: OptionTable;
    pageNumberAssign: number;
    pageSizeAssign: number;
    sortAssign: string;
    formSearchUser: any;
    isShowDialogAssignPlan: boolean = false;
    listProvince: Array<any>;
    listProvincOrigins: Array<any>;
    userType: number;
    isShowModalDetail: boolean = false;
    planId: number;
    checkedReload: boolean;
    checkedFlexible: boolean;
    response: any={};
    ratingPlanInfo: {
        id: number|null;
        code: string| null,
        name: string| null,
        status: number| null,
        dispatchCode: string| null,
        customerType: string| null,
        subscriptionFee: string| null,
        subscriptionType: string| null,
        ratingScope: number| null,
        cycleTimeUnit: string| null,
        cycleInterval: string| null,
        reload: string| null,
        flat: string| null,
        limitDataUsage: string| null,
        limitSmsOutside: string| null,
        limitSmsInside: string| null,
        flexible: string| null,
        feePerDataUnit: string| null,
        squeezedSpeed: string| null,
        feeSmsInside: string| null,
        feeSmsOutside: string| null,
        maximumFee: string| null,
        dataRoundUnit: string| null,
        downSpeed: string| null,
        provinceCode: Array<string>| null,
        description: string | null
        dataMax: string | null,
        userIds: number | null,
    };
    isShowDialogShowCustomerAccount: boolean = false;
    optionTableShowCustomerAccount: OptionTable;
    columnsInfoUserForDetail: Array<ColumnInfo>;
    dataSetAssignPlanForDetail: {
        content: Array<any>,
        total: number
    };
    planStatuses: any = CONSTANTS.RATING_PLAN_STATUS;
    provinces: any[] | undefined;
    myProvices: string| null;
    subscriptionTypes: any=[];
    provinceInfo: string = "";
    planScopes = CONSTANTS.RATING_PLAN_SCOPE;
    allPermissions = CONSTANTS.PERMISSIONS;
    allUserType = CONSTANTS.USER_TYPE;
    constructor(public ratingPlanService: RatingPlanService,
                private accountService: AccountService,
        private formBuilder: FormBuilder,
        injector: Injector) {
            super(injector);
    }
    ngOnInit(): void {
        let me = this;
        this.optionTableShowCustomerAccount = {
            hasClearSelected: true,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
            action: null,
            paginator: false
        };

        this.columnsInfoUserForDetail = [
            {
                name: this.tranService.translate("ratingPlan.label.username"),
                key: "username",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
                // style:{
                //     cursor: "pointer",
                //     color: "var(--mainColorText)"
                // },
                // funcGetRouting(item) {
                //     return [`/plans/detail/${item.id}`]
                // },
            },
            {
                name: this.tranService.translate("ratingPlan.label.fullName"),
                key: "fullName",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("ratingPlan.label.email"),
                key: "email",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("ratingPlan.label.province"),
                key: "provinceName",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
            },
        ];
        this.dataSetAssignPlanForDetail = {
            content: [],
            total: 0,
        };
        this.userType = this.sessionService.userInfo.type;
        this.items = [{ label: this.tranService.translate("global.menu.ratingplanmgmt") }, { label: this.tranService.translate("global.menu.listplan") }];
        this.home = { icon: 'pi pi-home', routerLink: '/' };

        this.listStatus = [
            {name: this.tranService.translate("ratingPlan.status.create"), value: CONSTANTS.RATING_PLAN_STATUS.CREATE_NEW},
            {name: this.tranService.translate("ratingPlan.status.pending"), value: CONSTANTS.RATING_PLAN_STATUS.PENDING},
            {name: this.tranService.translate("ratingPlan.status.activated"), value: CONSTANTS.RATING_PLAN_STATUS.ACTIVATED},
            {name: this.tranService.translate("ratingPlan.status.deactivated"), value: CONSTANTS.RATING_PLAN_STATUS.DEACTIVATED},
        ],

        this.listCycle = [
            {name: this.tranService.translate("ratingPlan.cycle.day"), value: CONSTANTS.RATING_PLAN_CYCLE.DAY},
            {name: this.tranService.translate("ratingPlan.cycle.month"), value: CONSTANTS.RATING_PLAN_CYCLE.MONTH},
        ]

        this.listPaidType = [
            {name: this.tranService.translate("ratingPlan.subscriptionType.pre"), value: CONSTANTS.SUBSCRIPTION_TYPE.PREPAID},
            {name: this.tranService.translate("ratingPlan.subscriptionType.post"), value: CONSTANTS.SUBSCRIPTION_TYPE.POSTPAID},
        ]

        this.listCustomerType = [
            {name: this.tranService.translate("ratingPlan.customerType.personal"), value: CONSTANTS.CUSTOMER_TYPE.PERSONAL},
            {name: this.tranService.translate("ratingPlan.customerType.enterprise"), value: CONSTANTS.CUSTOMER_TYPE.INTERPRISE}
            // ,
            // {name: this.tranService.translate("ratingPlan.customerType.agency"), value: CONSTANTS.CUSTOMER_TYPE.AGENCY},
        ]

        this.listRatingScope = [
            {name: this.tranService.translate("ratingPlan.ratingScope.nativeWide"), value: CONSTANTS.RATING_PLAN_SCOPE.NATION_WIDE},
            {name: this.tranService.translate("ratingPlan.ratingScope.province"), value: CONSTANTS.RATING_PLAN_SCOPE.PROVINCE},
            {name: this.tranService.translate("ratingPlan.ratingScope.customer"), value: CONSTANTS.RATING_PLAN_SCOPE.CUSTOMER},
        ];

        this.ratingPlanInfo = {
            id:  null,
            code:  null,
            name:  null,
            status:  null,
            dispatchCode:  null,
            customerType:  null,
            subscriptionFee:  null,
            subscriptionType:  null,
            ratingScope:  null,
            cycleTimeUnit:  null,
            cycleInterval:  null,
            reload:  null,
            flat:  null,
            limitDataUsage: null,
            limitSmsOutside: null,
            limitSmsInside: null,
            flexible: null,
            feePerDataUnit: null,
            squeezedSpeed: null,
            feeSmsInside: null,
            feeSmsOutside: null,
            maximumFee: null,
            dataRoundUnit: null,
            downSpeed: null,
            provinceCode: null,
            description: null,
            dataMax: null,
            userIds: null,
        }

        this.searchInfo = {
            id: null,
            name: null,
            status: null,
            cycleTimeUnit: null,
            paidType: null,
            customerType: null,
            ratingScope: null
        }

        this.createSearchUserInfo();
        this.formSearchUser = this.formBuilder.group(this.searchInfoUser);
        this.formSearch = this.formBuilder.group(this.searchInfo);
        this.selectItems = [];
        this.pageNumber = 0;
        this.pageSize = 10;
        this.sort = "name,asc";

        this.selectItemsUser = [];

        this.optionTable = {
            hasClearSelected: true,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
            action: [
                {
                    icon: "pi pi-pencil",
                    tooltip: this.tranService.translate("global.button.edit"),
                    func: function(id, item){
                        me.router.navigate([`/plans/update/${id}`]);
                    },
                    funcAppear: function(id, item) {
                        return me.checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN.UPDATE]);
                    }
                },
                {
                    icon: "pi pi-trash",
                    tooltip: this.tranService.translate("global.button.delete"),
                    func: function(id, item){
                        me.messageCommonService.confirm(
                            me.tranService.translate("global.message.titleConfirmDeletePlan"),
                            me.tranService.translate("global.message.confirmDeletePlan"),
                            {
                                ok:()=>{
                                    me.ratingPlanService.deleteById(id,(response)=>{
                                        me.messageCommonService.success(me.tranService.translate("global.message.deleteSuccess"));
                                        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);
                                    })
                                },
                                cancel: ()=>{
                                    // me.messageCommonService.error(me.tranService.translate("global.message.deleteFail"));
                                }
                            }
                        )
                    },
                    funcAppear: function(id, item) {
                        return item.status != CONSTANTS.RATING_PLAN_STATUS.ACTIVATED && me.checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN.DELETE]);
                    }
                },
                {
                    icon: "pi pi-file",
                    tooltip: this.tranService.translate("global.button.assignPlan"),
                    func: function(id, item){
                        me.selectItemsUser = [];
                        me.pageNumberAssign = 0;
                        me.pageSizeAssign = 10;
                        me.sortAssign = "createdDate,asc";
                        const userInfo = JSON.parse(localStorage.getItem('userInfo'))
                        me.ratingPlanService.getById(id, (response)=>{
                            me.planSelected = response;
                            if(me.planSelected.ratingScope == CONSTANTS.RATING_PLAN_SCOPE.NATION_WIDE){
                                me.listProvince = [...me.listProvincOrigins];
                            }else{
                                if(userInfo.provinceCode){
                                    me.listProvince = userInfo.provinceCode
                                }else if(userInfo.type == 1){
                                    me.listProvince = me.listProvincOrigins.filter(el => response.provinceCode.includes(el.code));
                                }else{
                                    me.listProvince = []
                                }
                            }
                            me.ratingPlanService.getAllAccountPlanAssign(me.planSelected.id, (response)=>{
                                me.selectItemsUser = (response || []).map(el => {
                                    return {
                                        provinceCode: el.provinceCode,
                                        userId: el.userId,
                                        id: el.userId
                                    }
                                });
                                me.selectItemsUserOld = [...me.selectItemsUser]
                            })
                            me.searchUser(me.pageNumberAssign, me.pageSizeAssign, me.sortAssign, me.searchInfoUser)
                            me.isShowDialogAssignPlan = true;
                        })
                    },
                    funcAppear: function(id, item) {
                        return item.status == CONSTANTS.RATING_PLAN_STATUS.ACTIVATED && me.checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN.ISSUE])
                            && item.ratingScope ==  CONSTANTS.RATING_PLAN_SCOPE.CUSTOMER;
                    }
                }
            ]
        }
        this.optionTableAssignPLan = {
            hasClearSelected: false,
            hasShowIndex: true,
            hasShowChoose: true,
            hasShowToggleColumn: false,
        }
        this.columns = [
            {
                name: this.tranService.translate("ratingPlan.label.planCode"),
                key: "code",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: true,
            },
            {
                name: this.tranService.translate("ratingPlan.label.planName"),
                key: "name",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: true,
                style:{
                    cursor: "pointer",
                    color: "var(--mainColorText)",
                    display: 'inline-block',
                    maxWidth: '400px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                },
                isShowTooltip: true,
                funcClick(id, item) {
                    me.planId = id;
                    me.getDetailPLan();
                    me.checkSubscriptionType();
                    me.isShowModalDetail = true;
                },
            },
            {
                name: this.tranService.translate("ratingPlan.label.status"),
                key: "status",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: true,
                funcConvertText(value) {
                    if(value == CONSTANTS.RATING_PLAN_STATUS.CREATE_NEW){
                        return me.tranService.translate("ratingPlan.status.create");
                    }else if(value == CONSTANTS.RATING_PLAN_STATUS.PENDING){
                        return me.tranService.translate("ratingPlan.status.pending");
                    }else if(value == CONSTANTS.RATING_PLAN_STATUS.ACTIVATED){
                        return me.tranService.translate("ratingPlan.status.activated");
                    }else if(value == CONSTANTS.RATING_PLAN_STATUS.DEACTIVATED){
                        return me.tranService.translate("ratingPlan.status.deactivated");
                    }else{
                        return "";
                    }
                },
                funcGetClassname(value) {
                    if(value == CONSTANTS.RATING_PLAN_STATUS.CREATE_NEW){
                        return ['p-2', 'text-white', "bg-cyan-300", "border-round","inline-block"];
                    }else if(value == CONSTANTS.RATING_PLAN_STATUS.PENDING){
                        return ['p-2', 'text-white', "bg-red-500", "border-round","inline-block"];
                    }else if(value == CONSTANTS.RATING_PLAN_STATUS.ACTIVATED){
                        return ['p-2', 'text-white', "bg-green-500", "border-round","inline-block"];
                    }else if(value == CONSTANTS.RATING_PLAN_STATUS.DEACTIVATED){
                        return ['p-2', 'text-white' , "bg-orange-400", "border-round","inline-block"];
                    }else{
                        return [];
                    }
                },
            },
            {
                name: this.tranService.translate("ratingPlan.label.customerType"),
                key: "customerType",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: true,
                funcConvertText(value) {
                    if(value == CONSTANTS.CUSTOMER_TYPE.INTERPRISE){
                        return me.tranService.translate("ratingPlan.customerType.enterprise");
                    }else if(value == CONSTANTS.CUSTOMER_TYPE.PERSONAL){
                        return me.tranService.translate("ratingPlan.customerType.personal");
                    }else if(value == CONSTANTS.CUSTOMER_TYPE.AGENCY){
                        return me.tranService.translate("ratingPlan.customerType.agency");
                    }else{
                        return "";
                    }
                },
            },
            {
                name: this.tranService.translate("ratingPlan.label.paidType"),
                key: "paidType",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: true,
                funcConvertText(value) {
                    if(value == CONSTANTS.SUBSCRIPTION_TYPE.POSTPAID){
                        return me.tranService.translate("ratingPlan.subscriptionType.post");
                    }else if(value == CONSTANTS.SUBSCRIPTION_TYPE.PREPAID){
                        return me.tranService.translate("ratingPlan.subscriptionType.pre");
                    }else{
                        return "";
                    }
                },
            },
            {
                name: this.tranService.translate("ratingPlan.label.ratingScope"),
                key: "ratingScope",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: true,
                funcConvertText(value) {
                    if(value == CONSTANTS.RATING_PLAN_SCOPE.NATION_WIDE){
                        return me.tranService.translate("ratingPlan.ratingScope.nativeWide");
                    }else if(value == CONSTANTS.RATING_PLAN_SCOPE.CUSTOMER){
                        return me.tranService.translate("ratingPlan.ratingScope.customer");
                    }else if(value == CONSTANTS.RATING_PLAN_SCOPE.PROVINCE){
                        return me.tranService.translate("ratingPlan.ratingScope.province");
                    }else{
                        return "";
                    }
                },
            },
            {
                name: this.tranService.translate("ratingPlan.label.cycle"),
                key: "cycleTimeUnit",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: true,
                funcConvertText(value) {
                    if(value == CONSTANTS.RATING_PLAN_CYCLE.DAY){
                        return me.tranService.translate("ratingPlan.cycle.day");
                    }else if(value == CONSTANTS.RATING_PLAN_CYCLE.MONTH){
                        return me.tranService.translate("ratingPlan.cycle.month");
                    }else{
                        return "";
                    }
                },
            },
            {
                name: this.tranService.translate("ratingPlan.label.subscriptionFee"),
                key: "subscriptionFee",
                size: "150px",
                align: "right",
                isShow: true,
                isSort: true,
                funcConvertText(value){
                    return me.utilService.convertNumberToString(value);
                }
            },
            {
                name: this.tranService.translate("ratingPlan.label.limitDataUsage"),
                key: "limitDataUsage",
                size: "150px",
                align: "right",
                isShow: true,
                isSort: true,
                funcConvertText(value){
                    return me.utilService.convertNumberToString(value);
                }
            },
        ]

        this.dataSetAssignPlan = {
            content: [],
            total: 0
        }
        this.getListProvince()
        this.columnsInfoUser = [
            {
                name: this.tranService.translate("ratingPlan.label.username"),
                key: "username",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: true,
                style:{
                    cursor: "pointer",
             color: "var(--mainColorText)"
                },
                funcGetRouting(item) {
                    return [`/plans/detail/${item.id}`]
                },
            },
            {
                name: this.tranService.translate("ratingPlan.label.fullName"),
                key: "fullName",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: true,
            },
            {
                name: this.tranService.translate("ratingPlan.label.email"),
                key: "email",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: true,
            },
            {
                name: this.tranService.translate("ratingPlan.label.province"),
                key: "provinceCode",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: true,
                funcConvertText(value) {
                    for (let i = 0; i < me.listProvincOrigins.length; i++) {
                        if (value == me.listProvincOrigins[i].code){
                            return `${me.listProvincOrigins[i].name} (${me.listProvincOrigins[i].code})`;
                        }
                    }
                    return null
                }
            },
        ]
        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
    }

    ngAfterContentChecked(): void {
        if (this.isShowDialogAssignPlan == false){
            this.formSearchUser.reset();
        }
    }

    onSubmitSearch(){
        this.pageNumber = 0;
        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
    }

    onSubmitSearchUser(){
        this.pageNumberAssign = 0;
        this.searchUser(this.pageNumberAssign, this.pageSizeAssign, this.sortAssign, this.searchInfoUser);
    }

    createSearchUserInfo(){
        this.searchInfoUser = {
            username: null,
            fullName: null,
            email: null,
            provinceCode: null
        }
    }
    checkInValidAssignPlan(){
        if(this.selectItemsUser.length == 0 && this.selectItemsUserOld.length == 0){
            return true;
        }
        let flag = false;
        for(let i = 0;i<this.selectItemsUser.length;i++){
            if((this.selectItemsUser[i].provinceCode || "") == ""){
                flag = true;
                break;
            }
        }
        return flag;
    }

    assignPlan(){
        let me = this;
        let oldIds = this.selectItemsUserOld.map(el => el.id);
        let dataAdd = this.selectItemsUser.filter(el => !oldIds.includes(el.id)).map(el => {
            return {
                userId: el.id,
                provinceCode: el.provinceCode,
                type: 1
            }
        })
        let currentSelected = this.selectItemsUser.map(el => el.id);
        let dataRemove = this.selectItemsUserOld.filter(el => !currentSelected.includes(el.id)).map(el => {
            return {
                userId: el.id,
                provinceCode: el.provinceCode,
                type: -1
            }
        })
        let data = {
            planId: this.planSelected.id,
            data: [...dataAdd, ...dataRemove]
        }
        if(data.data.length <= 0){
            return;
        }
        me.ratingPlanService.assignPlan(data.planId, data.data, (response)=>{
            me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);
        })
        me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"));
        this.isShowDialogAssignPlan = false;
    }

    searchUser(page, limit, sort, params){
        let me = this;
        this.pageNumberAssign = page;
        this.pageSizeAssign = limit;
        this.sortAssign = sort;
        let dataParams = {
            page,
            size: limit,
            sort,
            type: CONSTANTS.USER_TYPE.CUSTOMER,
            ratingPlanId: this.planSelected.id,
        }

        Object.keys(this.searchInfoUser).forEach(key => {
            if(this.searchInfoUser[key] != null){
                dataParams[key] = this.searchInfoUser[key];
            }
        })
        if((dataParams["provinceCode"] || "").trim().length == 0){
            if(this.userType == CONSTANTS.USER_TYPE.ADMIN){
                if(this.planSelected.ratingScope == CONSTANTS.RATING_PLAN_SCOPE.NATION_WIDE){
                    dataParams["provinceCode"] = "";
                }else{
                    if(this.planSelected.provinceCode) {
                        dataParams["provinceCode"] = this.planSelected.provinceCode.toLocaleString();
                    }else{
                        dataParams["provinceCode"] = [];
                    }
                }
            }else{
                dataParams["provinceCode"] = this.sessionService.userInfo.provinceCode;
            }
        }


        // me.messageCommonService.onload();//checkfix
        this.ratingPlanService.searchUser(dataParams, (response)=>{
            me.dataSetAssignPlan = {
                content: response.content,
                total: response.totalElements
            }
        })
    }

    search(page, limit, sort, params){
        let me = this;
        this.pageNumber = page;
        this.pageSize = limit;
        this.sort = sort;
        let dataParams = {
            page,
            size: limit,
            sort
        }
        Object.keys(this.searchInfo).forEach(key => {
            if(this.searchInfo[key] != null){
                dataParams[key] = this.searchInfo[key];
            }
        })
        this.dataSet = {
            content: [],
            total: 0
        }
        me.messageCommonService.onload();
        this.ratingPlanService.search(dataParams, (response)=>{
            me.dataSet = {
                content: response.content,
                total: response.totalElements
            }
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }

    getListProvince(){
        let me = this;
        this.ratingPlanService.getListProvince((response)=>{
            this.listProvince = response.map(el => {
                if(el.code == me.sessionService.userInfo.provinceCode){
                    me.provinceInfo = `${el.name} - ${el.code}`
                }
                return {
                    ...el,
                    display: `${el.name} - ${el.code}`
                }
            })
            this.listProvincOrigins = [...this.listProvince];
        })
    };

    getDetailPLan(){
        let me = this;
        this.messageCommonService.onload();
        me.ratingPlanService.getById(Number(me.planId), (response)=>{
            me.response = response

            me.ratingPlanInfo.id = response.id
            me.ratingPlanInfo.code = response.code
            me.ratingPlanInfo.name = response.name
            me.ratingPlanInfo.status = response.status
            me.ratingPlanInfo.dispatchCode = response.dispatchCode
            me.ratingPlanInfo.customerType = response.customerType
            me.ratingPlanInfo.subscriptionFee = response.subscriptionFee
            me.ratingPlanInfo.subscriptionType = response.paidType
            me.ratingPlanInfo.ratingScope = response.ratingScope
            me.ratingPlanInfo.cycleTimeUnit = response.cycleTimeUnit
            me.ratingPlanInfo.cycleInterval = response.cycleInterval
            me.ratingPlanInfo.reload = response.reload
            me.ratingPlanInfo.flat = response.flat
            me.ratingPlanInfo.limitDataUsage = response.limitDataUsage
            me.ratingPlanInfo.limitSmsOutside = response.limitSmsOutside
            me.ratingPlanInfo.limitSmsInside = response.limitSmsInside
            me.ratingPlanInfo.flexible = response.flexible
            me.ratingPlanInfo.feePerDataUnit = response.feePerDataUnit
            me.ratingPlanInfo.squeezedSpeed = response.squeezedSpeed
            me.ratingPlanInfo.feeSmsInside = response.feeSmsInside
            me.ratingPlanInfo.feeSmsOutside = response.feeSmsOutside
            me.ratingPlanInfo.maximumFee = response.maximumFee
            me.ratingPlanInfo.dataRoundUnit = response.dataRoundUnit
            me.ratingPlanInfo.downSpeed = response.downSpeed
            me.ratingPlanInfo.provinceCode = response.provinceCode
            me.ratingPlanInfo.description = response.description;
            me.ratingPlanInfo.dataMax = response.dataMax;
            me.ratingPlanInfo.userIds = response.userIds;

            me.getReload(me.ratingPlanInfo.reload)
            me.getFlexible(me.ratingPlanInfo.flexible)
            me.myProvices = ""
            if (response.provinceCode != null){

                me.accountService.getListProvinceByCode(response.provinceCode,(data)=>{
                    me.provinces = data.map(el => {
                        return {
                            code: el.code,
                            name: `${el.name}`
                        }
                    })
                    me.provinces.forEach(el => {
                        if(me.ratingPlanInfo.provinceCode.includes(el.code)){
                            me.myProvices += `${el.name}, `;

                        }
                    })
                    if(me.myProvices.length > 0){
                        me.myProvices = me.myProvices.substring(0, me.myProvices.length - 2);
                    }
                })
            }
            me.accountService.getUserAssignedOnRatingPlan({ratingPlanId: response.id}, (resp) => {
                this.dataSetAssignPlanForDetail = {
                    content: resp,
                    total: resp ? resp.length : 0
                }
            })

        }, null, ()=>{
            me.messageCommonService.offload();
        })
    };

    getReload(value){
        if(value == CONSTANTS.RELOAD.YES){
            return this.checkedReload = true;
        }else if(value == CONSTANTS.RELOAD.NO){
            return this.checkedReload = false
        }
        return "";
    }

    getFlexible(value){
        if(value == CONSTANTS.FLEXIBLE.YES){
            return this.checkedFlexible = true;
        }else if(value == CONSTANTS.FLEXIBLE.NO){
            return this.checkedFlexible = false
        }
        return "";
    };

    getClassStatus(value){
        if(value == CONSTANTS.RATING_PLAN_STATUS.ACTIVATED){
            return ['p-2', "text-teal-800", "bg-teal-100","border-round","inline-block"];
        }else if(value == CONSTANTS.RATING_PLAN_STATUS.CREATE_NEW){
            return ['p-2', "text-primary-600", "bg-primary-100", "border-round","inline-block"];
        }else if(value == CONSTANTS.RATING_PLAN_STATUS.PENDING){
            return ['p-2', "text-red-700", "bg-red-100","border-round","inline-block"];
        }else if(value == CONSTANTS.RATING_PLAN_STATUS.DEACTIVATED){
            return ['p-2', "text-orange-700", "bg-orange-100", "border-round","inline-block"];
        }
        return [];
    };

    getNameCustomerType(value){
        if(value == CONSTANTS.CUSTOMER_TYPE.PERSONAL){
            return this.tranService.translate("ratingPlan.customerType.personal");
        }else if(value == CONSTANTS.CUSTOMER_TYPE.INTERPRISE){
            return this.tranService.translate("ratingPlan.customerType.enterprise");
        }else if(value == CONSTANTS.CUSTOMER_TYPE.AGENCY){
            return this.tranService.translate("ratingPlan.customerType.agency");
        }
        return "";
    };

    checkSubscriptionType(){
        this.subscriptionTypes = [{
            type: this.tranService.translate("ratingPlan.subscriptionType.post"),
            ip: CONSTANTS.SUBSCRIPTION_TYPE.POSTPAID,
        },
            {
                type: this.tranService.translate("ratingPlan.subscriptionType.pre"),
                ip: CONSTANTS.SUBSCRIPTION_TYPE.PREPAID,
            }]
    };

    getCycleTimeUnit(value){
        if(value == CONSTANTS.CYCLE_TIME_UNITS.DAY){
            return this.tranService.translate("ratingPlan.cycle.day");
        }else if(value == CONSTANTS.CYCLE_TIME_UNITS.MONTH){
            return this.tranService.translate("ratingPlan.cycle.month");
        }
        return "";
    };

    getRatingScope(value){
        if(value == CONSTANTS.RATING_PLAN_SCOPE.NATION_WIDE){
            return this.tranService.translate("ratingPlan.ratingScope.nativeWide");
        }else if(value == CONSTANTS.RATING_PLAN_SCOPE.CUSTOMER){
            return this.tranService.translate("ratingPlan.ratingScope.customer");
        }else if(value == CONSTANTS.RATING_PLAN_SCOPE.PROVINCE){
            return this.tranService.translate("ratingPlan.ratingScope.province");
        }
        return "";
    };

    getNameStatus(value){
        if(value == CONSTANTS.RATING_PLAN_STATUS.ACTIVATED){
            return this.tranService.translate("ratingPlan.status.activated");
        }else if(value == CONSTANTS.RATING_PLAN_STATUS.CREATE_NEW){
            return this.tranService.translate("ratingPlan.status.create");
        }else if(value == CONSTANTS.RATING_PLAN_STATUS.PENDING){
            return this.tranService.translate("ratingPlan.status.pending");
        }else if(value == CONSTANTS.RATING_PLAN_STATUS.DEACTIVATED){
            return this.tranService.translate("ratingPlan.status.deactivated");
        }
        return "";
    };

    active(){
        let me = this
        me.messageCommonService.confirm(
            me.tranService.translate("global.message.confirmActivePlan"),
            me.tranService.translate("global.message.titleConfirmActivePlan"),
            {
                ok:()=>{
                    me.ratingPlanService.activePlan(me.planId,(response)=>{
                    })
                    me.messageCommonService.success(me.tranService.translate("global.message.activeSuccess"));
                    window.location.reload();
                },
                cancel: ()=>{
                    // me.messageCommonService.error(me.tranService.translate("global.message.deleteFail"));
                }
            }
        )
    }
    approve(){
        let me = this
        me.messageCommonService.confirm(
            me.tranService.translate("global.message.confirmApprovePlan"),
            me.tranService.translate("global.message.titleConfirmApprovePlan"),
            {
                ok:()=>{
                    me.ratingPlanService.activePlan(me.planId,(response)=>{
                        // me.router.navigate(['/plans']);
                    })
                    me.messageCommonService.success(me.tranService.translate("global.message.approveSuccess"));
                    window.location.reload();
                },
                cancel: ()=>{
                    // me.messageCommonService.error(me.tranService.translate("global.message.deleteFail"));
                }
            }
        )
    }
    suspend(){
        let me = this
        me.messageCommonService.confirm(
            me.tranService.translate("global.message.confirmSuspendPlan"),
            me.tranService.translate("global.message.titleConfirmSuspendPlan"),
            {
                ok:()=>{
                    me.ratingPlanService.suspendPlan(me.planId,(response)=>{
                        // me.router.navigate(['/plans']);
                    })
                    me.messageCommonService.success(me.tranService.translate("global.message.suspendSuuccess"));
                    window.location.reload();
                },
                cancel: ()=>{
                    // me.messageCommonService.error(me.tranService.translate("global.message.deleteFail"));
                }
            }
        )
    }

    changeManageLevel(){

    }

    protected readonly CONSTANTS = CONSTANTS;

    openDialogAddCustomerAccount() {
        this.isShowDialogShowCustomerAccount = true
    }
}
