<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{tranService.translate("global.menu.alertList")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
<!--    <div class="col-5 flex flex-row justify-content-end align-items-center">-->
<!--    </div>-->
</div>

<p-card class="p-4" styleClass="responsive-form">
    <form action="" [formGroup]="formAlert" (submit)="onSubmitCreate()">
        <div class="p-3 pt-0 shadow-2 border-round-md m-1 flex p-fluid p-formgrid grid">
            <!-- ten canh bao -->
            <div class="col-4 flex flex-row justify-content-between align-items-center pb-0">
                <label htmlFor="name" style="width:90px">{{tranService.translate("alert.label.name")}}<span class="text-red-500">*</span></label>
                <div style="width: calc(100% - 90px)" class="relative">
                    <input class="w-full"
                           pInputText id="name"
                           [(ngModel)]="alertInfo.name"
                           formControlName="name"
                           [required]="true"
                           [maxLength]="255"
                           pattern="^[a-zA-ZÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠƯàáâãèéêìíòóôõùúăđĩũơưẠ-ỹ0-9 ._-]+$"
                           [placeholder]="tranService.translate('alert.text.inputName')"
                           (blur)="onNameBlur()"
                    />
                </div>
            </div>
            <!-- loai -->
            <div class="col-4 flex flex-row justify-content-between align-items-center pb-0">
                <label for="ruleCategory" style="width:90px">{{tranService.translate("alert.label.rule")}}<span class="text-red-500">*</span></label>
                <div style="width: calc(100% - 90px)">
                    <p-dropdown styleClass="w-full"
                                id="ruleCategory" [autoDisplayFirst]="false"
                                [(ngModel)]="alertInfo.ruleCategory"
                                [required]="true"
                                formControlName="ruleCategory"
                                [options]="ruleOptions"
                                optionLabel="name"
                                optionValue="value"
                                [placeholder]="tranService.translate('alert.text.rule')"
                    ></p-dropdown>
                </div>
            </div>
            <!-- dieu kien kich hoat -->
            <div class="col-4 flex flex-row justify-content-between align-items-center pb-0">
                <label for="eventType" style="width:90px">{{tranService.translate("alert.label.event")}}<span class="text-red-500">*</span></label>
                <div style="width: calc(100% - 90px)">
                    <vnpt-select *ngIf="alertInfo.ruleCategory == CONSTANTS.ALERT_RULE_CATEGORY.MANAGEMENT" styleClass="w-full"
                                 class="w-full"
                                 [control]="controlComboSelectEventType"
                                 [(value)]="alertInfo.eventType"
                                 paramKey="name"
                                 keyReturn="value"
                                 displayPattern="${name}"
                                 [options]="eventOptionManagement"
                                 (onchange)="onChangeEventOption($event)"
                                 [isFilterLocal]="true"
                                 [lazyLoad]="false"
                                 [isMultiChoice]="false"
                                 [placeholder]="tranService.translate('alert.text.eventType')"
                                 [showClear]="false"
                    ></vnpt-select>
                    <vnpt-select *ngIf="alertInfo.ruleCategory == CONSTANTS.ALERT_RULE_CATEGORY.MONITORING" styleClass="w-full"
                                 class="w-full"
                                 [control]="controlComboSelectEventType"
                                 [(value)]="alertInfo.eventType"
                                 paramKey="name"
                                 keyReturn="value"
                                 displayPattern="${name}"
                                 [options]="eventOptionMonitoring"
                                 (onchange)="onChangeEventOption($event)"
                                 [isFilterLocal]="true"
                                 [lazyLoad]="false"
                                 [isMultiChoice]="false"
                                 [placeholder]="tranService.translate('alert.text.eventType')"
                                 [showClear]="false"
                    ></vnpt-select>
                </div>
            </div>
            <div class="col-4 flex flex-row p-0 w-full" *ngIf="formAlert.controls.name.invalid || formAlert.controls.severity.invalid || formAlert.controls.statusSim.invalid || isAlertNameExisted">
                <div class="flex-1 py-0 col-4 flex flex-row justify-content-between align-items-center w-full" style="height: fit-content"
                     *ngIf="formAlert.controls.name.invalid || formAlert.controls.severity.invalid || formAlert.controls.statusSim.invalid || isAlertNameExisted">
                    <label htmlFor="name" style="width:90px; height: fit-content"></label>
                    <div style="width: calc(100% - 90px)">
                        <small class="text-red-500" *ngIf="formAlert.controls.name.dirty && formAlert.controls.name.errors?.required">{{tranService.translate("global.message.required")}}</small>
                        <small class="text-red-500" *ngIf="formAlert.controls.name.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:255})}}</small>
                        <small class="text-red-500" *ngIf="formAlert.controls.name.errors?.pattern">{{tranService.translate("global.message.wrongFormatName")}}</small>
                        <small class="text-red-500" *ngIf="isAlertNameExisted">{{tranService.translate("global.message.exists",{type: tranService.translate("alert.label.name").toLowerCase()})}}</small>
                    </div>
                </div>

                <div class="flex-1 py-0 col-4 flex flex-row justify-content-between align-items-center w-full" style="height: fit-content"
                     *ngIf="formAlert.controls.name.invalid || formAlert.controls.severity.invalid || formAlert.controls.statusSim.invalid || isAlertNameExisted">
                    <label htmlFor="severity" style="width:90px; height: fit-content"></label>
                    <div style="width: calc(100% - 90px)">
                        <small class="text-red-500" *ngIf="formAlert.controls.severity.dirty && formAlert.controls.severity.errors?.required">{{tranService.translate("global.message.required")}}</small>
                    </div>
                </div>

                <div class="flex-1 py-0 col-4 flex flex-row justify-content-between align-items-center w-full" style="height: fit-content"
                     *ngIf="formAlert.controls.name.invalid || formAlert.controls.severity.invalid || formAlert.controls.statusSim.invalid || isAlertNameExisted">
                    <label htmlFor="statusSim"  style="width:150px; height: fit-content"></label>
                    <div style="width: calc(100% - 150px)">
                        <small class="text-red-500" *ngIf="formAlert.controls.statusSim.dirty && formAlert.controls.statusSim.errors?.required">{{tranService.translate("global.message.required")}}</small>
                    </div>
                </div>
            </div>
            <!-- muc do -->
            <div class="col-4 flex flex-row justify-content-between align-items-center pb-0 pt-3">
                <label for="severity" style="width:90px">{{tranService.translate("alert.label.level")}}<span class="text-red-500">*</span></label>
                <div style="width: calc(100% - 90px)">
                    <p-dropdown styleClass="w-full"
                                id="severity" [autoDisplayFirst]="false"
                                [(ngModel)]="alertInfo.severity"
                                [required]="true"
                                formControlName="severity"
                                [options]="severityOptions"
                                optionLabel="name"
                                optionValue="value"
                                [placeholder]="tranService.translate('alert.text.inputlevel')"
                    ></p-dropdown>
                </div>
            </div>
            <div class="col-4 pb-0 pt-0 flex flex-row justify-content-between align-items-center text-error-field" style="height: fit-content">
            </div>
            <div class="col-4 flex flex-row p-0 w-full" *ngIf="formAlert.controls.name.invalid || formAlert.controls.severity.invalid || formAlert.controls.statusSim.invalid || isAlertNameExisted">
                <!-- error muc do -->
                <div class="flex-1 py-0 col-4 flex flex-row justify-content-between align-items-center w-full" style="height: fit-content"
                     *ngIf="formAlert.controls.name.invalid || formAlert.controls.severity.invalid || formAlert.controls.statusSim.invalid || isAlertNameExisted">
                    <label htmlFor="severity" style="width:90px; height: fit-content"></label>
                    <div style="width: calc(100% - 90px)">
                        <small class="text-red-500" *ngIf="formAlert.controls.severity.dirty && formAlert.controls.severity.errors?.required">{{tranService.translate("global.message.required")}}</small>
                    </div>
                </div>
            </div>
            <!-- mo ta -->
            <div class="col-8 flex flex-row justify-content-between align-items-center pt-3">
                <label htmlFor="description" style="width:90px">{{tranService.translate("alert.label.description")}}</label>
                <div style="width: calc(100% - 90px)">
                    <input class="w-full input-full-v3"
                           pInputText id="description"
                           [(ngModel)]="alertInfo.description"
                           formControlName="description"
                           [maxLength]="255"
                           [placeholder]="tranService.translate('alert.text.inputDescription')"
                    />
                </div>
            </div>

        </div>

        <h4 class="ml-2">{{tranService.translate("alert.text.filterApplieInfo")}}</h4>
        <div *ngIf="alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP  && alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD" class="p-3 pt-0 shadow-2 border-round-md m-1 flex p-fluid p-formgrid grid">
            <!-- khach hang -->
            <div class="col-4 flex flex-row justify-content-between align-items-center pb-0">
                <label for="customerId"  style="width:130px">{{tranService.translate("alert.label.customer")}}</label>
                <div style="width: calc(100% - 130px)">{{alertResponse.customerName + ' - ' + alertResponse.customerCode}}
<!--                    <vnpt-select-->
<!--                            [control]="comboSelectCustomerControl"-->
<!--                            class="w-full"-->
<!--                            [(value)]="alertInfo.customerId"-->
<!--                            [placeholder]="tranService.translate('alert.text.inputCustomer')"-->
<!--                            objectKey="customer"-->
<!--                            paramKey="keyword"-->
<!--                            keyReturn="id"-->
<!--                            displayPattern="${customerName} - ${customerCode}"-->
<!--                            typeValue="object"-->
<!--                            [isMultiChoice]="false"-->
<!--                            (onchange)="filerGroupByCustomerOrContractCode($event)"-->
<!--                            [disabled]="true"-->
<!--                            [required]="true"-->
<!--                    ></vnpt-select>-->
                </div>
            </div>
<!--            mã hợp đồng-->
            <div *ngIf="(alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP && alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD)" class="col-4 flex flex-row justify-content-between align-items-center pb-0">
                <label for="contractCode"  style="width:130px">{{tranService.translate("alert.label.contractCode")}}</label>
                <div style="width: calc(100% - 130px)">{{alertResponse.contractCode}}
                    <!--<vnpt-select
                        [control]="comboSelectContracCodeControl"
                        class="w-full"
                        [(value)]="alertInfo.contractCode"
                        [placeholder]="tranService.translate('alert.text.inputContractCode')"
                        objectKey="contract"
                        paramKey="contractCode"
                        keyReturn="contractCode"
                        displayPattern="${contractCode}"
                        typeValue="object"
                        [paramDefault]="paramSearchContract"
                        [isMultiChoice]="false"
                        (onchange)="filerGroupByCustomer($event)"
                        [required]="true"
                        [disabled]="true"
                    ></vnpt-select>-->
                </div>
            </div>

            <!-- nhom thue bao -->
            <div class="col-4 flex flex-row justify-content-between align-items-center pb-0">
                <label for="groupId" style="width:130px">{{tranService.translate("alert.label.group")}}<span class="text-red-500">*</span></label>
                <div style="width: calc(100% - 130px)">
                    <vnpt-select
                            [control]="comboSelectSubControl"
                            class="w-full"
                            [(value)]="alertInfo.groupId"
                            [placeholder]="tranService.translate('alert.text.inputGroup')"
                            objectKey="groupSim"
                            paramKey="name"
                            keyReturn="id"
                            displayPattern="${name} - ${groupKey}"
                            typeValue="primitive"
                            [isMultiChoice]="false"
                            [paramDefault]="paramSearchGroupSim"
                            [required]="alertInfo.subscriptionNumber == null"
                            [disabled]="alertInfo.customerId == null"
                            (onchange)="checkChange($event)"
                    ></vnpt-select>
                </div>
            </div>

            <div class="col-4 flex flex-row p-0 w-full" *ngIf="comboSelectCustomerControl.error.required || comboSelectSubControl.error.required || comboSelectGroupSubControl.error.required">
                <!-- error khach hang -->
                <div class="flex-1 py-0 col-4 flex flex-row justify-content-between align-items-center w-full" style="height: fit-content">
                    <label htmlFor="customerId" class="col-fixed py-0" style="width:130px"></label>
                    <div style="width: calc(100% - 130px)" class="py-0">
                        <small class="text-red-500" *ngIf="comboSelectCustomerControl.dirty && comboSelectCustomerControl.error.required">{{tranService.translate("global.message.required")}}</small>
                    </div>
                </div>
                <!-- error mã hợp đồng -->
                <div class="flex-1 py-0 col-4 flex flex-row justify-content-between align-items-center w-full" style="height: fit-content">
                    <label htmlFor="customerId" class="col-fixed py-0" style="width:130px"></label>
                    <div style="width: calc(100% - 130px)" class="py-0">
                        <small class="text-red-500" *ngIf="comboSelectContracCodeControl.dirty && comboSelectContracCodeControl.error.required">{{tranService.translate("global.message.required")}}</small>
                    </div>
                </div>
                <!-- error nhom thue bao -->
                <div class="flex-1 py-0 col-4 flex flex-row justify-content-between align-items-center w-full" style="height: fit-content">
                    <label htmlFor="groupId" class="col-fixed p-0" style="width:130px"></label>
                    <div style="width: calc(100% - 130px)" class="py-0">
                        <small class="text-red-500" *ngIf="comboSelectSubControl.dirty && comboSelectSubControl.error.required">{{tranService.translate("global.message.required")}}</small>
                    </div>
                </div>

            </div>

            <!--so thue bao -->
            <div class="col-4 flex flex-row justify-content-between align-items-center pb-0">
                <label for="subscriptionNumber" style="width:130px">{{tranService.translate("alert.label.subscriptionNumber")}}<span class="text-red-500">*</span></label>
                <div style="width: calc(100% - 130px)">
                    <vnpt-select
                        [control]="comboSelectGroupSubControl"
                        class="w-full"
                        [(value)]="alertInfo.subscriptionNumber"
                        [placeholder]="tranService.translate('alert.text.inputSubscriptionNumber')"
                        objectKey="sim"
                        paramKey="msisdn"
                        keyReturn="msisdn"
                        displayPattern="${msisdn}"
                        typeValue="primitive"
                        [isMultiChoice]="false"
                        [paramDefault]="paramSearchSim"
                        [required]="alertInfo.groupId == null"
                        [disabled]="alertInfo.customerId == null"
                        (onchange)="checkChange($event)"
                    ></vnpt-select>
                </div>
            </div>
            <!-- gia tri -->
            <div class="col-4 flex flex-row gap-3 justify-content-start pb-0"
                 [class]="alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_VALUE ||
            alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_PACKAGE ||
            alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_VALUE ||
            alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_PACKAGE? '' : 'hidden'" >
                <label *ngIf="alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_PACKAGE" style="height: fit-content; margin-top: 8px" for="value">{{tranService.translate("alert.label.exceededPakage")}}<span class="text-red-500">*</span></label>
                <label *ngIf="alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_VALUE" style="height: fit-content; margin-top: 8px" for="value">{{tranService.translate("alert.label.exceededValue")}}<span class="text-red-500">*</span></label>
                <label *ngIf="alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_PACKAGE" style="height: fit-content; margin-top: 8px" for="value">{{tranService.translate("alert.label.smsExceededPakage")}}<span class="text-red-500">*</span></label>
                <label *ngIf="alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_VALUE" style="height: fit-content; margin-top: 8px" for="value">{{tranService.translate("alert.label.smsExceededValue")}}<span class="text-red-500">*</span></label>
                <div style="width: 150px">
                    <input pInputText styleClass="w-full" type="number"
                           id="value"
                           [(ngModel)]="alertInfo.value"
                           [required]="checkRequiredOutLine()"
                           (keydown)="checkValidValue($event)"
                           [min]="1"
                           [max]="checkRequiredLength()"
                           formControlName="value">
                    <div>
                        <small class="text-red-500" *ngIf="formAlert.controls.value.dirty && formAlert.controls.value.errors?.required">{{tranService.translate("global.message.required")}}</small>
                        <small [class]="alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_PACKAGE || alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_PACKAGE ? 'hidden': ''"  class="text-red-500" *ngIf="formAlert.controls.value.dirty && formAlert.controls.value.errors?.max">{{tranService.translate("global.message.twentydigitlength")}}</small>
                        <small [class]="alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.EXCEEDED_VALUE || alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.SMS_EXCEEDED_VALUE  ? 'hidden': ''" class="text-red-500" *ngIf="formAlert.controls.value.dirty && formAlert.controls.value.errors?.max">{{tranService.translate("global.message.oneHundredLength")}}</small>
                        <small class="text-red-500" *ngIf="formAlert.controls.value.dirty && formAlert.controls.value.errors?.min">{{tranService.translate("global.message.onlyPositiveInteger")}}</small>
                    </div>
                </div>
            </div>
            <div class="col-4 flex flex-row justify-content-between align-items-center pb-0"></div>
            <!-- error so thue bao -->
            <div class="flex-1 py-0 col-4 flex flex-row justify-content-between align-items-center w-full" style="height: fit-content">
                <label htmlFor="subscriptionNumber" class="col-fixed p-0" style="width:130px"></label>
                <div style="width: calc(100% - 130px)" class="py-0">
                    <small class="text-red-500" *ngIf="comboSelectGroupSubControl.dirty && comboSelectGroupSubControl.error.required">{{tranService.translate("global.message.required")}}</small>
                </div>
            </div>
        </div>

        <div *ngIf="alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP" class="pb-3 shadow-2 border-round-md m-1 flex p-fluid p-formgrid grid">
            <!-- goi cuoc dang dung -->
            <div class="col-4 pb-0 flex flex-row justify-content-between align-items-center">
                <label for="appliedPlan"  style="width:150px">{{tranService.translate("alert.label.appliedPlan")}}<span class="text-red-500">*</span></label>
                <div style="width: calc(100% - 150px)">
                    <p-multiSelect styleClass="w-full"
                                id="appliedPlan" [autoDisplayFirst]="false"
                                [(ngModel)]="alertInfo.appliedPlan"
                                formControlName="appliedPlan"
                                [options]="appliedPlanOptions"
                                [filter]="true"
                                filterBy="code"
                                [placeholder]="tranService.translate('alert.text.appliedPlan')"
                                optionLabel="code"
                                optionValue="code"
                                [required]="true"
                                [emptyFilterMessage]="tranService.translate('global.text.nodata')"
                    ></p-multiSelect>
                    <small class="text-red-500" *ngIf="isPlanExisted">{{tranService.translate("alert.message.existedPlan")}}</small>
                    <small class="text-red-500" *ngIf="formAlert.controls.appliedPlan.dirty && formAlert.controls.appliedPlan.errors?.required">{{tranService.translate("global.message.required")}}</small>
                </div>
            </div>
        </div>
        <div
            *ngIf="alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD">
            <div class="pb-3 shadow-2 border-round-md m-1 flex p-fluid p-formgrid grid">
                <!-- Ví áp dụng -->
                <div class="col-4 pb-0 flex flex-row justify-content-between">
                    <label class="mt-2" for="subCode"
                           style="width:200px">{{ tranService.translate("alert.label.wallet") }}<span
                        class="text-red-500">*</span></label>
                    <div style="width: calc(100% - 200px)">
                        <vnpt-select
                            id="subCode"
                            [control]="controlComboSelectWallet"
                            [(value)]="alertInfo.walletSubCode"
                            class="w-full"
                            [placeholder]="tranService.translate('alert.label.wallet')"
                            objectKey="walletToAlert"
                            paramKey="subCode"
                            [paramDefault]="paramUpdateSubCode"
                            keyReturn="subCode"
                            displayPattern="${subCode} - ${packageCode}"
                            typeValue="primitive"
                            [required]="true"
                            [showTextRequired]
                            [isMultiChoice] = "false"
                            (onchange)="changeWalletSubCode($event)"
                        ></vnpt-select>
                        <!-- Thông báo lỗi -->
                        <small *ngIf="controlComboSelectWallet.dirty && controlComboSelectWallet.error.required"
                               class="text-red-500">{{tranService.translate("global.message.required")}}</small>
                    </div>
                </div>
                <div class="col-1"></div>
                <!-- Giá trị ngưỡng -->
                <div class="col-4 pb-0 flex flex-row justify-content-between">
                    <label class="mt-2" for="walletValue"
                           style="width:200px">{{ tranService.translate("alert.label.thresholdValue") }}<span
                        class="text-red-500">*</span></label>
                    <div style="width: calc(100% - 200px)">
                        <input pInputText type="number"
                               id="walletValue"
                               [(ngModel)]="alertInfo.value"
                               [required]="true"
                               (keydown)="checkValidValue($event)"
                               [min]="1"
                               [max]="checkRequiredLength()"
                               formControlName="value"
                               class="w-full">
                        <!-- Thông báo lỗi -->
                        <small class="text-red-500 block" *ngIf="formAlert.controls.value.dirty && formAlert.controls?.value.errors?.required">{{tranService.translate("global.message.required")}}</small>
                        <small class="text-red-500 block" *ngIf="formAlert.controls.value.dirty && formAlert.controls?.value.errors?.min">{{tranService.translate("global.message.required")}}</small>
                        <small class="text-red-500 block" *ngIf="(alertInfo.unit == CONSTANTS.ALERT_UNIT.SMS || alertInfo.unit == CONSTANTS.ALERT_UNIT.MB) && formAlert.controls.value.dirty && formAlert.controls?.value.errors?.max">{{tranService.translate("global.message.twentydigitlength")}}</small>
                        <small class="text-red-500 block" *ngIf="alertInfo.unit == CONSTANTS.ALERT_UNIT.PERCENT && formAlert.controls.value.dirty && formAlert.controls?.value.errors?.max">{{tranService.translate("global.message.oneHundredLength")}}</small>
                    </div>
                </div>

                <!-- Đơn vị -->
                <div class="col-2 pb-0 flex flex-row justify-content-between">
                    <p-dropdown
                        id="unit"
                        [options]="unitWalletOptions"
                        optionLabel="label"
                        optionValue="value"
                        [(ngModel)]="alertInfo.unit"
                        formControlName="unit"
                        [readonly]="disableUnit"
                    />
                </div>

                <!-- Email và Số điện thoại -->
                <div class="col-4 pb-0 flex flex-row justify-content-between">
                    <label class="mt-2">{{ tranService.translate("alert.label.walletEmail") }}</label>
                    <span class="mt-2" style="width: calc(100% - 200px)">{{ alertInfo.emailList }}</span>
                </div>
                <div class="col-1"></div>
                <div class="col-4 pb-0 flex flex-row justify-content-between">
                    <label class="mt-2">{{ tranService.translate("alert.label.walletPhone") }}</label>
                    <span class="mt-2" style="width: calc(100% - 200px)">{{ alertInfo.smsList }}</span>
                </div>
            </div>
        </div>

        <div class="ml-2 my-4 flex flex-row justify-content-start align-items-center gap-3">
            <h4 for="actionType" class="mb-0">{{tranService.translate("alert.label.action")}}</h4>
            <div>
                <p-dropdown styleClass="w-full"
                            id="actionType" [autoDisplayFirst]="false"
                            [(ngModel)]="alertInfo.actionType"
                            [required]="true"
                            formControlName="actionType"
                            (onChange)="onChangeActionType()"
                            [options]="actionOptions"
                            optionLabel="name"
                            optionValue="value"
                            [disabled]="alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD"
                            [placeholder]="tranService.translate('alert.text.actionType')"
                ></p-dropdown>
            </div>
        </div>
        <div [class]="alertInfo.actionType == CONSTANTS.ALERT_ACTION_TYPE.ALERT ? '' : 'hidden'" class="pt-0 shadow-2 border-round-md m-1 flex flex-column p-fluid p-formgrid grid">
            <div class="flex flex-row gap-4">
                <div class="flex-1">
                    <div *ngIf="alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP" class="col-12 flex flex-row justify-content-start align-items-center pt-4 pr-4">
                        <label class="col-fixed" htmlFor="value">{{tranService.translate("alert.text.sendNotifyExpiredData")}}</label>
                        <div>
                            <input  class="w-full" style="resize: none;"
                                    rows="5"
                                    pInputText
                                    [autoResize]="false"
                                    pInputTextarea id="value"
                                    [(ngModel)]="alertInfo.value"
                                    (keydown)="checkValidValueNotify($event)"
                                    (ngModelChange)="checkChangeValueNotify()"
                                    formControlName="value"
                                    type="number"
                                    [defaultValue]="1"
                                    [min]="1"
                                    [max]="99"
                            />
                        </div>
                        <label class="col-fixed" htmlFor="value">{{tranService.translate("alert.text.day")}}</label>
                    </div>
                </div>
                <div class="flex-1" *ngIf="alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP">
                    <div class="col-12 flex flex-row pb-0">
                        <div class="col-fixed pr-0 mr-0" style="margin-top: 7px;">
                            <p-checkbox
                                    [(ngModel)]="repeat"
                                    formControlName="notifyRepeat"
                                    (ngModelChange)="onChangeNotify()"
                                    [binary]="true"
                                    inputId="binary" />
                        </div>
                        <label class="col-fixed" htmlFor="notifyRepeat" style="margin-top: 7px;">{{tranService.translate("alert.label.repeat")}}</label>
                        <label class="col-fixed" [style.color]="!repeat ? '#a1a1a1' : '#495057'"style="margin-top: 7px;" htmlFor="notifyInterval">{{tranService.translate("alert.label.frequency")}}</label>
                        <div class="col pl-0 pr-0" style="padding-right: 8px;">
                            <input class="w-full"
                                   pInputText id="notifyInterval"
                                   [(ngModel)]="alertInfo.notifyInterval"
                                   formControlName="notifyInterval"
                                   type="number"
                                   (keydown)="checkValidNotifyRepeat($event)"
                                   [defaultValue]="1"
                                   [min]="1"
                                   [max]="99"
                                   [required]="true"
                            />
                            <small class="text-red-500 block" *ngIf="formAlert.controls.notifyInterval.dirty && formAlert.controls?.notifyInterval.errors?.required">{{tranService.translate("global.message.required")}}</small>
                        </div>
                        <label class="col-fixed" [style.color]="!repeat ? '#a1a1a1' : '#495057'" for="notifyInterval">{{tranService.translate('alert.text.day')}}</label>
                    </div>
                </div>
            </div>

            <div [class]="alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP && alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD ? '' : 'hidden'" class="flex flex-row">
                <div style="width: 50px">
                    <div class="col px-4 py-5">
<!--                        <p-checkbox-->
<!--                                [(ngModel)]="alertInfo.typeAlert"-->
<!--                                name="Group"-->
<!--                                formControlName="typeAlert"-->
<!--                                value="Group"-->
<!--                                (onChange)="onChangeCheckBox()"-->
<!--                                [required]="alertInfo.actionType == CONSTANTS.ALERT_ACTION_TYPE.ALERT && alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP && alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD "-->
<!--                        ></p-checkbox>-->
                    </div>
                </div>
                <div class="flex-1">
                    <!-- nhom nhan canh bao-->
                    <div class="col-12 flex flex-row justify-content-start align-items-center pb-0 group-alert-div">
                        <label for="listAlertReceivingGroupId" class="col-fixed" style="width:180px">{{tranService.translate("alert.label.groupReceiving")}}<span class="text-red-500"></span></label>
                        <div class="col pl-0 pr-0 pb-0 alert-select">
                            <vnpt-select
                                    class="w-full"
                                    [(value)]="alertInfo.listAlertReceivingGroupId"
                                    [control]="controlAlertReceiving"
                                    [placeholder]="tranService.translate('alert.text.inputgroupReceiving')"
                                    objectKey="receivingGroupAlert"
                                    paramKey="name"
                                    keyReturn="id"
                                    displayPattern="${name}"
                                    typeValue="primitive"
                                    [required]="!isDisableReceiveGroup"
                                    [disabled]="true"
                            ></vnpt-select>
                        </div>
                    </div>
                    <!-- error nhom nhan canh bao-->
                    <div class="field grid px-4 flex flex-row flex-nowrap pb-2">
                        <label htmlFor="groupId" class="col-fixed" style="width:180px"></label>
                        <small class="text-red-500 block" *ngIf="controlAlertReceiving.dirty && controlAlertReceiving?.error?.required">{{tranService.translate("global.message.required")}}</small>
                    </div>
                </div>
                <div style="width: 50px;">

                </div>
                <div class="flex-1">

                </div>
            </div>

            <div [class]="alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP && alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD ? '' : 'hidden'" class="flex flex-row">
                <div class="alert-checkbox-email" style="width: 50px">
                    <div class="col px-4 py-5">
                        <p-checkbox
                                [(ngModel)]="alertInfo.typeAlert"
                                name="Email"
                                formControlName="typeAlert"
                                value="Email"
                                (onChange)="onChangeCheckBox()"
                                [required]="alertInfo.actionType == CONSTANTS.ALERT_ACTION_TYPE.ALERT && alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP && alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD "
                        />
                    </div>
                </div>
                <div class="flex-1">
                    <!-- email -->
                    <div class="col-12 flex flex-row justify-content-start pb-0 alert-creation-div">
                        <label class="col-fixed" htmlFor="emailList" style="width:180px; height: fit-content;">{{tranService.translate("alert.label.emails")}}<span class="text-red-500">*</span></label>
                        <div style="width: calc(100% - 180px)">
                            <textarea  class="w-full" style="resize: none;"
                                       rows="5"
                                       [autoResize]="false"
                                       pInputTextarea id="emailList"
                                       [(ngModel)]="alertInfo.emailList"
                                       formControlName="emailList"
                                       [placeholder]="tranService.translate('alert.text.inputemails')"
                                       pattern="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(?:, ?[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})*$"
                                       [required]="true"
                            ></textarea>
                        </div>
                    </div>
                    <!-- emailList-->
                    <div class="field grid px-4 flex flex-row flex-nowrap pb-2 alert-error">
                        <label htmlFor="emailList" class="col-fixed" style="width:180px"></label>
                        <div class="alert-error-email">
                            <small class="text-red-500 block" *ngIf="formAlert.controls.emailList.dirty && formAlert.controls.emailList.errors?.required">{{tranService.translate("global.message.required")}}</small>
                            <small class="text-red-500 block" *ngIf="formAlert.controls.emailList.dirty && checkExistEmailList()">{{tranService.translate("global.message.emailExist")}}</small>
                            <small class="text-red-500 block" *ngIf="formAlert.controls.emailList.dirty && check50Email()">{{tranService.translate("global.message.max50Emails")}}</small>
                            <small class="text-red-500 block" *ngIf="formAlert.controls.emailList.errors?.pattern">{{tranService.translate("global.message.formatEmail")}}</small>
                        </div>
                    </div>
                </div>
                <div class="alert-checkbox-sms" style="width: 50px">
                    <div class="col px-4 py-5">
                        <p-checkbox
                                [(ngModel)]="alertInfo.typeAlert"
                                name="SMS"
                                formControlName="typeAlert"
                                value="SMS"
                                (onChange)="onChangeCheckBox()"
                                [required]="alertInfo.actionType == CONSTANTS.ALERT_ACTION_TYPE.ALERT && alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP && alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD">
                        </p-checkbox>
                    </div>
                </div>
                <div class="flex-1">
                    <!-- sms -->
                    <div class="col-12 flex flex-row justify-content-start pb-0 alert-creation-div">
                        <label class="col-fixed sms-label" htmlFor="smsList" style="width:180px; height: fit-content;">{{tranService.translate("alert.label.sms")}}<span class="text-red-500">*</span></label>
                        <div style="width: calc(100% - 150px)">
                            <textarea  class="w-full" style="resize: none;"
                                       rows="5"
                                       [autoResize]="false"
                                       pInputTextarea id="smsList"
                                       [(ngModel)]="alertInfo.smsList"
                                       formControlName="smsList"
                                       [placeholder]="tranService.translate('alert.text.inputsms')"
                                       pattern="^(?:0|84)\d{9,10}(?:, ?(?:0|84)\d{9,10})*$"
                                       [required]="true"
                            ></textarea>
                        </div>
                    </div>
                    <!-- smsList-->
                    <div class="field grid px-4 flex flex-row flex-nowrap pb-2 alert-error">
                        <label htmlFor="smsList" class="col-fixed" style="width:180px"></label>
                        <div class="alert-error-sms">
                            <small class="text-red-500 block" *ngIf="formAlert.controls.smsList.dirty && formAlert.controls.smsList.errors?.required">{{tranService.translate("global.message.required")}}</small>
                            <small class="text-red-500 block" *ngIf="formAlert.controls.smsList.dirty && checkExistSmsList()">{{tranService.translate("global.message.phoneExist")}}</small>
                            <small class="text-red-500 block" *ngIf="formAlert.controls.smsList.dirty && check50Sms()">{{tranService.translate("global.message.max50Sms")}}</small>
                            <small class="text-red-500 block sms-error" *ngIf="formAlert.controls.smsList.errors?.pattern">{{tranService.translate("global.message.formatPhone")}}</small>                        </div>
                    </div>
                </div>

            </div>

            <div [class]="alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP && alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD ? '' : 'hidden'" class="flex flex-row">
                <div style="width: 50px">

                </div>
                <div class="flex-1 alert-email-content">
                    <!-- noi dung email -->
                    <div class="col-12 flex flex-row justify-content-start pb-0 alert-creation-div-content">
                        <label class="col-fixed" htmlFor="emailContent" style="width:180px; height: fit-content;">{{tranService.translate("alert.label.contentEmail")}}<span class="text-red-500">*</span></label>
                        <div style="width: calc(100% - 180px);">
                            <textarea  class="w-full" style="resize: none;"
                                       rows="5"
                                       [autoResize]="false"
                                       pInputTextarea id="emailContent"
                                       [(ngModel)]="alertInfo.emailContent"
                                       formControlName="emailContent"
                                       [maxlength]="255"
                                       [placeholder]="tranService.translate('alert.text.inputcontentEmail')"
                                       [required]="alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP && alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD"
                            ></textarea>
                            <div class="field alert-content-error" *ngIf="formAlert.controls.emailContent.dirty && formAlert.controls.emailContent.errors?.required">
                                <small class="text-red-500" *ngIf="formAlert.controls.emailContent.dirty && formAlert.controls.emailContent.errors?.required">{{tranService.translate("global.message.required")}}</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="alert-hide-div" style="width: 50px">

                </div>
                <div class="flex-1 alert-sms-content">
                    <!-- noi dung sms -->
                    <div class="col-12 flex flex-row pb-0 alert-creation-div-content">
                        <label class="col-fixed" htmlFor="smsContent" style="width:180px; height: fit-content;">{{tranService.translate("alert.label.contentSms")}}<span class="text-red-500">*</span></label>
                        <div style="width: calc(100% - 180px);">
                            <textarea  class="w-full" style="resize: none;"
                                       rows="5"
                                       [autoResize]="false"
                                       pInputTextarea id="smsContent"
                                       [(ngModel)]="alertInfo.smsContent"
                                       formControlName="smsContent"
                                       [maxlength]="255"
                                       [placeholder]="tranService.translate('alert.text.inputcontentSms')"
                                       [required]="alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP && alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD"
                            ></textarea>
                            <!-- error noi dung sms -->
                            <div class="field alert-content-error"
                                 *ngIf="formAlert.controls.smsContent.dirty && formAlert.controls.smsContent.errors?.required">
                                <small class="text-red-500" *ngIf="formAlert.controls.smsContent.dirty && formAlert.controls.smsContent.errors?.required">{{tranService.translate("global.message.required")}}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--            error checkbox-->
            <div class="col" *ngIf="alertInfo.actionType == CONSTANTS.ALERT_ACTION_TYPE.ALERT && alertInfo.eventType != CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP">
                <small class="text-red-500" *ngIf="formAlert.controls.typeAlert.dirty && formAlert.controls.typeAlert.errors?.required">{{tranService.translate("alert.message.checkboxRequired")}}</small>
            </div>

            <div *ngIf="alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP || alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD" class="flex flex-row gap-4 p-5 pt-0">
                <div class="text-xl font-bold">{{tranService.translate("alert.text.sendType")}}</div>
            </div>

            <div *ngIf="alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.DATAPOOL_EXP || alertInfo.eventType == CONSTANTS.ALERT_EVENT_TYPE.WALLET_THRESHOLD" class="flex flex-row gap-4 p-5 pt-0">
                <div class="flex-1 flex justify-content-center">
                    <p-checkbox
                            [binary]="true"
                            inputId="binary"
                            formControlName="sendTypeEmail"/>
                    <div>&nbsp;Email</div>
                </div>
                <div class="flex-1 flex justify-content-center">
                    <p-checkbox
                            [binary]="true"
                            inputId="binary"
                            formControlName="sendTypeSMS" />
                    <div>&nbsp;SMS</div>
                </div>
            </div>
        </div>

        <div [class]="alertInfo.actionType == CONSTANTS.ALERT_ACTION_TYPE.API ? '' : 'hidden'" class="pt-0 pb-2 shadow-2 border-round-md m-1 flex p-fluid p-formgrid grid">
            <div class="flex-1">
                <!-- url -->
                <div class="field  px-4 pt-4  flex-row ">
                    <div class="col-12 flex flex-row justify-content-between align-items-center pb-0">
                        <label htmlFor="url" style="width:90px">{{tranService.translate("alert.label.url")}}<span class="text-red-500">*</span></label>
                        <div style="width: calc(100% - 90px)">
                            <input class="w-full"
                                   [required]="alertInfo.actionType == CONSTANTS.ALERT_ACTION_TYPE.API"
                                   pInputText id="url"
                                   [(ngModel)]="alertInfo.url"
                                   formControlName="url"
                                   [maxLength]="255"
                                   pattern="^(https?|ftp):\/\/[^\s/$.?#].[^\s]*$|^www\.[^\s/$.?#].[^\s]*$|^localhost[^\s]*$|^(?:\d{1,3}\.){3}\d{1,3}[^\s]*$"
                                   [placeholder]="tranService.translate('alert.text.inputurl')"
                            />
                        </div>
                    </div>
                    <div class="field grid px-4 flex flex-row flex-nowrap pb-2">
                        <label htmlFor="name" style="width:90px; height: fit-content"></label>
                        <div style="width: calc(100% - 90px);padding-right: 8px;">
                            <small *ngIf="formAlert.controls.url.dirty && formAlert.controls.url.errors?.required" class="text-red-500">{{tranService.translate("global.message.required")}}</small>
                            <small *ngIf="formAlert.controls.url.errors?.pattern" class="text-red-500">{{tranService.translate("global.message.urlNotValid")}}</small>
                        </div>
                    </div>
                </div>

            </div>
        </div>
        <div class="flex flex-row justify-content-center gap-3 p-2">
            <button  pButton [label]="tranService.translate('global.button.cancel')" class="p-button-secondary p-button-outlined" type="button" (click)="closeForm()"></button>
            <button pButton [label]="tranService.translate('global.button.save')"  class="p-button-info" type="submit"[disabled]="checkDisableSave()" *ngIf="checkAuthen([CONSTANTS.PERMISSIONS.ALERT.UPDATE, CONSTANTS.PERMISSIONS.ALERT.UPDATE_WALLET_EXPIRY, CONSTANTS.PERMISSIONS.ALERT.UPDATE_WALLET_THRESHOLD]) && alertResponse.status == CONSTANTS.ALERT_STATUS.INACTIVE"></button>
        </div>
    </form>
</p-card>
