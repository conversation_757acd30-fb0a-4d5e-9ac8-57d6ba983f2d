import { Align, ChartDataset, ChartOptions, EasingFunction, InteractionMode, LayoutPosition, LegendItem, PointStyle, TitleOptions, TooltipOptions } from "chart.js";
import { CONSTANTS } from "src/app/service/comon/constants";
import chart from "src/i18n/vi/chart";

class CommonChart {
    CustomFunctionTooltip: Array<any>;
    CustomFunctionLegend: Array<any>;
    constructor() {
        this.CustomFunctionTooltip = [
            {
                name: "TOTAL",
                value: "{%s}",
                action: (tooltipItems) => {
                    let total = 0;
                    let chartType = tooltipItems[0].dataset.type;
                    if(chartType != 'pie' && chartType != 'doughnut'){
                        tooltipItems.forEach(el => total += parseInt(el.raw+""));
                    }else{
                        tooltipItems[0].dataset.data.forEach(el => total += parseInt(el+""));
                    }
                    return Intl.NumberFormat('vi-VI').format(total);
                }
            },
            {
                name: "TITLE",
                value: "{%t}",
                action: (tooltipItems) => {
                    if('label' in tooltipItems){
                        return tooltipItems.label;
                    }
                    return tooltipItems[0].label;
                }
            },
            {
                name: "LEGEND",
                value: "{%l}",
                action: (tooltipItem) => {
                    return tooltipItem.dataset.label;
                }
            },
            {
                name: "VALUE",
                value: "{%v}",
                action: (tooltipItem) => {
                    return tooltipItem.formattedValue;
                }
            },
            {
                name: "PERCENT",
                value: "{%npv}",
                action: (tooltipItem) => {
                    let datasets = tooltipItem.chart.data.datasets;
                    let total = 0;
                    let chartType = datasets[0].type;
                    if(chartType != 'pie' && chartType != 'doughnut'){
                        datasets.forEach(el => total += parseInt(el.data[tooltipItem.dataIndex]+""));
                    }else{
                        tooltipItem.dataset.data.forEach(el => total += parseInt(el+""));
                    }
                    return Math.round(tooltipItem.raw/total*100);
                }
            }
        ];
        this.CustomFunctionLegend = [
            {
                name: "LEGEND",
                value: "{%l}",
            },
            {
                name: "VALUE",
                value: "{%v}",
            },
        ];
    }

    genDatasetBarChart(label: string, data: number[], typeAnimation: EasingFunction,
                        xAxisID?:string,yAxisID?: string, stack?: string,
                        backgroundColor:string|null=null, barPercentage: number = 0.5, barThickness: number = 30,
                        maxBarThickness: number = 50, base: number = 50, borderColor: string|null=null,
                        borderRadius: number = 0, borderWidth: number = 0, categoryPercentage: number = 1,
                        hoverBackgroundColor: string|null=null, hoverBorderColor: string|null=null, hoverBorderWidth: number = 0): ChartDataset {
        let result: ChartDataset = {
            type: "bar",
            label,
            data,
            animation: {
                easing: typeAnimation
            },
            backgroundColor,
            barPercentage,
            barThickness,
            maxBarThickness,
            base,
            borderColor,
            borderRadius,
            borderWidth,
            categoryPercentage,
            hoverBackgroundColor,
            hoverBorderColor,
            hoverBorderWidth
        };
        if(xAxisID){
            result.xAxisID = xAxisID;
        }
        if(yAxisID){
            result.yAxisID = yAxisID;
        }
        if(stack){
            result.stack = stack;
        }
        return result;
    }
    // genDatasetBarThresholdChart(label: string, data: number[], typeAnimation: EasingFunction,
    //                    xAxisID?:string,yAxisID?: string, stack?: string,
    //                    backgroundColor:string[]|null=null, barPercentage: number = 0.5, barThickness: number = 30,
    //                    maxBarThickness: number = 50, base: number = 50, borderColor: string|null=null,
    //                    borderRadius: number = 0, borderWidth: number = 0, categoryPercentage: number = 1,
    //                    hoverBackgroundColor: string[]|null=null, hoverBorderColor: string|null=null, hoverBorderWidth: number = 0): ChartDataset {
    //     let result: ChartDataset = {
    //         type: "bar",
    //         label,
    //         data,
    //         animation: {
    //             easing: typeAnimation
    //         },
    //         backgroundColor,
    //         barPercentage,
    //         barThickness,
    //         maxBarThickness,
    //         base,
    //         borderColor,
    //         borderRadius,
    //         borderWidth,
    //         categoryPercentage,
    //         hoverBackgroundColor,
    //         hoverBorderColor,
    //         hoverBorderWidth
    //     };
    //     if(xAxisID){
    //         result.xAxisID = xAxisID;
    //     }
    //     if(yAxisID){
    //         result.yAxisID = yAxisID;
    //     }
    //     if(stack){
    //         result.stack = stack;
    //     }
    //     return result;
    // }
    genDatasetLineChart(label: string, data: number[], typeAnimation: EasingFunction,
                        backgroundColor: string, borderColor: string, borderCapStyle: CanvasLineCap, borderJoinStyle: CanvasLineJoin,
                        fill: boolean, tension: number, isLineDash: boolean, hoverBackgroundColor: string, hoverBorderColor: string,
                        hoverBorderWidth: number,pointBackgroundColor: string,pointBorderColor: string,pointBorderWidth: number, xAxisID: string, yAxisID: string): ChartDataset{
        let result: ChartDataset = {
            type: "line",
            label,
            data,
            animation: {
                easing: typeAnimation
            },
            backgroundColor,
            borderColor,
            borderDash: isLineDash ? [10,5] : [0,0],
            borderCapStyle,
            borderJoinStyle,
            borderWidth: 2,
            hoverBackgroundColor,
            hoverBorderColor,
            hoverBorderWidth,
            pointBackgroundColor,
            pointBorderColor,
            pointBorderWidth,
            fill,
            tension
        };
        if(xAxisID){
            result.xAxisID = xAxisID;
        }
        if(yAxisID){
            result.yAxisID = yAxisID;
        }
        return result;
    }

    genDatasetBubbleChart(label: string, data: Array<{x: number, y: number, r: number}>,
        backgroundColor: string, borderColor: string, borderWidth: number, hoverBackgroundColor: string, hoverBorderColor: string,
        hoverBorderWidth: number, pointStyle: PointStyle): ChartDataset{
        let result: ChartDataset = {
            type: "bubble",
            label,
            data,
            backgroundColor,
            borderColor,
            borderWidth,
            hoverBackgroundColor,
            hoverBorderColor,
            hoverBorderWidth,
            pointStyle
        };
        return result;
    }

    genDatasetDoughnutChart(data: number[], typeAnimation: EasingFunction, backgroundColor: string[], borderColor: string[],
        borderWidth: number, hoverBackgroundColor: string[], hoverBorderColor: string[], hoverBorderWidth: number, weight: number): ChartDataset{
        let result: ChartDataset = {
            type: "doughnut",
            data,
            animation: {
                easing: typeAnimation
            },
            backgroundColor,
            borderColor,
            borderWidth,
            hoverBackgroundColor,
            hoverBorderColor,
            hoverBorderWidth,
            weight
        };
        return result;
    }

    genDatasetPieChart(data: number[], typeAnimation: EasingFunction, backgroundColor: string[], borderColor: string[],
        borderWidth: number, hoverBackgroundColor: string[], hoverBorderColor: string[], hoverBorderWidth: number, weight: number): ChartDataset{
        let result: ChartDataset = {
            type: "pie",
            data,
            animation: {
                easing: typeAnimation
            },
            backgroundColor,
            borderColor,
            borderWidth,
            hoverBackgroundColor,
            hoverBorderColor,
            hoverBorderWidth,
            weight
        };
        return result;
    }

    genDatasetPolarAreaChart( data: number[], typeAnimation: EasingFunction, backgroundColor: string[], borderColor: string,
        borderWidth: number, hoverBackgroundColor: string, hoverBorderColor: string, hoverBorderWidth: number, weight: number): ChartDataset{
        let result: ChartDataset = {
            type: "polarArea",
            data,
            animation: {
                easing: typeAnimation
            },
            backgroundColor,
            angle: Math.max(...data),
            borderColor,
            borderWidth,
            hoverBackgroundColor,
            hoverBorderColor,
            hoverBorderWidth,
            weight
        };
        return result;
    }

    genDatasetRadarChart(label: string, data: number[], typeAnimation: EasingFunction,
                        backgroundColor: string, borderColor: string, borderCapStyle: CanvasLineCap, borderJoinStyle: CanvasLineJoin,
                        fill: boolean, tension: number, isDash:boolean, hoverBackgroundColor: string, hoverBorderColor: string,
                        hoverBorderWidth: number, pointBackgroundColor: string, pointBorderColor: string, pointBorderWidth: number,borderWidth: number): ChartDataset{
        let result: ChartDataset = {
            type: "radar",
            label,
            data,
            animation: {
                easing: typeAnimation
            },
            backgroundColor,
            borderCapStyle,
            borderColor,
            borderDash: isDash ? [10,5] : [0, 0],
            borderWidth,
            borderJoinStyle,
            hoverBackgroundColor,
            hoverBorderColor,
            hoverBorderWidth,
            pointBackgroundColor,
            pointBorderColor,
            pointBorderWidth,
            fill,
            tension
        };
        return result;
    }

    genDatasetScatterChart(label: string, data: Array<{x: number, y: number, r: number}>, typeAnimation: EasingFunction,
        backgroundColor: string, borderColor: string, borderWidth: number, hoverBackgroundColor: string, hoverBorderColor: string, hoverBorderWidth: number,
        isDash:boolean, fill: boolean, tension: number,borderCapStyle: CanvasLineCap, borderJoinStyle: CanvasLineJoin, pointBackgroundColor: string, pointBorderColor: string, pointBorderWidth: number): ChartDataset{
        let result: ChartDataset = {
            type: "scatter",
            label,
            data,
            animation: {
                easing: typeAnimation
            },
            backgroundColor,
            borderCapStyle,
            borderColor,
            borderDash: isDash ? [10,5] : [0, 0],
            borderWidth,
            borderJoinStyle,
            hoverBackgroundColor,
            hoverBorderColor,
            hoverBorderWidth,
            pointBackgroundColor,
            pointBorderColor,
            pointBorderWidth,
            fill,
            tension
        };
        return result;
    }

    // tooltipItems: {chart: object, dataIndex: number, datasetIndex: number, formattedValue: string, label: string, raw: number, dataset.data: number[]}

    // {
    //     family: "roboto",
    //     lineHeight: 1,
    //     size: 12,
    //     style: "inherit",
    //     weight: "bold"
    // }



    genOptionTooltip(mode: InteractionMode, intersect: boolean, backgroundColor: string,bodyColor: string, bodyAlign: string, bodyFont: any,
        borderColor: string, borderWidth: number, bodySpacing: number, boxHeight: number, boxWidth: number, radius: number, caretSize: number,
        titleAlign: string, titleColor: string, titleFont: any, footerColor: string, patternTitle: string, patternLabel: string, patternFooter: string){
        let tooltip = {
            mode,
            intersect,
            backgroundColor,
            bodyColor,
            bodyAlign,//center left right
            bodyFont,
            borderColor,
            borderWidth,
            bodySpacing,
            boxHeight,
            boxWidth,
            cornerRadius: {
                bottomLeft: radius,
                bottomRight: radius,
                topLeft: radius,
                topRight: radius
            },
            caretSize,
            titleAlign,
            titleColor,
            titleFont,
            footerColor,
        }
        if(patternTitle || patternLabel || patternFooter){
            let callbacks = {};
            if(patternTitle){
                callbacks['title'] = (tooltipItems) => {
                    let result = patternTitle;
                    this.CustomFunctionTooltip.forEach(item => {
                        if(result.indexOf(item.value) >= 0){
                            result = result.replace(item.value, item.action(tooltipItems));
                        }
                    })
                    return result.split("|");
                }
            }
            if(patternLabel){
                callbacks['label'] = (tooltipItems) => {
                    let result = patternLabel;
                    this.CustomFunctionTooltip.forEach(item => {
                        if(result.indexOf(item.value) >= 0){
                            result = result.replace(item.value, item.action(tooltipItems));
                        }
                    })
                    return result.split("|");
                }
            }
            if(patternFooter){
                callbacks['footer'] = (tooltipItems) => {
                    let result = patternFooter;
                    this.CustomFunctionTooltip.forEach(item => {
                        if(result.indexOf(item.value) >= 0){
                            result = result.replace(item.value, item.action(tooltipItems));
                        }
                    })
                    return result.split("|");
                }
            }
            tooltip['callbacks'] = callbacks;
        }
        return tooltip;
    }

    genOptionLegend(titleColor: string, titleDisplay: boolean, titleFont, titleText: string, align: Align, display: boolean,
        labelColor: string, labelFont, boxWidth: number, boxHeight: number, position: LayoutPosition, patternBody: string){
        let legend = {
            title: {
                color: titleColor,
                display: titleDisplay,
                font: titleFont,
                text: titleText
            },
            align,
            display,
            labels: {
                color: labelColor,
                font: labelFont,
                boxWidth,
                boxHeight,
                generateLabels(chart) {
                    let result: LegendItem[] = [];
                    let datasets = chart.data.datasets;
                    let labels = chart.data.labels;
                    if(!datasets || datasets.length == 0) return null;
                    if(datasets[0].type == CONSTANTS.CHART_TYPE.PIE || datasets[0].type == CONSTANTS.CHART_TYPE.DOUGHNUT){
                        labels.forEach((label, i)=>{
                            let text = `${label} (${Intl.NumberFormat('vi-VI').format(parseInt(datasets[0].data[i].toString()))})`;
                            if(patternBody != undefined && patternBody != null){
                                text = patternBody.replaceAll("{%l}", label);
                                text = text.replaceAll("{%v}", Intl.NumberFormat('vi-VI').format(parseInt(datasets[0].data[i].toString())));
                            }
                            result.push({
                                text: text,
                                index: i,
                                fillStyle: datasets[0].backgroundColor[i]
                            })
                        })
                    }else{
                        datasets.forEach((dataset, index)=>{
                            let total = 0;
                            let data = dataset.data;
                            data.forEach(el => total += parseInt(el.toString()));
                            let strTotal = Intl.NumberFormat('vi-VI').format(total);

                            let text = `${dataset.label} (${strTotal})`;
                            if(patternBody != undefined && patternBody != null){
                                text = patternBody.replaceAll("{%l}", dataset.label);
                                text = text.replaceAll("{%v}", strTotal);
                            }
                            result.push({
                                text: text,
                                datasetIndex: index,
                                fillStyle: dataset.backgroundColor.toString(),
                                hidden: false,
                            })
                        })
                    }
                    //sort legend biểu đồ ngưỡng
                    const order = ["cảnh báo", "xem xét", "an toàn"];
                    result.sort((a, b) => {
                        const indexA = order.findIndex(item => a.text.toLowerCase().startsWith(item));
                        const indexB = order.findIndex(item => b.text.toLowerCase().startsWith(item));
                        return indexA - indexB;
                    });

                    return result;
                },
            },
            position,
        }
        return legend;
    }

    genOptionsTitleAndSubTitle(align: Align, color: string, display: boolean, font, position: 'top' | 'left' | 'bottom' | 'right', text: string){
        let title = {
            align,
            color,
            display,
            font,
            position,
            text
        }
        return title;
    }

    genOptionScaleX(stacked: boolean, beginAtZero: boolean, position: 'top' | 'left' | 'bottom' | 'right', tickColor: string,rotation: number,
    drawOnChartArea: boolean, colorGrid: string, borderGridColor: string, isDash: boolean, titleText: string, titleAlign: Align,
    titleDisplay: boolean, titleColor: string, titleFont){
        let x = {
            beginAtZero,
            position,
            ticks: {
                color: tickColor,
                minRotation: rotation,
                // callback: function (value) {
                //     return Number.isInteger(value) ? value : null; // Chỉ hiển thị số nguyên
                // },
            },
            grid: {
                drawOnChartArea,
                color: colorGrid,
                borderColor: borderGridColor,
                borderDash: isDash ? [5,5] : [0,0]
            },
            stacked,
            title: {
                text: titleText,
                align: titleAlign,
                display: titleDisplay,
                color: titleColor,
                font: titleFont
            }
        }
        return x;
    }

    genOptionScaleY(stacked: boolean, beginAtZero: boolean, position: 'top' | 'left' | 'bottom' | 'right', tickColor: string,rotation: number,
    drawOnChartArea: boolean, colorGrid: string, borderGridColor: string, isDash: boolean, titleText: string, titleAlign: Align,
    titleDisplay: boolean, titleColor: string, titleFont){
        let y = {
            stacked,
            position,
            beginAtZero,
            ticks: {
                color: tickColor,
                align: "center",
                minRotation: rotation
            },
            grid: {
                color: colorGrid,
                borderColor: borderGridColor,
                drawOnChartArea,
                borderDash: isDash ? [5,5] : [0,0]
            },
            title: {
                text: titleText,
                align: titleAlign,
                display: titleDisplay,
                color: titleColor,
                font: titleFont
            }
        }
        return y;
    }

    getOptionBoxValue(isShowBoxValue: boolean){
        return {
            align: "center",
            anchor(context){
                if(context.dataset.type == CONSTANTS.CHART_TYPE.BAR){
                    return "end"
                }else{
                    return "center";
                }
            },
            backgroundColor(context){
                // if(context.dataset.type == CONSTANTS.CHART_TYPE.BAR){
                //     return "transparent";
                // }else{
                    return "#FFFAFA";
                // }
            },
            borderColor(context){
                // if(context.dataset.type == CONSTANTS.CHART_TYPE.BAR){
                //     return "none";
                // }else{
                    return "#8B8989"
                // }
            },
            borderRadius: 8,
            borderWidth(context) {
                if(context.dataset.type == CONSTANTS.CHART_TYPE.BAR){
                    return 1;
                }else{
                    return 1;
                }
            },
            color(context){
                if(context.dataset.type == CONSTANTS.CHART_TYPE.BAR){
                    return "#000";
                }else{
                    return "#528B8B"
                }
            },
            display(context) {
                return isShowBoxValue;
                // if(context.dataset.type == CONSTANTS.CHART_TYPE.BAR){
                //     return true;
                // }else{
                //     return false;
                // }
            },
            font: {
                size: 14,
            },
            formatter(value, context) {
                let strValue = Intl.NumberFormat('vi-VI').format(value);
                if(context.dataset.type == CONSTANTS.CHART_TYPE.BAR){
                    return strValue;
                }else{
                    return strValue;
                }
            },
            opacity: 1,
            padding:{
                bottom: 1,
                left: 8,
                right: 8,
                top: 1
            },
            rotation: 0,
            textAlign: "center",
        }
    }

    genOptions(isBarHorizontal, tooltip, legend, title, subtitle, scales, datalabels): ChartOptions {
        let result : ChartOptions = {
            indexAxis: isBarHorizontal ? "y" : "x",
            maintainAspectRatio: false,
            aspectRatio: 0.6,
            plugins: {
                tooltip,
                legend,
                title,
                subtitle,
                datalabels

            },
        }
        if(scales != null){
            result.scales = scales;
        }
        return result;
    }


}

export const commonChart = new CommonChart();
