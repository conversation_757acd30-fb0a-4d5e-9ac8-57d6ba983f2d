<form [formGroup]="formAccount" (ngSubmit)="onSubmitCreate()">
<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    <div class="col-5 flex flex-row justify-content-end align-items-center">
        <div class="flex flex-row justify-content-center align-items-center mr-2">
            <p-button [label]="tranService.translate('global.button.cancel')" styleClass="p-button-secondary p-button-outlined mr-2" (click)="closeForm()"></p-button>
            <p-button [label]="tranService.translate('global.button.save')" styleClass="p-button-info" type="submit" [disabled]="formAccount.invalid || isPhoneExisted || isUsernameExisted || isEmailExisted"></p-button>
        </div>
    </div>
</div>

<p-card styleClass="mt-3 responsive-form" *ngIf="formAccount">
    <div>
        <p-tabView (onChange)="onTabChange($event)">
            <p-tabPanel header="{{tranService.translate('account.label.generalInfo')}}">
                    <div class="flex flex-row justify-content-between profile-create">
                        <div style="width: 49%;">
                            <!-- username -->
                            <div class="w-full field grid">
                                <label htmlFor="accountName" class="col-fixed" style="width:180px">{{tranService.translate("account.label.username")}}</label>
                                <div class="col">
                                    {{accountResponse.username}}
                                </div>
                            </div>
                            <!-- fullname -->
                            <div class="w-full field grid">
                                <label htmlFor="fullName" class="col-fixed" style="width:180px">{{tranService.translate("account.label.fullname")}}<span class="text-red-500">*</span></label>
                                <div class="col wrap-div wrap-div-1">
                                    <input class="w-full"
                                           pInputText id="fullName"
                                           [(ngModel)]="accountInfo.fullName"
                                           formControlName="fullName"
                                           [required]="true"
                                           [maxLength]="255"
                                           pattern="^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]*$"
                                           [placeholder]="tranService.translate('account.text.inputFullname')"
                                    />
                                </div>
                            </div>
                            <!-- error fullname -->
                            <div class="w-full field grid text-error-field">
                                <label htmlFor="fullName" class="col-fixed" style="width:180px"></label>
                                <div class="col wrap-div">
                                    <small class="text-red-500" *ngIf="formAccount.controls.fullName.dirty && formAccount.controls.fullName.errors?.required">{{tranService.translate("global.message.required")}}</small>
                                    <small class="text-red-500" *ngIf="formAccount.controls.fullName.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:255})}}</small>
                                    <small class="text-red-500" *ngIf="formAccount.controls.fullName.errors?.pattern">{{tranService.translate("global.message.formatContainVN")}}</small>
                                </div>
                            </div>
                            <!-- email -->
                            <div class="w-full field grid">
                                <label htmlFor="email" class="col-fixed" style="width:180px">{{tranService.translate("account.label.email")}}<span class="text-red-500">*</span></label>
                                <div class="col wrap-div">
                                    <input class="w-full"
                                           pInputText id="email"
                                           [(ngModel)]="accountInfo.email"
                                           formControlName="email"
                                           [required]="true"
                                           [maxLength]="255"
                                           pattern="^[a-z0-9]+[a-z0-9\-\._]*[a-z0-9]+@([a-z0-9]+[a-z0-9\-\._]*[a-z0-9]+)+(\.[a-z]{2,})$"
                                           [placeholder]="tranService.translate('account.text.inputEmail')"
                                           (ngModelChange)="checkExistAccount('email')"
                                    />
                                </div>
                            </div>
                            <!-- error email -->
                            <div class="w-full field grid text-error-field">
                                <label htmlFor="email" class="col-fixed" style="width:180px"></label>
                                <div class="col wrap-div">
                                    <small class="text-red-500" *ngIf="formAccount.controls.email.dirty && formAccount.controls.email.errors?.required">{{tranService.translate("global.message.required")}}</small>
                                    <small class="text-red-500" *ngIf="formAccount.controls.email.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:255})}}</small>
                                    <small class="text-red-500" *ngIf="formAccount.controls.email.errors?.pattern">{{tranService.translate("global.message.invalidEmail")}}</small>
                                    <small class="text-red-500" *ngIf="isEmailExisted">{{tranService.translate("global.message.exists",{type: tranService.translate("account.label.email").toLowerCase()})}}</small>
                                </div>
                            </div>
                            <!-- phone -->
                            <div class="w-full field grid">
                                <label htmlFor="phone" class="col-fixed" style="width:180px">{{tranService.translate("account.label.phone")}}</label>
                                <div class="col wrap-div">
                                    <input class="w-full"
                                           pInputText id="phone"
                                           [(ngModel)]="accountInfo.phone"
                                           formControlName="phone"
                                           pattern="^((\+?[1-9][0-9])|0?)[1-9][0-9]{8,9}$"
                                           [placeholder]="tranService.translate('account.text.inputPhone')"
                                    />
                                </div>
                            </div>
                            <!-- error phone -->
                            <div class="w-full field grid text-error-field">
                                <label htmlFor="phone" class="col-fixed" style="width:180px"></label>
                                <div class="col wrap-div">
                                    <small class="text-red-500" *ngIf="formAccount.controls.phone.errors?.pattern">{{tranService.translate("global.message.invalidPhone")}}</small>
                                    <small class="text-red-500" *ngIf="isPhoneExisted">{{tranService.translate("global.message.exists",{type: tranService.translate("account.label.phone").toLowerCase()})}}</small>
                                </div>
                            </div>
                            <!-- description -->
                            <div class="w-full field grid">
                                <label htmlFor="description" class="col-fixed" style="width:180px">{{tranService.translate("account.label.description")}}</label>
                                <div class="col wrap-div">
                            <textarea  class="w-full" style="resize: none;"
                                       rows="5"
                                       [autoResize]="false"
                                       pInputTextarea id="description"
                                       [(ngModel)]="accountInfo.description"
                                       formControlName="description"
                                       [maxlength]="255"
                                       [placeholder]="tranService.translate('sim.text.inputDescription')"
                            ></textarea>
                                </div>
                            </div>
                            <!-- error description -->
                            <div class="w-full field grid text-error-field">
                                <label htmlFor="description" class="col-fixed" style="width:180px"></label>
                                <div class="col wrap-div">
                                    <small class="text-red-500" *ngIf="formAccount.controls.description.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:255})}}</small>
                                </div>
                            </div>
                        </div>
                        <div style="width: 49%;">

                            <!-- loai tai khoan -->
                            <div class="w-full field grid">
                                <label for="userType" class="col-fixed" style="width:180px">{{tranService.translate("account.label.userType")}}</label>
                                <div class="col">
                                    <span>{{getStringUserType(accountResponse.type)}}</span>
                                </div>
                            </div>
                            <!-- Tinh thanh pho -->
                            <div class="w-full field grid" *ngIf="accountInfo.userType != optionUserType.ADMIN && accountInfo.userType != optionUserType.AGENCY">
                                <label htmlFor="province" class="col-fixed" style="width:180px">{{tranService.translate("account.label.province")}}</label>
                                <div class="col">
                                    <span>{{accountResponse.provinceName}} ({{accountResponse.provinceCode}})</span>
                                </div>
                            </div>
                            <!-- nhom quyen -->
                            <div class="w-full field grid">
                                <label htmlFor="roles" class="col-fixed" style="width:180px">{{tranService.translate("account.label.role")}}</label>
                                <div class="col" style="max-width: calc(100% - 180px) !important;">
                                    <div>{{getStringRoles()}}</div>
                                </div>
                            </div>
                            <!-- ten khach hang -->
<!--                            <div class="w-full field grid" *ngIf="accountInfo.userType == optionUserType.CUSTOMER">-->
<!--                                <label htmlFor="roles" class="col-fixed" style="width:180px">{{tranService.translate("account.label.customerName")}}</label>-->
<!--                                <div class="col" style="max-width: calc(100% - 180px) !important;">-->
<!--                                    <div>{{getStringCustomers()}}</div>-->
<!--                                </div>-->
<!--                            </div>-->
                        </div>
                    </div>
            </p-tabPanel>
            <p-tabPanel header="{{tranService.translate('global.menu.listcustomer')}}" *ngIf="accountInfo.userType ==  CONSTANTS.USER_TYPE.CUSTOMER">
                <div class="flex flex-row justify-content-center gap-3 mt-4">
                    <input style="min-width: 35vw"  type="text" pInputText [placeholder]="tranService.translate('sim.label.quickSearch')" (keydown.enter)="onSearchCustomer(true)" [(ngModel)]="paramQuickSearchCustomer.keyword" [ngModelOptions]="{standalone: true}">
                    <p-button icon="pi pi-search"
                              styleClass="ml-3 p-button-rounded p-button-secondary p-button-text button-search"
                              type="button"
                              (click)="onSearchCustomer(true)"
                    ></p-button>
                </div>
                <table-vnpt
                    [fieldId]="'id'"
                    [pageNumber]="paginationCustomer.page"
                    [pageSize]="paginationCustomer.size"
                    [columns]="columnInfoCustomer"
                    [dataSet]="dataSetCustomer"
                    [options]="optionTableCustomer"
                    [loadData]="searchCustomer.bind(this)"
                    [rowsPerPageOptions]="[5,10,20,25,50]"
                    [scrollHeight]="'400px'"
                    [sort]="paginationCustomer.sortBy"
                    [params]="paramQuickSearchCustomer"
                ></table-vnpt>
            </p-tabPanel>
            <p-tabPanel header="{{tranService.translate('global.menu.listbill')}}" *ngIf="accountInfo.userType == CONSTANTS.USER_TYPE.CUSTOMER">
                <div class="flex flex-row justify-content-center gap-3 mt-4">
                    <input style="min-width: 35vw"  type="text" pInputText [placeholder]="tranService.translate('sim.label.quickSearch')" (keydown.enter)="onSearchContract(true)" [(ngModel)]="paramQuickSearchContract.keyword" [ngModelOptions]="{standalone: true}">
                    <p-button icon="pi pi-search"
                              styleClass="ml-3 p-button-rounded p-button-secondary p-button-text button-search"
                              type="button"
                              (click)="onSearchContract(true)"
                    ></p-button>
                </div>
                <table-vnpt
                    [fieldId]="'id'"
                    [pageNumber]="paginationContract.page"
                    [pageSize]="paginationContract.size"
                    [columns]="columnInfoContract"
                    [dataSet]="dataSetContract"
                    [options]="optionTableContract"
                    [loadData]="searchContract.bind(this)"
                    [rowsPerPageOptions]="[5,10,20,25,50]"
                    [scrollHeight]="'400px'"
                    [sort]="paginationContract.sortBy"
                    [params]="paramQuickSearchContract"
                ></table-vnpt>
            </p-tabPanel>
            <p-tabPanel header="{{tranService.translate('account.text.grantApi')}}" *ngIf="userType == optionUserType.CUSTOMER && statusGrantApi != null" [pt]="'ProfileTab'">
                <div class="mb-3">
                    <p-panel [showHeader]="false">
                        <div class="flex gap-2">
                            <p-radioButton
                                    [label]="tranService.translate('account.text.working')"
                                    value="1"
                                    class="p-3"
                                    [(ngModel)]="statusGrantApi"
                                    [ngModelOptions]="{standalone: true}"
                            ></p-radioButton>

                            <p-radioButton
                                    [label]="tranService.translate('account.text.notWorking')"
                                    value="0"
                                    class="p-3"
                                    [(ngModel)]="statusGrantApi"
                                    [ngModelOptions]="{standalone: true}"
                            ></p-radioButton>
                        </div>
                        <div class="flex gap-3 align-items-center">
                            <div class="col-5">
                                <div class="flex align-items-center">
                                    <label style="min-width: 100px" class="mr-3">Client ID</label>
                                    <input [(ngModel)]="genGrantApi.clientId"  [disabled]="true" [ngModelOptions]="{standalone: true}" class="w-full" type="text" pInputText>
                                </div>
                            </div>
                            <div class="col-5">
                                <div class="flex align-items-center">
                                    <label style="min-width: 100px" class="mr-3">Secret Key</label>
                                    <div class="w-full flex align-items-center">
                                        <input class="w-full mr-2" style="padding-right: 30px;"
                                               [(ngModel)]="genGrantApi.secretKey"
                                               [ngModelOptions]="{standalone: true}"
                                               [type]="isShowSecretKey ? 'text': 'password'"
                                               pInputText
                                               [disabled]="true"
                                        />
                                        <label style="margin-left: -30px;z-index: 1;" *ngIf="isShowSecretKey == false" class="pi pi-eye toggle-password" (click)="isShowSecretKey = true"></label>
                                        <label style="margin-left: -30px;z-index: 1;" *ngIf="isShowSecretKey == true" class="pi pi-eye-slash toggle-password" (click)="isShowSecretKey = false"></label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-2">
                                <p-button (click)="genToken()" [label]="tranService.translate('account.text.gen')" styleClass="p-button-primary mr-2"></p-button>
                            </div>
                        </div>
                    </p-panel>
                </div>
                <div>
                    <p-panel [showHeader]="false" class="  ">
                        <div class="flex gap-3 align-items-center">
                            <div class="col-3">
                                <p-dropdown class="w-full"
                                            [showClear]="true"
                                            [(ngModel)]="paramsSearchGrantApi.module"
                                            [ngModelOptions]="{standalone: true}"
                                            [options]="listModule"
                                            optionLabel="name"
                                            optionValue="value"
                                            [emptyFilterMessage]="tranService.translate('global.text.nodata')"
                                            filter="true"
                                            [placeholder]="tranService.translate('account.text.module')"
                                ></p-dropdown>
                            </div>
                            <div class="col-3">
                                <input [(ngModel)]="paramsSearchGrantApi.api" [ngModelOptions]="{standalone: true}" class="w-full mr-2" type="text" pInputText placeholder="API"/>
                            </div>
                            <p-button icon="pi pi-search"
                                      styleClass="ml-3 p-button-rounded p-button-secondary p-button-text button-search"
                                      type="button"
                                      (click)="onSearchGrantApi(true)"
                            ></p-button>
                        </div>

                        <table-vnpt
                                [fieldId]="'id'"
                                [pageNumber]="paginationGrantApi.page"
                                [pageSize]="paginationGrantApi.size"
                                [columns]="columnInfoGrantApi"
                                [dataSet]="dataSetGrantApi"
                                [options]="optionTableGrantApi"
                                [loadData]="searchGrantApi.bind(this)"
                                [rowsPerPageOptions]="[5,10,20,25,50]"
                                [scrollHeight]="'400px'"
                                [sort]="paginationGrantApi.sortBy"
                                [params]="paramsSearchGrantApi"
                        ></table-vnpt>
                    </p-panel>
                </div>
            </p-tabPanel>
        </p-tabView>
    </div>
</p-card>
</form>

