import { NgModule } from "@angular/core";
import { RouterModule } from "@angular/router";
import { AppAccountListComponent } from "./list/app.account.list.component";
import { AppAccountCreateComponent } from "./create/app.account.create.component";
import { AppAccountEditComponent } from "./edit/app.account.edit.component";
import { AppAccountDetailComponent } from "./detail/app.account.detail.component";
import DataPage from "src/app/service/data.page";
import { CONSTANTS } from "src/app/service/comon/constants";
import {ApiLogComponent} from "./api-log/api-log.component";

@NgModule({
    imports:[
        RouterModule.forChild([
            {path: "", component: AppAccountListComponent, data: new DataPage("global.menu.listaccount", [CONSTANTS.PERMISSIONS.ACCOUNT.VIEW_LIST])},
            {path: "create", component: AppAccountCreateComponent,data: new DataPage("global.titlepage.createaccount", [CONSTANTS.PERMISSIONS.ACCOUNT.CREATE])},
            {path: "edit/:id", component: AppAccountEditComponent, data: new DataPage("global.titlepage.editaccount", [CONSTANTS.PERMISSIONS.ACCOUNT.UPDATE])},
            // {path: "detail/:id", component: AppAccountDetailComponent, data: new DataPage("global.titlepage.detailaccount", [CONSTANTS.PERMISSIONS.ACCOUNT.VIEW_DETAIL])}
            {path: "logApi", component: ApiLogComponent, data: new DataPage("global.titlepage.apiLogs", [CONSTANTS.PERMISSIONS.ACCOUNT.SEARCH_LOG_API])},
        ])
    ],
    exports: [RouterModule]
})
export class AppAccountRoutingModule{}
