<div>
<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{this.tranService.translate("datapool.label.autoShareWalletDetail")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
</div>

<p-card styleClass="my-3" [header]="tranService.translate('datapool.label.generalInfomation')">
    <div class="grid">
        <div class="col-6">
            <div class="flex justify-content-between col-12 md:col-12 py-0">
                <div style="min-width: 130px">
                    <label class="m-0 p-0 text-lg font-medium" style="min-width: 130px; align-self: flex-end;">{{this.tranService.translate("groupSim.label.groupKey")}}:</label>
                </div>
                <div class="col-9 md:col-10 p-0 text-lg font-medium">
                    {{groupAutoShareInfo?.code}}
                </div>
            </div>
        </div>
        <div class="col-6">
            <div class="col-6 pt-0 pb-0">
                <div class="flex justify-content-between col-12 md:col-12 py-0">
                    <div style="min-width: 130px">
                        <label class="m-0 p-0 text-lg font-medium h-full" style="min-width: 130px; align-self: flex-end;">{{this.tranService.translate("groupSim.label.groupName")}}:</label>
                    </div>
                    <div class="col-9 md:col-10 p-0 text-lg font-medium">
                        {{groupAutoShareInfo?.name}}
                    </div>
                </div>
            </div>
        </div>
        <div class="col-6">
            <div class="flex justify-content-between col-12 md:col-12 py-0">
                <div style="min-width: 130px">
                    <label class="m-0 p-0 text-lg font-medium" style="min-width: 130px; align-self: flex-end;">{{this.tranService.translate("groupSim.label.description")}}:</label>
                </div>
                <div class="col-9 md:col-10 p-0 text-lg font-medium">
                    {{groupAutoShareInfo?.description}}
                </div>
            </div>
        </div>
    </div>
    <div class="mt-5">
        <div class="flex flex-column">
            <div class="flex gap-1">
                <button (click)="goToWalletListTab()" type="button" class="table-title-button"
                        [ngClass]="{'chosen-device-button':currentServiceTab === AutoShareWalletType.WALLET_LIST_TAB}">
                    {{tranService.translate('datapool.label.walletList')}}
                </button>
                <button (click)="goToSharePhoneListTab()" type="button" class="table-title-button"
                        [ngClass]="{'chosen-device-button':currentServiceTab === AutoShareWalletType.SHARE_PHONE_LIST_TAB}">
                    {{tranService.translate('datapool.label.sharePhoneList')}}
                </button>
            </div>
        </div>
        <ng-container
                *ngTemplateOutlet="currentServiceTab === AutoShareWalletType.WALLET_LIST_TAB ? walletListTemplateRef : sharePhoneListTemplateRef"></ng-container>
    </div>
</p-card>
</div>
<wallet-list-tab *ngIf="currentServiceTab === AutoShareWalletType.WALLET_LIST_TAB" [trafficType]="trafficType" (walletListTemplateRefEmitter)="getWalletListActivationTemplateRef($event)"></wallet-list-tab>
<share-phone-list-tab *ngIf="currentServiceTab === AutoShareWalletType.SHARE_PHONE_LIST_TAB" [trafficType]="trafficType" (sharePhoneListTemplateRefEmitter)="getSharePhoneListTemplateRef($event)"></share-phone-list-tab>
