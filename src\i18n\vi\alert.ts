export default {
    label: {
        name: "<PERSON><PERSON><PERSON>",
        customer: "<PERSON><PERSON><PERSON><PERSON> hàng",
        contractCode: "<PERSON><PERSON> hợp đồng",
        statusSIM: "Tình trạng thuê bao",
        subscriptionNumber: "<PERSON><PERSON> thuê bao",
        group: "<PERSON>h<PERSON><PERSON> thuê bao",
        status: "Trạng thái",
        afterTime: "Sau thời gian",
        afterCount: "Sau số lần",
        unit: "Đơn vị",
        unitValue: "Giá trị",
        description: "<PERSON><PERSON> tả",
        level: "Mức độ",
        groupReceiving: "Nhóm nhận cảnh báo",
        url: "URL:",
        emails: "Email nhận cảnh báo",
        topic: "Ch<PERSON> đề email",
        contentEmail: "Nội dung email",
        contentSms: "Nội dung SMS",
        sms: "Số điện thoại nhận SMS",
        alertreceivinggroup: "Tên nhóm nhận cảnh báo",
        simstatus: "Tình trạng Sim",
        time: "Thờ<PERSON> gian",
        fromdate: "Thời gian từ",
        todate: "Thời gian đến",
        minutes: "Phút",
        news: "Bản tin",
        rule: "Loại",
        event: "Điều kiện kích hoạt",
        action: "Loại hành động",
        appliedPlan: "Gói cước áp dụng",
        frequency:"Tần suất lặp",
        repeat:"Lặp lại",
        exceededPakage:"Ngưỡng gói cước (%)",
        exceededValue: "Ngưỡng giá trị (MB)",
        smsExceededPakage:"Ngưỡng SMS (%)",
        smsExceededValue:"Ngưỡng giá trị (Số lượng)",
        inactivePopup: "Bấm để ngừng hoạt động" ,
        activePopup :"Bấm để kích hoạt",
        wallet: "Ví lưu lượng",
        thresholdValue: "Giá trị ngưỡng",
        walletEmail: "Email chủ ví nhận cảnh báo",
        walletPhone: "SĐT chủ ví nhận cảnh báo",
    },
    text: {
        inputName: "Nhập tên quy tắc",
        inputStatusSIM: "Chọn tình trạng thuê bao",
        inputCustomer: "Chọn khách hàng",
        inputContractCode: "Chọn mã hợp đồng",
        inputSubscriptionNumber: "Chọn số thuê bao",
        inputGroup: "Chọn nhóm thuê bao",
        inputafterTime: "Nhập thời gian",
        inputafterCount: "Nhập số lần",
        inputunit: "Chọn đơn vị",
        inputunitValue: "Nhập giá trị",
        inputDescription: "Nhập mô tả",
        inputlevel: "Chọn mức độ",
        inputgroupReceiving: "Chọn nhóm nhận cảnh báo",
        inputurl: "Nhập URL:",
        inputemails: "Nhập email nhận cảnh báo",
        inputtopic: "Nhập chủ đề email",
        inputcontentEmail: "Nhập nội dung email",
        inputcontentSms: "Nhập nội dung sms",
        inputsms: "Nhập số điện thoại nhận sms",
        headerAPI: "Nhận quy tắc qua API",
        headerEmail: "Nhận quy tắc qua Email",
        headerSMS: "Nhận quy tắc qua SMS",
        labelAlert: "Thông tin nhận quy tắc",
        inputNameReceiving: "Nhập tên nhóm nhận cảnh báo",
        removeAlert: "Xóa Email",
        removeSms: "Xóa SMS",
        rule: "Chọn loại",
        eventType: "Chọn điều kiện kích hoạt",
        appliedPlan: "Nhập gói cước áp dụng",
        actionType: "Chọn hành động",
        filterApplieInfo: "Lọc thông tin áp dụng",
        sendNotifyExpiredData : "Gửi thông báo khi gói sắp hết hiệu lực trước",
        hour: "giờ",
        day : "Ngày",
        sendType : "Hình thức gửi thông báo"
    },
    status: {
        active: "Hoạt động",
        inactive: "Ngừng hoạt động",
    },
    type: {
        admin: "Admin",
        customer: "Customer",
        province: "Province",
        district: "District",
        agency: "Agency",
    },
    statusSim: {
        outPlan: "Data chạm ngưỡng gói",
        outLine: "Data chạm ngưỡng giá trị",
        disconnected: "Mất kết nối",
        newConnection: "Phát sinh kết nối mới"
    },

    receiving: {
        name: "Tên nhóm nhận quy tắc",
        description: "Mô tả",
        emails: "Email",
        sms: "SMS",
    },
    event: {
        thresholddatapacket: "Data chạm ngưỡng gói",
        thresholddatavalue: "Data chạm ngưỡng giá trị",
        lostconnecting: "Mất kết nối",
        connecting: "Phát sinh kết nối",
        purgesim: "Huỷ SIM",
    },
    severity: {
        critical: "Nghiêm trọng",
        major: "Cao",
        minor: "Trung bình",
        info: "Thấp",
    },
    eventType:{
        exceededPakage:"Data chạm ngưỡng gói",
        exceededValue: "Data chạm ngưỡng giá trị",
        sessionEnd : "Mất kết nối",
        sessionStart : "Phát sinh kết nối mới",
        smsExceededPakage:"SMS chạm ngưỡng gói",
        smsExceededValue:"SMS chạm ngưỡng giá trị ",
        owLock:"Khoá 1 chiều",
        twLock:"Khoá 2 chiều",
        noConection:"Không kết nối",
        simExp:"Hết hạn gói cước",
        datapoolExp:"Datapool qúa hạn",
        subExp: "Hết hạn thuê bao",
        dataWalletExp: "Hết hạn ví lưu lượng",
        owtwlock: "Khoá 1 chiều - 2 chiều",
        walletThreshold: "Ví lưu lượng vượt ngưỡng giá trị",
    },
    actionType:{
        alert:"Cảnh báo",
        api:"API"
    },
    ruleCategory:{
        monitoring:"Giám sát sử dụng",
        management:"Quản lý sử dụng gói cước",
    },
    message:{
        existedPlan:"Gói cước đã được sử dụng",
        checkboxRequired : "Vui lòng chọn ít nhất 1 check box",
        exceededPakage: "Cảnh báo thuê bao [msisdn] sử dụng Data chạm ngưỡng [value] % của gói cước [plan_name]",
        smsExceededPakage: "Cảnh báo thuê bao [msisdn] sử dụng SMS chạm ngưỡng [value] % của gói cước [plan_name]",
        exceededValue: "Cảnh báo thuê bao [msisdn] sử dụng Data chạm ngưỡng giá trị [value] MB",
        smsExceededValue: "Cảnh báo thuê bao [msisdn] sử dụng số lượng SMS chạm ngưỡng giá trị [value]",
        status: "Cảnh báo thuê bao [msisdn] đã [status]",
    }

}
