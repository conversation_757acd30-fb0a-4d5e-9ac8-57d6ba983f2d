import { CommonModule } from "@angular/common";
import { NgModule } from "@angular/core";
import { AppRatingPlanRouting } from "./app.ratingplan.routing";
import { AppRatingPlanListComponent } from "./list-plan/app.ratingplan.list.component";
import { RatingPlanService } from "src/app/service/rating-plan/RatingPlanService";
import { BreadcrumbModule } from "primeng/breadcrumb";
import { FieldsetModule } from "primeng/fieldset";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { InputTextModule } from "primeng/inputtext";
import { ButtonModule } from "primeng/button";
import { CommonVnptModule } from "../common-module/common.module";
import { SplitButtonModule } from "primeng/splitbutton";
import { AutoCompleteModule } from "primeng/autocomplete";
import { CalendarModule } from "primeng/calendar";
import { DropdownModule } from "primeng/dropdown";
import { CardModule } from "primeng/card";
import { DialogModule } from "primeng/dialog";
import { InputTextareaModule } from 'primeng/inputtextarea';
import { MultiSelectModule } from 'primeng/multiselect';
import { AppRegisterPlanListComponent } from "./list-register-plan/app.registerplan.list.component";
import { CustomerService } from "src/app/service/customer/CustomerService";
import { GroupSimService } from "src/app/service/group-sim/GroupSimService";
import { TableModule } from "primeng/table";
import { CreatePlanComponent } from './list-plan/create-plan/create-plan.component';
import { InputSwitchModule } from 'primeng/inputswitch';
import { RadioButtonModule } from 'primeng/radiobutton';
import { UpdatePlanComponent } from './list-plan/update-plan/update-plan.component';
import {AppRatingPlanDetailComponent} from "./detail-plan/app.ratingplan.detail.component";
import {ToggleButtonModule} from "primeng/togglebutton";
import {CheckboxModule} from "primeng/checkbox";
import {TagModule} from "primeng/tag";
import { AppHistoryRegisterplanListComponent } from './list-history-register-plan/app.history.registerplan.list.component';
import { SimService } from "src/app/service/sim/SimService";
import { AccountService } from "src/app/service/account/AccountService";
import {PanelModule} from "primeng/panel";
@NgModule({
    imports: [
        CommonModule,
        AppRatingPlanRouting,
        BreadcrumbModule,
        FieldsetModule,
        FormsModule,
        ReactiveFormsModule,
        InputTextModule,
        ButtonModule,
        CommonVnptModule,
        SplitButtonModule,
        AutoCompleteModule,
        CalendarModule,
        DropdownModule,
        CardModule,
        DialogModule,
        InputTextareaModule,
        MultiSelectModule,
        InputSwitchModule,
        RadioButtonModule,
        TableModule,
        InputSwitchModule,
        RadioButtonModule,
        ToggleButtonModule,
        CheckboxModule,
        InputSwitchModule,
        RadioButtonModule,
        TagModule,
        PanelModule
    ],
    declarations: [
        AppRatingPlanListComponent,
        AppRegisterPlanListComponent,
        AppRatingPlanDetailComponent,
        AppHistoryRegisterplanListComponent,
        AppRegisterPlanListComponent,
        CreatePlanComponent,
        UpdatePlanComponent
    ],
    providers: [RatingPlanService, CustomerService, GroupSimService, SimService, AccountService]
})
export class AppRatingPlanModule{}
