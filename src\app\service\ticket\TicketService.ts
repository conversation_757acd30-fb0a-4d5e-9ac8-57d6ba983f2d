import { Inject, Injectable } from "@angular/core";
import { HttpService } from "../comon/http.service";

@Injectable()
export class TicketService{
    private prefixApi: string;
    constructor(@Inject(HttpService) private httpService:HttpService) {
        this.prefixApi = "/ticket";
    }

    public searchTicketConfig(params:{[key:string]:any}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(`${this.prefixApi}/searchConfig`,{}, params,callback, errorCallBack, finallyCallback);
    }
    public getDetailTicketConfig(provinceCode: string, callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/detailConfig/${provinceCode}`,{}, {}, callback, errorCallback, finallyCallback);
    }

    public updateTicketConfig(body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.post(`${this.prefixApi}/updateConfig`, {},body,{}, callback, errorCallback, finallyCallback);
    }

    public searchTicket(params:{[key:string]:any}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(`${this.prefixApi}/search`,{}, params,callback, errorCallBack, finallyCallback);
    }

    public getDetailTicket(ticketId: string, callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/${ticketId}`,{}, {}, callback, errorCallback, finallyCallback);
    }

    public updateTicket(ticketId, body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.put(`${this.prefixApi}/update/${ticketId}`, {},body,{}, callback, errorCallback, finallyCallback);
    }

    public createTicket(body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.post(`${this.prefixApi}/create`, {},body,{}, callback, errorCallback, finallyCallback);
    }

    public getListAssignee(params:{[key:string]:any}, callback?:Function, errorCallBack?:Function, finallyCallback?:Function){
        this.httpService.get(`${this.prefixApi}/getListAssginee`,{}, params,callback, errorCallBack, finallyCallback);
    }

    public sendMailNotify(body,callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.post(`${this.prefixApi}/sendMailNotify`, {},body,{}, callback, errorCallback, finallyCallback);
    }

    public countNotify(params:{[key:string]:any},callback?: Function, errorCallback?:Function, finallyCallback?: Function){
        this.httpService.get(`${this.prefixApi}/countNotify`, {},params, callback, errorCallback, finallyCallback);
    }
    public downloadTemplate() {
        this.httpService.downloadLocal(`/assets/data/Template_active_IMSI.xlsx`, "Template_active_IMSI.xlsx");
    }
}
