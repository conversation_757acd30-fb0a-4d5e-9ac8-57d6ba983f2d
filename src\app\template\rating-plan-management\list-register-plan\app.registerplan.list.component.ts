import {Component, Injector, OnInit} from "@angular/core";
import {<PERSON><PERSON><PERSON><PERSON>, Validators} from "@angular/forms";
import {MenuItem} from "primeng/api";
import {CONSTANTS} from "src/app/service/comon/constants";
import {RatingPlanService} from "src/app/service/rating-plan/RatingPlanService";
import {ColumnInfo, OptionTable} from "../../common-module/table/table.component";
import {CustomerService} from "src/app/service/customer/CustomerService";
import {GroupSimService} from "src/app/service/group-sim/GroupSimService";
import {OptionInputFile} from "../../common-module/input-file/input.file.component";
import {SimService} from "src/app/service/sim/SimService";
import {ComponentBase} from "src/app/component.base";
import {AccountService} from "../../../service/account/AccountService";

@Component({
    selector: "app-register-plan-list",
    templateUrl: "./app.registerplan.list.component.html",
})
export class AppRegisterPlanListComponent extends ComponentBase implements OnInit {
    searchInfo: {
        msisdn: string | null
        imei: string | null
        ratingPlanId: number | null
        status: any
        contractCode: string | null
        dateFrom: Date | null
        dateTo: Date | null
        customer: string | null
    }
    listStatus: Array<any>;
    items: MenuItem[];
    home: MenuItem;
    itemRegisters: MenuItem[];
    listCustomer: Array<any>;
    listRatingPlan: Array<any> = [];
    listRatingPlanOrigin: Array<any> = [];
    listGroupSim: Array<any>;
    selectItems: Array<any> = [];
    columns: Array<ColumnInfo>;
    dataSet: {
        content: Array<any>,
        total: number
    };
    optionTable: OptionTable;
    pageNumber: number;
    pageSize: number;
    sort: string;
    formSearch: any;
    maxDateFrom: Date | number | string | null = new Date();
    minDateTo: Date | number | string | null = null;
    maxDateTo: Date | number | string | null = new Date();
    //register plan
    headerDialogRegisterForSim: string | null;
    isShowDialogRegisterSim: boolean = false;
    planSelected: number | null;
    planGroupSelected: number | null;
    simSelected: any | null;

    isShowDialogRegisterGroupSim: boolean = false;
    groupSimSelected: number | null;

    isShowDialogResultRegisterGroupSim: boolean = false;
    dataSetResultRegisterGroup: {
        content: Array<any>,
        total: number
    }
    dataResultRegisterGroupOrigin: Array<any>;
    columnsResultRegisterGroup: Array<ColumnInfo>;
    optionTableResultRegisterGroup: OptionTable;
    pageNumberResultRegister: number | null;
    pageSizeResultRegister: number | null;
    totalResultRegisterGroup: number;
    totalSuccessResultRegisterGroup: number;
    totalFailResultRegisterGroup: number;

    isShowDialogResultRegisterFile: boolean = false;
    optionInputFile: OptionInputFile;
    fileObject: any;


    simImports: Array<any>;
    simImportsOrigin: Array<any>;
    mapFormSimImports: any;
    mapPlanNameError: any;
    mapImsiError: any;
    pageSizeSimImport: number = 10;
    rowFirstSimImport: number = 0;
    isShowErrorUpload: boolean = false;
    isShowErrorGroup: boolean = false;
    messageErrorUpload: string | null;
    userType: number;
    allPermissions = CONSTANTS.PERMISSIONS;
    isShowModalDetailSim: boolean = false;
    detailSim: any = {};
    detailStatusSim: any = {};
    detailCustomer: any = {};
    detailRatingPlan: any = {};
    detailContract: any = {};
    detailAPN: any = {};
    simId: string;
    isShowModalDetailPlan: boolean = false;
    ratingPlanInfo: {
        id: number | null;
        code: string | null,
        name: string | null,
        status: number | null,
        dispatchCode: string | null,
        customerType: string | null,
        subscriptionFee: string | null,
        subscriptionType: string | null,
        ratingScope: number | null,
        cycleTimeUnit: string | null,
        cycleInterval: string | null,
        reload: string | null,
        flat: string | null,
        limitDataUsage: string | null,
        limitSmsOutside: string | null,
        limitSmsInside: string | null,
        flexible: string | null,
        feePerDataUnit: string | null,
        squeezedSpeed: string | null,
        feeSmsInside: string | null,
        feeSmsOutside: string | null,
        maximumFee: string | null,
        dataRoundUnit: string | null,
        downSpeed: string | null,
        provinceCode: Array<string> | null,
        description: string | null
    };
    provinces: any[] | undefined;
    myProvices: string | null;
    subscriptionTypes: any = [];
    planId: number;
    checkedReload: boolean;
    checkedFlexible: boolean;
    response: any = {};
    planScopes = CONSTANTS.RATING_PLAN_SCOPE;
    typeRegisterForSim: "register" | "change" | "cancel" = "register"

    constructor(private ratingPlanService: RatingPlanService,
                private customerService: CustomerService,
                private groupSimService: GroupSimService,
                private simService: SimService,
                private formBuilder: FormBuilder,
                private accountService: AccountService,
                injector: Injector) {
        super(injector);
    }

    ngOnInit(): void {
        let me = this;
        this.userType = this.sessionService.userInfo.type;
        this.items = [{label: this.tranService.translate("global.menu.ratingplanmgmt")}, {label: this.tranService.translate("global.menu.registerplan")}];
        this.home = {icon: 'pi pi-home', routerLink: '/'};
        this.itemRegisters = [
            {
                label: this.tranService.translate("global.button.registerPlanForGroup"),
                command: () => {
                    me.isShowDialogRegisterGroupSim = true;
                },
                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN_SIM.REGISTER_BY_LIST])
            },
            {
                label: this.tranService.translate("global.button.registerPlanByFile"),
                command: () => {
                    me.isShowDialogResultRegisterFile = true;
                    //clear input file
                    me.observableService.next(CONSTANTS.OBSERVABLE.KEY_INPUT_FILE_VNPT, {});
                    me.simImportsOrigin = undefined;
                    me.isShowErrorUpload = false;
                },
                visible: this.checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN_SIM.REGISTER_BY_FILE])
            }
        ];
        this.detailSim = {};
        this.ratingPlanInfo = {
            id: null,
            code: null,
            name: null,
            status: null,
            dispatchCode: null,
            customerType: null,
            subscriptionFee: null,
            subscriptionType: null,
            ratingScope: null,
            cycleTimeUnit: null,
            cycleInterval: null,
            reload: null,
            flat: null,
            limitDataUsage: null,
            limitSmsOutside: null,
            limitSmsInside: null,
            flexible: null,
            feePerDataUnit: null,
            squeezedSpeed: null,
            feeSmsInside: null,
            feeSmsOutside: null,
            maximumFee: null,
            dataRoundUnit: null,
            downSpeed: null,
            provinceCode: null,
            description: null
        };
        this.listStatus = [
            // {
            //     value: CONSTANTS.SIM_STATUS.READY,
            //     name: this.tranService.translate("sim.status.ready")
            // },
            {
                value: [CONSTANTS.SIM_STATUS.ACTIVATED],
                name: this.tranService.translate("sim.status.activated")
            },
            {
                value: [CONSTANTS.SIM_STATUS.INACTIVED],
                name: this.tranService.translate("sim.status.inactivated")
            },
            {
                value: [CONSTANTS.SIM_STATUS.DEACTIVATED],
                name: this.tranService.translate("sim.status.deactivated")
            },
            {
                value: [CONSTANTS.SIM_STATUS.PURGED],
                name: this.tranService.translate("sim.status.purged")
            },
            {
                value: [15 + CONSTANTS.SIM_STATUS.ACTIVATED, 15 + CONSTANTS.SIM_STATUS.READY],
                name: this.tranService.translate("sim.status.processingChangePlan")
            },
            {
                value: [10 + CONSTANTS.SIM_STATUS.ACTIVATED, 10 + CONSTANTS.SIM_STATUS.READY],
                name: this.tranService.translate("sim.status.processingRegisterPlan")
            },
            {
                value: [20 + CONSTANTS.SIM_STATUS.ACTIVATED, 20 + CONSTANTS.SIM_STATUS.READY],
                name: this.tranService.translate("sim.status.waitingCancelPlan")
            },
        ]

        this.searchInfo = {
            msisdn: null,
            status: null,
            imei: null,
            ratingPlanId: null,
            customer: null,
            contractCode: null,
            dateFrom: null,
            dateTo: null
        }
        this.formSearch = this.formBuilder.group(this.searchInfo);
        this.selectItems = [];
        this.pageNumber = 0;
        this.pageSize = 10;
        // this.sort = "createdDate,asc";
        this.sort = "ratingPlanName,asc";

        this.optionTable = {
            hasClearSelected: true,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
            action: [
                {
                    icon: "pi pi-file",
                    tooltip: this.tranService.translate("global.button.registerRatingPlan"),
                    func: function (id, item) {
                        me.simSelected = item;
                        me.headerDialogRegisterForSim = me.tranService.translate("global.button.registerRatingPlan");
                        me.typeRegisterForSim = "register";
                        me.getListRatingPlanByCustomerCode(item.customerCode);
                        // me.listRatingPlan = [...me.listRatingPlanOrigin];
                        me.isShowDialogRegisterSim = true;
                    },
                    funcAppear: function (id, item) {
                        return me.listRatingPlanOrigin && (item.status == CONSTANTS.SIM_STATUS.ACTIVATED || item.status == CONSTANTS.SIM_STATUS.READY) && (item.ratingPlanId == null)
                            && me.listRatingPlan.length > 0 && me.checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN_SIM.REGISTER_PLAN]);
                    }
                },
                {
                    icon: "pi pi-sync",
                    tooltip: this.tranService.translate("global.button.changeRatingPlan"),
                    func: function (id, item) {
                        me.simSelected = item;
                        me.headerDialogRegisterForSim = me.tranService.translate("global.button.changeRatingPlan");
                        me.typeRegisterForSim = "change"
                        me.getListRatingPlanByCustomerCode(item.customerCode);
                        me.listRatingPlanOrigin = me.listRatingPlanOrigin.filter(el => el.id != item.ratingPlanId);
                        me.isShowDialogRegisterSim = true;
                    },
                    funcAppear: function (id, item) {
                        return me.listRatingPlanOrigin && item.ratingPlanId != null && (item.status == CONSTANTS.SIM_STATUS.ACTIVATED || item.status == CONSTANTS.SIM_STATUS.READY) && me.listRatingPlan.length > 0 && me.checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN_SIM.REGISTER_PLAN]);
                    }
                },
                {
                    icon: "pi pi-times",
                    tooltip: this.tranService.translate("global.button.cancelRatingPlan"),
                    func: function (id, item) {
                        me.simSelected = item;
                        me.typeRegisterForSim = "change"
                        me.messageCommonService.confirm(me.tranService.translate("global.button.cancelRatingPlan"), me.tranService.translate("global.message.confirmCancelPlan", {
                            planName: item.ratingPlanName,
                            msisdn: item.msisdn
                        }), {
                            ok: () => {
                                me.ratingPlanService.cancelPlanForSubcriber(item.msisdn, item.ratingPlanId, () => {
                                    me.messageCommonService.success(me.tranService.translate("ratingPlan.text.textCancelSuccess"));
                                    me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);
                                });
                            }
                        })
                    },
                    funcAppear: function (id, item) {
                        return me.listRatingPlanOrigin && item.ratingPlanId != null && (item.status == CONSTANTS.SIM_STATUS.ACTIVATED || item.status == CONSTANTS.SIM_STATUS.READY) && me.listRatingPlan.length > 0 && me.checkAuthen([CONSTANTS.PERMISSIONS.RATING_PLAN_SIM.REGISTER_PLAN]);
                    }
                }
            ]
        },
            this.columns = [
                {
                    name: this.tranService.translate("sim.label.sothuebao"),
                    key: "msisdn",
                    size: "200px",
                    align: "left",
                    isShow: true,
                    isSort: true,
                    style: {
                        cursor: "pointer",
                        color: "var(--mainColorText)"
                    },
                    funcClick(id, item) {
                        me.simId = item?.msisdn.toString();
                        me.getDetailSim();
                        me.isShowModalDetailSim = true;
                    },
                },
                {
                    name: this.tranService.translate("sim.label.trangthaisim"),
                    key: "status",
                    size: "150px",
                    align: "left",
                    isShow: true,
                    isSort: true,
                    funcGetClassname: (value) => {
                        if (value == 0) {
                            return ['p-2', "border-round", "border-400", "text-color", "inline-block"];
                        } else if (value == CONSTANTS.SIM_STATUS.READY) {
                            // return ['p-1', "bg-blue-600", "border-round","inline-block"];
                            return ['p-2', 'text-green-800', "bg-green-100", "border-round", "inline-block"];
                        } else if (value == CONSTANTS.SIM_STATUS.ACTIVATED) {
                            return ['p-2', 'text-green-800', "bg-green-100", "border-round", "inline-block"];
                        } else if (value == CONSTANTS.SIM_STATUS.INACTIVED) {
                            return ['p-2', 'text-yellow-800', "bg-yellow-100", "border-round", "inline-block"];
                        } else if (value == CONSTANTS.SIM_STATUS.DEACTIVATED) {
                            return ['p-2', 'text-indigo-600', "bg-indigo-100", "border-round", "inline-block"];
                        } else if (value == CONSTANTS.SIM_STATUS.PURGED) {
                            return ['p-2', 'text-red-700', "bg-red-100", "border-round", "inline-block"];
                        } else if (value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY) {
                            return ['p-2', 'text-cyan-800', "bg-cyan-100", "border-round", "inline-block"];
                        } else if (value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY) {
                            return ['p-2', 'text-teal-800', "bg-teal-100", "border-round", "inline-block"];
                        } else if (value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY) {
                            return ['p-2', 'text-orange-700', "bg-orange-100", "border-round", "inline-block"];
                        }
                        return [];
                    },
                    funcConvertText: (value) => {
                        if (value == 0) {
                            return me.tranService.translate("sim.status.inventory");
                        } else if (value == CONSTANTS.SIM_STATUS.READY) {
                            // return me.tranService.translate("sim.status.ready");
                            return me.tranService.translate("sim.status.activated");
                        } else if (value == CONSTANTS.SIM_STATUS.ACTIVATED) {
                            return me.tranService.translate("sim.status.activated");
                        } else if (value == CONSTANTS.SIM_STATUS.DEACTIVATED) {
                            return me.tranService.translate("sim.status.deactivated");
                        } else if (value == CONSTANTS.SIM_STATUS.PURGED) {
                            return me.tranService.translate("sim.status.purged");
                        } else if (value == CONSTANTS.SIM_STATUS.INACTIVED) {
                            return me.tranService.translate("sim.status.inactivated");
                        } else if (value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY) {
                            return this.tranService.translate("sim.status.processingChangePlan");
                        } else if (value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY) {
                            return this.tranService.translate("sim.status.processingRegisterPlan");
                        } else if (value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY) {
                            return this.tranService.translate("sim.status.waitingCancelPlan");
                        }
                        return "";
                    },
                    style: {
                        color: "white"
                    }
                },
                {
                    name: this.tranService.translate("sim.label.goicuoc"),
                    key: "ratingPlanName",
                    size: "200px",
                    align: "left",
                    isShow: true,
                    isSort: true,
                    style: {
                        cursor: "pointer",
                        color: "var(--mainColorText)"
                    },
                    funcClick(id, item) {
                        me.planId = item?.ratingPlanId;
                        me.getDetailPLan();
                        me.checkSubscriptionType();
                        me.isShowModalDetailPlan = true;
                    },
                    className: "white-space-normal"
                },
                {
                    name: this.tranService.translate("sim.label.imeiDevice"),
                    key: "imei",
                    size: "175px",
                    align: "left",
                    isShow: true,
                    isSort: true,
                },
                {
                    name: this.tranService.translate("sim.label.khachhang"),
                    key: "customerName",
                    size: "250px",
                    align: "left",
                    isShow: true,
                    isSort: true,
                    className: "white-space-normal"
                },
                {
                    name: this.tranService.translate("sim.label.mahopdong"),
                    key: "contractCode",
                    size: "200px",
                    align: "left",
                    isShow: true,
                    isSort: true,
                },
                {
                    name: this.tranService.translate("sim.label.ngaylamhopdong"),
                    key: "contractDate",
                    size: "150px",
                    align: "left",
                    isShow: true,
                    isSort: true,
                    funcConvertText(value) {
                        return me.utilService.convertLongDateToString(value);
                    },
                }
            ]
        this.getListRatingPlan();
        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);

        this.columnsResultRegisterGroup = [
            {
                name: this.tranService.translate("sim.label.sothuebao"),
                key: "msisdn",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: true,
                style: {
                    cursor: "pointer",
                    color: "var(--mainColorText)"
                },
                funcGetRouting(item) {
                    return [`/sims/detail/${item.imsi}`]
                },
            },
            {
                name: this.tranService.translate("sim.label.trangthaisim"),
                key: "status",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: true,
                funcGetClassname: (value) => {
                    if (value == 0) {
                        return ['p-1', "border-round", "border-400", "text-color", "inline-block"];
                    } else if (value == CONSTANTS.SIM_STATUS.READY) {
                        // return ['p-1', "bg-blue-600", "border-round","inline-block"];
                        return ['p-1', "bg-green-600", "border-round", "inline-block"];
                    } else if (value == CONSTANTS.SIM_STATUS.ACTIVATED) {
                        return ['p-1', "bg-green-600", "border-round", "inline-block"];
                    } else if (value == CONSTANTS.SIM_STATUS.INACTIVED) {
                        return ['p-1', "bg-yellow-500", "border-round", "inline-block"];
                    } else if (value == CONSTANTS.SIM_STATUS.DEACTIVATED) {
                        return ['p-1', "bg-indigo-600", "border-round", "inline-block"];
                    } else if (value == CONSTANTS.SIM_STATUS.PURGED) {
                        return ['p-1', "bg-red-500", "border-round", "inline-block"];
                    } else if (value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY) {
                        return ['p-2', 'text-cyan-800', "bg-cyan-100", "border-round", "inline-block"];
                    } else if (value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY) {
                        return ['p-2', 'text-teal-800', "bg-teal-100", "border-round", "inline-block"];
                    } else if (value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY) {
                        return ['p-2', 'text-orange-700', "bg-orange-100", "border-round", "inline-block"];
                    }
                    return [];
                },
                funcConvertText: (value) => {
                    if (value == 0) {
                        return me.tranService.translate("sim.status.inventory");
                    } else if (value == CONSTANTS.SIM_STATUS.READY) {
                        // return me.tranService.translate("sim.status.ready");
                        return me.tranService.translate("sim.status.activated");
                    } else if (value == CONSTANTS.SIM_STATUS.ACTIVATED) {
                        return me.tranService.translate("sim.status.activated");
                    } else if (value == CONSTANTS.SIM_STATUS.DEACTIVATED) {
                        return me.tranService.translate("sim.status.deactivated");
                    } else if (value == CONSTANTS.SIM_STATUS.PURGED) {
                        return me.tranService.translate("sim.status.purged");
                    } else if (value == CONSTANTS.SIM_STATUS.INACTIVED) {
                        return me.tranService.translate("sim.status.inactivated");
                    } else if (value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY) {
                        return this.tranService.translate("sim.status.processingChangePlan");
                    } else if (value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY) {
                        return this.tranService.translate("sim.status.processingRegisterPlan");
                    } else if (value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY) {
                        return this.tranService.translate("sim.status.waitingCancelPlan");
                    }
                    return "";
                },
                style: {
                    color: "white"
                }
            },
            {
                name: this.tranService.translate("sim.label.khachhang"),
                key: "customerName",
                size: "250px",
                align: "left",
                isShow: true,
                isSort: true,
                funcGetClassname: (value) => {
                    return ['uppercase', 'white-space-normal'];
                }
            },
            {
                name: this.tranService.translate("sim.label.mahopdong"),
                key: "contractCode",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: true,
            },
        ]

        this.optionTableResultRegisterGroup = {
            hasClearSelected: true,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
            hasShowJumpPage: false
        }

        this.optionInputFile = {
            type: ['xls', 'xlsx'],
            messageErrorType: this.tranService.translate("global.message.wrongFileExcel"),
            maxSize: 10,
            unit: "MB",
            required: true,
            isShowButtonUpload: true,
            actionUpload: this.uploadFile.bind(this),
            disabled: false
        }
    }

    ngAfterContentChecked(): void {
        if (this.isShowDialogRegisterSim == false) {
            this.planSelected = null;
        }
        if (this.isShowDialogRegisterGroupSim == false) {
            this.planGroupSelected = null;
            this.groupSimSelected = null;
        }
    }

    onSubmitSearch() {
        this.pageNumber = 0;
        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
    }

    search(page, limit, sort, params) {
        let me = this;
        this.pageNumber = page;
        this.pageSize = limit;
        this.sort = sort;
        let dataParams = {
            page,
            size: limit,
            sort
        }
        this.updateParams(dataParams);
        this.dataSet = {
            content: [],
            total: 0
        }
        me.messageCommonService.onload();
        this.simService.search(dataParams, (response) => {
            // console.log(dataParams)
            me.dataSet = {
                content: response.content,
                total: response.totalElements
            }
            // console.log(this.dataSet)
        }, null, () => {
            me.messageCommonService.offload();
        })
    }

    updateParams(dataParams) {
        let me = this;
        Object.keys(this.searchInfo).forEach(key => {
            if (this.searchInfo[key] != null) {
                if (key == "dateFrom") {
                    dataParams["contractDateFrom"] = this.searchInfo.dateFrom.getTime();
                } else if (key == "dateTo") {
                    dataParams["contractDateTo"] = this.searchInfo.dateTo.getTime();
                } else if (key == "contractCode") {
                    dataParams["contractCode"] = me.utilService.stringToStrBase64(this.searchInfo.contractCode);
                } else {
                    dataParams[key] = this.searchInfo[key];
                }
            }
        })
    }


    getListRatingPlan(){
        let me = this;
        this.ratingPlanService.getAllRatingPlanPushForUser((response)=>{
            me.listRatingPlan = (response || []).map(el => {
                return {
                    ...el,
                    display: `${el.name||'unknown'} - ${el.code||'unknown'}`
                }
            });
            me.listRatingPlan.sort((a,b) => (a.name || "").toUpperCase().localeCompare((b.name || "").toUpperCase()) > 0 ? 1: -1);
            // me.listRatingPlanOrigin = [...this.listRatingPlan];
        })
    }
    getListRatingPlanByCustomerCode(customerCode) {
        let me = this;
        let param = {
            "customerCode": customerCode
        }
        this.ratingPlanService.getListRatingPlanByCustomerCode(param, (response) => {
            me.listRatingPlanOrigin = (response || []).map(el => {
                return {
                    ...el,
                    display: `${el.name || 'unknown'} - ${el.code || 'unknown'}`
                }
            });
        })
    }


    onChangeDateFrom(value) {
        if (value) {
            this.minDateTo = value;
        } else {
            this.minDateTo = null
        }
    }

    onChangeDateTo(value) {
        if (value) {
            this.maxDateFrom = value;
        } else {
            this.maxDateFrom = new Date();
        }
    }

    clearFileCallback() {
        this.isShowErrorUpload = false;
    }

    downloadTemplate() {
        this.ratingPlanService.downloadTemplate();
    }

    uploadFile(objectFile: any) {
        let me = this;
        me.messageCommonService.onload();
        this.ratingPlanService.uploadRegisterByFile(objectFile, (resApi) => {
            me.excuteResponseRegisterFileOrList(resApi);
        })
    }

    excuteResponseRegisterFileOrList(resApi) {
        let me = this;
        me.simImportsOrigin = undefined;
        me.isShowErrorUpload = false;
        this.optionInputFile.disabled = true;
        let response = resApi.listRatingError || [];
        if (resApi.total == 0) {
            me.messageCommonService.error(me.tranService.translate(resApi.message));
            me.isShowDialogResultRegisterFile = false;
            me.isShowDialogResultRegisterGroupSim = false;
            me.isShowDialogRegisterGroupSim = false;
            return;
        }
        if (resApi.message.toUpperCase() == "ok".toUpperCase()) {
            me.messageCommonService.warning(me.tranService.translate("ratingPlan.text.textResultRegisterByFile", {
                error: resApi.total - resApi.error,
                total: resApi.total
            }));
        } else {
            me.messageCommonService.error(me.tranService.translate(resApi.message))
        }
        if (response.length == 0) {
            me.isShowDialogResultRegisterFile = false;
            me.isShowDialogResultRegisterGroupSim = false;
            me.isShowDialogRegisterGroupSim = false;
            return;
        }
        //0 normal, 1 error imsi, 2 error planName
        let index = 0;
        me.mapFormSimImports = {};
        me.mapPlanNameError = {};
        me.mapImsiError = {};
        let excludeDescription = ['error.invalid.isdn.empty', "error.invalid.isdn.not.format", "error.invalid.rating.empty", "error.invalid.rating.not.format"];
        response.forEach(el => {
            if (!excludeDescription.includes(el.description)) {
                if (parseInt(el.type) == 0) {
                    if (el.msisdn != null && el.msisdn != "" && /^(\+?84)[1-9][0-9]{8,9}$/.test(el.msisdn)) {
                        if ((el.description || "") != "") {
                            if (el.description in me.mapImsiError) {
                                me.mapImsiError[el.description].push(el.msisdn);
                            } else {
                                me.mapImsiError[el.description] = [el.msisdn];
                            }
                        }
                    }
                } else if (parseInt(el.type) == 1) {
                    if (el.ratingPlanName != null && el.ratingPlanName != "" && /^[a-zA-Z0-9\-_]*$/.test(el.ratingPlanName)) {
                        if ((el.description || "") != "") {
                            if (el.description in me.mapPlanNameError) {
                                me.mapPlanNameError[el.description].push(el.planName);
                            } else {
                                me.mapPlanNameError[el.description] = [el.planName];
                            }
                        }
                    }
                }
            } else {
                el.description = ""
            }
            el['keyForm'] = `keyForm${index++}`;
            me.mapFormSimImports[el['keyForm']] = me.formBuilder.group(el);
            me.mapFormSimImports[el['keyForm']].controls['msisdn'].setValidators([Validators.required, Validators.pattern('^(\\+?84)[1-9][0-9]{8,9}$')]);
            me.mapFormSimImports[el['keyForm']].controls['msisdn'].updateValueAndValidity();
            me.mapFormSimImports[el['keyForm']].controls['ratingPlanName'].setValidators([Validators.required, Validators.pattern('^[^~`!@#\\$%\\^&*\\(\\)=\\+\\[\\]\\{\\}\\|\\\\,<>\\/?]*$')]);
            me.mapFormSimImports[el['keyForm']].controls['ratingPlanName'].updateValueAndValidity();
        });
        me.rowFirstSimImport = 0;
        me.pageSizeSimImport = 10;
        me.simImportsOrigin = [...response];
        me.simImports = me.simImportsOrigin.slice(0, 10);
    }

    removeItemSimImport(item, index) {
        // console.log(index);
        this.simImportsOrigin.splice(index, 1);
        this.simImports = this.simImportsOrigin.slice(this.rowFirstSimImport, this.rowFirstSimImport + this.pageSizeSimImport)
        delete this.mapFormSimImports[item['keyForm']];
        if (this.simImportsOrigin.length == 0) {
            this.isShowDialogResultRegisterFile = false;
        }
    }

    checkValidListImport() {
        if (this.simImports.length == 0) {
            return false;
        }
        let keys = Object.keys(this.mapFormSimImports);
        for (let i = 0; i < keys.length; i++) {
            let key = keys[i];
            if (this.mapFormSimImports[key].invalid) {
                return false;
            }
        }
        for (let i = 0; i < this.simImports.length; i++) {
            if (this.simImports[i].description != null && this.simImports[i].description != "") {
                return false;
            }
        }
        return true;
    }

    checkValueSimImportChange(item) {
        if (item.ratingPlanName != null && item.msisdn != null) {
            let description = "";
            let keyImsis = Object.keys(this.mapImsiError);
            let keyPlans = Object.keys(this.mapPlanNameError);
            for (let i = 0; i < keyImsis.length; i++) {
                if (this.mapImsiError[keyImsis[i]].includes(item.msisdn)) {
                    description = keyImsis[i];
                    break;
                }
            }
            if (description == "") {
                for (let i = 0; i < keyPlans.length; i++) {
                    if (this.mapPlanNameError[keyPlans[i]].includes(item.ratingPlanName)) {
                        description = keyPlans[i];
                        break;
                    }
                }
            }
            // console.log(description);
            if (description.indexOf("duplicated") >= 0) {
                let len = this.simImportsOrigin.map(el => el.msisdn).filter(el => el == item.msisdn).length;
                if (len == 1) {
                    description = "";
                    this.simImportsOrigin.forEach(el => {
                        if (el.description.indexOf("duplicated") >= 0 && el.msisdn == item) {
                            el.description = "";
                        }
                    })
                }
            }
            item.description = description;
        }
    }

    registerForSim() {
        if (this.planSelected == null || this.planSelected == undefined) return;
        let dataRequest = {
            "userId": 1,
            "msisdn": this.simSelected.msisdn,
            "ratingPlanId": this.planSelected
        }
        let me = this;
        me.messageCommonService.onload();
        this.ratingPlanService.registerPlanForSim(dataRequest, (response) => {
            if (me.typeRegisterForSim == "register") {
                me.messageCommonService.success(me.tranService.translate("ratingPlan.text.textRegisterSuccess"));
            } else {
                me.messageCommonService.success(me.tranService.translate("ratingPlan.text.textChangeSuccess"));
            }
            me.isShowDialogRegisterSim = false;
            me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);
        })

    }

    registerForGroupSim() {
        if (this.planGroupSelected == null || this.planGroupSelected == undefined || this.groupSimSelected == null || this.groupSimSelected == undefined) return;
        let dataRequest = {
            "userId": 1,
            "groupSimId": this.groupSimSelected,
            "ratingPlanId": this.planGroupSelected
        }
        let me = this;
        me.messageCommonService.onload();
        this.ratingPlanService.registerPlanForGroupSim(dataRequest, (response) => {
            // me.messageCommonService.success(me.tranService.translate("global.message.success"));
            // me.isShowDialogRegisterSim = false;
            // me.search(me.pageNumber,me.pageSize, me.sort, me.searchInfo);
            //
            // me.dataResultRegisterGroupOrigin = [{
            //     imsi: "841388100826",
            //     status: CONSTANTS.SIM_STATUS.ACTIVATED,
            //     customerInfo: "UBND Thị trấn Vân Đình",
            //     contractCode: "HNI-LD/01060626"
            // },{
            //     imsi: "84842138660",
            //     status: CONSTANTS.SIM_STATUS.PURGED,
            //     customerInfo: "Nguyễn Văn A",
            //     contractCode: "HNI-LD/00661242"
            // },{
            //     imsi: "84842139301",
            //     status: CONSTANTS.SIM_STATUS.ACTIVATED,
            //     customerInfo: "Tổng công ty Điện Lực Thành phố Hà Nội",
            //     contractCode: "HNI-LD/01060638"
            // },{
            //     imsi: "841388100826",
            //     status: CONSTANTS.SIM_STATUS.ACTIVATED,
            //     customerInfo: "UBND Thị trấn Vân Đình",
            //     contractCode: "HNI-LD/01060626"
            // },{
            //     imsi: "84842138660",
            //     status: CONSTANTS.SIM_STATUS.PURGED,
            //     customerInfo: "Nguyễn Văn A",
            //     contractCode: "HNI-LD/00661242"
            // },{
            //     imsi: "84842139301",
            //     status: CONSTANTS.SIM_STATUS.ACTIVATED,
            //     customerInfo: "Tổng công ty Điện Lực Thành phố Hà Nội",
            //     contractCode: "HNI-LD/01060638"
            // },{
            //     imsi: "841388100826",
            //     status: CONSTANTS.SIM_STATUS.ACTIVATED,
            //     customerInfo: "UBND Thị trấn Vân Đình",
            //     contractCode: "HNI-LD/01060626"
            // },{
            //     imsi: "84842138660",
            //     status: CONSTANTS.SIM_STATUS.PURGED,
            //     customerInfo: "Nguyễn Văn A",
            //     contractCode: "HNI-LD/00661242"
            // },{
            //     imsi: "84842139301",
            //     status: CONSTANTS.SIM_STATUS.ACTIVATED,
            //     customerInfo: "Tổng công ty Điện Lực Thành phố Hà Nội",
            //     contractCode: "HNI-LD/01060638"
            // }]
            //
            // me.totalResultRegisterGroup = response.total;
            // me.totalSuccessResultRegisterGroup = response.inact1 + response.inact2;
            // me.totalFailResultRegisterGroup = response.active + response.purge;
            // me.dataResultRegisterGroupOrigin = response.simInGroupResponses;
            // me.pageNumberResultRegister = 0;
            // me.pageSizeResultRegister = 10;
            // me.pagingResultRegisterGroup(0,10,null, null)
            // me.isShowDialogRegisterGroupSim = false;
            // me.isShowDialogResultRegisterGroupSim = true;
            me.excuteResponseRegisterForGroupSim(response);
            me.messageCommonService.offload();

        }, (error) => {
            me.messageCommonService.error(me.tranService.translate(error.error.error.message));
            me.isShowDialogRegisterGroupSim = false;
            me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);
        })
    }

    excuteResponseRegisterForGroupSim(resApi) {
        let me = this;
        me.simImportsOrigin = undefined;
        me.isShowErrorGroup = false;
        let response = resApi.listRatingError || [];
        if (resApi.total == 0) {
            me.messageCommonService.error(me.tranService.translate(resApi.message));
            me.isShowDialogResultRegisterGroupSim = false;
            me.isShowDialogRegisterGroupSim = false;
            return;
        }
        if (resApi.message.toUpperCase() == "ok".toUpperCase()) {
            me.messageCommonService.warning(me.tranService.translate("ratingPlan.text.textResultRegisterGroupSim", {
                error: resApi.total - resApi.error,
                total: resApi.total
            }));
        } else {
            me.messageCommonService.error(me.tranService.translate(resApi.message))
        }
        if (response.length == 0) {
            me.isShowDialogResultRegisterGroupSim = false;
            me.isShowDialogRegisterGroupSim = false;
            return;
        }
        me.isShowDialogResultRegisterGroupSim = true;
        //0 normal, 1 error imsi, 2 error planName
        let index = 0;
        me.mapFormSimImports = {};
        me.mapPlanNameError = {};
        me.mapImsiError = {};
        let excludeDescription = ["error.invalid.msisdn.not.active", 'error.invalid.isdn.empty', "error.invalid.isdn.not.format", "error.invalid.rating.empty", "error.invalid.rating.not.format"];
        response.forEach(el => {
            if (!excludeDescription.includes(el.description)) {
                if (parseInt(el.type) == 0) {
                    if (el.msisdn != null && el.msisdn != "" && /^(\+?84)[1-9][0-9]{8,9}$/.test(el.msisdn)) {
                        if ((el.description || "") != "") {
                            if (el.description in me.mapImsiError) {
                                me.mapImsiError[el.description].push(el.msisdn);
                            } else {
                                me.mapImsiError[el.description] = [el.msisdn];
                            }
                        }
                    }
                } else if (parseInt(el.type) == 1) {
                    if (el.ratingPlanName != null && el.ratingPlanName != "" && /^[a-zA-Z0-9\-_]*$/.test(el.ratingPlanName)) {
                        if ((el.description || "") != "") {
                            if (el.description in me.mapPlanNameError) {
                                me.mapPlanNameError[el.description].push(el.planName);
                            } else {
                                me.mapPlanNameError[el.description] = [el.planName];
                            }
                        }
                    }
                }
            } else {
                el.description = me.tranService.translate(el.description)
            }
            el['keyForm'] = `keyForm${index++}`;
            me.mapFormSimImports[el['keyForm']] = me.formBuilder.group(el);
            me.mapFormSimImports[el['keyForm']].controls['msisdn'].setValidators([Validators.required, Validators.pattern('^(\\+?84)[1-9][0-9]{8,9}$')]);
            me.mapFormSimImports[el['keyForm']].controls['msisdn'].updateValueAndValidity();
            me.mapFormSimImports[el['keyForm']].controls['ratingPlanName'].setValidators([Validators.required, Validators.pattern('^[^~`!@#\\$%\\^&*\\(\\)=\\+\\[\\]\\{\\}\\|\\\\,<>\\/?]*$')]);
            me.mapFormSimImports[el['keyForm']].controls['ratingPlanName'].updateValueAndValidity();
        });
        me.rowFirstSimImport = 0;
        me.pageSizeSimImport = 10;
        me.simImportsOrigin = [...response];
        me.simImports = me.simImportsOrigin.slice(0, 10);

    }

    registerForFile() {
        if (!this.checkValidListImport()) return;
        let me = this;
        let data = {
            listRegisterRatingPlan: this.simImportsOrigin,
            fileName: ""
        }
        me.messageCommonService.onload();
        this.ratingPlanService.uploadRegisterByList(data, (resApi) => {
            me.excuteResponseRegisterFileOrList(resApi);
        }, null, () => {
            me.messageCommonService.offload();
        })
    }

    pagingResultRegisterGroup(page, size, sort, params) {
        this.dataSetResultRegisterGroup = {
            content: this.dataResultRegisterGroupOrigin.slice(page * size, page * size + size),
            total: this.dataResultRegisterGroupOrigin.length
        }
    }

    pagingResultSimImport(event) {
        let first = event.first;
        let size = event.rows;
        this.rowFirstSimImport = first;
        this.pageSizeSimImport = size;
        this.simImports = this.simImportsOrigin.slice(first, first + size);
    }

    getDetailSim() {
        let me = this;
        this.messageCommonService.onload();
        me.simService.getById(me.simId, (response) => {
            me.detailSim = {
                ...response
            }
            me.getStatusSim();
            me.getDetailCustomer();
            me.getDetailRatingPlan();
            me.getDetailContract();
            me.getDetailApn();
            me.simService.getConnectionStatus([me.simId], (resp) => {
                me.detailSim.connectionStatus = resp[0]?.userstate
            }, () => {
            })
        }, null, () => {
            this.messageCommonService.offload();
        })
    }

    getStatusSim() {
        let me = this;
        this.simService.getDetailStatus(this.detailSim.msisdn, (response) => {
            me.detailStatusSim = {
                statusData: response.gprsStatus == 1,
                statusReceiveCall: response.icStatus == 1,
                statusSendCall: response.ocStatus == 1,
                statusWorldCall: response.iddStatus == 1,
                statusReceiveSms: response.smtStatus == 1,
                statusSendSms: response.smoStatus == 1
            };
        }, () => {
        })
    }

    getDetailCustomer() {
        this.detailCustomer = {
            name: this.detailSim.customerName,
            code: this.detailSim.customerCode
        }
    }

    getDetailRatingPlan() {
        this.simService.getDetailPlanSim(this.detailSim.msisdn, (response) => {
            this.detailRatingPlan = {
                ...response
            }
        }, () => {
        })

    }

    getDetailContract() {
        this.simService.getDetailContract(this.utilService.stringToStrBase64(this.detailSim.contractCode), (response) => {
            this.detailContract = response;
        }, () => {
        })
    }

    getDetailApn() {
        this.detailAPN = {
            code: this.detailSim.apnCode,
            type: "Kết nối bằng 3G",
            ip: 0,
            rangeIp: this.detailSim.ip
        }
    }

    getNameStatus(value) {
        if (value == 0) {
            return this.tranService.translate("sim.status.inventory");
        } else if (value == CONSTANTS.SIM_STATUS.READY) {
            // return this.tranService.translate("sim.status.ready");
            return this.tranService.translate("sim.status.activated");
        } else if (value == CONSTANTS.SIM_STATUS.ACTIVATED) {
            return this.tranService.translate("sim.status.activated");
        } else if (value == CONSTANTS.SIM_STATUS.DEACTIVATED) {
            return this.tranService.translate("sim.status.deactivated");
        } else if (value == CONSTANTS.SIM_STATUS.PURGED) {
            return this.tranService.translate("sim.status.purged");
        } else if (value == CONSTANTS.SIM_STATUS.INACTIVED) {
            return this.tranService.translate("sim.status.inactivated");
        } else if (value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY) {
            return this.tranService.translate("sim.status.processingChangePlan");
        } else if (value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY) {
            return this.tranService.translate("sim.status.processingRegisterPlan");
        } else if (value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY) {
            return this.tranService.translate("sim.status.waitingCancelPlan");
        }
        return "";
    }

    getClassStatus(value) {
        if (value == 0) {
            return ['p-1', "border-round", "border-400", "text-color", "inline-block"];
        } else if (value == CONSTANTS.SIM_STATUS.READY) {
            // return ['p-1', "bg-blue-600", "border-round","inline-block"];
            return ['p-2', "text-green-800", "bg-green-100", "border-round", "inline-block"];
        } else if (value == CONSTANTS.SIM_STATUS.ACTIVATED) {
            return ['p-2', 'text-green-800', "bg-green-100", "border-round", "inline-block"];
        } else if (value == CONSTANTS.SIM_STATUS.INACTIVED) {
            return ['p-2', 'text-yellow-800', "bg-yellow-100", "border-round", "inline-block"];
        } else if (value == CONSTANTS.SIM_STATUS.DEACTIVATED) {
            return ['p-2', 'text-indigo-600', "bg-indigo-100", "border-round", "inline-block"];
        } else if (value == CONSTANTS.SIM_STATUS.PURGED) {
            return ['p-2', 'text-red-700', "bg-red-100", "border-round", "inline-block"];
        } else if (value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY) {
            return ['p-2', 'text-cyan-800', "bg-cyan-100", "border-round", "inline-block"];
        } else if (value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY) {
            return ['p-2', 'text-teal-800', "bg-teal-100", "border-round", "inline-block"];
        } else if (value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY) {
            return ['p-2', 'text-orange-700', "bg-orange-100", "border-round", "inline-block"];
        }
        return [];
    };

    getServiceType(value) {
        if (value == CONSTANTS.SERVICE_TYPE.PREPAID) return this.tranService.translate("sim.serviceType.prepaid")
        else if (value == CONSTANTS.SERVICE_TYPE.POSTPAID) return this.tranService.translate("sim.serviceType.postpaid")
        else return ""
    };

    getDetailPLan() {
        let me = this;
        this.messageCommonService.onload();
        me.ratingPlanService.getById(Number(me.planId), (response) => {
            me.response = response

            me.ratingPlanInfo.id = response.id
            me.ratingPlanInfo.code = response.code
            me.ratingPlanInfo.name = response.name
            me.ratingPlanInfo.status = response.status
            me.ratingPlanInfo.dispatchCode = response.dispatchCode
            me.ratingPlanInfo.customerType = response.customerType
            me.ratingPlanInfo.subscriptionFee = response.subscriptionFee
            me.ratingPlanInfo.subscriptionType = response.paidType
            me.ratingPlanInfo.ratingScope = response.ratingScope
            me.ratingPlanInfo.cycleTimeUnit = response.cycleTimeUnit
            me.ratingPlanInfo.cycleInterval = response.cycleInterval
            me.ratingPlanInfo.reload = response.reload
            me.ratingPlanInfo.flat = response.flat
            me.ratingPlanInfo.limitDataUsage = response.limitDataUsage
            me.ratingPlanInfo.limitSmsOutside = response.limitSmsOutside
            me.ratingPlanInfo.limitSmsInside = response.limitSmsInside
            me.ratingPlanInfo.flexible = response.flexible
            me.ratingPlanInfo.feePerDataUnit = response.feePerDataUnit
            me.ratingPlanInfo.squeezedSpeed = response.squeezedSpeed
            me.ratingPlanInfo.feeSmsInside = response.feeSmsInside
            me.ratingPlanInfo.feeSmsOutside = response.feeSmsOutside
            me.ratingPlanInfo.maximumFee = response.maximumFee
            me.ratingPlanInfo.dataRoundUnit = response.dataRoundUnit
            me.ratingPlanInfo.downSpeed = response.downSpeed
            me.ratingPlanInfo.provinceCode = response.provinceCode
            me.ratingPlanInfo.description = response.description;

            me.getReload(me.ratingPlanInfo.reload)
            me.getFlexible(me.ratingPlanInfo.flexible)
            me.myProvices = ""

            me.accountService.getListProvince((data) => {
                me.provinces = data.map(el => {
                    return {
                        code: el.code,
                        name: `${el.name}`
                    }
                })
                me.provinces.forEach(el => {
                    if (me.ratingPlanInfo.provinceCode.includes(el.code)) {
                        me.myProvices += `${el.name}, `;

                    }
                })
                if (me.myProvices.length > 0) {
                    me.myProvices = me.myProvices.substring(0, me.myProvices.length - 2);
                }
            })

        }, null, () => {
            me.messageCommonService.offload();
        })
    };

    getReload(value) {
        if (value == CONSTANTS.RELOAD.YES) {
            return this.checkedReload = true;
        } else if (value == CONSTANTS.RELOAD.NO) {
            return this.checkedReload = false
        }
        return "";
    }

    getFlexible(value) {
        if (value == CONSTANTS.FLEXIBLE.YES) {
            return this.checkedFlexible = true;
        } else if (value == CONSTANTS.FLEXIBLE.NO) {
            return this.checkedFlexible = false
        }
        return "";
    };

    getNameCustomerType(value) {
        if (value == CONSTANTS.CUSTOMER_TYPE.PERSONAL) {
            return this.tranService.translate("ratingPlan.customerType.personal");
        } else if (value == CONSTANTS.CUSTOMER_TYPE.INTERPRISE) {
            return this.tranService.translate("ratingPlan.customerType.enterprise");
        } else if (value == CONSTANTS.CUSTOMER_TYPE.AGENCY) {
            return this.tranService.translate("ratingPlan.customerType.agency");
        }
        return "";
    };

    checkSubscriptionType() {
        this.subscriptionTypes = [{
            type: this.tranService.translate("ratingPlan.subscriptionType.post"),
            ip: CONSTANTS.SUBSCRIPTION_TYPE.POSTPAID,
        },
            {
                type: this.tranService.translate("ratingPlan.subscriptionType.pre"),
                ip: CONSTANTS.SUBSCRIPTION_TYPE.PREPAID,
            }]
    };

    getCycleTimeUnit(value) {
        if (value == CONSTANTS.CYCLE_TIME_UNITS.DAY) {
            return this.tranService.translate("ratingPlan.cycle.day");
        } else if (value == CONSTANTS.CYCLE_TIME_UNITS.MONTH) {
            return this.tranService.translate("ratingPlan.cycle.month");
        }
        return "";
    };

    getRatingScope(value) {
        if (value == CONSTANTS.RATING_PLAN_SCOPE.NATION_WIDE) {
            return this.tranService.translate("ratingPlan.ratingScope.nativeWide");
        } else if (value == CONSTANTS.RATING_PLAN_SCOPE.CUSTOMER) {
            return this.tranService.translate("ratingPlan.ratingScope.customer");
        }
        return "";
    };


    protected readonly CONSTANTS = CONSTANTS;
}
