<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{this.tranService.translate("ticket.menu.config")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
    <div class="col-5 flex flex-row justify-content-end align-items-center">

    </div>
</div>


<p-card styleClass="mt-3" *ngIf="formTicketConfig">
    <div>
        <form [formGroup]="formTicketConfig" (ngSubmit)="onSubmitUpdate()">
            <div class="flex flex-row justify-content-between">
                <div style="width: 49%;">
                    <!-- ten tinh -->
                    <div class="w-full field grid">
                        <label htmlFor="accountName" class="col-fixed" style="width:180px">{{tranService.translate("ticket.label.config.provinceName")}}</label>
                        <div class="col">
                            {{ticketConfig.provinceName}}
                        </div>
                    </div>
                    <!-- ma tinh -->
                    <div class="w-full field grid">
                        <label htmlFor="fullName" class="col-fixed" style="width:180px">{{tranService.translate("ticket.label.config.provinceCode")}}</label>
                        <div class="col">
                            {{ticketConfig.provinceCode}}
                        </div>
                    </div>
                    <!-- danh sách email -->
                    <div class="w-full field grid">
                        <label htmlFor="roles" class="col-fixed" style="width:180px">{{tranService.translate("ticket.label.config.email")}}</label>
                        <div class="col" style="max-width: calc(100% - 180px) !important;">
                            <vnpt-select
                                [control]="controlComboSelect"
                                class="w-full"
                                [(value)]="ticketConfig.emailInfos"
                                [placeholder]="tranService.translate('ticket.text.selectEmail')"
                                objectKey="account"
                                paramKey="email"
                                keyReturn="id"
                                displayPattern="${email}"
                                typeValue="object"
                                [paramDefault]="paramSearchCustomerProvince"
                            ></vnpt-select>
                        </div>
                    </div>

                    <div class="flex flex-row justify-content-center align-items-center">
                        <p-button [label]="tranService.translate('global.button.cancel')" styleClass="p-button-secondary p-button-outlined mr-2" (click)="closeForm()"></p-button>
                        <p-button [label]="tranService.translate('global.button.save')" styleClass="p-button-info" type="submit"></p-button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</p-card>
