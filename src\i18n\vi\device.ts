export default {
    label: {
        imei: "IMEI",
        location: "Địa chỉ",
        subcriber: "<PERSON>ố thuê bao",
        country: "Xuất xứ",
        category: "Chủng loại",
        expireFrom: "<PERSON><PERSON><PERSON> hết hạn từ",
        expireTo: "<PERSON><PERSON><PERSON> hết hạn đến",
        expireDate: "<PERSON><PERSON>y hết hạn",
        deviceType: "Loại thiết bị",
        msisdn: "Số thuê bao",
        note: "Ghi chú",
        importByFile: "Nhập thiết bị bằng file",
        iotLink: "Quản lý bởi nền tảng IoT",
    },
    text: {
        messageSuccess: "L<PERSON>u thành công",
        textResultImportByFile: "Danh sách đăng ký lỗi đang được tải về",
        wrongFormat: "Tệp phải là excel (xlsx)",
        tooBig: "Tập tin quá lớn",
        columnInvalid: "file không đúng định dạng",
        msisdnEmpty: "<PERSON><PERSON> thuê bao không được để trống",
        msisdnNotExists: "<PERSON><PERSON> thuê bao không tồn tại",
        msisdnAssign: "Số thuê bao đã được gán cho thiết bị khác",
        msisdnInvalid: "Số thuê bao phải là số có đầu 84 (11-12 ký tự)",
        msisdnIsEmptly: "Số thuê bao không được để trống",
        msisdnIsDuplicate: "Số thuê bao trùng trong file",
        imeiIsDuplicate: "IMEI trùng trong file",
        expriredDateInvalid: "Ngày hết hạn phải có định dạng dd/mm/yyyy",
        msisdnNotPermission: "Số thuê bao không tồn tại hoặc không có quyền trên số thuê bao",
        imeiIsExist: "IMEI đã được gán cho thiết bị khác",
        maxRowImport: "File không được quá 1000 dòng",
        imeiLen: "IMEI không hợp lệ. Vui lòng nhập từ 2 đến 64 ký tự không bao gồm ký tự đặc biệt",
        deviceTypeLen: "DEVICE TYPE không hợp lệ. Vui lòng nhập từ 2 đến 64 ký tự không bao gồm ký tự đặc biệt",
        countryLen: "MADEIN không hợp lệ. Vui lòng nhập từ 2 đến 32 ký tự không bao gồm ký tự đặc biệt",
    }
}
