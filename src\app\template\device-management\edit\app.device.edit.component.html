<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{ tranService.translate("global.menu.listdevice") }}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
</div>
<p-card styleClass="mt-3 responsive-form">
    <form [formGroup]="formEditDevice" (ngSubmit)="update()">
        <div class="grid mx-4 my-3">
            <!--            imei-->
            <div class="col-3 ">
                <span class="p-float-label">
                    <input class="w-full"
                           pInputText id="imei"
                           [(ngModel)]="deviceInfo.imei"
                           formControlName="imei"
                           pattern="^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]{1,64}$"
                           (ngModelChange)="checkExistsImei()"
                    >
                    <div
                        *ngIf="formEditDevice.controls['imei'].invalid && (formEditDevice.controls['imei'].dirty || formEditDevice.controls['imei'].touched)">
                        <div class="text-red-500" *ngIf="formEditDevice.controls['imei'].errors?.pattern">
                            {{ tranService.translate("global.message.invalidinformation64") }}
                        </div>
                    </div>
                    <div class="text-red-500" *ngIf="isShowExistsImei">
                        {{ tranService.translate("global.message.exists", {type: tranService.translate("device.label.imei").toLowerCase()}) }}
                    </div>
                    <label htmlFor="imei">{{ tranService.translate("device.label.imei") }}</label>
                </span>
            </div>
            <!--            vị trí-->
            <div class="col-3">
                <span class="p-float-label">
                    <input class="w-full"
                           pInputText id="deviceType"
                           [(ngModel)]="deviceInfo.deviceType"
                           formControlName="deviceType"
                           pattern="^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]{2,64}$">
                   <div
                       *ngIf="formEditDevice.controls['deviceType'].invalid && (formEditDevice.controls['deviceType'].dirty || formEditDevice.controls['deviceType'].touched)">
                    <div class="text-red-500" *ngIf="formEditDevice.controls['deviceType'].errors?.pattern">
                        {{ tranService.translate("global.message.invalidinformation64") }}
                    </div>
                </div>
            <label htmlFor="deviceType">{{ tranService.translate("device.label.deviceType") }}</label>
            </span>
            </div>
            <!--            so thue bao-->
            <!--            <div class="col-3">-->
            <!--                <span class="p-float-label">-->
            <!--                    <p-dropdown styleClass="w-full"-->
            <!--                        [options]="listSubscription"-->
            <!--                        [(ngModel)]="deviceInfo.msisdn"-->
            <!--                                optionLabel="msisdn"-->
            <!--                                optionValue="msisdn"-->
            <!--                                filter="true"-->
            <!--                                filterBy="msisdn"-->
            <!--                                formControlName="msisdn"-->
            <!--                    ></p-dropdown>-->
            <!--            <label htmlFor="msisdn">{{tranService.translate("device.label.msisdn")}}</label>-->
            <!--            </span>-->
            <!--            </div>-->
            <div class="col-3">
                <span class="p-float-label">
                    <p-autoComplete styleClass="w-full" inputStyle=""
                                    [suggestions]="filteredSubscription"
                                    (completeMethod)="filterMsisdn($event)"
                                    field="msisdn"
                                    inputStyleClass="w-full"
                                    formControlName="msisdn"
                                    (input)="onInput($event)"
                                    (onSelect)="onSelect($event)"
                                    [required]="true"
                    ></p-autoComplete>
            <label htmlFor="msisdn">{{ tranService.translate("device.label.msisdn") }}</label>
            </span>
                <div class="text-red-500" *ngIf="!msisdnEntered">
                    {{ tranService.translate("global.message.required") }}
                </div>
                <div class="text-red-500" *ngIf="msisdnEntered && showValidationMsisdnError">
                    {{ tranService.translate("global.message.invalidPhone") }}
                </div>
                <div class="text-red-500" *ngIf="msisdnEntered && !showValidationMsisdnError && notPermissionMisidn">
                    {{ tranService.translate("global.message.notPermissionMisidn") }}
                </div>
            </div>
            <div class="col-3">
                <span class="p-float-label">
                    <input class="w-full"
                           pInputText id="country"
                           [(ngModel)]="deviceInfo.country"
                           formControlName="country"
                           pattern="^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]{2,32}$">
                    <div
                        *ngIf="formEditDevice.controls['country'].invalid && (formEditDevice.controls['country'].dirty || formEditDevice.controls['country'].touched)">
                    <div class="text-red-500" *ngIf="formEditDevice.controls['country'].errors?.pattern">
                        {{ tranService.translate("global.message.invalidinformation32") }}
                    </div>
                </div>
            <label htmlFor="country">{{ tranService.translate("device.label.country") }}</label>
            </span>
            </div>
            <!--category-->
            <!--            <div class="col-3">-->
            <!--                <span class="p-float-label">-->
            <!--                    <input class="w-full"-->
            <!--                           pInputText id="category"-->
            <!--                           [(ngModel)]="deviceInfo.category"-->
            <!--                           formControlName="category"-->
            <!--                           pattern="^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]{2,255}$">-->
            <!--                    <div-->
            <!--                        *ngIf="formEditDevice.controls['category'].invalid && (formEditDevice.controls['category'].dirty || formEditDevice.controls['category'].touched)">-->
            <!--                    <div class="text-red-500" *ngIf="formEditDevice.controls['category'].errors?.pattern">-->
            <!--                        {{tranService.translate("global.message.invalidinformation")}}-->
            <!--                    </div>-->
            <!--                </div>-->
            <!--            <label htmlFor="category">{{tranService.translate("device.label.category")}}</label>-->
            <!--            </span>-->
            <!--            </div>-->

            <div class="col-3 ">
                <span class="p-float-label">
                    <p-calendar styleClass="w-full"
                                id="expiredDate"
                                [(ngModel)]="deviceInfo.expiredDate"
                                formControlName="expiredDate"
                                [showIcon]="true"
                                [showClear]="true"
                                dateFormat="dd/mm/yy"
                    ></p-calendar>
            <label htmlFor="expiredDate">{{ tranService.translate("device.label.expireDate") }}</label>
            </span>
            </div>
<!--            <div class="col-3">-->
<!--                <span class="p-float-label">-->
<!--                    <input class="w-full"-->
<!--                           pInputText id="location"-->
<!--                           [(ngModel)]="deviceInfo.location"-->
<!--                           formControlName="location"-->
<!--                           pattern="^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]{2,255}$">-->
<!--                    <div-->
<!--                        *ngIf="formEditDevice.controls['location'].invalid && (formEditDevice.controls['location'].dirty || formEditDevice.controls['location'].touched)">-->
<!--                    <div class="text-red-500" *ngIf="formEditDevice.controls['location'].errors?.pattern">-->
<!--                        {{ tranService.translate("global.message.invalidinformation") }}-->
<!--                    </div>-->
<!--                </div>-->
<!--            <label htmlFor="location">{{ tranService.translate("device.label.location") }}</label>-->
<!--            </span>-->
<!--            </div>-->
            <div class="col-3">
                <label class="flex align-items-center">
                    <p-checkbox formControlName="iotLink" inputId="iotLink" [binary]="true"
                                [ngStyle]="{'margin': '6px'}"></p-checkbox>
                    <label for="iotLink">{{ tranService.translate("device.label.iotLink") }}</label>
                </label>
            </div>
            <!--&lt;!&ndash;            ghi chú&ndash;&gt;-->
            <!--            <div class="col-3">-->
            <!--                <span class="p-float-label">-->
            <!--                    <input class="w-full"-->
            <!--                           pInputText id="note"-->
            <!--                           [(ngModel)]="deviceInfo.note"-->
            <!--                           formControlName="note"-->
            <!--                           pattern="^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]{2,255}$">-->
            <!--                    <div-->
            <!--                        *ngIf="formEditDevice.controls['note'].invalid && (formEditDevice.controls['note'].dirty || formEditDevice.controls['note'].touched)">-->
            <!--                    <div class="text-red-500" *ngIf="formEditDevice.controls['note'].errors?.pattern">-->
            <!--                        {{tranService.translate("global.message.invalidinformation")}}-->
            <!--                    </div>-->
            <!--                </div>-->
            <!--            <label htmlFor="note">{{tranService.translate("device.label.note")}}</label>-->
            <!--            </span>-->
            <!--            </div>-->

        </div>
<!--        <div class="col-offset-3 col-6">-->
<!--            <iframe [src]="safeUrl" class="w-full" style="border:0;" allowfullscreen="" loading="lazy"-->
<!--                    referrerpolicy="no-referrer-when-downgrade"></iframe>-->
<!--        </div>-->
        <div class="flex flex-row justify-content-center align-items-center mt-3">
            <p-button styleClass="p-button-secondary p-button-outlined mr-2" (click)="goBack()">
                {{ tranService.translate("global.button.back") }}
            </p-button>
            <p-button styleClass="p-button-info" type="submit"
                      [disabled]="formEditDevice.invalid || !msisdnEntered || showValidationMsisdnError || notPermissionMisidn || isShowExistsImei"
                      *ngIf="checkAuthen([CONSTANTS.PERMISSIONS.DEVICE.CREATE])">
                {{ tranService.translate("global.button.save") }}
            </p-button>
        </div>
    </form>
</p-card>
