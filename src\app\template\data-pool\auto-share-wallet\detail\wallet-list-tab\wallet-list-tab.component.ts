import {
    AfterContentChecked,
    AfterViewInit,
    Component,
    EventEmitter,
    Inject,
    Injector, Input,
    OnInit,
    Output,
    TemplateRef,
    ViewChild
} from '@angular/core';
import {ComponentBase} from "../../../../../component.base";
import {
    SeperateFilterInfo,
    SeperateSearchInfo
} from "../../../../common-module/search-filter-separate/search-filter-separate.component";
import {ColumnInfo, OptionTable} from "../../../../common-module/table/table.component";
import {TrafficWalletService} from "../../../../../service/datapool/TrafficWalletService";
import {CONSTANTS} from "../../../../../service/comon/constants";
import {UtilService} from "../../../../../service/comon/util.service";


@Component({
    selector: 'wallet-list-tab',
    templateUrl: './wallet-list-tab.component.html',
    styleUrls: ['./wallet-list-tab.component.scss']
})
export class WalletListTabComponent extends ComponentBase implements OnInit, AfterViewInit, AfterContentChecked {
    @Output() walletListTemplateRefEmitter: EventEmitter<TemplateRef<any>> = new EventEmitter<TemplateRef<any>>();
    @ViewChild('walletListTemplateRef') walletListTemplateRef: TemplateRef<any>;
    searchList: Array<SeperateSearchInfo>;
    filterList: Array<SeperateFilterInfo>;
    columns: Array<ColumnInfo>;
    dataSet: {
        content: Array<any>,
        total: number
    };
    @Input() trafficType: any
    autoGroupId : number
    optionTable: OptionTable;
    pageNumber: number = 0;
    pageSize: number = 10;
    sort: string;
    searchInfo: any
    isShowModalDetail: boolean = false;
    subCodeId: number | string;
    walletDetail: any;
    listDetail:any;
    canView: boolean = false;
    minutesText = this.tranService.translate("alert.label.minutes");
    columnsShareWallet: Array<ColumnInfo>;
    dataSetShareWallet: {
        content: Array<any>,
        total: number
    };
    optionTableShareWallet: OptionTable;
    pageNumberShareWallet: number;
    pageSizeShareWallet: number;
    sortShareWallet: string;
    // Lưu lại purchasedTraffic để chia phần trăm khi xem chi tiết
    purchasedTraffic: number;
    isShowTableInDialogDetail: boolean;
    getUnixTime(dateString: string): number {
        const date = new Date(dateString);
        return date.getTime() / 1000; // Chia cho 1000 để chuyển từ milliseconds sang seconds
    };

    getFormattedDateCrd(dateString: string, addDate?: number): string {
        let date = new Date(dateString);

        if (addDate) {
            date.setUTCDate(date.getUTCDate() + addDate);
        }

        const day = date.getUTCDate().toString().padStart(2, '0'); // Lấy ngày và thêm số 0 nếu cần
        const month = (date.getUTCMonth() + 1).toString().padStart(2, '0'); // Lấy tháng (lưu ý tháng trong JavaScript bắt đầu từ 0)
        const year = date.getUTCFullYear();
        const hours = date.getUTCHours().toString().padStart(2, '0');
        const minutes = date.getUTCMinutes().toString().padStart(2, '0');
        const seconds = date.getUTCSeconds().toString().padStart(2, '0');

        return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
    }
    getWalletDetail() {
        let me = this;
        me.messageCommonService.onload();
        me.trafficWalletService.getById({subCode:me.subCodeId}, (res) => {
            me.walletDetail = res
            let startDate = me.getUnixTime(res.startDate);
            let endDate = me.getUnixTime(res.endDate);
            me.walletDetail.timeToUse = (endDate-startDate)/(60 * 60 * 24) +" ngày " +"["+me.getFormattedDateCrd(res.startDate)+"-"+me.getFormattedDateCrd(res.endDate)+"]"
            me.listDetail = res.listShared
            me.canView = true
            me.isShowModalDetail = true;
            me.searchShareWallet(0, 10, me.sortShareWallet, {subCode: this.subCodeId})
        },(error)=>{
            if(error.error.error.errorCode === "error.forbidden.view.detail"){
                this.messageCommonService.error("Bạn không có quyền truy cập thông tin này")
                this.router.navigate(["/data-pool/walletMgmt/list"])
            }
        },()=>{ me.messageCommonService.offload() })
    };

    getFormattedDate(dateString: string, addDate?: number): string {
        let me = this;
        let date = new Date(dateString);

        if (addDate) {
            date.setDate(date.getDate() + addDate);
        }

        const day = date.getDate().toString().padStart(2, '0'); // Lấy ngày và thêm số 0 nếu cần
        const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Lấy tháng (lưu ý tháng trong JavaScript bắt đầu từ 0)
        const year = date.getFullYear();
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        const seconds = date.getSeconds().toString().padStart(2, '0');

        return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
    };

    parseDateTime(dateString) {
        // Split the date and time parts
        let [datePart, timePart] = dateString.split(' ');

        // Split the date part by '/'
        let dateParts = datePart.split('/');
        let day = parseInt(dateParts[0], 10);
        let month = parseInt(dateParts[1], 10) - 1; // Months are 0-based in JavaScript
        let year = parseInt(dateParts[2], 10);

        // Split the time part by ':'
        let timeParts = timePart.split(':');
        let hours = parseInt(timeParts[0], 10);
        let minutes = parseInt(timeParts[1], 10);
        let seconds = parseInt(timeParts[2], 10);

        // Create a new Date object
        return new Date(year, month, day, hours, minutes, seconds);
    };

    getValueMethodAutoShare(value) {
        let me = this;
        if (value == CONSTANTS.METHOD_AUTO_SHARE.PAY_CODE) {
            return me.tranService.translate("datapool.methodAutoShare.payCode")
        } else if (value == CONSTANTS.METHOD_AUTO_SHARE.SUB_CODE) {
            return me.tranService.translate("datapool.methodAutoShare.subCode")
        } else {
            return me.tranService.translate("datapool.methodAutoShare.none")
        }
    }

    constructor(
        @Inject(TrafficWalletService) private trafficWalletService: TrafficWalletService, injector: Injector
    ) {
        super(injector);
        this.autoGroupId = parseInt(this.route.snapshot.paramMap.get('id'));
    }

    search(page, limit, sort, params) {
        let me = this;
        this.pageNumber = page;
        this.pageSize = limit;
        // this.sort = sort;
        let dataParams = {
            page,
            size: limit,
            // sort
        }
        this.messageCommonService.onload()

        Object.keys(this.searchInfo).forEach(key => {
            dataParams[key] = this.searchInfo[key];
        })
        dataParams['autoGroupId'] = this.autoGroupId
        // console.log(dataParams)
        this.trafficWalletService.searchWallet(dataParams, (response) => {
            me.dataSet = {
                content: response.content,
                total: response.totalElements
            }
        }, null, () => {
            me.messageCommonService.offload();
        })
    }

    ngOnInit(): void {
        this.optionTable = {
            hasClearSelected: false,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
        }
        this.searchInfo = {
            searchPackageName: 1,
            searchWalletCode: 1,
            searchName: 0,
            searchPayCode: 0,
            searchPhone: 0,
            autoGroupId: this.autoGroupId,
            value: ""
        };
        this.searchList = [{
            name: this.tranService.translate("datapool.label.walletCode"),
            key: "searchWalletCode"
        }, {
            name: this.tranService.translate("datapool.label.payCode"),
            key: "searchPayCode"
        }, {
            name: this.tranService.translate("datapool.label.phone"),
            key: "searchPhone"
        }];
        let me = this;
        this.dataSet = {
            content: [],
            total: 0
        }
        this.columns = [
            {
                name: this.tranService.translate("datapool.label.walletCode"),
                key: "subCode",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
                style:{
                    cursor: "pointer",
                    color: "var(--mainColorText)"
                },
                funcClick(id, item) {
                    me.subCodeId = item.subCode;
                    me.getWalletDetail();
                    me.purchasedTraffic = item.purchasedTraffic;
                    me.isShowTableInDialogDetail= false
                },
            },
            {
                name: this.tranService.translate("datapool.label.packageName"),
                key: "packageName",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
                style:{
                    whiteSpace: "pre",
                }
            }, {
                name: this.tranService.translate('datapool.label.remainData')+"/ "+ this.tranService.translate("datapool.label.purchasedData"),
                key: "remainData",
                size: "fit-content",
                align: "left",
                isShow: true,
                isSort: false,
                funcConvertText(value, item) {
                    let remain = me.utilService.convertNumberToString(item.totalRemainingTraffic);
                    let total =  me.utilService.convertNumberToString(item.purchasedTraffic);
                    if(item.trafficType == "Gói Data"){
                        return remain+"/ "+total +" MB";
                    }else if(item.trafficType == "Gói thoại"){
                        return remain+"/ "+total + me.minutesText;
                    } else if((item.trafficType || "").toUpperCase().includes("Gói SMS".toUpperCase())){
                        return remain+"/ "+total + " SMS";
                    }
                    return item.totalRemainingTraffic+"/ "+item.purchasedTraffic;
                },
            },
            {
                name: this.tranService.translate("datapool.label.usedTime"),
                key: "timeUsed",
                size: "fit-content",
                align: "left",
                isShow: true,
                isSort: false,
            }
        ];
        this.columnsShareWallet = [{
            name: this.tranService.translate("datapool.label.phoneFull"),
            key: "phoneReceipt",
            size: "fit-content",
            align: "left",
            isShow: true,
            isSort: false
        },{
            name: this.tranService.translate("datapool.label.fullName"),
            key: "name",
            size: "fit-content",
            align: "left",
            isShow: true,
            isSort: false
        },{
            name: this.tranService.translate("datapool.label.email"),
            key: "email",
            size: "fit-content",
            align: "left",
            isShow: true,
            isSort: false
        },{
            name: this.tranService.translate("datapool.label.sharedTime"),
            key: "timeUpdate",
            size: "fit-content",
            align: "left",
            isShow: true,
            isSort: false,
            funcConvertText(value) {
                return me.getFormattedDate(value)
            }
        },{
            name: this.tranService.translate("datapool.label.usedDate"),
            key: "sharingDay",
            size: "fit-content",
            align: "left",
            isShow: true,
            isSort: false,
            funcConvertText(value, item) {
                return me.getFormattedDate(value, item.dayExprired)
            }
        },{
            name: this.tranService.translate("datapool.label.shared"),
            key: "trafficShare",
            size: "fit-content",
            align: "left",
            isShow: true,
            isSort: false,
            funcConvertText(value) {
                console.log(me.trafficType)
                if ((me.trafficType || '').toUpperCase() == 'Data'.toUpperCase()) return value + ' MB'
                else if ((me.trafficType || '').toUpperCase().includes('SMS'.toUpperCase())) return value + ' SMS'
                else if ((me.trafficType || '').toUpperCase() == 'thoại'.toUpperCase()) return value + me.tranService.translate('alert.label.minutes')
                else return value;
            }
        },{
            name: this.tranService.translate("datapool.label.percentage"),
            key: "trafficShare",
            size: "fit-content",
            align: "left",
            isShow: true,
            isSort: false,
            funcConvertText(value) {
                let result = 100.0 * value/me.purchasedTraffic;
                return Math.round(result * 100.0)/100.0 + " %"
            }
        },
        ]
        this.dataSetShareWallet = {
            content: [],
            total: 0
        }
        this.pageNumberShareWallet = 0,
            this.pageSize = 10;
        this.sortShareWallet = '';
        this.optionTableShareWallet = {
            hasClearSelected: false,
            hasShowChoose: false,
            hasShowIndex: false,
            hasShowToggleColumn: false,
            action: null
        }
        this.isShowTableInDialogDetail = false;
        this.search1();
    }


    ngAfterViewInit() {
        this.walletListTemplateRefEmitter.emit(this.walletListTemplateRef)
    }

    getTypeSharingDataColumn(value) {
        if (this.trafficType?.toUpperCase().includes('SMS')) {
            return value + " SMS"
        }
        if (this.trafficType?.toUpperCase().includes('DATA')) {
            return value + " MB"
        }
        return ""
    }


    ngAfterContentChecked() {
    }

    search1() {
        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
    }

    formatNumber(value: number): string {
        return new Intl.NumberFormat('vi-VN').format(value);
    }
    searchShareWallet(page,limit, sort, params) {
        let me = this;
        this.pageNumberShareWallet = page,
            this.pageSizeShareWallet = limit,
            this.sortShareWallet = sort
        let dataParams = {
            page,
            size: limit,
            sort: sort,
            subCode: me.subCodeId.toString()
        }
        me.messageCommonService.onload()
        this.trafficWalletService.getListShareWallet(dataParams, (response) => {
            me.dataSetShareWallet = {
                content: response.content,
                total: response.totalElements
            }
            me.isShowTableInDialogDetail = true;
        }, null, () => {
            me.messageCommonService.offload();
        })
    }
}
