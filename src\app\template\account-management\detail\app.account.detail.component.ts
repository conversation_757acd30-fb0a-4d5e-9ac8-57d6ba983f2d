import { AfterContentChecked, Component, Injector, OnInit } from "@angular/core";
import { FormBuilder } from "@angular/forms";
import { ActivatedRoute, Router } from "@angular/router";
import { MenuItem } from "primeng/api";
import { ComponentBase } from "src/app/component.base";
import { AccountService } from "src/app/service/account/AccountService";
import { CONSTANTS } from "src/app/service/comon/constants";
import { DebounceInputService } from "src/app/service/comon/debounce.input.service";
import { MessageCommonService } from "src/app/service/comon/message-common.service";
import { TranslateService } from "src/app/service/comon/translate.service";
import { UtilService } from "src/app/service/comon/util.service";
import { CustomerService } from "src/app/service/customer/CustomerService";
import {an} from "@fullcalendar/core/internal-common";

@Component({
    selector: "app-account-detail",
    templateUrl: './app.account.detail.component.html'
})
export class AppAccountDetailComponent extends ComponentBase implements OnInit, AfterContentChecked{
    constructor(public accountService: AccountService, private customerService: CustomerService,injector: Injector) {
        super(injector);
    }
    items: Array<MenuItem>;
    home: MenuItem;
    accountInfo: {
        accountName: string| null,
        fullName: string|null,
        email: string|null,
        phone: string|null,
        userType: number| null,
        province: any,
        roles: Array<any>,
        description: string|null,
        manager: any,
        customers: Array<any>
    };
    listRole: Array<any>;
    listProvince: Array<any>;
    listCustomer: Array<any>;
    optionUserType: any;
    oldUserType: number | null = null;
    accountResponse: any;
    allPermissions = CONSTANTS.PERMISSIONS;
    userType : any;
    ngOnInit(): void {
        this.optionUserType = CONSTANTS.USER_TYPE;
        this.userType = this.sessionService.userInfo.type;
        this.items = [
            { label: this.tranService.translate("global.menu.accountmgmt") }, 
            { label: this.tranService.translate("global.menu.listaccount"), routerLink:"/accounts" },
            { label: this.tranService.translate("global.button.view") }
        ];
        this.home = { icon: 'pi pi-home', routerLink: '/' };
        
        let fullTypeAccount = [
            {name: this.tranService.translate("account.usertype.admin"),value:CONSTANTS.USER_TYPE.ADMIN, accepts:[CONSTANTS.USER_TYPE.ADMIN]},
            {name: this.tranService.translate("account.usertype.customer"),value:CONSTANTS.USER_TYPE.CUSTOMER,accepts:[CONSTANTS.USER_TYPE.ADMIN,CONSTANTS.USER_TYPE.PROVINCE,CONSTANTS.USER_TYPE.DISTRICT, CONSTANTS.USER_TYPE.AGENCY, CONSTANTS.USER_TYPE.CUSTOMER]},
            {name: this.tranService.translate("account.usertype.province"),value:CONSTANTS.USER_TYPE.PROVINCE,accepts:[CONSTANTS.USER_TYPE.ADMIN]},
            {name: this.tranService.translate("account.usertype.district"),value:CONSTANTS.USER_TYPE.DISTRICT,accepts:[CONSTANTS.USER_TYPE.ADMIN,CONSTANTS.USER_TYPE.PROVINCE]},
            {name: this.tranService.translate("account.usertype.agency"),value:CONSTANTS.USER_TYPE.AGENCY,accepts:[CONSTANTS.USER_TYPE.ADMIN,CONSTANTS.USER_TYPE.PROVINCE,CONSTANTS.USER_TYPE.DISTRICT]},
        ]
        this.accountInfo = {
            accountName: null,
            fullName: null,
            email: null,
            phone: null,
            userType: null,
            province: null,
            roles: null,
            description: null,
            manager: null,
            customers: null
        }
        this.getListProvince();
        this.getDetail();
    }

    ngAfterContentChecked(): void {
           
    }

    getDetail(){
        let me = this;
        let accountid = this.route.snapshot.paramMap.get("id");
        me.messageCommonService.onload();
        this.accountService.getById(parseInt(accountid), (response)=>{
            me.accountResponse = response;
            me.accountInfo.accountName = response.username;
            me.accountInfo.fullName = response.fullName;
            me.accountInfo.email = response.email;
            me.accountInfo.description = response.description;
            me.accountInfo.phone = response.phone;
            me.accountInfo.province = response.provinceCode;
            me.accountInfo.userType = response.type;
            me.getListRole(false);
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }

    getListRole(isClear){
        this.accountService.getListRole(this.accountInfo.userType, (response)=>{
            this.listRole = response.map(el => {
                return {
                    id: el.id,
                    name: el.name
                }
            });
            if(isClear){
                this.accountInfo.roles = null;
            }else{
                this.accountInfo.roles = this.listRole.filter(el => (this.accountResponse.roles||[]).includes(el.id));
            }
        })
    }

    getListProvince(){
        this.accountService.getListProvince((response)=>{
            this.listProvince = response.map(el => {
                return {
                    id: el.code,
                    name: `${el.name} (${el.code})`
                }
            })
        })
    }

    deleteAccount(){
        let me = this;

        me.messageCommonService.confirm(
            me.tranService.translate("global.message.titleConfirmDeleteAccount"),
            me.tranService.translate("global.message.confirmDeleteAccount"),
            {
                ok:()=>{
                    me.messageCommonService.onload()
                    me.accountService.deleleUser(this.accountResponse.id, (response)=>{
                        me.messageCommonService.success(me.tranService.translate("global.message.deleteSuccess"));
                        this.router.navigate([`/accounts`]);
                    }, null, ()=>{
                        me.messageCommonService.offload();
                    })
                },
                cancel: ()=>{
                    // me.messageCommonService.error(me.tranService.translate("global.message.deleteFail"));
                }
            }
        )
    }

    goToEdit(){
        this.router.navigate([`/accounts/edit/${this.accountResponse.id}`]);
    }

    getStringCustomers(){
        return (this.accountResponse.customers || []).map(el => el.customerName + ' - ' + el.customerCode).toLocaleString();
    }

    getStringRoles(){
        return (this.accountResponse.roles || []).map(el => el.roleName).toLocaleString()
    }

    getStringUserType(value) {
        if(value == CONSTANTS.USER_TYPE.ADMIN){
            return this.tranService.translate("account.usertype.admin");
        }else if(value == CONSTANTS.USER_TYPE.CUSTOMER){
            return this.tranService.translate("account.usertype.customer");
        }else if(value == CONSTANTS.USER_TYPE.PROVINCE){
            return this.tranService.translate("account.usertype.province");
        }else if(value == CONSTANTS.USER_TYPE.DISTRICT){
            return this.tranService.translate("account.usertype.district");
        }else if(value == CONSTANTS.USER_TYPE.AGENCY){
            return this.tranService.translate("account.usertype.agency");
        }else{
            return "";
        }
    }

    getStringUserStatus(value) {
        if(value == CONSTANTS.USER_STATUS.ACTIVE){
            return this.tranService.translate("account.userstatus.active");
        }else if(value == CONSTANTS.USER_STATUS.INACTIVE){
            return this.tranService.translate("account.userstatus.inactive");
        }else{
            return "";
        }
    }
}
