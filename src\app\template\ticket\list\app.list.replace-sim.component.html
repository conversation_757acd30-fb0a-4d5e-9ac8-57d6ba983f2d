<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{tranService.translate("ticket.menu.replaceSim")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
    <div class="col-5 flex flex-row justify-content-end align-items-center">
        <p-button styleClass="p-button-info"
                  *ngIf="userInfo.type == userType.CUSTOMER && checkAuthen([CONSTANTS.PERMISSIONS.TICKET.CREATE])"
                  [label]="tranService.translate('global.button.create')"
                  (click)="showModalCreate()" icon="">
        </p-button>
    </div>
</div>

<form [formGroup]="formSearchTicket" (ngSubmit)="onSubmitSearch()" class="pt-3 pb-2 vnpt-field-set">
    <p-panel [toggleable]="true" [header]="tranService.translate('global.text.filter')">
        <div class="grid search-grid-4">
            <!-- ma tinh -->
            <div *ngIf="this.userInfo.type == this.userType.ADMIN" class="col-3">
                <span class="p-float-label">
                    <p-dropdown styleClass="w-full"
                                [showClear]="true" [filter]="true" filterBy="display"
                                id="provinceCode" [autoDisplayFirst]="false"
                                [(ngModel)]="searchInfo.provinceCode"
                                [required]="false"
                                formControlName="provinceCode"
                                [options]="listProvince"
                                optionLabel="display"
                                optionValue="code"
                    ></p-dropdown>
                    <label class="label-dropdown" htmlFor="provinceCode">{{tranService.translate("account.label.province")}}</label>
                </span>
            </div>
            <!-- trạng thái ticket -->
            <div class="col-3">
                <span class="p-float-label">
                    <p-dropdown styleClass="w-full"
                                [showClear]="true" [filter]="true" filterBy="display"
                                id="provinceCode" [autoDisplayFirst]="false"
                                [(ngModel)]="searchInfo.status"
                                [required]="false"
                                formControlName="status"
                                [options]="listTicketStatus"
                                optionLabel="label"
                                optionValue="value"
                                [filter] = true
                                filterBy = "label"
                    ></p-dropdown>
                    <label class="label-dropdown" htmlFor="status">{{tranService.translate("ticket.label.status")}}</label>
                </span>
            </div>
            <!-- email -->
            <div class="col-3">
                <span class="p-float-label">
                    <input class="w-full"
                           pInputText id="contactEmail"
                           [(ngModel)]="searchInfo.contactEmail"
                           formControlName="contactEmail"
                    />
                    <label htmlFor="email">{{tranService.translate("ticket.label.email")}}</label>
                </span>
            </div>
            <!-- phone -->
            <div class="col-2">
                <span class="p-float-label">
                    <input class="w-full"
                           pInputText id="contactPhone"
                           [(ngModel)]="searchInfo.contactPhone"
                           formControlName="contactPhone"
                           type="number"
                           (keydown)="preventCharacter($event)"
                           min="0"
                    />
                    <label htmlFor="contactPhone">{{tranService.translate("ticket.label.phone")}}</label>
                </span>
            </div>
            <div class="col-2 pb-0">
                <p-button icon="pi pi-search"
                          styleClass="p-button-rounded p-button-secondary p-button-text button-search"
                          type="submit"
                ></p-button>
            </div>
        </div>
    </p-panel>
</form>

<table-vnpt *ngIf="!changeTable"
    [tableId]="'tableTicketConfigList'"
    [fieldId]="'provinceCode'"
    [columns]="columns"
    [dataSet]="dataSet"
    [options]="optionTable"
    [pageNumber]="pageNumber"
    [loadData]="search.bind(this)"
    [pageSize]="pageSize"
    [sort]="sort"
    [params]="searchInfo"
    [labelTable]="tranService.translate('ticket.menu.requestList')"
></table-vnpt>
<table-vnpt *ngIf="changeTable"
    [tableId]="'tableTicketConfigList'"
    [fieldId]="'provinceCode'"
    [columns]="columns"
    [dataSet]="dataSet"
    [options]="optionTable"
    [pageNumber]="pageNumber"
    [loadData]="search.bind(this)"
    [pageSize]="pageSize"
    [sort]="sort"
    [params]="searchInfo"
    [labelTable]="tranService.translate('ticket.menu.requestList')"
></table-vnpt>
<!--    dialog tạo sửa yêu cầu-->
<div class="flex justify-content-center dialog-vnpt">
    <p-dialog [breakpoints]="{ '1199px': '75vw', '575px': '90vw' }" [header]="titlePopup"
              [(visible)]="isShowCreateRequest" [modal]="true" [style]="{ width: '800px', overflowY :'scroll', maxHeight : '80%' }" [draggable]="false" [resizable]="false">
        <form class="mt-3" [formGroup]="formTicketSim" (ngSubmit)="createOrUpdateRequest()">
            <div class="flex flex-row flex-wrap justify-content-between w-full">
                <div class="w-full field grid chart-grid" *ngIf="this.userInfo.type == this.userType.ADMIN && typeRequest == 'detail'">
                    <label htmlFor="contactName" class="col-fixed"
                           style="width:180px">{{ tranService.translate("account.label.province") }}<span
                        class="text-red-500">*</span></label>
                    <div class="col">
                        <span>{{getProvinceName(ticket.provinceCode)}}</span>
                    </div>
                </div>
                <!-- contactName -->
                <div class="w-full field grid chart-grid">
                    <label htmlFor="contactName" class="col-fixed" style="width:180px;height: fit-content">{{tranService.translate("ticket.label.customerName")}}<span class="text-red-500">*</span></label>
                    <div class="col" style="width: calc(100% - 180px);overflow-wrap: break-word">
                        <input *ngIf="typeRequest == 'update' || typeRequest == 'create'" class="w-full"
                               pInputText id="contactName"
                               [(ngModel)]="ticket.contactName"
                               formControlName="contactName"
                               [required]="true"
                               [maxLength]="maxlengthContactName"
                               pattern="^[^~`!@#\$%\^&*\(\)=\+\[\]\{\}\|\\,<>\/?]*$"
                               [placeholder]="tranService.translate('account.text.inputFullname')"
                        />
                      <span *ngIf="typeRequest == 'detail'">{{ticket.contactName}}</span>
                    </div>
                </div>
                <!-- error fullname -->
                <div class="w-full field grid text-error-field">
                    <label htmlFor="fullName" class="col-fixed" style="width:180px"></label>
                    <div class="col">
                        <small class="text-red-500" *ngIf="formTicketSim.controls.contactName.dirty && formTicketSim.controls.contactName.errors?.required">{{tranService.translate("global.message.required")}}</small>
                        <small class="text-red-500" *ngIf="formTicketSim.controls.contactName.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:255})}}</small>
                        <small class="text-red-500" *ngIf="formTicketSim.controls.contactName.errors?.pattern">{{tranService.translate("global.message.formatContainVN")}}</small>
                    </div>
                </div>

                <!-- email -->
                <div class="w-full field grid chart-grid">
                    <label htmlFor="email" class="col-fixed" style="width:180px;height: fit-content">{{tranService.translate("ticket.label.email")}}<span class="text-red-500">*</span></label>
                    <div class="col" style="width: calc(100% - 180px);overflow-wrap: break-word">
                        <input *ngIf="typeRequest == 'update'|| typeRequest == 'create'" class="w-full"
                               pInputText id="contactEmail"
                               [(ngModel)]="ticket.contactEmail"
                               formControlName="contactEmail"
                               [required]="true"
                               [maxLength]="50"
                               pattern="^[a-z0-9]+[a-z0-9\-\._]*[a-z0-9]+@([a-z0-9]+[a-z0-9\-\._]*[a-z0-9]+)+(\.[a-z]{2,})$"
                               [placeholder]="tranService.translate('account.text.inputEmail')"
                        />
                      <span *ngIf="typeRequest == 'detail'">{{ticket.contactEmail}}</span>
                    </div>
                </div>
                <!-- error email -->
                <div class="w-full field grid text-error-field">
                    <label htmlFor="email" class="col-fixed" style="width:180px"></label>
                    <div class="col">
                        <small class="text-red-500" *ngIf="formTicketSim.controls.contactEmail.dirty && formTicketSim.controls.contactEmail.errors?.required">{{tranService.translate("global.message.required")}}</small>
                        <small class="text-red-500" *ngIf="formTicketSim.controls.contactEmail.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:255})}}</small>
                        <small class="text-red-500" *ngIf="formTicketSim.controls.contactEmail.errors?.pattern">{{tranService.translate("global.message.invalidEmail")}}</small>
                        <!--                            <small class="text-red-500" *ngIf="isEmailExisted">{{tranService.translate("global.message.exists",{type: tranService.translate("account.label.email").toLowerCase()})}}</small> -->
                    </div>
                </div>
                <!-- phone -->
                <div class="w-full field grid chart-grid">
                    <label htmlFor="phone" class="col-fixed" style="width:180px">{{tranService.translate("ticket.label.phone")}}<span class="text-red-500">*</span></label>
                    <div class="col">
                        <input *ngIf="typeRequest == 'update'|| typeRequest == 'create'" class="w-full"
                               pInputText id="contactPhone"
                               [(ngModel)]="ticket.contactPhone"
                               formControlName="contactPhone"
                               [required]="true"
                               [maxLength]="11"
                               pattern="^((\+?[1-9][0-9])|0?)[1-9][0-9]{8,9}$"
                               (keydown)="preventCharacter($event)"
                               [placeholder]="tranService.translate('account.text.inputPhone')"
                        />
                      <span *ngIf="typeRequest == 'detail'">{{ticket.contactPhone}}</span>
                    </div>
                </div>
                <!-- error phone -->
                <div class="w-full field grid text-error-field">
                    <label htmlFor="phone" class="col-fixed" style="width:180px"></label>
                    <div class="col">
                        <small class="text-red-500" *ngIf="formTicketSim.controls.contactPhone.dirty && formTicketSim.controls.contactPhone.errors?.required">{{tranService.translate("global.message.required")}}</small>
                        <small class="text-red-500" *ngIf="formTicketSim.controls.contactPhone.errors?.pattern">{{tranService.translate("ticket.message.invalidPhone")}}</small>
                    </div>
                </div>
                <!-- change sim -->
                <div *ngIf="ticket.type == 0" class="w-full field grid chart-grid">
                    <label htmlFor="changeSim" class="col-fixed" style="width:180px;display:inline-block">{{tranService.translate("ticket.label.changeSim")}}<span class="text-red-500">*</span></label>
                    <div class="col">
                        <p-chips *ngIf="typeRequest=='create'" class="w-full p-fluid"
                                 formControlName="sim"
                                 [(ngModel)]="ticket.sim"
                                 (keydown)="preventCharacter($event)" separator=","
                                 (ngModelChange)="onSimChangeAdd($event)"  [required]="true"
                                 pTooltip="{{tranService.translate('ticket.message.noteChangeSim')}}"
                        >
                        </p-chips>
                        <span style="word-break: break-all" *ngIf="typeRequest=='update' || typeRequest == 'detail'">{{ticket.sim}}</span>
                    </div>
                </div>
                <!-- error change sim  -->
                <div *ngIf="ticket.type == 0" class="w-full field grid text-error-field">
                    <label htmlFor="numSub" class="col-fixed" style="width:180px"></label>
                    <div class="col">
                        <small class="text-red-500 block" *ngIf="formTicketSim.controls.sim.dirty && formTicketSim.controls.sim.errors?.required">{{tranService.translate("global.message.required")}}</small>
                        <small class="text-red-500 block" *ngIf="!isValidChangeSim">{{tranService.translate("ticket.message.invalidPhone")}}</small>
                    </div>
                </div>

                <!-- content-->
                <div class="w-full field grid chart-grid">
                    <label htmlFor="content" class="col-fixed" style="width:180px;height: fit-content;">{{tranService.translate("ticket.label.content")}}</label>
                    <div class="col">
                            <textarea *ngIf="typeRequest=='create'" class="w-full" style="resize: none;"
                                       rows="5"
                                       [autoResize]="false"
                                       pInputTextarea id="content"
                                       [(ngModel)]="ticket.content"
                                       formControlName="content"
                                       [maxlength]="255"
                                       [placeholder]="tranService.translate('ticket.label.content')"
                                      (keydown)="onKeyDownContent($event)"
                            ></textarea>
                            <span style="word-break: break-all" *ngIf="typeRequest=='update' || typeRequest == 'detail'">{{ticket.content}}</span>
                    </div>
                </div>
                <!-- error content -->
                <div class="w-full field grid text-error-field">
                    <label htmlFor="content" class="col-fixed" style="width:180px"></label>
                    <div class="col">
                        <small class="text-red-500" *ngIf="formTicketSim.controls.content.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:255})}}</small>
                    </div>
                </div>
                <!-- note-->
                <div class="w-full field grid chart-grid">
                    <label htmlFor="note" class="col-fixed" style="width:180px;height: fit-content;">{{tranService.translate("ticket.label.note")}}</label>
                    <div class="col">
                            <textarea *ngIf="typeRequest=='create'"  class="w-full" style="resize: none;"
                                       rows="5"
                                       [autoResize]="false"
                                       pInputTextarea id="note"
                                       [(ngModel)]="ticket.note"
                                       formControlName="note"
                                       [maxlength]="255"
                                       [placeholder]="tranService.translate('ticket.label.note')"
                                      (keydown)="onKeyDownNote($event)"
                            ></textarea>
                            <span style="word-break: break-all" *ngIf="typeRequest=='update' || typeRequest == 'detail'">{{ticket.note}}</span>
                    </div>
                </div>
                <!-- error note -->
                <div class="w-full field grid text-error-field">
                    <label htmlFor="note" class="col-fixed" style="width:180px"></label>
                    <div class="col">
                        <small class="text-red-500" *ngIf="formTicketSim.controls.note.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:255})}}</small>
                    </div>
                </div>
                <!-- chuyen xu ly-->
              <div *ngIf="(typeRequest=='update' && userInfo.type == userType.PROVINCE && ticket.status == null && ticket.statusOld == 0) || (userInfo.type == userType.PROVINCE && typeRequest == 'detail' && ticket.assigneeId != null)" class="w-full field grid chart-grid">
                    <label for="type" class="col-fixed" style="width:180px">{{tranService.translate("ticket.label.transferProcessing")}}</label>
                    <div class="col" style="max-width: calc(100% - 180px); position: relative">
                      <vnpt-select
                          class="w-full"
                          [(value)]="ticket.assigneeId"
                          [placeholder]="tranService.translate('ticket.label.transferProcessing')"
                          [disabled]="ticket.assigneeId != null && typeRequest == 'detail'"
                          objectKey="account"
                          paramKey="email"
                          keyReturn="id"
                          displayPattern="${email}"
                          [isMultiChoice]="false"
                          [paramDefault]="{type : 3}"
                          [stylePositionBoxSelect] = "{bottom: '40px', left: '12px', 'min-width':'calc(100% - 20px)'}"
                      ></vnpt-select>
                    </div>
                </div>
                <!-- error chuyen xu ly-->
                <div *ngIf="(typeRequest=='update' && userInfo.type == userType.PROVINCE && ticket.status == null && ticket.statusOld == 0) || typeRequest == 'detail'" class="w-full field grid text-error-field">
                    <label htmlFor="userType" class="col-fixed" style="width:180px"></label>
                    <div class="col">
                        <small class="text-red-500" *ngIf="formTicketSim.controls.assigneeId.dirty && formTicketSim.controls.assigneeId.errors?.required">{{tranService.translate("global.message.required")}}</small>
                    </div>
                </div>
                <!-- trang thai-->
              <div *ngIf="typeRequest=='update' || typeRequest == 'detail'" [class]="(userInfo.type == userType.PROVINCE && typeRequest != 'detail') ? (ticket.assigneeId == null || (ticket.assigneeId != null && !listActivatedAccount.includes(ticket.assigneeId))? '': 'hidden'): ''" class="w-full field grid chart-grid">
                    <label for="status" class="col-fixed" style="width:180px">{{tranService.translate("ticket.label.status")}}</label>
                    <div class="col">
                        <p-dropdown *ngIf="typeRequest=='update'" styleClass="w-full"
                                    [showClear]="true"
                                    id="type" [autoDisplayFirst]="true"
                                    [(ngModel)]="ticket.status"
                                    formControlName="status"
                                    [options]="ticket.statusOld !== null ? mapTicketStatus[ticket.statusOld] : listTicketStatus"
                                    optionLabel="label"
                                    optionValue="value"
                                    [placeholder]="tranService.translate('ticket.label.status')"
                                    [emptyMessage]="tranService.translate('global.text.nodata')"
                        ></p-dropdown>
                      <span *ngIf="typeRequest=='detail' && ticket.statusOld == 0" [class]="['p-2', 'text-white', 'bg-cyan-300', 'border-round', 'inline-block']">{{getValueStatus(ticket.statusOld)}}</span>
                      <span *ngIf="typeRequest=='detail' && ticket.statusOld == 1"  [class]="['p-2', 'text-white', 'bg-bluegray-500', 'border-round', 'inline-block']">{{getValueStatus(ticket.statusOld)}}</span>
                      <span *ngIf="typeRequest=='detail' && ticket.statusOld == 2"  [class]="['p-2', 'text-white', 'bg-orange-400', 'border-round', 'inline-block']">{{getValueStatus(ticket.statusOld)}}</span>
                      <span *ngIf="typeRequest=='detail' && ticket.statusOld == 3"  [class]="['p-2', 'text-white', 'bg-red-500', 'border-round', 'inline-block']">{{getValueStatus(ticket.statusOld)}}</span>
                      <span *ngIf="typeRequest=='detail' && ticket.statusOld == 4"  [class]="['p-2', 'text-white', 'bg-green-500', 'border-round', 'inline-block']">{{getValueStatus(ticket.statusOld)}}</span>
                    </div>
                </div>
                <!-- error trang thai -->
              <div *ngIf="typeRequest=='update' || typeRequest == 'detail' && ticket.assigneeId == null" class="w-full field grid text-error-field">
                    <label htmlFor="userType" class="col-fixed" style="width:180px"></label>
                    <div class="col">
                        <small class="text-red-500" *ngIf="formTicketSim.controls.status.dirty && formTicketSim.controls.status.errors?.required">{{tranService.translate("global.message.required")}}</small>
                    </div>
                </div>
            </div>
          <!-- ghi chu xu ly-->
          <div *ngIf="typeRequest=='update'" [class]="userInfo.type == userType.PROVINCE ? (ticket.assigneeId == null || (ticket.assigneeId != null && !listActivatedAccount.includes(ticket.assigneeId))? '': 'hidden'): ''" class="w-full field grid chart-grid">
            <label htmlFor="contactName" class="col-fixed" style="width:180px">{{tranService.translate("ticket.label.processingNotes")}}<span *ngIf="ticket.status" class="text-red-500">*</span></label>
            <div class="col">
              <input class="w-full"
                     pInputText id="cause"
                     [(ngModel)]="ticket.cause"
                     formControlName="cause"
                     [required]="ticket.status != null"
                     [maxLength]="255"
                     [placeholder]="tranService.translate('ticket.label.processingNotes')"
                     (keydown)="onKeyDownCause($event)"
              />
            </div>
          </div>
          <!-- error ghi chu xu ly-->
          <div *ngIf="typeRequest=='update' || typeRequest == 'detail' && (ticket.assigneeId == null || (ticket.assigneeId != null && !listActivatedAccount.includes(ticket.assigneeId)))" class="w-full field grid text-error-field">
            <label htmlFor="fullName" class="col-fixed" style="width:180px"></label>
            <div class="col">
              <small class="text-red-500" *ngIf="(formTicketSim.controls.cause.dirty && formTicketSim.controls.cause.errors?.required) || (this.ticket.status != null && (this.ticket.cause == null || this.ticket.cause.trim() == ''))">{{tranService.translate("global.message.required")}}</small>
            </div>
          </div>

<!--          danh sach ghi chu xu ly-->
          <div *ngIf="(typeRequest=='update' || typeRequest == 'detail') && (listNotes && listNotes.length > 0)" class="w-full field grid chart-grid">
            <label htmlFor="content" class="col-fixed font-medium"
                   style="width:180px;height: fit-content;">{{ tranService.translate("ticket.label.listNote") }}</label>
            <p-table style="width: 100%" [value]="listNotes" [tableStyle]="{ 'min-width': '50rem' }">
              <ng-template pTemplate="header">
                <tr>
                  <th>{{tranService.translate('global.text.stt')}}</th>
                  <th>{{tranService.translate('account.text.account')}}</th>
                  <th style="min-width: 146px">{{tranService.translate('global.button.changeStatus')}}</th>
                  <th style="min-width: 155px">{{tranService.translate('account.label.time')}}</th>
                  <th>{{tranService.translate('ticket.label.content')}}</th>
                </tr>
              </ng-template>
              <ng-template pTemplate="body" let-note let-i="rowIndex">
                <tr [formGroup]="mapForm[note.id]">
                  <td>{{ i + 1 }}</td>
                  <td>{{ note.userName }}</td>
                  <td>{{getValueStatus(note.status)}}</td>
                  <td>{{note.createdDate | date:"HH:mm:ss dd/MM/yyyy"}}</td>
                  <td>
                    <input *ngIf="typeRequest == 'update'"  class="w-full"
                           pInputText id="content"
                           [(ngModel)]="note.content"
                           formControlName="content"
                           [required]="true"
                           [maxLength]="255"
                           (keydown)="onKeyDownNoteContent($event, note)"
                    />
                    <span *ngIf="typeRequest == 'detail'">{{note.content}}</span>
                    <small class="text-red-500" *ngIf="mapForm[note.id].controls.content.dirty && mapForm[note.id].controls.content.errors?.required">{{tranService.translate("global.message.required")}}</small>
                    <small class="text-red-500" *ngIf="mapForm[note.id].controls.content.errors?.maxLength">{{tranService.translate("global.message.maxLength",{len:255})}}</small>

                  </td>
                </tr>
              </ng-template>
            </p-table>
          </div>

          <div *ngIf="typeRequest != 'detail'" class="flex flex-row justify-content-center align-items-center mt-3">
                <p-button styleClass="mr-2 p-button-secondary" [label]="tranService.translate('global.button.cancel')" (click)="isShowCreateRequest = false"></p-button>
                <p-button type="submit" styleClass="p-button-info" [disabled]="formTicketSim.invalid || (typeRequest=='create' && !isValidChangeSim) || (this.ticket.status != null && this.ticket.cause != null && this.ticket.cause.trim() == '') || (this.listNotes.length > 0 && !isFormValid())" [label]="tranService.translate('global.button.save')"></p-button>
            </div>
        </form>
    </p-dialog>
</div>
