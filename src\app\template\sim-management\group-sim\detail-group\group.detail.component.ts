import { Component, Inject, Injector, OnD<PERSON>roy, OnInit, inject } from '@angular/core';
import { MenuItem } from 'primeng/api';
import { CONSTANTS } from 'src/app/service/comon/constants';
import { MessageCommonService } from 'src/app/service/comon/message-common.service';
import { TranslateService } from 'src/app/service/comon/translate.service';
import { GroupSimService } from 'src/app/service/group-sim/GroupSimService';
import { SimService } from 'src/app/service/sim/SimService';
import {
    ColumnInfo,
    OptionTable,
} from 'src/app/template/common-module/table/table.component';
import { debounceTime, switchMap,tap } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { AbstractControl, FormControl, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { CustomerService } from 'src/app/service/customer/CustomerService';
import { AccountService } from 'src/app/service/account/AccountService';
import {ComponentBase} from 'src/app/component.base';
import { ComboLazyControl } from 'src/app/template/common-module/combobox-lazyload/combobox.lazyload';

interface SubscriptionNumber{
    id:number;
    msisdn:number;
}

interface Customer {
    id: string;
    name: string;
}

@Component({
    selector: 'app-list-group',
    templateUrl: './group.detail.component.html',
})
export class ListGroupComponent extends ComponentBase implements OnInit, OnDestroy{
    idForEdit:string;
    idForDelete:string[];
    submitted = false;
    items: MenuItem[];
    itemForPlans: MenuItem[] = [];
    home: MenuItem;
    groupScope: number;
    groupKey: string;
    customer: string;
    customerCode: string;
    provinceCode: string;
    province: string;
    subNumbers: SubscriptionNumber[];
    selectedSimItems:Array<any> = []
    groupName: string;
    description: string;
    columns: Array<ColumnInfo>;
    displayAddSim: boolean = false;
    displayEdit: boolean = false;
    dataSet: {
        content: Array<any>;
        total: number;
    };
    searchSubject = new Subject<string>();
    selectItems: Array<any>;
    optionTable: OptionTable;
    pageNumber: number;
    pageSize: number;
    sort: string;
    userType: number;
    subcribeDebounce: any;
    groupScopeObjects = CONSTANTS.GROUP_SCOPE;
    buttonSaveSimToGroup = this.tranService.translate("global.button.save");
    paramSearchSim = {};
    boxSimAddController: ComboLazyControl = new ComboLazyControl();
    constructor(
        @Inject(GroupSimService) private groupSimService: GroupSimService,
        @Inject(SimService) private simService: SimService,
        @Inject(CustomerService) private customerService: CustomerService,
        private accountService: AccountService,
        private injector: Injector
    ) {
        super(injector);
        let me = this;
        this.subcribeDebounce = this.searchSubject.pipe(
            debounceTime(300),  // wait for 300ms pause in events
            tap(query => {
                if (query === null
                    // || query === ''
                    ) {
                    this.subNumbers = [];
                }
            }),
            switchMap(query =>
                query ?  // check if query is non-null and non-empty
                    // wrap your callback-based API call in a new Promise
                    new Promise<any[]>((resolve, reject) => {
                        let params = {size:100, msisdn:query};
                        if(this.groupScope == CONSTANTS.GROUP_SCOPE.GROUP_CUSTOMER){
                            params['customer'] = this.customerCode;
                        }else if(this.groupScope == CONSTANTS.GROUP_SCOPE.GROUP_PROVINCE){
                            params['provinceCode'] = this.provinceCode;
                        }
                        this.simService.search(params, (response) => {
                            const result = [];
                            response.content.map((item: any) => {
                                result.push({
                                    id:item.imsi,
                                    msisdn:item.msisdn
                                });
                            });
                            resolve(result);  // resolve the promise with the result
                        });
                    })
                : []  // return an empty array immediately if query is null or empty
            ) // cancel previous pending API call and make a new one
        ).subscribe(data => {
            this.subNumbers = data.map(item => ({ msisdn: item.msisdn, id: item.id }));  // update the options with the API response
        });
    }

    headerSim: string = this.tranService.translate("groupSim.label.buttonAddSim");
    headerEdit: string = this.tranService.translate("groupSim.breadCrumb.update");
    labelBtnSave: string = this.tranService.translate("groupSim.label.buttonSave");
    labelBtnCancel: string = this.tranService.translate("groupSim.label.buttonCancel");
    placeHolderGroupName:string = this.tranService.translate("groupSim.placeHolder.groupName")
    placeHolderDescription: string = this.tranService.translate("groupSim.placeHolder.description")

    getValueLabel(option: Customer): string {
        return `${option.id} - ${option.name}`;
    }

    handleSavetoGroup(){
        if (this.selectedSimItems.length > 0) {
            this.simService.pushSimToGroup(this.selectedSimItems,{id:parseInt(this.idForEdit)},
            (response)=>{
                this.messageCommonService.success(this.tranService.translate("global.message.addGroupSuccess"))
                this.search(0, this.pageSize,this.sort, null)

            })
            this.displayAddSim=false;
        }
    }

    handleModelClose(){
        this.displayAddSim=false;
    }

    placeholderSIM= this.tranService.translate("groupSim.placeHolder.addSim")
    buttonPlan = this.tranService.translate("groupSim.label.buttonPlan")

    showOverLayAddSim(){
        this.boxSimAddController.reload();
        this.displayAddSim=true;
        this.selectedSimItems = []
    }

    showOverLayEdit(){
        this.router.navigate(["/sims/group/update", this.idForEdit]);
    }

    removeMany(){
        // console.log(this.selectItems)
        if(this.selectItems.length==0)
        return null
        this.idForDelete=[];
        this.selectItems.map((item)=>{
            this.idForDelete.push(item.msisdn);
        })
        this.removeSim(this.idForDelete)
    }


    removeSim(ids:string[]){
        this.messageCommonService.confirm(this.tranService.translate("groupSim.label.confirmDelete"),this.tranService.translate("groupSim.label.deleteTextSim"), {
            ok: () => {
                this.simService.removeSIMFromGroup(ids,parseInt(this.idForEdit),(response)=>{
                    // console.log(response);
                    this.selectItems=[];
                    this.messageCommonService.success(this.tranService.translate("global.message.deleteSuccess"))
                    this.search(0, this.pageSize,this.sort, null)
                })
            },
            cancel: () => {
            },
        });
    }

    onFilter(event) {
        this.searchSubject.next(event.filter);
    }

    ngOnInit() {
        this.itemForPlans = [
            { label: this.tranService.translate("groupSim.label.buttonPlanRegister"), disabled:true},
            { label: this.tranService.translate("groupSim.label.buttonPlanChange")},
            { label: this.tranService.translate("groupSim.label.buttonPlanCancel")}
        ];
        let me = this;
        this.idForEdit = String(this.route.snapshot.params["idgroup"]);
        this.items = [
            { label: this.tranService.translate("global.menu.simmgmt") },
            { label: this.tranService.translate("groupSim.breadCrumb.group"), routerLink: '/sims/group' },
            { label: this.tranService.translate("groupSim.breadCrumb.detail") },
        ];

        this.home = { icon: 'pi pi-home', routerLink: '/' };

        this.messageCommonService.onload();
        this.groupSimService.getSimGroupById(this.idForEdit,{},{},(response)=>{
            this.groupKey = response.groupKey
            this.customer = response.customer;
            this.groupName = response.name;
            this.description = response.description;
            this.groupScope = response.scope;
            this.customerCode = response.customerCode;
            this.provinceCode = response.provinceCode;
            if(this.groupScope == CONSTANTS.GROUP_SCOPE.GROUP_CUSTOMER){
                this.paramSearchSim = {
                    customer: this.customerCode
                }
                this.customerService.getByKey("customerCode", this.customerCode,(res)=>{
                    me.customer = `${res[0].customerName} - ${res[0].customerCode}`;
                })
            }
            if(this.groupScope == CONSTANTS.GROUP_SCOPE.GROUP_PROVINCE){
                this.paramSearchSim = {
                    provinceCode: this.provinceCode
                }
                this.accountService.getListProvince((res)=>{
                    (res || []).forEach(el => {
                        if(el.code == response.provinceCode){
                            me.province = `${el.name} (${el.code})`
                        }
                    })
                })
            }
        }, null, ()=>{
            me.messageCommonService.offload();
        })


        this.columns = [
            {
                name: this.tranService.translate('groupSim.detail.subNumber'),
                key: 'msisdn',
                size: '20%',
                align: 'left',
                isShow: true,
                isSort: true,
                style: {
                    color: 'var(--mainColorText)',
                },
                funcGetRouting(item) {
                    return ["/sims/detail/"+item.msisdn];
                },
            },
            {
                name: "IMSI",
                key: 'imsi',
                size: '20%',
                align: 'left',
                isShow: true,
                isSort: true,
            },
            {
                name: this.tranService.translate("sim.label.trangthaisim"),
                key: "status",
                size: "20%",
                align: "left",
                isShow: true,
                isSort: true,
                funcGetClassname: (value) => {
                    if(value == 0){
                        return ['p-1' , "border-round", "border-400", "text-color","inline-block"];
                    }else if(value == CONSTANTS.SIM_STATUS.READY){
                        // return ['p-1', "bg-blue-600", "border-round","inline-block"];
                        return ['p-2', "text-green-800", "bg-green-100","border-round","inline-block"];
                    }else if(value == CONSTANTS.SIM_STATUS.ACTIVATED){
                        return ['p-2', 'text-green-800', "bg-green-100","border-round","inline-block"];
                    }else if(value == CONSTANTS.SIM_STATUS.INACTIVED){
                        return ['p-2', 'text-yellow-800', "bg-yellow-100", "border-round","inline-block"];
                    }else if(value == CONSTANTS.SIM_STATUS.DEACTIVATED){
                        return ['p-2', 'text-indigo-600', "bg-indigo-100", "border-round","inline-block"];
                    }else if(value == CONSTANTS.SIM_STATUS.PURGED){
                        return ['p-2', 'text-red-700', "bg-red-100", "border-round","inline-block"];
                    }else if(value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY){
                        return ['p-2', 'text-cyan-800', "bg-cyan-100", "border-round","inline-block"];
                    }else if(value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY){
                        return ['p-2', 'text-teal-800', "bg-teal-100", "border-round","inline-block"];
                    }else if(value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY){
                        return ['p-2', 'text-orange-700', "bg-orange-100", "border-round","inline-block"];
                    }
                    return [];
                },
                funcConvertText: (value)=>{
                    if(value == 0){
                        return me.tranService.translate("sim.status.inventory");
                    }else if(value == CONSTANTS.SIM_STATUS.READY){
                        // return me.tranService.translate("sim.status.ready");
                        return me.tranService.translate("sim.status.activated");
                    }else if(value == CONSTANTS.SIM_STATUS.ACTIVATED){
                        return me.tranService.translate("sim.status.activated");
                    }else if(value == CONSTANTS.SIM_STATUS.DEACTIVATED){
                        return me.tranService.translate("sim.status.deactivated");
                    }else if(value == CONSTANTS.SIM_STATUS.PURGED){
                        return me.tranService.translate("sim.status.purged");
                    }else if(value == CONSTANTS.SIM_STATUS.INACTIVED){
                        return me.tranService.translate("sim.status.inactivated");
                    }else if(value == 15 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 15 + CONSTANTS.SIM_STATUS.READY){
                        return this.tranService.translate("sim.status.processingChangePlan");
                    }else if(value == 10 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 10 + CONSTANTS.SIM_STATUS.READY){
                        return this.tranService.translate("sim.status.processingRegisterPlan");
                    }else if(value == 20 + CONSTANTS.SIM_STATUS.ACTIVATED || value == 20 + CONSTANTS.SIM_STATUS.READY){
                        return this.tranService.translate("sim.status.waitingCancelPlan");
                    }
                    return "";
                },
                style:{
                    color: "white"
                }
            },
            {
                name: this.tranService.translate('groupSim.detail.planName'),
                key: 'ratingPlanName',
                size: '20%',
                align: 'left',
                isShow: true,
                isSort: false,
            },
        ];

        this.optionTable = {
            hasClearSelected: false,
            hasShowChoose: true,
            hasShowIndex: true,
            hasShowToggleColumn: false,
            action: [
                {
                    icon: 'pi pi-fw pi-trash',
                    tooltip: this.tranService.translate('global.button.delete'),
                    func: (id: string) => {  let ids:string[] = [id];this.removeSim(ids)
                        // me.messageCommonService.error('Hello error');
                    },
                },
            ],
        };
        this.pageNumber = 0;
        this.pageSize = 10;
        this.sort = 'msisdn,asc';

        this.selectItems = [];
        this.dataSet = {
            content: [],
            total: 0,
        };

        this.search(0, this.pageSize,this.sort, null)
    }

    search(page, limit, sort, params){
        let me = this;
        this.pageNumber = page;
        this.pageSize = limit;
        this.sort = sort;
        let dataParam = {
            page,
            size:limit,
            sort
        }
        this.groupSimService.getListDetailGroupSim(this.idForEdit,{},dataParam,(response)=>{
            me.dataSet.content=response.content;
            me.dataSet.total = response.totalElements;
        })
    }

    ngOnDestroy(): void {
        this.subcribeDebounce.unsubscribe();
    }

    deleteGroupSim(){
        let me = this;
        this.messageCommonService.confirm(this.tranService.translate("groupSim.label.deleteTextGroup"), this.tranService.translate("groupSim.label.confirmDelete"),{
            ok: ()=>{
                me.groupSimService.deleteSimGroup(me.idForEdit, {}, {}, (response)=>{
                    me.messageCommonService.success(me.tranService.translate("global.message.success"));
                    me.router.navigate(['/sims/group'])
                })
            }
        })
    }

    loadSimNotInGroup(data, callback){
        this.simService.searchNotInGroup(data, callback);
    }

    protected readonly CONSTANTS = CONSTANTS
}
