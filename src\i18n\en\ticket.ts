export default {
    label: {
        config: {
            provinceName: "Province name",
            provinceCode: "Province code",
            email: "Email list",
            clue: "Đầu mối",
        },
        customerName : "Contact name",
        email : "Contact email",
        phone : "Contact phone",
        content : "Content",
        type : "Type request",
        createdDate : "Created date",
        status : "Status",
        typeRequest : "Type request",
        changeSim : "Subscriber number change",
        note : "Note",
        noteAdmin: "Note admin",
        createRequest : "Create request",
        updateRequest : "Update status request",
        transferProcessing : 'Assignee',
        province: 'Province',
        fullName : 'Contact name',
        requestConfigUpdate : "Edit Config Province",
        emailSearch : 'Email',
        address: 'Address',
        quantity: 'Quantity SIM',
        listImsi: 'List IMSI',
        orderAddress: 'Order Address',
        detailAddress: 'Detailed Address',
        district: 'District',
        commune: 'Commune',
        updateBy: 'Update By',
        dateFrom: "Date From",
        dateTo: "Date To",
        imsi: "IMSI",
        allocationDate: "Allocation Date",
        activedDate: "Actived Date",
        notActivated: "Not Activated",
        awaitingActivation: "Awaiting Activation",
        activated: "Activated",
        updateOrderSim: "Update request status",
        processingNotes: "Processing notes",
        orderHistory: "Order History",
        updatedDate: "Updated Date",
        requestActiveSim: "Create a SIM activation request",
        deliveryAddress: 'Delivery Address',
        viewOrderSim: 'Request Status Details',
        listNotes: "List of processing notes",
        listImsis: "List SIM issued",
        listNote : "List Note",
        viewDetailReplaceSim : "View Detail Replace SIM",
        viewDetailTestSim : "View Detail Test SIM",
        time : "Time",
        implementer :"Implementer",
        historyOrder : "Order History",
        generalInfo: "General Information",
        enterImsi: "Enter IMSI information",
        listactiveImsis: "List IMSI",
        viewActiveSim: 'Detail request active SIM',
        imsiByFile: "Nhập IMSI bằng file",
        viewDetailDiagnose: "View Detail Diagnose"
    },
    menu : {
        config : "Config List",
        requestMgmt : "Manage Requests",
        requestConfig : "Edit Config Province",
        requestList :"Request List",
        detail : "Xem chi tiết",
        testSim : 'Test SIM Request',
        replaceSim : 'Replace SIM Request',
        orderSim: 'Order SIM Request',
        activeSim: 'Active SIM',
        listIssuedSim: "List of issued SIM cards",
        errorContent: "Error content",
        diagnose: "Diagnose Request",
    },
    text : {
        selectEmail : "Select email",
        addImsi: "Add Imsi",
    },
    status : {
        new : 'NEW',
        received : 'RECEIVED',
        inProgress : 'IN-PROGRESS',
        reject : 'REJECT',
        done : 'DONE'
    },
    type : {
        replaceSim : 'Replace SIM',
        testSim : 'Test SIM',
        orderSim: 'Order SIM',
        activeSim: 'Active SIM'
    }
    ,message : {
        invalidPhone : 'Subcriber is invalid format',
        noteChangeSim : 'It is allowed to enter multiple subscription numbers, separated by commas',
        minQuantity: 'The smallest value is 1',
        invalidListImsi: 'Incorrect format, imsi series separated by commas',
        large: "The file list is larger than \${limitRow}\ lines",
        empty: "The list is empty",
        searchInfoNull: "Please enter search information before downloading the file",
        largeFile: "File size must not exceed 100MB",
        emptyFile: "Empty file",
        redundantColumns: "File upload is redundant columns",
        missingColumns: "File upload is missing columns",
        wrongSample: "Wrong sample file format",
        wrongImsiFormat: "IMSI is in wrong format, IMSI must be numeric and cannot exceed 18 characters",
        missingImsiInfo: "Missing IMSI info",
        imsiNotExist: "IMSI not exist",
        imsiIsActivated: "IMSI is Activated",
        downloadFile: "Download file",
        isError: "An error occurred!",
        isDownloadMessage: "The uploaded file has incorrect information, please correct it!",
        uploadFile: "Import file",
        maxQuantity: "This field should have 5 numeric characters or fewer",
        imsiMaxLength: "This field should have 18 numeric characters or fewer",
        imsiIsExist: "IMSI is exist",
    },
    diagnose: {
        label: {
            name: "Full name",
            email: "Email",
            phone: "Phone number",
            content: "Request content",
            number: "Diagnosis subscriber number",
            diagnoseNumber: "Diagnosis phone number",
        }
    }
}
