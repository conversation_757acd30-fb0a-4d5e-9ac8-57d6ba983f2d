import {Component, Inject, Injector, OnInit} from "@angular/core";
import {MenuItem} from "primeng/api";
import {TicketService} from "src/app/service/ticket/TicketService";
import {ColumnInfo, OptionTable} from "../../common-module/table/table.component";
import {CONSTANTS} from "src/app/service/comon/constants";
import {ComponentBase} from "src/app/component.base";
import {AccountService} from "../../../service/account/AccountService";
import {AbstractControl, FormBuilder, FormGroup, ValidationErrors, ValidatorFn, Validators} from "@angular/forms";
import {LogHandleTicketService} from "../../../service/ticket/LogHandleTicketService";

@Component({
  selector: "ticket-config-list",
  templateUrl: './app.list.test-sim.component.html'
})
export class ListTestSimTicketComponent extends ComponentBase implements OnInit {
  items: MenuItem[];
  home: MenuItem
  searchInfo: {
    provinceCode: string | null,
    email: string | null,
    contactPhone: string | null,
    contactEmail: string | null,
    type: number | null,
    status: number | null,
  };
  columns: Array<ColumnInfo>;
  dataSet: {
    content: Array<any>,
    total: number
  };
  selectItems: Array<any>;
  optionTable: OptionTable;
  pageNumber: number;
  pageSize: number;
  sort: string;
  formSearchTicket: any;
  listProvince: Array<any>;
  listTicketType: Array<any>;
  listTicketStatus: Array<any>;
  mapTicketStatus: any;
  listEmail: Array<any>;
  isShowCreateRequest: boolean;
  formTicketSim: any;
  ticket: {
    id: number
    contactName: string | null,
    contactEmail: string | null,
    contactPhone: string | null,
    content: string | null,
    note: string | null,
    cause : string| null,
    type: number | null, // 0: thay thế sim, 1: test sim
    changeSim: string | null
    status: number | null,
    statusOld?: number | null,
    assigneeId: number | null,
      provinceCode: string | null,
  };
  maxlengthContactName: number = 50;
  typeRequest: string
  userInfo: any
  userType: any
  oldTicket : any = {}
  listNotes : Array<any>
  isShowTableNote : boolean = false;
  mapForm : any = {}
  titlePopup : string = ''
  listActivatedAccount: number[];
  changeTable: boolean = false;
  constructor(
      @Inject(TicketService) private ticketService: TicketService,
      @Inject(AccountService) private accountService: AccountService,
      @Inject(LogHandleTicketService) private logHandleTicketService: LogHandleTicketService,
      private formBuilder: FormBuilder,
      private injector: Injector) {
    super(injector);
  }

  ngOnInit() {
    let me = this;
    me.changeTable = false;
    this.userInfo = this.sessionService.userInfo;
    this.isShowCreateRequest = false;
    this.typeRequest = 'create'
    this.userType = CONSTANTS.USER_TYPE;
    this.listNotes = []
    this.ticket = {
      id: null,
      contactName: null,
      contactEmail: null,
      contactPhone: null,
      content: null,
      note: null,
      cause : null,
      type: CONSTANTS.REQUEST_TYPE.TEST_SIM, // 0: thay thế sim, 1: test sim
      changeSim: null,
      status: null,
      statusOld: null,
      assigneeId: null,
        provinceCode: null,
    };
    this.listTicketType = [
      {
        label: this.tranService.translate('ticket.type.testSim'),
        value: 1
      }
    ]
    this.mapTicketStatus = {
      0: [{
        label: me.tranService.translate('ticket.status.received'),
        value: 1
      },
      {
          label: me.tranService.translate('ticket.status.reject'),
          value: 3
      },
      ],
      1: [
        {
          label: me.tranService.translate('ticket.status.inProgress'),
          value: 2
        },
        {
          label: me.tranService.translate('ticket.status.reject'),
          value: 3
        }
      ],
      2: [
        {
          label: me.tranService.translate('ticket.status.done'),
          value: 4
        },
        {
          label: me.tranService.translate('ticket.status.reject'),
          value: 3
        }
      ]
    }
    this.listTicketStatus = [
      {
        label: me.tranService.translate('ticket.status.new'),
        value: 0
      },
      {
        label: me.tranService.translate('ticket.status.received'),
        value: 1
      },
      {
        label: me.tranService.translate('ticket.status.inProgress'),
        value: 2
      },
      {
        label: me.tranService.translate('ticket.status.reject'),
        value: 3
      },
      {
        label: me.tranService.translate('ticket.status.done'),
        value: 4
      }
    ]
    this.searchInfo = {
      provinceCode: null,
      email: null,
      contactPhone: null,
      contactEmail: null,
      type: CONSTANTS.REQUEST_TYPE.TEST_SIM,
      status: null
    }
    this.columns = [
      {
        name: this.tranService.translate("ticket.label.province"),
        key: "provinceName",
        size: "150px",
        align: "left",
        isShow: this.userInfo.type == CONSTANTS.USER_TYPE.ADMIN,
        isSort: true
      },
      {
        name: this.tranService.translate("ticket.label.customerName"),
        key: "contactName",
        size: "150px",
        align: "left",
        isShow: true,
        isSort: true,
          isShowTooltip: true,
          style: {
              display: 'inline-block',
              maxWidth: '350px',
              overflow: 'hidden',
              textOverflow: 'ellipsis'
          }
      }, {
        name: this.tranService.translate("ticket.label.email"),
        key: "contactEmail",
        size: "150px",
        align: "left",
        isShow: true,
        isSort: true,
            isShowTooltip: true,
            style: {
                display: 'inline-block',
                maxWidth: '350px',
                overflow: 'hidden',
                textOverflow: 'ellipsis'
            }
      }, {
        name: this.tranService.translate("ticket.label.phone"),
        key: "contactPhone",
        size: "fit-content",
        align: "left",
        isShow: true,
        isSort: true
      }, {
        name: this.tranService.translate("ticket.label.content"),
        key: "content",
        size: "fit-content",
        align: "left",
        isShow: true,
        isSort: true,
        isShowTooltip: true,
        style: {
          display: 'inline-block',
          maxWidth: '350px',
          overflow: 'hidden',
          textOverflow: 'ellipsis'
        }
      },
      {
        name: this.tranService.translate("ticket.label.createdDate"),
        key: "createdDate",
        size: "fit-content",
        align: "left",
        isShow: true,
        isSort: true,
        funcConvertText(value) {
          return me.utilService.convertDateToString(new Date(value))
        },
      },
      {
        name: this.tranService.translate("ticket.label.updatedDate"),
        key: "updatedDate",
        size: "fit-content",
        align: "left",
        isShow: true,
        isSort: true,
        funcConvertText(value) {
          return value ? me.utilService.convertDateToString(new Date(value)) : ""
        },
      },
      {
        name: this.tranService.translate("ticket.label.updateBy"),
        key: "updatedByName",
        size: "fit-content",
        align: "left",
        isShow: true,
        isSort: true
      },
      {
        name: this.tranService.translate("ticket.label.status"),
        key: "status",
        size: "fit-content",
        align: "left",
        isShow: true,
        isSort: true,
        funcGetClassname: (value) => {
          if (value == CONSTANTS.REQUEST_STATUS.NEW) {
            return ['p-2', 'text-white', "bg-cyan-300", "border-round", "inline-block"];
          } else if (value == CONSTANTS.REQUEST_STATUS.RECEIVED) {
            return ['p-2', 'text-white', "bg-bluegray-500", "border-round", "inline-block"];
          } else if (value == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {
            return ['p-2', 'text-white', "bg-orange-400", "border-round", "inline-block"];
          } else if (value == CONSTANTS.REQUEST_STATUS.REJECT) {
            return ['p-2', 'text-white', "bg-red-500", "border-round", "inline-block"];
          } else if (value == CONSTANTS.REQUEST_STATUS.DONE) {
            return ['p-2', 'text-white', "bg-green-500", "border-round", "inline-block"];
          }
          return '';
        },
        funcConvertText: function (value) {
          if (value == CONSTANTS.REQUEST_STATUS.NEW) {
            return me.tranService.translate("ticket.status.new");
          } else if (value == CONSTANTS.REQUEST_STATUS.RECEIVED) {
            return me.tranService.translate("ticket.status.received");
          } else if (value == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {
            return me.tranService.translate("ticket.status.inProgress");
          } else if (value == CONSTANTS.REQUEST_STATUS.REJECT) {
            return me.tranService.translate("ticket.status.reject");
          } else if (value == CONSTANTS.REQUEST_STATUS.DONE) {
            return me.tranService.translate("ticket.status.done");
          }
          return "";
        }
      }
    ];

    this.optionTable = {
      hasClearSelected: false,
      hasShowChoose: false,
      hasShowIndex: true,
      hasShowToggleColumn: false,
      action: [
        {
          icon: "pi pi-info-circle",
          tooltip: this.tranService.translate("global.button.view"),
          func: function (id, item) {
            me.handleDetailRequest(id, item)
          }
        },
        {
          icon: "pi pi-window-maximize",
          tooltip: this.tranService.translate("global.button.edit"),
          func: function (id, item) {
            me.handleEditRequest(id, item)
          },
          funcAppear : function (id, item) {
            if(me.userInfo.type == CONSTANTS.USER_TYPE.ADMIN || me.userInfo.type == CONSTANTS.USER_TYPE.CUSTOMER) return false;
            if(!item.updatedBy && !item.assigneeId && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) return true;
            if((me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && item.updatedBy !== me.userInfo.id) ||
                (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && item.assigneeId != null) ) {
              return false;
            }
              if (me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) return true
              else return false;
          }
        }
      ]
    }
    this.pageNumber = 0;
    this.pageSize = 10;
    this.sort = "createdDate,desc";
    this.dataSet = {
      content: [],
      total: 0
    }
    this.formSearchTicket = this.formBuilder.group(this.searchInfo);
    this.formTicketSim = this.formBuilder.group(this.ticket);
    this.getListProvince();
    this.listActivatedAccount = [];
    this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
  }

  getValueStatus(value) {
    let me = this;
    {
      if (value == CONSTANTS.REQUEST_STATUS.NEW) {
        return me.tranService.translate("ticket.status.new");
      } else if (value == CONSTANTS.REQUEST_STATUS.RECEIVED) {
        return me.tranService.translate("ticket.status.received");
      } else if (value == CONSTANTS.REQUEST_STATUS.IN_PROGRESS) {
        return me.tranService.translate("ticket.status.inProgress");
      } else if (value == CONSTANTS.REQUEST_STATUS.REJECT) {
        return me.tranService.translate("ticket.status.reject");
      } else if (value == CONSTANTS.REQUEST_STATUS.DONE) {
        return me.tranService.translate("ticket.status.done");
      }
      return "";
    }
  }

  getValueDate(value) {
    let me = this;
    // console.log(value)
    return me.utilService.convertDateToString(new Date(value))
  }

  search(page, limit, sort, params) {
    let me = this;
    me.changeTable = false;
    this.pageNumber = page;
    this.pageSize = limit;
    this.sort = sort;
    let dataParams = {
      page,
      size: limit,
      sort
    }
    Object.keys(this.searchInfo).forEach(key => {
      if (this.searchInfo[key] != null) {
        dataParams[key] = this.searchInfo[key];
      }
    })
    this.dataSet = {
      content: [],
      total: 0
    }
    me.messageCommonService.onload();
    this.ticketService.searchTicket(dataParams, (response) => {
      me.dataSet = {
        content: response.content,
        total: response.totalElements
      }
      if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE ||
                me.userInfo.type == CONSTANTS.USER_TYPE.DISTRICT) {
                let listAssigneeId = Array.from(new Set(me.dataSet.content.filter(item => item.assigneeId !== null)
                    .map(item => item.assigneeId as number)));

                me.dataSet.content.forEach(item => {
                    if (item.updateBy !== null) {
                        listAssigneeId.push(item.updateBy as number);
                    }
                });

                const statusCheckListId = Array.from(new Set(listAssigneeId));

                me.accountService.getListActivatedAccount(statusCheckListId, (response) => {
                    me.listActivatedAccount = response;
                    this.optionTable = {
      hasClearSelected: false,
      hasShowChoose: false,
      hasShowIndex: true,
      hasShowToggleColumn: false,
      action: [
        {
          icon: "pi pi-info-circle",
          tooltip: this.tranService.translate("global.button.view"),
          func: function (id, item) {
            me.handleDetailRequest(id, item)
          }
        },
        {
          icon: "pi pi-window-maximize",
          tooltip: this.tranService.translate("global.button.edit"),
          func: function (id, item) {
            me.handleEditRequest(id, item)
          },
          funcAppear : function (id, item) {
            if(me.userInfo.type == CONSTANTS.USER_TYPE.ADMIN || me.userInfo.type == CONSTANTS.USER_TYPE.CUSTOMER) return false;
              if (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE]) && ( me.listActivatedAccount === undefined || me.listActivatedAccount == null )) return true;
            if ((me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) && ((item.assigneeId != null && me.listActivatedAccount.includes(item.assigneeId)))) return false;
            if ((me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) && ((item.assigneeId == null && item.updatedBy != null && me.listActivatedAccount.includes(item.updatedBy) && item.updatedBy != me.userInfo.id))) return false;
            if(!item.updatedBy && !item.assigneeId && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) return true;
            if ((me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) && ((item.assigneeId != null && !me.listActivatedAccount.includes(item.assigneeId)) || (item.updatedBy != null && !me.listActivatedAccount.includes(item.updatedBy)))) return true;
            if((me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && item.updatedBy !== me.userInfo.id) ||
                (me.userInfo.type == CONSTANTS.USER_TYPE.PROVINCE && item.assigneeId != null) ) {
              return false;
            }
              if (me.checkAuthen([CONSTANTS.PERMISSIONS.TICKET.UPDATE])) return true
              else return false;
          }
        }
      ]
    }
    me.changeTable = true
                })
            }
    }, null, () => {
      me.messageCommonService.offload();
    })
  }

  resetTicket() {
    this.ticket = {
      id: null,
      contactName: null,
      contactEmail: null,
      contactPhone: null,
      content: null,
      note: null,
      cause : null,
      type: CONSTANTS.REQUEST_TYPE.TEST_SIM, // 0: thay thế sim, 1: test sim
      changeSim: null,
      status: null,
      statusOld: null,
      assigneeId: null,
        provinceCode: null,
    };
  }

  onSubmitSearch() {
    this.pageNumber = 0;
    this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
  }

  getListProvince() {
    this.accountService.getListProvince((response) => {
      this.listProvince = response.map(el => {
        return {
          ...el,
          display: `${el.code} - ${el.name}`
        }
      })
    })
  }
    getProvinceName(provinceCode) {
        const province = this.listProvince.find(el => el.code === provinceCode);
        return province ? province.code + ' - ' + province.name : "";
    }

  // tạo sửa yêu cầu
  createOrUpdateRequest() {
    if(this.messageCommonService.isloading == true || this.isShowCreateRequest == false) return;
    let me = this;
    this.messageCommonService.onload()
    if (this.typeRequest == 'create') {
      let bodySend = {
        contactName: this.ticket.contactName,
        contactEmail: this.ticket.contactEmail,
        contactPhone: this.ticket.contactPhone,
        content: this.ticket.content,
        note: this.ticket.note,
        type: this.ticket.type,
        changeSim: this.ticket.type == 0 ? this.ticket.changeSim : null
      }
      if (bodySend.contactPhone != null){
        if(bodySend.contactPhone.startsWith('0')){
          bodySend.contactPhone = "84"+bodySend.contactPhone.substring(1, bodySend.contactPhone.length);
        }else if(bodySend.contactPhone.length == 9 || bodySend.contactPhone.length == 10){
          bodySend.contactPhone = "84"+bodySend.contactPhone;
        }
      }
      this.ticketService.createTicket(bodySend, (resp) => {
        me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"));
        me.isShowCreateRequest = false
        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo)
        // nếu KH đc gán cho GDV thì gửi mail cho GDV và danh sách admin đc cấu hình không thì chỉ gửi mail cho danh sách admin đc cấu hình
        // get mail admin tinh dc cau hinh
        me.ticketService.getDetailTicketConfig(me.userInfo.provinceCode, (resp1) => {
          let array = []
          for (let info of resp1.emailInfos) {
            array.push({
              userId: info.userId,
              ticketId: resp.id
            })
          }
          if(resp?.assigneeId) {
            array.push({
              userId: resp.assigneeId,
              ticketId: resp.id
            })
          }
          me.ticketService.sendMailNotify(array);
        })
      }, null, () => {
        me.messageCommonService.offload()
      })
    } else if (this.typeRequest == 'update') {
      let bodySend = {
        contactName: this.ticket.contactName,
        contactEmail: this.ticket.contactEmail,
        contactPhone: this.ticket.contactPhone,
        content: this.ticket.content,
        note: this.ticket.note,
        type: this.ticket.type,
        changeSim: this.ticket.type == 0 ? this.ticket.changeSim : null,
        status: this.ticket.status,
        cause : this.ticket.cause,
        assigneeId: this.ticket.assigneeId,
        listLog : this.listNotes
      }
      if (bodySend.contactPhone != null){
        if(bodySend.contactPhone.startsWith('0')){
          bodySend.contactPhone = "84"+bodySend.contactPhone.substring(1, bodySend.contactPhone.length);
        }else if(bodySend.contactPhone.length == 9 || bodySend.contactPhone.length == 10){
          bodySend.contactPhone = "84"+bodySend.contactPhone;
        }
      }
      // update ticket
      this.ticketService.updateTicket(this.ticket.id, bodySend, (resp) => {
        me.isShowCreateRequest = false
        me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"));
        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo)
        if(resp.assigneeId != null && resp.assigneeId != undefined) {
          me.ticketService.sendMailNotify([{
            userId: resp.assigneeId,
            ticketId: resp.id
          }])
        }
      }, null, () => {
        me.messageCommonService.offload()
      })
    }
  }

  showModalCreate() {
    this.isShowCreateRequest = true
    this.typeRequest = 'create'
    this.titlePopup = this.tranService.translate('ticket.label.createRequest')
    this.resetTicket()
    // auto fill thong tin khi tao
    if (this.userInfo.type === CONSTANTS.USER_TYPE.CUSTOMER) {
      this.ticket.contactName = this.userInfo.fullName.substring(0, this.maxlengthContactName);
      this.ticket.contactPhone = this.userInfo.phone;
      this.ticket.contactEmail = this.userInfo.email;
    }
    this.formTicketSim = this.formBuilder.group(this.ticket)
  }

  handleEditRequest(id, item) {
    let me = this
    this.formTicketSim.reset()
    this.typeRequest = 'update'
    this.titlePopup = this.tranService.translate('ticket.label.updateRequest')
    this.isShowCreateRequest = true;
    this.ticketService.getDetailTicket(item.id, (resp) => {
      me.ticket = {
        id: resp.id,
        contactName: resp.contactName,
        contactEmail: resp.contactEmail,
        contactPhone: resp.contactPhone,
        content: resp.content,
        note: resp.note,
        cause : resp.cause,
        type: resp.type, // 0: thay thế sim, 1: test sim
        changeSim: resp.changeSim,
        status: null,
        statusOld: resp.status,
        assigneeId: resp.assigneeId,
          provinceCode: resp.provinceCode
      }
      me.oldTicket = {...me.ticket}
      me.formTicketSim = me.formBuilder.group(me.ticket)
      // lấy list note
      this.logHandleTicketService.search({ticketId: item.id}, (res)=> {
        console.log(res.content)
        this.listNotes = res.content;
        // for(let note of this.listNotes) {
        //   this.mapForm[note.id] = this.formBuilder.group(note);
        // }
          this.listNotes.forEach(note => {
              this.mapForm[note.id] = this.formBuilder.group({
                  content: ['', [Validators.required, Validators.maxLength(255), this.noWhitespaceValidator()]]
              });
          });
        me.isShowTableNote = true;
      })
    })
    this.ticketService.getDetailTicketConfig(me.userInfo.provinceCode, (resp) => {
      me.listEmail = resp.emailInfos;
    })
  }

  handleDetailRequest(id, item) {
    let me = this
    this.formTicketSim.reset()
    this.titlePopup = this.tranService.translate('ticket.label.viewDetailTestSim')
    this.typeRequest = 'detail'
    this.isShowCreateRequest = true;
    this.ticketService.getDetailTicket(item.id, (resp) => {
      me.ticket = {
        id: resp.id,
        contactName: resp.contactName,
        contactEmail: resp.contactEmail,
        contactPhone: resp.contactPhone,
        content: resp.content,
        note: resp.note,
        cause: resp.cause,
        type: resp.type, // 0: thay thế sim, 1: test sim
        changeSim: resp.changeSim,
        status: null,
        statusOld: resp.status,
        assigneeId: resp.assigneeId,
          provinceCode: resp.provinceCode,
      }
      me.oldTicket = {...me.ticket}
      me.formTicketSim = me.formBuilder.group(me.ticket)
      // lấy list note
      this.logHandleTicketService.search({ticketId: item.id}, (res)=> {
        console.log(res.content)
        this.listNotes = res.content;
        for(let note of this.listNotes) {
          this.mapForm[note.id] = this.formBuilder.group(note);
        }
        me.isShowTableNote = true;
      })
    })
    this.ticketService.getDetailTicketConfig(me.userInfo.provinceCode, (resp) => {
      me.listEmail = resp.emailInfos;
    })
  }

  preventCharacter(event){
    if(event.ctrlKey){
      return;
    }
    if(event.keyCode == 8 || event.keyCode == 13 || event.keyCode == 37 || event.keyCode == 39){
      return;
    }
    if(event.keyCode < 48 || event.keyCode > 57){
      event.preventDefault();
    }
      // Chặn ký tự 'e', 'E' và dấu '+'
      if (event.keyCode == 69 || event.keyCode == 101 || event.keyCode == 107 || event.keyCode == 187) {
          event.preventDefault();
      }
  }
    isFormValid() {
        return Object.values(this.mapForm).every((formGroup: FormGroup) => formGroup.valid);
    }
    noWhitespaceValidator(): ValidatorFn {
        return (control: AbstractControl): ValidationErrors | null => {
            const isWhitespace = (control.value || '').trim().length === 0;
            const isValid = !isWhitespace;
            return isValid ? null : {whitespace: true};
        }
    };
    onKeyDownNote(event): void {
        if (event.key === ' ' && (this.ticket.note == null || this.ticket.note != null && this.ticket.note.trim() === '')) {
            event.preventDefault();
        }

        if (this.ticket.note != null && this.ticket.note.trim() != '') {
            this.ticket.note = this.ticket.note.trimStart().replace(/\s{2,}/g, ' ');
            return;
        }
    }
    onKeyDownContent(event) {
        if (event.key === ' ' && (this.ticket.content == null || this.ticket.content != null && this.ticket.content.trim() === '')) {
            event.preventDefault();
        }

        if (this.ticket.content != null && this.ticket.content.trim() != '') {
            this.ticket.content = this.ticket.content.trimStart().replace(/\s{2,}/g, ' ');
            return;
        }
    }
    onKeyDownNoteContent(event: KeyboardEvent, note: any): void {
        if (event.key === ' ' && (!note.content || note.content.trim() === '')) {
            event.preventDefault();
        }

        if (note.content && note.content.trim() !== '') {
            note.content = note.content.trimStart().replace(/\s{2,}/g, ' ');
            return;
        }
    }
    onKeyDownCause(event) {
        if (event.key === ' ' && (this.ticket.cause == null || this.ticket.cause != null && this.ticket.cause.trim() === '')) {
            event.preventDefault();
        }

        if (this.ticket.cause != null && this.ticket.cause.trim() != '') {
            this.ticket.cause = this.ticket.cause.trimStart().replace(/\s{2,}/g, ' ');
            return;
        }
    }

    protected readonly CONSTANTS = CONSTANTS;
}
