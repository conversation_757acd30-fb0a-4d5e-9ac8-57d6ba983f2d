<div class="vnpt flex flex-row justify-content-between align-items-center bg-white p-2 border-round">
    <div class="">
        <div class="text-xl font-bold mb-1">{{this.tranService.translate("datapool.label.shareData")}}</div>
        <p-breadcrumb class="max-w-full col-7" [model]="items" [home]="home"></p-breadcrumb>
    </div>
</div>

<style>
    input[type=number]::-webkit-outer-spin-button,
    input[type=number]::-webkit-inner-spin-button {
        /* Ẩn nút */
        display: none;
    }
</style>

<upload-file-vnpt
    [shareList]="shareList"
    [remainDataTotal]="remainDataTotal"
    [isShowDialogImportByFile]="isShowDialogImportByFile"
    [isShowErrorUpload]="isShowErrorUpload"
    (onHideImport)="onHideImport()"
    [totalRemain]="originRemain"
    [trafficType]="trafficType"
></upload-file-vnpt>

<form action="" [formGroup]="shareDataForm" (submit)="submitForm()" class="responsive-form">
    <div class="mt-3">
        <p-card>
            <div class="font-bold text-xl">{{tranService.translate("datapool.label.generalInfomation")}}</div>
            <div class="flex flex-row gap-4 mt-3 px-2 mb-0">
                <div class="flex flex-column gap-2 flex-1">
                    <label htmlFor="walletName">{{tranService.translate("datapool.label.dataWallet")}}</label>
                    <p-dropdown [options]="dummyWallet"
                                formControlName="walletValue" optionLabel="subCode"
                                optionValue="subCode"
                                [placeholder]="tranService.translate('datapool.label.selectWallet')"
                                id="walletName"
                                (onChange)="changeWallet()"
                                [emptyFilterMessage]="tranService.translate('global.text.nodata')"
                                filter="true">
                    </p-dropdown>
                </div>
                <div class="flex flex-column gap-2 flex-1">
                    <label htmlFor="datePicker">{{tranService.translate("datapool.label.shareDate")}}</label>
                    <p-calendar
                        styleClass="w-full"
                        id="datePicker"
                        formControlName="dateShare"
                        [showTime]="true" [showSeconds]="true"
                        dateFormat="dd/mm/yy"
                    ></p-calendar>
                </div>
            </div>
            <div class="flex flex-row gap-4 px-2 py-0 m-0">
                <div *ngIf="shareDataForm.get('walletValue').invalid && shareDataForm.get('walletValue').dirty">
                    <div *ngIf="shareDataForm.get('walletValue').errors.required" class="text-red-500" >{{tranService.translate("global.message.required")}}</div>
                </div>
            </div>
            <div class="flex flex-row gap-4 mt-3 px-2 mb-0">
                <div class="flex flex-column gap-2 flex-1">
                    <label htmlFor="description">{{tranService.translate("datapool.label.description")}}</label>
                    <textarea
                        class="w-full" style="resize: none;"
                        rows="5"
                        [autoResize]="false"
                        pInputTextarea id="description"
                        formControlName="description"
                        [placeholder]="tranService.translate('sim.text.inputDescription')"
                    ></textarea>
                </div>

                <div class="flex flex-column gap-2 flex-1 field">
                    <p-radioButton
                        [label]="tranService.translate('datapool.label.shareNormal')"
                        name="selectedShareMethod" value="0"
                        formControlName="selectedShareMethod"
                        class="p-3"
                        [(ngModel)]="shareMethod"
                        inputId="1"
                        (onClick)="clearSelectedGroup()">
                    </p-radioButton>
                    <p-radioButton
                        [label]="tranService.translate('datapool.label.shareByGroup')"
                        name="selectedShareMethod"
                        value="1"
                        inputId="2"
                        class="p-3"
                        [(ngModel)]="shareMethod"
                        formControlName="selectedShareMethod"
                        (onClick)="clearSelectedData()">
                    </p-radioButton>
                </div>
            </div>
            <div class="w-full field grid px-2 m-0 py-0 mb-3">
                <div *ngIf="shareDataForm.get('description').invalid && shareDataForm.get('description').dirty">
                    <div *ngIf="shareDataForm.get('description').errors.maxlength" class="text-red-500" >{{tranService.translate("global.message.maxLength",{len:255})}}</div>
                </div>
            </div>
        </p-card>
    </div>

    <div class="mt-3 table-vnpt" *ngIf="showShareList">
        <p-card>
            <div class="font-bold text-xl">{{walletName}}</div>
            <div class="mt-5 flex flex-row gap-3 justify-content-between">
                <div class="flex flex-row gap-3 col-7">
                    <div class="col-5" style="max-width: calc(100% - 1px) !important;">
                        <vnpt-select
                            [control]="phoneReceiptSelectControl"
                            *ngIf="shareMethod == 0"
                            [(value)]="phoneReceiptSelect"
                            (onchange)="checkValidAdd()"
                            (onSelectItem)="addPhone(phoneReceiptSelect, true)"
                            [isAutoComplete]="true"
                            [isMultiChoice]="false"
                            paramKey="phoneReceipt"
                            keyReturn="phoneReceipt"
                            [lazyLoad]="true"
                            [placeholder]="tranService.translate('datapool.label.receiverPhone')"
                            displayPattern="${phoneReceipt}"
                            [loadData]="getListShareInfoCbb.bind(this)"
                        ></vnpt-select>

                        <vnpt-select
                            *ngIf="shareMethod == 1"
                            [(value)]="groupSelected"
                            objectKey="groupSubWallet"
                            (onSelectItem)="onSelectGroup(groupSelected)"
                            paramKey="groupName"
                            keyReturn="id"
                            styleClass="w-full"
                            [placeholder]="tranService.translate('datapool.label.shareGroup')"
                            displayPattern="${groupName}"
                            typeValue="object"
                            [isMultiChoice]="false"
                            (onchange)="clearSelectedGroup()"
                        ></vnpt-select>
                    </div>
                    <button *ngIf="shareMethod == 0" [disabled]="isClickCreate || !isValidPhone" type="button" pButton [label]="tranService.translate('datapool.button.add')" (click)="addPhone(phoneReceiptSelect)"></button>
                </div>
                <div class="flex flex-wrap justify-content-end gap-4">
                    <!--                    <p-button icon="pi pi-users" [label]="tranService.translate('datapool.button.shareByGroup')" (click)="shareByGroup()" styleClass="p-button-info"></p-button>-->
                    <p-button *ngIf="shareMethod == 0" icon="pi pi-file-excel" [label]="tranService.translate('datapool.button.importFile')" (click)="importByFile()" styleClass="p-button-success"></p-button>
                </div>
            </div>
            <div class="mb-5 flex flex-row gap-3 justify-content-between text-red-500 px-1">
                <div *ngIf="!isValidPhone && shareMethod == 0">
                    {{tranService.translate("datapool.message.digitError")}}
                </div>
            </div>
            <div class="flex flex-row justify-content-between mb-2">
                <div class="font-semibold text-lg">{{tranService.translate('datapool.label.remainData')}}: {{shareMethod == 1 ? formatNumber(dataShareGroupTotal) : formatNumber(remainDataTotal)}}/{{formatNumber(totalData)}}</div>
                <div class="flex flex-row gap-2">
                    <button [disabled]="shareList.length <= 0 && dataSet.total <= 0" pButton class="p-button-outlined" type="button" (click)="shareData()">{{tranService.translate("datapool.button.equalSharing")}}</button>
                    <button [disabled]="shareList.length <= 0 && dataSet.total <= 0" pButton class="p-button-outlined" type="button" (click)="defineData()">{{tranService.translate("datapool.button.fixedAllocation")}}</button>
                    <button [disabled]="shareList.length <= 0 && dataSet.total <= 0" pButton class="p-button-outlined" type="button" (click)="resetData()">{{tranService.translate("datapool.button.revokeSharing")}}</button>
                </div>
            </div>
            <div class="flex flex-row gap-3 mb-2" *ngIf="selectedTableData.length > 0">
                <div class="align-self-auto my-auto">Đã chọn {{selectedTableData.length}} giá trị</div>
                <button pButton (click)="selectedTableData = []" pRipple type="button" icon="pi pi-times"></button>
            </div>
            <p-table #normalShare class="normalShareTable"
                [value]="shareList" [tableStyle]="{ 'min-width': '50rem','padding':'21px' }"
                [(selection)]="selectedTableData" *ngIf="shareMethod == 0"
                [totalRecords]="shareList.length"
                dataKey="phoneReceipt"
                [paginator]="shareList.length > 0"
                [rows]="10"
                (onPage)="pageChange($event)"
                [showCurrentPageReport]="true"
                [currentPageReportTemplate]="tranService.translate('global.text.templateTextPagination')"
                [rowsPerPageOptions]="[5,10,20, 25, 50]"
            >
                <ng-template pTemplate="header">
                    <tr>
                        <th style="width: 112px;!important;">
                            <div class="flex flex-row gap-3 ml-2 align-items-center">
                                <p-tableHeaderCheckbox [disabled]="!isAutoWallet" (click)="onClickSelection()"></p-tableHeaderCheckbox>
                                <div class="align-self-auto">{{tranService.translate('datapool.label.autoSharing')}}</div>
                            </div>
                        </th>
                        <th >{{tranService.translate("global.text.stt")}}</th>
                        <th>{{tranService.translate("datapool.label.phone")}}</th>
                        <th>{{tranService.translate('datapool.label.fullName')}}</th>
                        <th>{{tranService.translate('datapool.label.email')}}</th>
                        <th *ngIf="trafficType == 'Gói Data'">{{tranService.translate('datapool.label.sharingData', {type: 'MB'})}}</th>
                        <th *ngIf="trafficType == 'Gói thoại'">{{tranService.translate('datapool.label.sharingData', {type: 'Phút'})}}</th>
                        <th *ngIf="(trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase())">{{tranService.translate('datapool.label.sharingData', {type: 'SMS'})}}</th>
                        <th *ngIf="trafficType != 'Gói Data' && trafficType != 'Gói thoại' && !(trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase())">{{tranService.translate('datapool.label.sharingData', {type: ''})}}</th>
                        <th>{{tranService.translate('datapool.label.percentage')}}</th>
                        <th></th>
                    </tr>
                    <tr *ngIf="!shareList || shareList.length === 0">
                        <td [attr.colspan]="7" class="box-table-nodata">
                            <span class="pi pi-inbox" style="font-size: x-large;">&nbsp;</span>{{tranService.translate("global.text.nodata")}}
                        </td>
                    </tr>
                </ng-template>
                <ng-template pTemplate="body" let-list let-index="rowIndex">
                    <tr>
                        <td style="max-width:150px">
                            <p-tableCheckbox [value]="list" [disabled]="!isAutoWallet" (click)="onClickSelection()"></p-tableCheckbox>
                        </td>
                        <td>{{index+1}}</td>
                        <td>{{ list.phoneReceipt }}</td>
                        <td>
                            <input type="text" (input)="changeDataName($event, index)" pInputText [value]="list.name">
                            <div class="text-red-500" *ngIf="list.name?.length >=50">
                                {{tranService.translate("global.message.maxLength",{len:50})}}
                            </div>
                            <div *ngIf="!utilService.checkValidCharacterVietnamese(list.name)" class="text-red-500">
                                {{tranService.translate("global.message.wrongFormatName",{len:150})}}
                            </div>
                        </td>
                        <td>
                            <input type="text" (input)="changeDataMail($event, index)" pInputText [value]="list.email">
                            <div class="text-red-500" *ngIf="list.email?.length >=100">
                                {{tranService.translate("global.message.maxLength",{len:100})}}
                            </div>
                            <div class="text-red-500" *ngIf="isMailInvalid(list.email)">
                                {{tranService.translate("global.message.formatEmail")}}
                            </div>
                        </td>
                        <td>
                            <div class="flex flex-row align-items-center">
                                <input [ngClass]="{'surface-200':list.locked}" (input)="changeData($event, index)" (keydown)="onKeyDown($event, index)" [disabled]="list.locked" class="border-noround-right" pInputText type="number" [value]="list.data">
                                <div pInputText class="border-round-right border-noround-left  cursor-pointer surface-300" styleClass="cursor-pointer" (click)="list.locked=!list.locked">
                                    <i *ngIf="list.locked" class="pi pi-lock"></i>
                                    <i *ngIf="!list.locked" class="pi pi-lock-open"></i>
                                </div>
                            </div>
                            <div class="text-red-500" *ngIf="(trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase()) && (list.data < 10 || !checkDataCondition(list))">
                                {{tranService.translate("datapool.message.smsError")}}
                            </div>
                            <div class="text-red-500" *ngIf="trafficType == 'Gói Data' && (list.data < 100 || !checkDataCondition(list))">
                                {{tranService.translate("datapool.message.dataError")}}
                            </div>
                        </td>
                        <td><div class="flex flex-row ">{{ list.percent }} <div *ngIf="list.percent||list.percent==0">%</div> </div></td>
                        <td><button type="button" pButton class="p-button-outlined" (click)="deleteItem(index)"><i class="pi pi-trash"></i></button></td>
                    </tr>
                </ng-template>
                <ng-template pTemplate="paginatorright">
                    <div class="flex flex-row justify-content-start align-items-center grap-2 ml-3">
                        <div class="mr-2">{{tranService.translate('global.text.page')}}</div>
                        <p-inputNumber (keyup.enter)="jumpPage()" formControlName="pageCurrent" mode="decimal" [min]="1" [max]="getMaxPage()"> </p-inputNumber>
                    </div>
                </ng-template>
            </p-table>

            <table-vnpt
                *ngIf="shareMethod == 1"
                [tableId]="'tbSubShareByGroup'"
                [fieldId]="'id'"
                [columns]="columns"
                [dataSet]="dataSet"
                [options]="optionTable"
                [loadData]="search.bind(this)"
                [pageNumber]="pageNumber"
                [pageSize]="pageSize"
                [sort]="sort"
                [params]="searchInfo"
                [labelTable]=""
                [(selectItems)]="groupSelectedValue"
                (onChangeSelectAllItems)="onClickSelection()"
                (onChangeSelectItem)="onClickSelection()"
                [tableSelectionText]="tranService.translate('datapool.label.autoSharing')"
                selectionWidth="8"
            ></table-vnpt>



            <div class="flex flex-row justify-content-center gap-3 p-2 mt-5">
                <a routerLink="/data-pool/shareMgmt/listShare"><button pButton [label]="tranService.translate('global.button.cancel')" class="p-button-secondary p-button-outlined" type="button"></button></a>
                <button pButton [disabled]="!(shareDataForm.controls.walletValue.valid && shareDataForm.controls.description.valid) || checkValidData()" [label]="tranService.translate('global.button.save')" class="p-button-info" type="button" (click)="handleShareData()"></button>
            </div>
        </p-card>
    </div>

    <p-dialog (onHide)="onHideDefine()" [header]="tranService.translate('datapool.button.fixedAllocation')" [(visible)]="isShowDefined" [modal]="true" [style]="{ width: '50vw' }" [draggable]="false" [resizable]="false">
        <div class="flex flex-column gap-2 flex-1">
            <label htmlFor="">{{tranService.translate("datapool.label.revokeData", {data: dataForLabel})}}</label>
            <input type="number" (input)="checkValidDefine($event)" formControlName="definedData" pInputText>
            <div class="text-red-500" *ngIf="(trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase()) && (shareDataForm.controls['definedData'].value < 10 || isValidDataFixed) && shareDataForm.controls['definedData'].dirty">
                {{tranService.translate("datapool.message.smsError")}}
            </div>
            <div class="text-red-500" *ngIf="trafficType == 'Gói Data' && (shareDataForm.controls['definedData'].value < 100 || isValidDataFixed) && shareDataForm.controls['definedData'].dirty">
                {{tranService.translate("datapool.message.dataError")}}
            </div>
            <div class="text-red-500" *ngIf="isDefineValueError">{{tranService.translate("datapool.message.exceededData")}}</div>
            <div class="m-auto">
                <button class="m-auto mr-2" [disabled]="((trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase()) && shareDataForm.controls['definedData'].value < 10) || isValidDataFixed ||(trafficType == 'Gói Data' && shareDataForm.controls['definedData'].value < 100) || isDefineValueError || shareDataForm.controls['definedData'].value=='' || shareDataForm.controls['definedData'].value == null || shareDataForm.controls['definedData'].value == undefined || isValidDataFixed" pButton type="button" (click)="definedDataChange()">{{tranService.translate("global.button.confirm")}}</button>
                <button class="m-auto ml-2 p-button-outlined" type="button" pButton (click)="isShowDefined = false">{{tranService.translate("global.button.cancel")}}</button>
            </div>
        </div>
    </p-dialog>

    <p-dialog [header]="tranService.translate('datapool.label.otpCode')" [(visible)]="isSubmit" [modal]="true" [style]="{ width: '30vw' }" [draggable]="false" [resizable]="false">
        <div class="flex flex-column gap-2 flex-1">
            <p-inputOtp formControlName="otp" class="mx-auto my-3" [integerOnly]="true" length="6"></p-inputOtp>
            <button
                type="button"
                class="border-none mb-4 cursor-pointer flex flex-row justify-content-center font-semibold"
                style="background-color: transparent;" [disabled]="countdown>0"
                (click)="resetTimer()"
            >{{tranService.translate("datapool.message.resendOtp")}}&nbsp;<div *ngIf="countdown>0"> {{tranService.translate("datapool.message.in")}} {{countdown}} {{tranService.translate("datapool.message.sec")}} </div>
            </button>
            <div class="m-auto">
                <button [disabled]="shareDataForm.invalid" class="m-auto mr-2" pButton>{{tranService.translate("global.button.save")}}</button>
                <button class="m-auto ml-2 p-button-outlined" pButton (click)="isSubmit = false">{{tranService.translate("global.button.cancel")}}</button>
            </div>
        </div>
    </p-dialog>

    <p-dialog class="dataError" [header]="tranService.translate('datapool.label.listShareError')" [(visible)]="isError" [modal]="true" [style]="{ width: '60vw' }" [draggable]="false" [resizable]="false" (onHide)="onHideError()">
        <p-button styleClass="mr-2 p-button-outlined" style="position: absolute;top: 30px;right: 45px;z-index: 5;font-size: 10px"
                  tooltipPosition="right"
                  [pTooltip]="tranService.translate('datapool.label.downloadErrorFile')"
                  icon="pi pi-download" (onClick)="downloadErrorFile()"></p-button>
        <table-vnpt
            [tableId]="'tbSubShareByGroup'"
            [fieldId]="'id'"
            [columns]="columnsError"
            [dataSet]="dataSetError"
            [options]="optionTableError"
            [pageNumber]="pageNumberError"
            [pageSize]="pageSizeError"
            [sort]="sortError"
            [params]="searchInfoError"
            [loadData]="pagingDataError.bind(this)"
            [labelTable]=""
        ></table-vnpt>
    </p-dialog>
</form>
