import {ChangeDetectorRef, Component, Inject, Injector, OnInit, ViewChild} from "@angular/core";
import {MenuItem} from "primeng/api";
import {ComponentBase} from "src/app/component.base";
import {ComboLazyControl} from "../common-module/combobox-lazyload/combobox.lazyload";
import {FormBuilder} from "@angular/forms";
import {DiagnoseService} from "../../service/diagnose/DiagnoseService";
import {ColumnInfo, OptionTable, TableVnptComponent} from "../common-module/table/table.component";
import {CONSTANTS} from "../../service/comon/constants";
import {InputFileVnptComponent} from "../common-module/input-file/input.file.component";

@Component({
    selector: "diagnose",
    templateUrl: "./app.diagnose.component.html",
    styleUrls: ['./app.diagnose.component.css']
})
export class DiagnoseComponent extends ComponentBase implements OnInit {
    constructor(@Inject(DiagnoseService) private diagnoseService: DiagnoseService,
                private formBuilder: FormBuilder,
                private cdr: ChangeDetectorRef,
                injector: Injector) {
        super(injector);
    }

    items: MenuItem[];
    home: MenuItem
    searchInfo: {
        msisdn: string | null
        dateFrom: Date | null,
        dateTo: Date | null,
    };
    controlComboSelect = new ComboLazyControl();
    minDateFrom: Date | number | string | null = null;
    maxDateFrom: Date | number | string | null = null;
    minDateTo: Date | number | string | null = null;
    maxDateTo: Date | number | string | null = null;
    pageNumber: number;
    pageSize: number;
    sort: string;
    dataSet: {
        content: Array<any>,
        total: number,
    };
    paginatedData: {
        content: Array<any>
        total: number,
    }
    formSearchDiagnose: any
    columns: Array<ColumnInfo>;
    optionTable: OptionTable;
    detailLastActivity: {
        lastActivity: Date | string | null,
        membershipClass: String | null,
        celLName: string | null,
        ratingPlan: null,
    }
    typeNetwork: number;
    typeNetworkSelect: Array<any>;
    showAfterSearch: boolean
    ngOnInit() {
        let me = this;
        this.home = {icon: 'pi pi-home', routerLink: '/'};
        this.items = [{label: this.tranService.translate("diagnose.titlepage.seardDiagnose")},];
        this.pageNumber = 0;
        this.pageSize = 10;
        this.sort = "";
        this.dataSet = {
            content: [],
            total: 0
        }
        this.paginatedData = {
            content: [],
            total: 0
        }
        this.showAfterSearch = false;
        me.typeNetwork = CONSTANTS.DIAGNOSE.TYPE_NETWORK.LTE
        this.typeNetworkSelect = [
            {
                label: this.tranService.translate("diagnose.label.typeNetwork4G"),
                value: CONSTANTS.DIAGNOSE.TYPE_NETWORK.LTE
            },
            {
                label: this.tranService.translate("diagnose.label.typeNetwork3G"),
                value: CONSTANTS.DIAGNOSE.TYPE_NETWORK.UMTS
            },
        ]
        this.detailLastActivity = {
            lastActivity: null,
            membershipClass: null,
            celLName: null,
            ratingPlan: null,
        };
        this.searchInfo = {
            msisdn: null,
            dateFrom: null,
            dateTo: null,
        }
        this.columns = [
            {
                name: this.tranService.translate("diagnose.label.time"),
                key: "filename_date",
                size: "250px",
                align: "left",
                isShow: true,
                isSort: false,
                funcConvertText(value) {
                    if (value == null) return "";
                    return me.formatDate(value)
                },
            },
            {
                name: this.tranService.translate("diagnose.label.volumeDownlink"),
                key: this.typeNetwork == CONSTANTS.DIAGNOSE.TYPE_NETWORK.LTE ? "vol_uplink_4g": "vol_uplink_3g",
                size: "250px",
                align: "left",
                isShow: true,
                isSort: false,
                funcConvertText(value) {
                    return me.utilService.bytesToMegabytes(value);
                }
            },
            {
                name: this.tranService.translate("diagnose.label.volumeUplink"),
                key: this.typeNetwork == CONSTANTS.DIAGNOSE.TYPE_NETWORK.LTE ? "vol_downlink_4g" : "vol_downlink_3g",
                size: "250px",
                align: "left",
                isShow: true,
                isSort: false,
                funcConvertText(value) {
                    return me.utilService.bytesToMegabytes(value);
                }
            },
            {
                name: this.tranService.translate("diagnose.label.coverageLevel"),
                key: "ux_final",
                size: "250px",
                align: "left",
                isShow: true,
                isSort: false,
                funcConvertText(value){
                    if (!me.isVisibleCEI()) return "";
                    if (value == CONSTANTS.DIAGNOSE.COVERAGE.EXCELLENT) {
                        return me.tranService.translate("diagnose.label.coverageExcellent");
                    } else if (value == CONSTANTS.DIAGNOSE.COVERAGE.GOOD) {
                        return me.tranService.translate("diagnose.label.coverageGood");
                    } else if (value == CONSTANTS.DIAGNOSE.COVERAGE.AVERAGE) {
                        return me.tranService.translate("diagnose.label.coverageAverage");
                    } else if (value == CONSTANTS.DIAGNOSE.COVERAGE.POOR) {
                        return me.tranService.translate("diagnose.label.coveragePoor");
                    } else if (value == CONSTANTS.DIAGNOSE.COVERAGE.BAD) {
                        return me.tranService.translate("diagnose.label.coverageBad");
                    }
                    return "";
                },
                funcGetClassname(value) {
                    if (!me.isVisibleCEI()) return ['p-2', "inline-block", "border-round"];
                    if (value == CONSTANTS.DIAGNOSE.COVERAGE.AVERAGE) {
                        return ['p-2', 'text-yellow-800', "bg-yellow-100", "border-round", "inline-block"];
                    } else if (value == CONSTANTS.DIAGNOSE.COVERAGE.EXCELLENT) {
                        return ['p-2', 'text-indigo-600', "bg-indigo-100", "border-round", "inline-block"];
                    } else if (value == CONSTANTS.DIAGNOSE.COVERAGE.BAD) {
                        return ['p-2', 'text-red-700', "bg-red-100", "border-round", "inline-block"];
                    } else if (value == CONSTANTS.DIAGNOSE.COVERAGE.GOOD) {
                        return ['p-2', 'text-teal-800', "bg-teal-100", "border-round", "inline-block"];
                    } else if (value == CONSTANTS.DIAGNOSE.COVERAGE.POOR) {
                        return ['p-2', 'text-orange-700', "bg-orange-100", "border-round", "inline-block"];
                    }
                    return [];
                }
            },
        ]
        this.optionTable = {
            hasClearSelected: true,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
        }
        this.optionTable = {
            hasClearSelected: true,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
        }

        me.minDateFrom = new Date()
        me.minDateFrom.setDate(new Date().getDate() - 30);
        me.maxDateFrom = new Date()

        me.maxDateTo = new Date()

        me.searchInfo.dateFrom = new Date()
        me.searchInfo.dateFrom.setDate(new Date().getDate() - 3);

        me.searchInfo.dateTo = new Date();

        me.onChangeDateFrom(me.searchInfo.dateFrom)
        this.formSearchDiagnose = this.formBuilder.group(this.searchInfo);
    }

    onSubmitSearch() {
        let me = this;
        if (me.formSearchDiagnose.invalid || me.controlComboSelect.invalid) {
            Object.keys(this.formSearchDiagnose.controls).forEach(key => {
                this.formSearchDiagnose.get(key).markAsDirty();
            });
            me.controlComboSelect.dirty = true
            me.showAfterSearch = false;
            return;
        } else {
            me.showAfterSearch = true;
            me.pageNumber = 0;
            me.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo);
        }
    }

    search(page, limit, sort, params) {
        this.pageNumber = page;
        this.pageSize = limit;
        this.sort = sort;
        let me = this;
        let dataParams = {
            page,
            size: limit,
            sort
        }
        this.updateParams(dataParams);
        me.messageCommonService.onload();
        this.diagnoseService.search(dataParams, (response) => {
            if (response != undefined && response != null && response.data != undefined && response.data != null) {
                me.detailLastActivity = {
                    lastActivity: me.formatDate(response.data.last_active),
                    celLName: response.data.last_cell,
                    membershipClass: response.data.membership_class,
                    ratingPlan: response.data.ratingPlan
                }
                if (response.data.sub_detail != undefined && response.data.sub_detail != null && response.data.sub_detail.length > 0) {
                    me.dataSet = {
                        content: response.data.sub_detail,
                        total: response.data.totalElements,
                    }
                } else {
                    me.dataSet = {
                        content: [],
                        total: 0
                    }
                }
            } else {
                me.detailLastActivity = {
                    lastActivity: null,
                    celLName: null,
                    membershipClass: null,
                    ratingPlan: null
                }
                me.dataSet = {
                    content: [],
                    total: 0
                }
            }
        }, null, () => {
            me.messageCommonService.offload();
        })
    }

    updateParams(dataParams) {
        Object.keys(this.searchInfo).forEach(key => {
            if (this.searchInfo[key] != null) {
                if (key == "dateFrom") {
                    dataParams["dateFrom"] = this.searchInfo.dateFrom.getTime();
                } else if (key == "dateTo") {
                    dataParams["dateTo"] = this.searchInfo.dateTo.getTime();
                } else {
                    dataParams[key] = this.searchInfo[key];
                }
            }
        })
    }

    onChangeDateFrom(value) {
        if (value) {
            this.minDateTo = value;
        } else {
            let today = new Date();
            today.setDate(today.getDate() - 30)
            this.minDateTo = today
        }
    }

    onChangeDateTo(value) {
        if (value) {
            this.maxDateFrom = value;
        } else {
            this.maxDateFrom = new Date();
        }
    }

    InputMsisdn($event) {
        this.searchInfo.msisdn = $event
    }
    onChangeSelectOption(option) {
        let me = this;
        me.typeNetwork = option.value
        me.columns = [
            {
                name: this.tranService.translate("diagnose.label.time"),
                key: "filename_date",
                size: "250px",
                align: "left",
                isShow: true,
                isSort: false,
                funcConvertText(value) {
                    if (value == null) return "";
                    return me.formatDate(value)
                },
            },
            {
                name: this.tranService.translate("diagnose.label.volumeDownlink"),
                key: me.typeNetwork == CONSTANTS.DIAGNOSE.TYPE_NETWORK.LTE ? "vol_uplink_4g": "vol_uplink_3g",
                size: "250px",
                align: "left",
                isShow: true,
                isSort: false,
                funcConvertText(value) {
                    return me.utilService.bytesToMegabytes(value);
                }
            },
            {
                name: this.tranService.translate("diagnose.label.volumeUplink"),
                key: me.typeNetwork == CONSTANTS.DIAGNOSE.TYPE_NETWORK.LTE ? "vol_downlink_4g" : "vol_downlink_3g",
                size: "250px",
                align: "left",
                isShow: true,
                isSort: false,
                funcConvertText(value) {
                    return me.utilService.bytesToMegabytes(value);
                }
            },
            {
                name: this.tranService.translate("diagnose.label.coverageLevel"),
                key: "ux_final",
                size: "250px",
                align: "left",
                isShow: true,
                isSort: false,
                funcConvertText(value){
                    if (!me.isVisibleCEI()) return "";
                    if (value == CONSTANTS.DIAGNOSE.COVERAGE.EXCELLENT) {
                        return me.tranService.translate("diagnose.label.coverageExcellent");
                    } else if (value == CONSTANTS.DIAGNOSE.COVERAGE.GOOD) {
                        return me.tranService.translate("diagnose.label.coverageGood");
                    } else if (value == CONSTANTS.DIAGNOSE.COVERAGE.AVERAGE) {
                        return me.tranService.translate("diagnose.label.coverageAverage");
                    } else if (value == CONSTANTS.DIAGNOSE.COVERAGE.POOR) {
                        return me.tranService.translate("diagnose.label.coveragePoor");
                    } else if (value == CONSTANTS.DIAGNOSE.COVERAGE.BAD) {
                        return me.tranService.translate("diagnose.label.coverageBad");
                    }
                    return "";
                },
                funcGetClassname(value) {
                    if (!me.isVisibleCEI()) return ['p-2', "inline-block", "border-round"];
                    if (value == CONSTANTS.DIAGNOSE.COVERAGE.AVERAGE) {
                        return ['p-2', 'text-yellow-800', "bg-yellow-100", "border-round", "inline-block"];
                    } else if (value == CONSTANTS.DIAGNOSE.COVERAGE.EXCELLENT) {
                        return ['p-2', 'text-indigo-600', "bg-indigo-100", "border-round", "inline-block"];
                    } else if (value == CONSTANTS.DIAGNOSE.COVERAGE.BAD) {
                        return ['p-2', 'text-red-700', "bg-red-100", "border-round", "inline-block"];
                    } else if (value == CONSTANTS.DIAGNOSE.COVERAGE.GOOD) {
                        return ['p-2', 'text-teal-800', "bg-teal-100", "border-round", "inline-block"];
                    } else if (value == CONSTANTS.DIAGNOSE.COVERAGE.POOR) {
                        return ['p-2', 'text-orange-700', "bg-orange-100", "border-round", "inline-block"];
                    }
                    return [];
                }
            },
        ]
    }
    isVisibleCEI() {
        if (this.typeNetwork == undefined && this.typeNetwork == null) {
            return false
        } else if (this.typeNetwork == CONSTANTS.DIAGNOSE.TYPE_NETWORK.LTE) {
            return true
        }
        return false
    }

    formatDate(input: string): string {
        let formattedDate = '';

        // Kiểm tra nếu input có định dạng "YYYYMMDD"
        if (/^\d{8}$/.test(input)) {
            const year = input.substring(0, 4);
            const month = input.substring(4, 6);
            const day = input.substring(6, 8);
            formattedDate = `${day}/${month}/${year}`;
        }
        // Kiểm tra nếu input có định dạng "YYYY-MM-DD HH:MM:SS"
        else if (/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(input)) {
            const datePart = input.split(' ')[0];
            const timePart = input.split(' ')[1];

            const [year, month, day] = datePart.split('-');
            formattedDate = `${day}/${month}/${year} ${timePart}`;
        }

        return formattedDate;
    }
    // paginate() {
    //     let me = this
    //     const startIndex = (me.pageNumber) * me.pageSize;
    //     const endIndex = startIndex + me.pageSize;
    //     let data = this.dataSet.content.slice(startIndex, endIndex);
    //     me.paginatedData = {
    //         content: data,
    //         total: this.dataSet.total,
    //     };
    //
    // }

    protected readonly CONSTANTS = CONSTANTS;
}
