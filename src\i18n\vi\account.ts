export default {
    label: {
        username: "<PERSON><PERSON><PERSON> đăng nhập",
        fullname: "<PERSON><PERSON> và tên",
        userType: "<PERSON>ại tài khoản",
        email: "Email",
        provinceCode: "<PERSON><PERSON> tỉnh",
        time: "<PERSON>h<PERSON><PERSON> gian",
        status: "Tr<PERSON><PERSON> thái",
        phone: "<PERSON><PERSON> điện thoại",
        description: "<PERSON><PERSON> tả",
        manager: "<PERSON><PERSON>p quản lý",
        province: "Tỉnh/Thành phố",
        role: "Nhóm quyền",
        permission:{
            name: "<PERSON>ê<PERSON> quyền",
            object: "Đối tượng tác động"
        },
        customerName: "Tên khách hàng",
        oldPass : "Mật khẩu cũ",
        newPass : "Mật khẩu mới",
        confirmPass: "Nhắc lại mật khẩu",
        submitChangePass : "Đồng ý & Đăng nhập",
        linkExpired : "<PERSON>i<PERSON><PERSON> khôi phục mật khẩu này đã hết hạn",
        cmpForgotPass : "M2M SIM - Quên mật khẩu",
        deviceType: "<PERSON><PERSON><PERSON> thiết bị",
        os: "<PERSON><PERSON> điều hành",
        ip: "Địa chỉ IP",
        managerName : "GDV quản lý",
        customerAccount : "Tài khoản khách hàng cấp trên",
        generalInfo: "Thông tin chung",
        addCustomerAccount : "Thêm tài khoản khách hàng",
        showCustomerAccount : "Xem tài khoản khách hàng",
        notiChangePass: "Mật khẩu đã hết hạn sử dụng. Vui lòng đổi lại mật khẩu để sử dụng tiếp. Thời hạn hiệu lực của mật khẩu mới là 6 tháng tính từ ngày thay đổi mật khẩu gần nhất."
    },
    text: {
        detailaccount: "Chi tiết tài khoản",
        infoAccount: "Thông tin tài khoản",
        active: "Đang hoạt động",
        inactive: "Chưa hoạt động",
        account: "Tài khoản",
        titleChangeManageLevel: "Chuyển cấp quản lý",
        selectAccount: "Chọn tài khoản",
        inputUsername: "Nhập tên đăng nhập",
        inputFullname: "Nhập tên đầy đủ",
        inputEmail: "Nhập email",
        inputPhone: "Nhập số điện thoại",
        selectUserType: "Chọn loại tài khoản",
        selectRoles: "Chọn nhóm quyền",
        selectManager: "Chọn cấp quản lý",
        selectProvince: "Chọn tỉnh/thành phố",
        selectCustomers: "Chọn khách hàng",
        disagreePolicy: "Bạn chưa đồng ý với điều khoản chính sách này.",
        typeSelectAll: "Chuyển toàn bộ",
        typeSelectList: "Chuyển theo danh sách",
        selectGDV : "Chọn GDV",
        selectCustomerAccount : "Chọn tài khoản khách hàng cấp trên",
        addCustomer: "Thêm Khách hàng",
        addContract: "Thêm Mã hợp đồng",
        grantApi : "Cấp quyền API",
        module : "Module",
        gen : "Gen",
        working: "Hoạt động",
        notWorking : "Không hoạt động"
    },
    usertype: {
        admin: "Admin",
        customer: "Khách hàng",
        district: "Giao dịch viên",
        province: "Tỉnh/Thành phố",
        agency: "Đại lý"
    },
    userstatus: {
        active: "Đang hoạt động",
        inactive: "Không hoạt động"
    },
    button: {
        disagreePolicy: "Phản đối, hạn chế, rút lại đồng ý chính sách",
        viewPolicyProtectPersonalData: "Xem chính sách bảo vệ dữ liệu cá nhân"
    },
    message: {
        customerRequired: "Phải chọn ít nhất một khách hàng",
        managerRequired: '"GDV quản lý" không được để trống'
    }
}
