pipeline {
    agent {
        label 'CMP_BUILD_NODE'
    }

    environment {
        SERVICE_NAME = "cmp-web-app"
    }

    stages {
        stage('Build Code Artifact') {
            steps {
                withCredentials([
                    usernamePassword(credentialsId: 'STC_CMP_NEXUS_ACCESS',
                    usernameVariable: 'username',
                    passwordVariable: 'password')
                ]) {
                sh "npm install"
                sh 'npm run build -- --c development'
                }
            }
        }
        stage('Deploy to Nexus Dev/Staging') {
            when {
                expression { return (env.TAG_NAME==null) }
            }
            steps {                
                withCredentials([
                    usernamePassword(credentialsId: 'STC_CMP_NEXUS_ACCESS',
                    usernameVariable: 'username',
                    passwordVariable: 'password')
                ]) {

                sh "docker build -t rdrepo.vnpt-technology.vn:1112/${SERVICE_NAME}:local-${GIT_COMMIT} ."
                sh "docker login --username ${username} --password ${password} rdrepo.vnpt-technology.vn:1112"
                sh "docker push rdrepo.vnpt-technology.vn:1112/${SERVICE_NAME}:local-${GIT_COMMIT}"
                }
            }
        }

        stage('Deploy to Nexus Production') {
            when {
                expression { return (env.TAG_NAME!=null) }
            }
            steps {
                sh "docker build -t rdrepo.vnpt-technology.vn:1112/${SERVICE_NAME}:${TAG_NAME} ."
                sh "docker login --username ${username} --password ${password} rdrepo.vnpt-technology.vn:1112"
                sh "docker push rdrepo.vnpt-technology.vn:1112/${SERVICE_NAME}:${TAG_NAME}"
            }
        }
    }
}

def publishTarToNexus(String repositoryName, String version, String nexusCredentialId, String name) {
    NEXUS_VERSION = "nexus3"
    NEXUS_PROTOCOL = "http" 
    NEXUS_URL = "rdrepo.vnpt-technology.vn"
    
    echo "${version}"

    tarfile = "${name}-${version}.tar.gz"
    tar file: tarfile, overwrite: true, dir: 'dist/sakai-ng'
    echo "*** File: ${tarfile}, group: vn.vnpt.cmp, packaging: tar.gz, version ${version}";
    nexusArtifactUploader(
      nexusVersion: NEXUS_VERSION,
      protocol: NEXUS_PROTOCOL,
      nexusUrl: NEXUS_URL,
      groupId: "vn.vnpt.cmp",
      version: version,
      repository: repositoryName,
      credentialsId: nexusCredentialId,
      artifacts: [
        [artifactId: "${name}",
          classifier: '',
          file: "${tarfile}",
          type: 'tar.gz'
        ]
      ]
    );
}
