import {Component, ElementRef, Inject, Injector, OnInit, Renderer2} from "@angular/core";
import {MenuItem} from "primeng/api";
import {DeviceService} from "../../../service/device/DeviceService";
import {Form, FormBuilder, FormGroup} from "@angular/forms";
import {AutoCompleteCompleteEvent} from "primeng/autocomplete";
import {ComponentBase} from "../../../component.base";
import {CONSTANTS} from "../../../service/comon/constants";
import {DomSanitizer, SafeResourceUrl} from '@angular/platform-browser';

@Component({
    selector: "app-deivce-update",
    templateUrl: './app.device.edit.component.html',
})
export class AppDeviceUpdateComponent extends ComponentBase implements OnInit {
    safeUrl: SafeResourceUrl;
    items: MenuItem[];
    deviceInfo: {
        imei: string | null,
        location: string | null,
        msisdn: string | null,
        country: string | null,
        category: string | null,
        expiredDate: Date | string | null,
        deviceType: string | null,
        // note: string | null,
        iotLink: number | null,
    }
    findCellIDDto: any;
    deviceInfoOld: any;
    home: MenuItem;
    formEditDevice: FormGroup;
    //Danh sách thuê bao chưa gán thiết bị
    listSubscription: any[] | undefined;
    filteredSubscription: any[] | undefined;
    showValidationMsisdnError: boolean = false;
    notPermissionMisidn: boolean = false;
    msisdnEntered: boolean = true;
    msisdnPattern = /^84\d{9,10}$/;
    msisdnInputPattern = /^\d{1,12}$/;
    msisdn = this.route.snapshot.paramMap.get("msisdn");
    msi: number;
    debounceTimeout: any;
    isShowExistsImei: boolean = false;

    constructor(@Inject(DeviceService) private deviceService: DeviceService,
                private formBuilder: FormBuilder,
                injector: Injector,
                private sanitizer: DomSanitizer,
                private renderer: Renderer2,
                private el: ElementRef
    ) {
        super(injector);
    }

    ngOnInit(): void {
        if (!this.checkAuthen([CONSTANTS.PERMISSIONS.DEVICE.UPDATE])) {window.location.hash = "/access";}
        this.home = {icon: 'pi pi-home', routerLink: '/'};
        this.items = [{label: this.tranService.translate("global.menu.devicemgmt"), routerLink: '/devices'},
            {label: this.tranService.translate("global.menu.listdevice"), routerLink: "/devices"},
            {label: this.tranService.translate("global.menu.deviceupdate")}];
        this.deviceInfo = {
            imei: null,
            location: null,
            msisdn: null,
            country: null,
            category: null,
            expiredDate: null,
            deviceType: null,
            // note: null,
            iotLink: null,
        }
        this.formEditDevice = this.formBuilder.group(this.deviceInfo);
        this.findCellIDDto = null
        const url = `https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3723.6482875031816!2d105.79183499999999!3d21.046754399999994!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3135ab303bc5f991%3A0x938485f81ec15900!2zMTI0IEhvw6BuZyBRdeG7kWMgVmnhu4d0LCBD4buVIE5odeG6vywgQ-G6p3UgR2nhuqV5LCBIw6AgTuG7mWkgMTAwMDA!5e0!3m2!1svi!2s!4v1715067085700!5m2!1svi!2s`
        this.safeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(url);
        this.getDetailDevice();
    }

    goBack() {
        window.history.back();
    }

    getListSubscription() {
        let me = this;
        this.deviceService.getListSubscription(me.msi, (response) => {
            if (response == 0) {
                me.notPermissionMisidn = true;
            } else {
                me.notPermissionMisidn = false;
                me.listSubscription = response
                    .map(el => {
                        return {
                            msisdn: el,
                        }
                    });
            }
        });

    }

    update() {
        let me = this;
        let dataBody = {...this.deviceInfo, ...this.formEditDevice.value};
        dataBody.msisdn = parseInt(me.msisdn);
        dataBody.expiredDate = dataBody.expiredDate != null ? new Date(dataBody.expiredDate) : null;
        dataBody.iotLink === true ? dataBody.iotLink = 1 : dataBody.iotLink = 0;
        me.messageCommonService.onload();
        if (dataBody.msisdn != this.deviceInfo.msisdn) {
            me.deviceService.checkMsisdnAndDevice(parseInt(me.msisdn), (response) => {
                if (response.countSim <= 0) {
                    me.messageCommonService.error(me.tranService.translate("device.text.msisdnNotExists"))
                } else if (response.countDevice > 0) {
                    me.messageCommonService.error(me.tranService.translate("device.text.msisdnAssign"))
                } else {
                    me.deviceService.updateDevice(this.deviceInfo.msisdn, dataBody, (response) => {
                        me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"));
                        me.router.navigate(['/devices']);
                    }, null, () => {
                        me.messageCommonService.offload();
                    })
                }
            }, null, () => {
                me.messageCommonService.offload();
            })
        } else {
            me.deviceService.updateDevice(this.deviceInfo.msisdn, dataBody, (response) => {
                me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"));
                me.router.navigate(['/devices']);
            })
        }
    }

    filterMsisdn(event: AutoCompleteCompleteEvent) {
        let me = this;
        let filtered: any[] = [];
        let query = event.query;

        this.showValidationMsisdnError = !me.msisdnPattern.test(query);
        if (me.listSubscription) {
            for (let i = 0; i < (me.listSubscription as any[]).length; i++) {
                let subscription = (me.listSubscription as any[])[i];
                if (subscription.msisdn.toString().toLowerCase().indexOf(query.toLowerCase()) >= 0) {
                    filtered.push(subscription);
                }
            }
            me.filteredSubscription = filtered.slice(0, 100);
        } else if (me.msisdnInputPattern.test(query)) {
            me.deviceService.getListSubscription(me.msi, (response) => {
                if (response == 0) {
                    me.notPermissionMisidn = true;
                } else {
                    me.notPermissionMisidn = false;
                    me.listSubscription = response
                        .map(el => {
                            return {
                                msisdn: el,
                            }
                        });
                    for (let i = 0; i < (me.listSubscription as any[]).length; i++) {
                        let subscription = (me.listSubscription as any[])[i];
                        if (subscription.msisdn.toString().toLowerCase().indexOf(query.toLowerCase()) >= 0) {
                            filtered.push(subscription);
                        }
                    }
                    me.filteredSubscription = filtered.slice(0, 100);
                }
            });
        }
    }

    onSelect($event) {
        let me = this;
        //selected
        let msisdnSelected = $event.msisdn;
        if (msisdnSelected != undefined) {
            me.notPermissionMisidn = false;
            me.showValidationMsisdnError = false;
            me.msisdnEntered = true;
            me.msisdn = msisdnSelected;
        }
    }

    onInput($event) {
        let me = this;
        me.msisdnEntered = true;
        let msisdnInput = me.formEditDevice.controls['msisdn'].value;
        if (msisdnInput.length == 0) {
            me.msisdnEntered = false;
        }
        //input
        me.msi = Number(msisdnInput);
        // Hủy bỏ timeout hiện tại (nếu có)

        if (me.msisdnPattern.test(msisdnInput)) {
            me.getListSubscription();
            me.msisdn = msisdnInput;
            // console.log("inputok")
        } else {
            if (me.debounceTimeout) {
                clearTimeout(me.debounceTimeout);
            }
            me.showValidationMsisdnError = !me.msisdnPattern.test(msisdnInput);
            // Thiết lập một timeout mới để gọi API sau một khoảng thời gian nhất định (ví dụ: 500ms)
            me.debounceTimeout = setTimeout(() => {
                if (me.msisdnInputPattern.test(msisdnInput)) {
                    me.getListSubscription();
                }
            }, 500);
        }
    }

    getDetailDevice() {
        let me = this;
        this.deviceService.detailDevice(Number(this.msisdn), (response) => {
            me.deviceInfo = {
                ...response
            }
            me.deviceInfoOld = {...me.deviceInfo};
            if (response.expiredDate != null && response.expiredDate != "") {
                me.deviceInfo.expiredDate = new Date(response.expiredDate);
            } else {
                me.deviceInfo.expiredDate = null;
            }
            me.initForm();
        })
        // this.deviceService.findCellId(Number(this.msisdn), (response) => {
        //     if (response != null) {
        //         me.findCellIDDto = {
        //             lat: response.findCellIDDto.lat,
        //             lng: response.findCellIDDto.lon
        //         }
        //         me.deviceInfo.location = response.place.display_name
        //         const url = `https://www.google.com/maps?q=${this.findCellIDDto.lat},${this.findCellIDDto.lng}&output=embed`;
        //         this.safeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(url);
        //     } else {
        //         me.deviceInfo.location = " "
        //         const url = `https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d42135.60828498614!2d105.78743105312334!3d21.020807357074563!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3135ab9bd9861ca1%3A0xe7887f7b72ca17a9!2sHanoi%2C%20Vietnam!5e0!3m2!1sen!2s!4v1713255802111!5m2!1sen!2s`;
        //         this.safeUrl = this.sanitizer.bypassSecurityTrustResourceUrl(url);
        //     }
        // })

    }

    initForm() {
        let me = this;
        me.formEditDevice = me.formBuilder.group({
            imei: [me.deviceInfo.imei],
            location: [me.deviceInfo.location],
            msisdn: [{msisdn: me.deviceInfo.msisdn}],
            country: [me.deviceInfo.country],
            expiredDate: [me.utilService.convertDateToString(new Date(this.deviceInfo.expiredDate))],
            deviceType: [me.deviceInfo.deviceType],
            iotLink: [me.deviceInfo.iotLink],
        });
        me.formEditDevice.controls["msisdn"].disable();
        me.formEditDevice.controls["location"].disable();
        if (me.deviceInfo.iotLink === 1) {
            me.formEditDevice.get("iotLink").setValue(true);
        }
    }

    checkExistsImei() {
        let me = this;
        if ((this.deviceInfo.imei || "").trim() != "" && (this.deviceInfo.imei || "").trim() != (this.deviceInfoOld.imei || "").trim()) {
            this.debounceService.set("checkExistsImei", this.deviceService.checkExistsImeiDevice.bind(this.deviceService), this.deviceInfo.imei, (response) => {
                if (response >= 1) {
                    me.isShowExistsImei = true;
                } else {
                    me.isShowExistsImei = false;
                }
            })
        }
    }

    ngAfterViewInit() {
        this.setIframeHeight();
    }

    setIframeHeight() {
        const iframe = this.el.nativeElement.querySelector('iframe');
        const width = iframe.offsetWidth;
        this.renderer.setStyle(iframe, 'height', `${width}px`);
    }

    protected readonly CONSTANTS = CONSTANTS;
}
