import {Component, Inject, Injector, OnInit} from "@angular/core";
import {FormBuilder} from "@angular/forms";
import {MenuItem} from "primeng/api";
import {DomSanitizer} from "@angular/platform-browser";
import {CONSTANTS} from "src/app/service/comon/constants";
import {GroupSubWalletService} from "../../../../service/group-sub-wallet/GroupSubWalletService";
import {ComponentBase} from "../../../../component.base";
import {ColumnInfo, OptionTable} from "../../../common-module/table/table.component";
import {PhoneInfo, PhoneInGroup, ShareDetail} from "../../data-pool.type-data";
import {ShareManagementService} from "../../../../service/datapool/ShareManagementService";

@Component({
    selector: "app-group-sub",
    templateUrl: "./group-sub-wallet.list.component.html"
})
export class GroupSubWalletListComponent extends ComponentBase implements OnInit{
    constructor(
        @Inject(GroupSubWalletService) private groupSubWalletService: GroupSubWalletService,
        @Inject(ShareManagementService) private shareService: ShareManagementService,
        private sanitizer: DomSanitizer,
        private formBuilder: FormBuilder,
        injector: Injector) {
        super(injector);
    }

    items: MenuItem[];
    home: MenuItem;
    searchInfo: {
        groupCode?: string,
        groupName?: string,
        description?: string,
    };
    groupInfo: {
        groupCode: string | null,
        groupName: string | null,
        description: string | null,
        listSub: ShareDetail[];
    };
    msisdn: number;
    formDetailDevice: any;
    isShowPopupDetail: boolean = false;
    idGroup: number;
    columns: Array<ColumnInfo>;
    dataSet: {
        content: Array<any>,
        total: number
    };
    dataStore: Array<any>;
    selectItems: Array<{id:number,[key:string]:any}>;
    optionTable: OptionTable;
    pageNumber: number;
    pageSize: number;
    sort: string;
    isShowErrorUpload: boolean = false;
    fileObject: any;
    messageErrorUpload: string| null;
    phoneReceiptSelect: string = "";
    isClickAdd: boolean = true;
    phoneList : PhoneInGroup[] = [];
    isValidPhone: boolean = true;
    shareList: ShareDetail[];
    selectItemsSub: Array<{id:number,[key:string]:any}>;
    columnsSub: Array<ColumnInfo>;
    dataSetSub: {
        content: Array<any>,
        total: number
    };
    searchInfoSub: {
        value?: string,
    };
    pageNumberSub: number;
    pageSizeSub: number;
    sortSub: string;
    optionTableSub: OptionTable;
    valueSearch: string;
    ngOnInit(): void {
        let me = this;
        me.home = { icon: 'pi pi-home', routerLink: '/' };
        me.items = [{ label: me.tranService.translate("global.menu.trafficManagement") }, { label: me.tranService.translate("global.menu.listGroupSub") },];
        me.searchInfo = {};
        me.selectItemsSub = [];
        me.columnsSub = [
            {
                name: me.tranService.translate("datapool.label.phone"),
                key: "phoneReceipt",
                size: "25%",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: me.tranService.translate("datapool.label.fullName"),
                key: "name",
                size: "25%",
                align: "left",
                isShow: true,
                isSort: false
            },
            {
                name: me.tranService.translate("datapool.label.email"),
                key: "email",
                size: "25%",
                align: "left",
                isShow: true,
                isSort: false,
            },
        ]
        me.searchInfoSub = {
            value: ""
        };
        me.pageNumberSub = 0;
        me.pageSizeSub = 10;
        me.sortSub = "id,desc";
        me.dataSetSub = {
            content: [],
            total: 0
        };
        me.optionTableSub = {
            action: undefined,
            hasClearSelected: false,
            hasShowIndex: true,
            hasShowJumpPage: false,
            hasShowToggleColumn: false,
            paginator: true,
            hasShowChoose: false
        };
        me.columns = [
            {
                name: me.tranService.translate("datapool.label.groupCode"),
                key: "groupCode",
                size: "450px",
                align: "left",
                isShow: true,
                isSort: false,
                style:{
                    cursor: "pointer",
                    color: "var(--mainColorText)"
                },
                funcClick(id, item) {
                    if (me.checkAuthen([CONSTANTS.PERMISSIONS.SHARE_GROUP.VIEW_DETAIL])) {
                        me.isShowPopupDetail = true;
                        me.idGroup = id;
                        me.groupSubWalletService.getDetail(Number(id), (response)=>{
                            me.groupInfo = {
                                ...response
                            };
                            me.shareList = me.groupInfo.listSub;
                        }, null,()=>{
                            me.messageCommonService.offload();
                        });
                        me.searchSub(me.pageNumberSub, me.pageSizeSub, me.sortSub, me.searchInfoSub);
                    } else {
                        window.location.hash = "/access";
                    }
                },
            },
            {
                name: me.tranService.translate("datapool.label.groupName"),
                key: "groupName",
                size: "450px",
                align: "left",
                isShow: true,
                isSort: false,
                isShowTooltip: true,
                style: {
                    display: 'inline-block',
                    maxWidth: '350px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                }
            },
            {
                name: me.tranService.translate("datapool.label.description"),
                key: "description",
                size: "450px",
                align: "left",
                isShow: true,
                isSort: false,
                isShowTooltip: true,
                style: {
                    display: 'inline-block',
                    maxWidth: '350px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                }
            },
        ]
        me.selectItems = [];
        me.optionTable = {
            hasClearSelected: true,
            hasShowChoose: false,
            hasShowIndex: true,
            hasShowToggleColumn: false,
            action: [
                {
                    icon: "pi pi-pencil",
                    tooltip: me.tranService.translate("global.button.edit"),
                    func: function(id, item){
                        me.router.navigate([`/data-pool/group/edit/${item.id}`]);
                    },
                    funcAppear: function (id, item) {
                        return me.checkAuthen([CONSTANTS.PERMISSIONS.SHARE_GROUP.EDIT])
                    }
                },
                {
                    icon: "pi pi-trash",
                    tooltip: me.tranService.translate("global.button.delete"),
                    func: function(id, item){
                        me.messageCommonService.confirm(
                            me.tranService.translate("global.message.titleConfirmDeleteShareGroup"),
                            me.tranService.translate("global.message.confirmDeleteShareGroup"),
                            {
                                ok:() => {
                                    me.messageCommonService.onload();
                                    me.groupSubWalletService.delete(id, (response) => {
                                        if (response.errorCode === 200) me.messageCommonService.success(me.tranService.translate("global.message.deleteSuccess"));
                                        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);
                                    }, null, ()=>{
                                        me.messageCommonService.offload();
                                    })
                                },
                                cancel: ()=>{
                                    // me.messageCommonService.error(me.tranService.translate("global.message.deleteFail"));
                                }
                            }
                        )
                    },
                    funcAppear: function (id, item) {
                        return me.checkAuthen([CONSTANTS.PERMISSIONS.SHARE_GROUP.DELETE])
                    }
                }
            ]
        }
        me.pageNumber = 0;
        me.pageSize = 10;
        me.sort = "createdDate,desc";
        me.groupInfo = {
            groupCode: "",
            groupName: "",
            description: "",
            listSub: []
        };

        me.dataSet = {
            content: [
            ],
            total: 0
        }
        me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo);
    }

    onSubmitSearch(){
        let me = this;
        me.pageNumber = 0;
        me.search(0, this.pageSize, this.sort, this.searchInfo);
    }

    searchSub(page, limit, sort, params) {
        this.pageNumberSub = page;
        this.pageSizeSub = limit;
        this.sortSub = sort;
        let me = this;
        let dataParams = {
            ...params,
            page,
            size: limit,
            sort
        }
        me.messageCommonService.onload();
        this.groupSubWalletService.searchInGroup(Number(me.idGroup), dataParams, (response)=>{
            me.dataSetSub = {
                content: response.content,
                total: response.totalElements
            }
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }

    search(page, limit, sort, params){
        this.pageNumber = page;
        this.pageSize = limit;
        this.sort = sort;
        let me = this;
        let dataParams = {
            ...params,
            page,
            size: limit,
            sort
        }
        me.messageCommonService.onload();
        this.groupSubWalletService.search(dataParams, (response)=>{
            me.dataSet = {
                content: response.content,
                total: response.totalElements
            }
        }, null, ()=>{
            me.messageCommonService.offload();
        })
    }

    createGroupSub() {
        this.router.navigate(['/data-pool/group/create']);
    }

    updateGroupSub() {
        this.router.navigate([`/group-sub/edit/${this.msisdn}`]);
    };

    checkValidAdd(){
        this.isClickAdd = true
        if (!this.phoneList.find(dta => dta.phoneReceipt.toString() === this.phoneReceiptSelect)) {
            this.isClickAdd = false
        } else {
            this.isClickAdd = true
        }
        if(!this.phoneReceiptSelect){
            this.isClickAdd = true
        }

        const regex = /^0[0-9]{9,10}$/;
        const inputValue = this.phoneReceiptSelect;
        this.isValidPhone = regex.test(inputValue);
    }

    loadSimNotInGroup(data, callback){
        this.messageCommonService.onload()
        this.groupSubWalletService.searchNotInGroup(data, (response) => {
            this.phoneList = [...response?.content];
            let data = {
                content: this.phoneList,
                totalPages: 1
            }
            callback(data);
        },null,()=>{
            this.messageCommonService.offload()
        });
    }

    changeDataName(event, i){
        const shareValue = event.target.value
        this.shareList[i].name = shareValue
    }

    changeDataMail(event, i){
        const shareValue = event.target.value
        this.shareList[i].email = shareValue
    }

    deleteItem(i, idSub){
        this.messageCommonService.confirm(this.tranService.translate("datapool.button.delete"),
            this.tranService.translate("groupSim.label.deleteTextSim"),{
                ok: () => {
                    const data = this.shareList[i].data

                    this.messageCommonService.onload();
                    if (idSub) {
                        this.groupSubWalletService.deleteSubInGroup(idSub, (response) => {
                            this.messageCommonService.success(this.tranService.translate("global.message.deleteSuccess"));
                        }, null, ()=>{
                            this.messageCommonService.offload();
                        })
                    }
                    if(data){
                        this.shareList[i].data = null
                    }
                    this.shareList = this.shareList.filter((item,index) => index != i);
                }
            })
    }

    isMailInvalid(email:string){
        if (!email){
            return false
        }
        const pattern:RegExp = /^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$/
        return !pattern.test(email);
    }
    onQuickSearch() {
        event.preventDefault();
        let me = this;
        if (me.valueSearch || me.valueSearch === "") {
            me.searchInfoSub = {
                value: me.valueSearch.trim()
            }
        }
        console.log(me.searchInfoSub)
        me.searchSub(me.pageNumber, me.pageSize, me.sort, me.searchInfoSub);
    }
    protected readonly CONSTANTS = CONSTANTS;

}
