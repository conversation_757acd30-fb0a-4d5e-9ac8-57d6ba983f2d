import { NgModule } from "@angular/core";
import { AccountService } from "src/app/service/account/AccountService";
import { AppProfileRoutingModule } from "./app.profile-routing";
import { CommonModule } from "@angular/common";
import { BreadcrumbModule } from "primeng/breadcrumb";
import { FieldsetModule } from "primeng/fieldset";
import { FormsModule, ReactiveFormsModule } from "@angular/forms";
import { InputTextModule } from "primeng/inputtext";
import { ButtonModule } from "primeng/button";
import { CommonVnptModule } from "../common-module/common.module";
import { SplitButtonModule } from "primeng/splitbutton";
import { AutoCompleteModule } from "primeng/autocomplete";
import { CalendarModule } from "primeng/calendar";
import { DropdownModule } from "primeng/dropdown";
import { CardModule } from "primeng/card";
import { DialogModule } from "primeng/dialog";
import { InputTextareaModule } from 'primeng/inputtextarea';
import { MultiSelectModule } from 'primeng/multiselect';
import { AppProfileDetailComponent } from "./detail/app.profile.detail.component";
import { AppProfileEditComponent } from "./edit/app.profile.edit.component";
import {AppProfileChangePasswordComponent} from "./change-password/app.profile.change-password.component";
import {TabViewModule} from "primeng/tabview";
import {CustomerService} from "../../service/customer/CustomerService";
import {ContractService} from "../../service/contract/ContractService";
import { RadioButtonModule } from 'primeng/radiobutton';

@NgModule({
    imports: [
        AppProfileRoutingModule,
        CommonModule,
        BreadcrumbModule,
        FieldsetModule,
        FormsModule,
        ReactiveFormsModule,
        InputTextModule,
        ButtonModule,
        CommonVnptModule,
        SplitButtonModule,
        AutoCompleteModule,
        CalendarModule,
        DropdownModule,
        CardModule,
        DialogModule,
        InputTextareaModule,
        MultiSelectModule,
        TabViewModule,
        RadioButtonModule
    ],
    declarations: [
        AppProfileDetailComponent,
        AppProfileEditComponent,
        AppProfileChangePasswordComponent
    ],
    providers: [
        AccountService,
        CustomerService,
        ContractService
    ]
})
export class AppProfileModule{}
