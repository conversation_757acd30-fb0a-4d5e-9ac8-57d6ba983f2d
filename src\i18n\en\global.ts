export default {
    text:{
        templateTextPagination: "Showing {first} to {last} of {totalRecords} entries",
        stt: "Index",
        page: "Page",
        action: "Action",
        nodata: "There are no items to display",
        itemselected: "Selected",
        filter: "Filter",
        advanceSearch: "Advance Search",
        createGroupSim: "Create Group Subcriber",
        createGroupSimAndPushSimToGroup: "Create Group Subcriber And Push Subscriber To Group",
        all: "All",
        resultRegister: "Result Register",
        inputText: "Input Text",
        inputNumber: "Input Number",
        selectValue: "Select Value",
        inputDate: "Pick Date",
        inputTimestamp: "Pick Time",
        homepage: "Homepage",
        selectOption: "Select Option",
        selectMoreItem: "+${maxDisplay} items selected",
        clearSelected:"Clear Selected",
        textCaptcha: "Slide to complete the puzzle",
        readandagree: "I have read and agree with",
        changeManageData: "Change Manage Data Customer For Teller",
    },
    field: {

    },
    lang: {
        vi: "Vietnamese",
        en: "English",
    },
    menu:{
        accountmgmt: "Account Management",
        listaccount: "Account List",
        listpermission: "Permissions List",
        billmgmt: "Billing Management",
        listbill: "Bill List",
        configuration: "Configuration",
        customermgmt: "Customer Management",
        listcustomer: "Customer List",
        dashboard: "Dashboard",
        devicemgmt: "Device Management",
        listdevice: "Device List",
        extraservice: "Extra Services",
        guide: "Guide",
        manual: "Manual",
        log: "Logs",
        ordermgmt: "Order Management",
        listorder: "Order List",
        report: "Report",
        dynamicreport: "Report Dynamic Config",
        dynamicreportgroup: "Group Dynamic Report",
        simmgmt: "Subcriber Management",
        listsim: "Subcriber List",
        subscriptionmgmt: "Subscription Management",
        listsubscription: "Subscription List",
        troubleshoot: "Troubleshoot",
        listroles: "Role List",
        listpermissions: "Permission List",
        detailroles: "Detail Role",
        editroles: "Edit Roles",
        groupSim: "Group Subcriber",
        contract:"Contract List",
        ratingplanmgmt: "Subscription Management",
        listplan: "Plan List",
        registerplan: "Register Plan",
        detailplan: "Detail Plan",
        historyRegister: "History",
        apnsim: "APN Subcriber",
        apnsimlist: "APN Subcriber List",
        apnsimdetail: "Detail APN Subcriber",
        account : "Account",
        detailAccount : "Detail",
        editAccount : "Edit",
        alerts: "Alert",
        rule: "Alert management",
        alertreceivinggroup: "Alert receiving group",
        alerthistory: "Alert history",
        devicedetail: "Device detail",
        deviceupdate: "Device update",
        devicecreate: "Device create",
        changePass: "Change password",
        alert: "Alerts",
        alertSettings: "Set up rules",
        alertReceivingGroup: "Alert receiving group",
        alertHistory: "Alert history",
        alertList: "Rule List",
        groupReceiving: "Receiving Group Alert",
        groupReceivingList: "Alert Receiving Group List",
        reportGroupReceivingList: "Dynamic Report Group List",
        termpolicy: "Terms and policies",
        termpolicyhistory: "Policies confirm history",
        cmpManagement: "M2M subscription management system",
        charts: "Config Chart",
        chartList: "List Config Chart",
        trafficManagement: "Share Traffic Management",
        subTrafficManagement: "Wallet Traffic Management",
        walletList: "Wallet List",
        shareManagement:"Share Management",
        shareList:"Share List",
        walletConfig: "Wallet Config",
        historyWallet: "History Wallet",
        listGroupSub: "Group List Share",
        autoShareGroup: "Auto Share Group",
        apiLogs: "Api Usage Log",
        userGuide: "User Guide",
        integrationGuide: "System Integration Guide",
    },
    button: {
        export: "Export",
        exportSelect: "CSV export of selected items",
        exportFilter: "CSV export of entire list",
        pushGroupSim: "Push Subcriber To Group",
        exportExelSelect: "Excel export of selected items",
        exportExelFilter: "Excel export the entire list",
        pushToGroupAvailable: "Push Group Available",
        pushToNewGroup: "Push Group New Group",
        cancel: "Cancel",
        registerRatingPlan: "Register Plan",
        changeRatingPlan: "Change Plan",
        cancelRatingPlan: "Cancel Plan",
        assignPlan: "Assign Plan",
        historyRegisterPlan: "History Register Plan",
        registerPlanForGroup: "Register Plan For Subcriber Group",
        registerPlanByFile: "Register Plan By File Importing",
        create: "Create",
        edit: "Edit",
        yes: "Yes",
        agree: "Agree",
        no: "No",
        save: "Save",
        changeStatus: "Change Status",
        delete: "Delete",
        active: "Active",
        approve: "Approve",
        suspend: "Suspend",
        uploadFile: "Drap/Drop file or Click to choose file",
        upFile: "Upload File",
        downloadTemp: "Download Template",
        back: "Back",
        add: "Create New",
        add2: "Add",
        addDefault: "Add Default",
        view: "View Detail",
        import: "Import",
        changePass : "Change Password",
        update: "Update",
        copy: "Copy",
        reset: "Reset",
        clear: "Remove All",
        preview: "Preview",
        pushUp: "Push Up",
        pushDown: "Push Down",
        confirm: "Confirm",
        changeManageData: "Change Manage Data",
        addSubToGroup: "Add each subscription",
        deleteSubInGroup: "Delete subscription"
    },
    message:{
        copied: "Copied",
        required: "This field is required",
        requiredField: "${field} is required",
        maxLength: "This field should have ${len} characters or fewer",
        minLength: "This field should have ${len} characters or more",
        max: "This field has a maximum value of ${value}",
        min: "This field has a minimum value of ${value}",
        numbericMin: "This field should have ${length} numeric characters or more",
        numbericMax: "This field should have ${length} numeric characters or fewer",
        duplicated: "Data is duplicated",
        invalidValue: "Value is invalid",
        formatContainVN: "Wrong Format. Only Accept (a-z, A-Z, 0-9, . -_, space, Vietnamese)",
        formatCode: "Wrong Format. Only Accept (a-z, A-Z, 0-9, - _)",
        formatCodeNotSub: "Wrong Format. Only Accept (a-z, A-Z, 0-9, _)",
        invalidEmail: "Email is invalid format",
        formatEmail: "Email is invalid format. <NAME_EMAIL>",
        invalidPhone: "Phone is invalid format",
        formatPhone: "Phone number must be a number starting with 0 (10-11 characters) or 84 (11-12 characters)",
        invalidSubsciption: "Subcriber is invalid format",
        exists: "Existed ${type}",
        success: "Action successfully",
        error: "Action failed",
        saveSuccess: "Save successfully",
        addGroupSuccess: "Add Subcriber to group successfully",
        saveError: "Save failed",
        timeout: "Timeout Expired",
        errorMatchCaptcha: "Position the piece in its slot",
        confirmDeleteAccount: "Are you sure you want to delete this account?",
        titleConfirmDeleteAccount: "Delete Account",
        confirmDeletePlan: "Are you sure you want to delete this plan?",
        titleConfirmDeletePlan: "Delete Plan",
        deleteSuccess: "Delete successfully",
        deleteFail: "Delete failure",
        confirmChangeStatusAccount: "Are you sure you want to change status for this account?",
        confirmChangeStatusAlert: "Are you sure you want to change status for this alert?",
        titleConfirmChangeStatusAccount: "Change Status Account",
        titleConfirmChangeStatusAlert: "Change Status Alert",
        changeStatusSuccess: "Change status successfully",
        changeStatusFail: "Change status failure",
        titleConfirmDeleteRoles: "Delete Roles",
        titleConfirmDeleteAlert: "Delete Rule",
        confirmDeleteRoles: "Are you sure you want to delete this role?",
        confirmDeleteAlert: "Are you sure you want to delete this rule?",
        titleConfirmChangeStatusRole: "Change Status Role",
        confirmChangeStatusRole: "Are you sure you want to change status for this role?",
        conditionExportChoose: "Limit 1 milion rows",
        conditionExportFilter: "The maximum number of subcribers cannot exceed 1 million",
        conditionExportExelFilter: "The export file list exceeds 100 thousand records",
        conditionExportFilterEmpty: "No subscribers have been selected yet",
        titleConfirmActivePlan: "Are you sure you want to activate this plan?",
        confirmActivePlan: "Active rating plan",
        titleConfirmApprovePlan: "Are you sure you want to approve this plan?",
        confirmApprovePlan: "Approve rating plan",
        titleConfirmSuspendPlan: "Are you sure you want to suspend this plan?",
        confirmSuspendPlan: "Suspend rating plan",
        titleConfirmDeleteDevice: "Delete device",
        confirmDeleteDevice: "Are you sure you want to delete this device?",
        activeSuccess:  "Activate successfully",
        approveSuccess: "Approve successfully",
        suspendSuccess: "Suspend successfully",
        activeError:  "Activate unsuccessfully",
        approveError: "Approve unsuccessfully",
        maxsizeupload: "Max size uploaded",
        invalidtypeupload: "Invalid type uploaded",
        maxSizeRecordRow: "The number of records exceeds the limit of ${row} items",
        wrongFileExcel: "Wrong file format, please import excel file",
        invalidFile: "Invalid File Format",
        planNotExists: "Plan does not exist",
        planNoPermit: "No permission with this plan",
        titleConfirmDeleteAlertReceivingGroup: "Delete alert receiving group",
        titleConfirmDeleteReportReceivingGroup: "Delete report receiving group",
        titleConfirmDeleteShareGroup: "Delete sharing group",
        confirmDeleteAlertReceivingGroup: "Are you sure you want to delete this alert receiving group?",
        confirmDeleteReportReceivingGroup: "Are you sure you want to delete this report receiving group?",
        confirmDeleteShareGroup: "Are you sure you want to delete this sharing group?",
        invalidinformation64: "Invalid information. Please enter between 2 and 64 characters excluding special characters",
        invalidinformation32: "Invalid information. Please enter between 2 and 32 characters excluding special characters",
        invalidPasswordFomat : "Password must be from 6 to 20 characters, include at least 1 letter, 1 number and 1 special character",
        passwordNotMatch: "Password does not match",
        wrongCurrentPassword : "Wrong current password",
        forgotPassSendMailSuccess : "A password recovery has been sent to your email. Please check it.",
        notPermissionMisidn: "The subscription number has been assigned to another device or There are no permissions on the subscription, please re-enter",
        titleConfirmDeleteReport: "Delete report",
        confirmDeleteReport: "Are you sure you want to delete this report?",
        titleConfirmChangeStatusReport: "Change status report",
        confirmChangeStatusReport: "Are you sure you want change status this report?",
        confirmCancelPlan: "Are you sure you want cancel plan \"${planName}\" for subscriber ${msisdn}?",
        accuracySuccess:"Wallet authentication successful",
        accuracyFail: "Wallet authentication failed",
        twentydigitlength:"This field should have 10 numeric characters or fewer",
        oneHundredLength : "This field cannot exceed the value 100",
        onlySelectGroupOrSub : "Only a maximum of 50 emails are allowed",
        max50Emails: "Only a maximum of 50 emails are allowed",
        max50Sms : "Only a maximum of 50 phone numbers are allowed",
        emailExist : "Email already exists",
        phoneExist : "Phone number already exists",
        urlNotValid : "The url is not in the correct format",
        onlyPositiveInteger : "Only positive integers are allowed",
        titleRejectPolicy: "ACKNOWLEDGMENT OF OBJECTION - RESTRICTION - WITHDRAWAL OF CONSENT TO PROCESSING OF PERSONAL DATA",
        messageRejectPolicy1: "Dear Customer,",
        messageRejectPolicy2: "The Customer has the right to object, restrict or withdraw consent to the processing of the Customer's Personal Data. However, objecting, restricting or withdrawing consent to process Customer's Personal Data may result in VNPT/VNPT Subsidiaries being unable to provide Products and services to Customers, which is This means that VNPT/VNPT's Subsidiary can unilaterally terminate the contract without having to compensate the Customer because the conditions for performing the contract have changed. Therefore, VNPT/VNPT Subsidiary recommends that Customers consider carefully before objecting, restricting or withdrawing consent to process Customer's Personal Data.",
        messageRejectPolicy3: "I have read and agree to the Objection, restriction, withdrawal of consent to processing of personal data",
        confirmationHistory: "Confirmation history of Personal Data Protection Policy",
        confirmationUserInfo: "Confirmation account information",
        confirmationDevice: "Confirmation device information",
        wrongFormatName: "Wrong format. Only spaces and Vietnamese letters (a-z, A-Z, 0-9, - _) are allowed",
        notChartData: "No data",
        isErrorQuery: "Error Query",
        errorLoading: "There was an error displaying data, please try again"
    },
    searchSeperate:{
        button:{
            add:"Add Filter",
            reset:"Reset Filter"
        },
        placeholder:{
            dropdownFlter:"Choose Filter",
            input:"Search",
            dropdown:"Select value",
            calendar:"Choose date",
            rangeCalendar:"Choose range date"
        }
    },
    titlepage: {
        createAlertReceivingGroup: "Create Alert Receiving Group",
        listAlertReceivingGroup: "Alert receiving group",
        detailAlertReceivingGroup: "Alert receiving group details",
        editAlertReceivingGroup: "Alert receiving group edit",
        deleteAlertReceivingGroup: "Delete alert receiving group",
        listApnSim: "Subscriber APN",
        detailApnSim: "Subscriber APN details",
        listAlertHistory: "Alert history",
        listDevice: "Device",
        createDevice: "Create new Device",
        detailDevice: "Device details",
        editDevice: "Edit device",
        deleteDevice: "Delete device",
        createaccount: "Create account",
        editaccount: "Edit account",
        detailaccount: "Detail account",
        createRole: "Create role",
        detailCustomer: "Detail customer",
        editCustomer: "Edit customer",
        detailsim: "Detail SIM",
        listGroupSim: "Group SIM List",
        createGroupSim: "Create group SIM",
        detailGroupSim: "Detail group SIM",
        editGroupSim: "Edit group SIM",
        listContract: "Contract List",
        createRatingPlan: "Create rating plan",
        editRatingPlan: "Edit rating plan",
        historyRegisterPlan: "Register plan history list",
        createAlarm: "Create alarm",
        detailAlarm: "Detail Alarm",
        editAlarm: "Edit alarm",
        reportDynamic: "Report Dynamic",
        listGroupReportDynamic: "Group report dynamic list",
        editGroupReportDynamic: "Edit group report dynamic",
        detailGroupReportDynamic: "Detail group report dynamic",
        createGroupReportDynamic: "Create group report dynamic",
        m2SubscriptionManagementSystem: "M2M subscription management system",
        apiLogs: "API Logs"
    }
}
