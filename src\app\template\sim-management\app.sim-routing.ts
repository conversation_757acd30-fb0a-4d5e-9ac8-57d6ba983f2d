import { RouterModule } from '@angular/router';
import { NgModule } from '@angular/core';
import { AppSimListComponent } from './list/app.sim.list.component';
import { AppSimCreateComponent } from './create/app.sim.create.component';
import { GroupSimComponent } from './group-sim/group-sim.list.component';
import { CreateGroupSimComponent } from './group-sim/create-group-sim/create-group-sim.component';
import { UpdateGroupSimComponent } from './group-sim/update-group-sim/update-group-sim.component';
import { ListGroupComponent } from './group-sim/detail-group/group.detail.component';
import { SimDetailComponent } from "./detail/app.sim.detail.component";
import { ContractManagementComponent } from './contract-management/contract-management.component';
import DataPage from 'src/app/service/data.page';
import { CONSTANTS } from 'src/app/service/comon/constants';
import {AppRechargeMoneyComponent} from "./recharge-money/app.sim.recharge-money-history.list.component";

@NgModule({
    imports: [
        RouterModule.forChild([
            { path: '', component: AppSimListComponent, data: new DataPage("global.menu.listsim", [CONSTANTS.PERMISSIONS.SIM.VIEW_LIST])},
            { path: 'create', component: AppSimCreateComponent , data: new DataPage()},
            { path: "detail/:id", component: SimDetailComponent, data: new DataPage("global.titlepage.detailsim", [CONSTANTS.PERMISSIONS.SIM.VIEW_DETAIL])},
            { path: 'group', component: GroupSimComponent, data: new DataPage("global.titlepage.listGroupSim",[CONSTANTS.PERMISSIONS.GROUP_SIM.VIEW_LIST]) },
            { path: 'group/create', component: CreateGroupSimComponent, data: new DataPage("global.titlepage.createGroupSim", [CONSTANTS.PERMISSIONS.GROUP_SIM.CREATE]) },
            { path: 'group/update/:idgroup', component: UpdateGroupSimComponent, data: new DataPage("global.titlepage.editGroupSim", [CONSTANTS.PERMISSIONS.GROUP_SIM.UPDATE]) },
            { path: 'group/detail/:idgroup', component: ListGroupComponent ,data: new DataPage("global.titlepage.detailGroupSim", ['getSimGroup'])},
            { path: 'contract', component: ContractManagementComponent, data: new DataPage("global.titlepage.listContract",[CONSTANTS.PERMISSIONS.CONTRACT.VIEW_LIST]) },
            { path: 'recharge-money', component: AppRechargeMoneyComponent, data: new DataPage()}
        ]),
    ],
    exports: [RouterModule],
})
export class AppSimRoutingModule {}
