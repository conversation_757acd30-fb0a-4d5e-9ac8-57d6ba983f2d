import {
    AfterViewInit,
    Component,
    EventEmitter,
    Inject,
    Injector,
    Input,
    OnInit,
    Output,
    TemplateRef,
    ViewChild
} from '@angular/core';
import {ComponentBase} from "../../../../../component.base";
import {TrafficWalletService} from "../../../../../service/datapool/TrafficWalletService";
import {
    SeperateFilterInfo,
    SeperateSearchInfo
} from "../../../../common-module/search-filter-separate/search-filter-separate.component";
import {ColumnInfo, OptionTable} from "../../../../common-module/table/table.component";
import {FormBuilder, Validators} from "@angular/forms";
import {CONSTANTS, isVinaphoneNumber} from "../../../../../service/comon/constants";
import {ShareManagementService} from "../../../../../service/datapool/ShareManagementService";
import {GroupSubWalletService} from "../../../../../service/group-sub-wallet/GroupSubWalletService";
import {Wallet} from "../../../data-pool.type-data";
import {convert84to0PhoneNumber, validate} from "../../../../common-module/utils/util";
import {CustomValidator} from "../../../../common-module/custom-validator/custom-validator.class";
import {
    ColumnInputInfo,
    OptionTableInput,
    TableInputControl
} from "../../../../common-module/table/table.input.component";
import {AutoShareService} from "../../../../../service/datapool/AutoShareService";
import {ComboLazyControl, VnptCombobox} from "../../../../common-module/combobox-lazyload/combobox.lazyload";
import * as XLSX from "xlsx-js-style";
import * as FileSaver from "file-saver";


@Component({
    selector: 'share-phone-list-tab',
    templateUrl: './share-phone-list-tab.component.html',
    styleUrls: ['./share-phone-list-tab.component.scss']
})
export class SharePhoneListTabComponent extends ComponentBase implements OnInit, AfterViewInit {
    @Output() sharePhoneListTemplateRefEmitter: EventEmitter<TemplateRef<any>> = new EventEmitter<TemplateRef<any>>();
    @ViewChild('sharePhoneListTemplateRef') sharePhoneListTemplateRef: TemplateRef<any>;
    searchList: Array<SeperateSearchInfo>;
    filterList: Array<SeperateFilterInfo>;
    dummyWallet: Wallet[];
    columns: Array<ColumnInfo>;
    columnTableInput: ColumnInputInfo[];
    dataSet: {
        content: Array<any>,
        total: number
    };
    selectItems: Array<any> = [];
    @Input() trafficType: any
    optionTable: OptionTable;
    optionTableInput: OptionTableInput;
    pageNumber: number = 0;
    pageSize: number = 10;
    sort: string;
    searchInfo: {
        name: string,
        phone: string,
        subCode: string,
        renewalStatus: number,
        startDate: number,
        endDate: number,
        autoGroupId: number,
        lstSubCode : Array<any>
    }
    phoneList: any
    isClickCreate: boolean = true;
    isValidPhone: boolean = true;
    typeDelete: number = 0 // 0: xóa 1 , 1: xóa nhiều
    isShowModalAddSharePhone: boolean = false
    phoneReceiptSelect: any = "";
    form: any
    formAddShare: any
    selectedWallet: any
    autoGroupId: any
    statusList = [
        {
            name: this.tranService.translate('datapool.renewStatus.notDueYet'),
            value: 0
        },
        {
            name: this.tranService.translate('datapool.renewStatus.dueDate'),
            value: 1
        },
        {
            name: this.tranService.translate('datapool.renewStatus.expired'),
            value: 2
        }
    ]
    isShowModalDeleteAutoShare: boolean = false
    shareList: Array<any>
    tableInputControl: TableInputControl;
    groupAutoShareInfo: any
    idDelete: any
    paramSearchPhone = {}
    walletCodeLst = []
    minDate!: Date;
    remainDataWallet : any
    isError = false
    dataSetError: {
        content: Array<any>,
        total: number
    } = {
        content: [],
        total: 0
    };
    FulldataSetError:any
    columnsError: Array<ColumnInfo> = [
        {
            name: this.tranService.translate("datapool.label.phone"),
            key: "phoneReceipt",
            size: "fit-content",
            align: "left",
            isShow: true,
            isSort: false,
        },
        {
            name: this.tranService.translate("datapool.label.description"),
            key: "description",
            size: "fit-content",
            align: "left",
            isShow: true,
            isSort: false
        }];
    optionTableError: OptionTable = {
        hasClearSelected: false,
        hasShowChoose: false,
        hasShowIndex: true,
        hasShowToggleColumn: false,
    };
    pageNumberError: number = 0;
    pageSizeError: number = 10;
    sortError: string = "";
    searchInfoError: {
        value?: string,
    } = {
        value: ""
    };
    constructor(
        @Inject(ShareManagementService) private shareService: ShareManagementService,
        @Inject(TrafficWalletService) private trafficWalletService: TrafficWalletService,
        @Inject(GroupSubWalletService) private groupSubService: GroupSubWalletService,
        @Inject(AutoShareService) private autoShareService: AutoShareService,
        @Inject(TrafficWalletService) private walletService: TrafficWalletService,
        public fb: FormBuilder,
        injector: Injector
    ) {
        super(injector);
        let me = this;
        this.autoGroupId = parseInt(this.route.snapshot.paramMap.get('id'));
        this.trafficWalletService.getPackageCbb({ packageName : "",
            type: "SUB_CODE_IN_GROUP",groupId: this.autoGroupId}, (response) => {
            this.messageCommonService.offload()
            me.walletCodeLst = response.map(r => {
                return {
                    name: r.name,
                    value: r.value
                }
            });
            me.walletCodeLst = me.walletCodeLst.filter((item, index, self) =>
                index === self.findIndex((t) => (
                    t.name === item.name && t.value === item.value
                ))
            );
        },null, ()=>{
            this.messageCommonService.offload()
        })
    }


    getTypeSharingDataColumn() {
        if (this.trafficType?.toUpperCase().includes('SMS')) {
            return "SMS"
        }
        if (this.trafficType?.toUpperCase().includes('DATA')) {
            return "MB"
        }
        return ""
    }

    get f1() {
        return this.formAddShare.controls;
    }

    deleteAutoShareMany() {
        let me = this;
        let listIdDelete = this.selectItems.map(e => e.autoId)
        me.messageCommonService.onload();
        this.autoShareService.deleteShareInfo({ids: listIdDelete}, (response) => {
            me.isShowModalDeleteAutoShare = false;
            me.messageCommonService.success(me.tranService.translate("global.message.deleteSuccess"));
            me.selectItems = []
            me.messageCommonService.offload()
            me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo)
        }, null, () => {
            me.messageCommonService.offload()
        })
    }

    deleteAutoShare() {
        let me = this;
        if (!this.idDelete) return;
        me.messageCommonService.onload();
        this.autoShareService.deleteShareInfo({ids: [this.idDelete]}, (response) => {
            me.isShowModalDeleteAutoShare = false;
            me.messageCommonService.success(me.tranService.translate("global.message.deleteSuccess"));
            me.messageCommonService.offload()
            me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo)
        }, null, () => {
            me.messageCommonService.offload()
        })
    }

    changeWalletCode(event) {
        if (this.getTypeOfWallet() == "SMS") {
            this.formAddShare.get('shareTraffic').setValidators([Validators.required,
                CustomValidator.isInvalidSmsUsage, Validators.max(30000)]);
            this.formAddShare.get('shareTraffic').updateValueAndValidity();
        }
        if (this.getTypeOfWallet() == "DATA") {
            this.formAddShare.get('shareTraffic').setValidators([Validators.required,
                CustomValidator.isInvalidDataUsage, Validators.max(3000000)]);
            this.formAddShare.get('shareTraffic').updateValueAndValidity();
        }
        this.selectedWallet = this.dummyWallet.find(el => el.subCode == event.value)
    }

    getTypeOfWallet() {
        let wallet = this.dummyWallet.find(el => el.subCode == this.f1.walletCode.value)
        if (wallet.trafficType.toUpperCase().includes("SMS")) {
            return "SMS";
        }
        if (wallet.trafficType.toUpperCase().includes("DATA")) {
            return "DATA";
        }
        return "";
    }


    onSubmitSearch() {
        this.searchInfo = this.form.value;
        this.pageNumber = 0;
        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo)
    }

    mapDataFormToSearchInfo() {
        this.searchInfo.name = this.form.get('fullName').value
        this.searchInfo.lstSubCode = this.form.get('lstSubCode').value
        this.searchInfo.phone = this.form.get('phoneNumber').value
        this.searchInfo.renewalStatus = this.form.get('status').value
        this.searchInfo.startDate = this.form.get('fromDate').value ? (this.form.get('fromDate').value as Date).getTime() : null
        this.searchInfo.endDate = this.form.get('toDate').value ? (this.form.get('toDate').value as Date).getTime() : null
    }

    getWalletCbb() {
        let me = this;
        this.messageCommonService.onload()
        let pageNumber = 0;
        let pageSize = 999999;
        // this.sort = sort;
        let dataParams = {
            pageNumber,
            size: pageSize,
            // sort
        }
        this.messageCommonService.onload()

        Object.keys(this.searchInfo).forEach(key => {
            dataParams[key] = this.searchInfo[key];
        })
        dataParams['autoGroupId'] = this.autoGroupId;
        this.trafficWalletService.searchWallet(dataParams, (response) => {

            me.dummyWallet = response.content.filter(el => el.canShare === true)
        }, null, () => {
            me.messageCommonService.offload();
        })
    }

    validateFormAddSharePhone(fieldName: string, error: string) {
        return validate(this.formAddShare, fieldName, error);
    }

    search(page, limit, sort, params) {
        this.mapDataFormToSearchInfo();
        let me = this;
        this.pageNumber = page;
        this.pageSize = limit;
        // this.sort = sort;
        let dataParams = {
            page,
            size: limit,
            // sort
        }
        this.messageCommonService.onload()
        Object.keys(this.searchInfo).forEach(key => {
            if (this.searchInfo[key] != null) {
                dataParams[key] = this.searchInfo[key];
            }
        })
        dataParams['autoGroupId'] = this.autoGroupId;
        this.autoShareService.searchShareInfo(dataParams, (response) => {
            me.dataSet = {
                content: response.content,
                total: response.totalElements
            }
        }, null, () => {
            me.messageCommonService.offload();
        })
    }

    ngOnInit(): void {
        const today = new Date();
        this.minDate = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);
        this.searchInfo = {
            name: null,
            phone: null,
            subCode: null,
            renewalStatus: null,
            startDate: null,
            endDate: null,
            autoGroupId: null,
            lstSubCode : []
        }
        this.tableInputControl = new TableInputControl();
        this.shareList = []
        this.phoneList = [];
        this.form = this.fb.group({
            phoneNumber: ['', [Validators.minLength(2), Validators.maxLength(255)]],
            fullName: ['', []],
            lstSubCode: [[], []],
            status: ['', []],
            fromDate: [],
            toDate: [],
        })
        this.formAddShare = this.fb.group({
            walletCode: ['', [Validators.required]],
            shareTraffic: ['', [Validators.required,  this.getTypeSharingDataColumn() == "MB"? Validators.max(3000000) : Validators.max(30000)]],
            typeShare : [0, [Validators.required]],
            fromDate :[null, [Validators.required]]
        })
        this.formAddShare.get('typeShare')?.valueChanges.subscribe((value) => {
            if (value === 0) {
                this.formAddShare.get('fromDate')?.disable();
            } else {
                this.formAddShare.get('fromDate')?.enable();
            }
        });
        this.optionTable = {
            hasClearSelected: false,
            hasShowChoose: true,
            hasShowIndex: true,
            hasShowToggleColumn: false,
            paginator: true,
            action: [
                {
                    icon: 'pi pi-fw pi-trash',
                    tooltip: this.tranService.translate('global.button.delete'),
                    func: (id) => {
                        this.openModelDelete(0);
                        this.idDelete = id; // delete one
                    },
                },
            ],
        }
        this.optionTableInput = {
            mode: CONSTANTS.MODE_VIEW.UPDATE
        }
        this.searchList = [{
            name: this.tranService.translate("datapool.label.walletCode"),
            key: "searchWalletCode"
        }, {
            name: this.tranService.translate("datapool.label.payCode"),
            key: "searchPayCode"
        }, {
            name: this.tranService.translate("datapool.label.phone"),
            key: "searchPhone"
        }];
        let me = this;
        this.dataSet = {
            content: [],
            total: 0
        }


        this.columns = [
            {
                name: this.tranService.translate("account.label.phone"),
                key: "phoneReceipt",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false
            },
            {
                name: this.tranService.translate("ratingPlan.label.fullName"),
                key: "name",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("ratingPlan.label.email"),
                key: "email",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false,
            },
            {
                name: this.tranService.translate("datapool.label.walletCode"),
                key: "subCode",
                size: "150px",
                align: "left",
                isShow: true,
                isSort: false
            },
            {
                name: this.tranService.translate('datapool.label.sharingData', {type: me.getTypeSharingDataColumn()}),
                key: "trafficShare",
                size: "250px",
                align: "left",
                isShow: true,
                isSort: false,
                style: {
                    textWrap: 'auto',
                },
                isEditable : true,
                editInputType : 'number',
                funcConvertText(value) {
                    return value + " " + me.getTypeSharingDataColumn();
                },
                validate: {
                    filterInput : me.allowOnlyNumbers,
                    required: true,
                    max : me.getTypeSharingDataColumn() === "MB"? 3000000 : 30000,
                    pattern: me.getTypeSharingDataColumn() === "MB"? /^([1-9][0-9]*00|100)$/ : /^(10|[1-9][0-9]*[05])$/,
                    messageErrorPattern: me.getTypeSharingDataColumn() === "MB"? me.tranService.translate("datapool.message.invalidDataUsage") : me.tranService.translate("datapool.message.invalidSmsUsage"),
                },
            },
            {
                name: this.tranService.translate("datapool.label.usedDate"),
                key: "expiryDate",
                size: "fit-content",
                align: "left",
                isShow: true,
                isSort: false,
                funcConvertText(value) {
                    return me.utilService.convertDateToString(new Date(value));
                }
            },
            {
                name: this.tranService.translate("datapool.label.renewalStatus"),
                key: "expiryDate",
                size: "fit-content",
                align: "left",
                isShow: true,
                isSort: false,
                funcGetClassname(value) {
                    if (me.getTypeFromDate(value) === 0) {
                        return ['p-2', 'text-teal-800', "bg-green-100", "border-round", "inline-block"]
                    } else if (me.getTypeFromDate(value) === 1) {
                        return ['p-2', 'text-green-800', "bg-yellow-100", "border-round", "inline-block"];
                    } else if (me.getTypeFromDate(value) === 2) {
                        return ['p-2', 'text-yellow-800', "bg-red-100", "border-round", "inline-block"];
                    }
                    return ""
                },
                funcConvertText(value) {
                    if (me.getTypeFromDate(value) === 0) {
                        return me.tranService.translate('datapool.renewStatus.notDueYet')
                    } else if (me.getTypeFromDate(value) === 1) {
                        return me.tranService.translate('datapool.renewStatus.dueDate')
                    } else if (me.getTypeFromDate(value) === 2) {
                        return me.tranService.translate('datapool.renewStatus.expired')
                    }
                    return ""
                }
            }
        ];

        this.columnTableInput = [
            {
                align: 'left',
                name: this.tranService.translate("customer.label.phoneNumber"),
                key: "phoneReceipt",
                size: "250px",
                type: CONSTANTS.PARAMETER_TYPE.STRING,
                allowUpdate: false
            },
            {
                align: 'left',
                name: this.tranService.translate("ratingPlan.label.fullName"),
                key: "name",
                size: "250px",
                type: CONSTANTS.PARAMETER_TYPE.STRING,
                allowUpdate: true,
                validate: {
                    required: true,
                    maxLength: 50
                }
            },
            {
                align: 'left',
                name: this.tranService.translate("ratingPlan.label.email"),
                key: "email",
                size: "250px",
                type: CONSTANTS.PARAMETER_TYPE.STRING,
                allowUpdate: true,
                validate: {
                    required: true,
                    maxLength: 100,
                    pattern: /^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/,
                    messageErrorPattern: this.tranService.translate("global.message.invalidEmail")
                }
            }
        ];
        this.search(this.pageNumber, this.pageSize, this.sort, this.searchInfo)
        this.getWalletCbb()
    }

    getTypeFromDate(value) {
        const expiryDate = new Date(value);
        const currentDate = new Date();
        const expiryDateOnly = new Date(expiryDate.getFullYear(), expiryDate.getMonth(), expiryDate.getDate());
        const currentDateOnly = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate());
        if (expiryDateOnly > currentDateOnly) {
            return 0;
        } else if (expiryDateOnly.getTime() === currentDateOnly.getTime()) {
            return 1;
        } else {
            return 2;
        }
    }

    openModelDelete(type) {
        this.typeDelete = type;
        this.isShowModalDeleteAutoShare = true;
    }

    deleteSharePhone() {
        if (this.typeDelete === 0) {
            this.deleteAutoShare()
        } else {
            this.deleteAutoShareMany()
        }
    }

    checkValidAdd() {
        this.isClickCreate = true
        if (!this.phoneList.find(dta => dta.phoneReceipt === this.phoneReceiptSelect)) {
            this.isClickCreate = false
        } else {
            this.isClickCreate = true
        }
        if (this.phoneReceiptSelect == "" || this.phoneReceiptSelect == null || this.phoneReceiptSelect === undefined) {
            this.isClickCreate = true
        }

        const regex = /^(\+84|84|0){1}(([0-9]{9})|([0-9]{10}))$/;
        const inputValue = this.phoneReceiptSelect;
        this.isValidPhone = regex.test(inputValue);
    }

    regexPhone = new RegExp('^(\\+84|84|0){1}(([0-9]{9})|([0-9]{10}))$');
    controlPhone = new ComboLazyControl();

    customFilter(value: string, filter: string): boolean {
        const normalize = (str: string) =>
            str.normalize("NFD").replace(/[\u0300-\u036f]/g, "").toLowerCase();

        return normalize(value).includes(normalize(filter));
    }

    async addPhoneTable(phoneInfo, phoneNumber) {
        let me = this;
        // check trung
        let dataParams = {
            page: 0,
            size: 1,
            autoGroupId: this.autoGroupId,
            phone: phoneNumber
        }
        me.resetSelectPhone()
        if (this.shareList.some(el => el.phoneReceipt == phoneNumber)) {
            this.messageCommonService.error(this.tranService.translate("datapool.message.existPhoneInTable", {phoneNumber :phoneNumber}), null, 10000)
            return;
        }else {
            this.messageCommonService.onload()
            await new Promise(function(resolve, reject) {
                me.autoShareService.searchShareInfo(dataParams, (response) => {
                    if (response.totalElements > 0) {
                        me.messageCommonService.warning(me.tranService.translate("datapool.message.existSharePhone",{phoneNumber :phoneNumber}), null, 10000)
                    }
                    me.messageCommonService.offload();
                    resolve("Success");
                }, null, () => {
                    me.messageCommonService.offload();
                })
            });
        }
        if (phoneInfo == null) {
            this.shareList.splice(0,0,{
                phoneReceipt: phoneNumber,
                name: null,
                email: null,
            })
        } else {
            this.shareList.splice(0,0, phoneInfo)
        }
    }

    convertDateToString(value: Date): string {
        if (value == null) return "";

        const pad = (num: number) => num.toString().padStart(2, '0');

        const day = pad(value.getDate());
        const month = pad(value.getMonth() + 1); // Tháng bắt đầu từ 0 nên cần cộng 1
        const year = value.getFullYear();

        const hours = pad(value.getHours());
        const minutes = pad(value.getMinutes());
        const seconds = pad(value.getSeconds());

        return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
    }

    async addSharePhone() {
        let errorCheck = await this.checkRemainTraffic()
        if(!errorCheck){
            this.messageCommonService.error(this.tranService.translate("datapool.message.exceedTrafficShare",
                {
                    remainDataWallet :this.formatNumber(this.remainDataWallet),
                    typeWallet : this.getTypeSharingDataColumn()
            }), null, 10000)
            return;
        }
        let me = this;
        const convertShareInfo = this.shareList.map((e) => ({
            phone: String(e?.phoneReceipt),
            trafficType: "DATA",
            numOfShare: Number(this.formAddShare.get("shareTraffic").value),
            expireDate: '30 ngày',
            name: e?.name,
            email: e?.email,
            type: 0,
            isAuto: true
        }));
        const dataBody = {
            subCode: this.selectedWallet.subCode,
            sharingDay: this.convertDateToString(new Date()),
            shareInfos: convertShareInfo,
            transId: '',
            fromAutoGroup: true
        };
        // body chia sẻ từ ngày
        const dataBodyFromDate = {
            subCode: this.selectedWallet.subCode,
            expiredDate: this.convertDateToString(this.formAddShare.get("fromDate").value),
            shareInfos: convertShareInfo,
            autoGroupId: this.autoGroupId,
            numOfShare: Number(this.formAddShare.get("shareTraffic").value),
        };
        me.messageCommonService.onload();
        // chia sẻ từ ngày == 1
        if(this.formAddShare.get("typeShare").value === 1) {
            this.shareService.shareTrafficFromDate(dataBodyFromDate, (res) => {
                me.messageCommonService.success(me.tranService.translate("datapool.message.addSuccess"));
                me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo)
                this.isShowModalAddSharePhone = false;
                this.formAddShare.reset()
            }, null, () => {
                me.messageCommonService.offload();
            });
        }else {// chia sẻ thường == 0
            this.shareService.shareTraffic(dataBody, (res) => {
                if (res.error_code === 'BSS-00000000') {
                    let ok = 1
                    let dataErrorTable = []
                    res.data.forEach(element => {
                        if(element.status == 0 && element.error != ""){
                            // me.messageCommonService.error(`Thuê bao ${element.sdt} có lỗi (${element.error})`);
                            dataErrorTable.push({
                                phoneReceipt: convert84to0PhoneNumber(element.sdt),
                                // description: element.error
                                description: "Lỗi do thuê bao không hợp lệ"
                            })
                            ok = 0
                        }
                    });
                    if(ok == 0){
                        this.isError = true
                        this.dataSetError = {
                            content: dataErrorTable,
                            total: dataErrorTable.length,
                        }
                        this.FulldataSetError = {...this.dataSetError}
                        me.messageCommonService.error(me.tranService.translate("datapool.message.shareNotifyFail", {success: this.shareList.length - this.dataSetError.content.length, total : this.shareList.length}), null, 10000);
                    }else {
                        me.messageCommonService.success(me.tranService.translate("datapool.message.shareNotifySuccess", {success: this.shareList.length, total : this.shareList.length}), null, 10000);
                    }
                    me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo)
                    this.isShowModalAddSharePhone = false;
                    this.formAddShare.reset()
                } else if (res.error_code === 'Success') {
                    me.messageCommonService.success(me.tranService.translate("datapool.message.shareNotifyBackground"),null, 15000);
                } else {
                    this.messageCommonService.error(res.message)
                }
            }, null, () => {
                me.messageCommonService.offload();
            });
        }
    }

    checkRemainTraffic() : Promise<any>{
        // return false là lỗi
        // cal api search theo mã ví để check lưu lượng còn lại
        return new Promise((resolve, reject)=> {
            let me = this;
            let dataParams = {
                "page": 0,
                "size": 10,
                "searchWalletCode": 1,
                "value": this.selectedWallet.subCode,
                "autoType": -1
            }
            this.messageCommonService.onload()
            this.trafficWalletService.searchWallet(dataParams, (response) => {
                this.messageCommonService.offload()
                let wallet = response.content[0];
                this.remainDataWallet = wallet.totalRemainingTraffic;
                if(this.shareList.length * Number(this.formAddShare.get("shareTraffic").value) > wallet.totalRemainingTraffic) {
                    resolve(false);
                }else {
                    resolve(true);
                }
            }, null, () => {
                this.messageCommonService.offload()
                reject(false);
            })
        })
    }

    checkShareTraffic(event: KeyboardEvent) {
        const input = event.target as HTMLInputElement;
        const currentValue = input.value;

        // Chặn ký tự không phải là số và không phải các phím điều khiển như Backspace, Delete
        const allowedKeys = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight'];
        if (!/^\d$/.test(event.key) && !allowedKeys.includes(event.key)) {
            event.preventDefault();
        }

        // Chặn nhập thêm nếu độ dài đã là 9 ký tự
        if (currentValue.length >= 9 && !allowedKeys.includes(event.key)) {
            event.preventDefault();
        }
    }


    addPhone(phoneNumber, onSelected?) {
        let me = this;
        // console.log(phoneNumber)
        if (phoneNumber === null || phoneNumber === undefined || phoneNumber.length === 0 || phoneNumber.trim().length == 0) {
            return
        }
        if(this.shareList.length >= 50) {
            me.messageCommonService.error(me.tranService.translate("datapool.message.maximumSubAdd"));
            return;
        }
        const phoneInfo = this.phoneList.find(dta => dta.phoneReceipt === phoneNumber)
        me.addPhoneTable(phoneInfo, phoneNumber)
        me.isClickCreate = true;
        /**
         * bỏ check số vina
         */
        // if (onSelected) {
        //     me.addPhoneTable(phoneInfo, phoneNumber)
        // } else {
        //     const phone = String(phoneNumber)?.replace(/^0/, "84");
        //     this.messageCommonService.onload()
        //     this.walletService.checkParticipant({phoneNumber: phone},
        //         (response) => {
        //             if (response.error_code === "0" && (response.result === "02" || response.result === "11")) {
        //                 me.addPhoneTable(phoneInfo, phoneNumber)
        //             } else if (response.error_code === "0" && response.result === "0") {
        //                 if (isVinaphoneNumber(phoneNumber)) {
        //                     me.addPhoneTable(phoneInfo, phoneNumber)
        //                 } else {
        //                     this.messageCommonService.error(this.tranService.translate("datapool.message.notValidPhone"))
        //                 }
        //             } else {
        //                 this.messageCommonService.error(this.tranService.translate("datapool.message.notValidPhone"))
        //             }
        //         },
        //         null, () => {
        //             this.messageCommonService.offload();
        //         })
        // }
    }
    resetSelectPhone() {
        this.controlPhone.reload();
        this.controlPhone.clearValue();
        this.controlPhone.clearFilter();
        this.controlPhone.reloadOption();
        this.phoneReceiptSelect = ""
    }

    getListShareInfoCbb(params, callback) {
        return this.shareService.getListShareInfoCbb(params, (response)=>{
            this.phoneList = response.content;
            callback(response)
        });
    }

    ngAfterViewInit() {
        this.sharePhoneListTemplateRefEmitter.emit(this.sharePhoneListTemplateRef)
    }

    changeDataName(event, i){
        const shareValue = event.target.value
        this.shareList[i].name = shareValue
    }

    changeDataMail(event, i){
        const shareValue = event.target.value
        this.shareList[i].email = shareValue
    }

    isMailInvalid(email:string){
        if (!email){
            return false
        }
        const pattern:RegExp = /^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$/
        return !pattern.test(email);
    }

    checkDataCondition(index: number): boolean {
        if (this.trafficType === 'Gói Data') {
            return this.shareList[index].data % 100 === 0;
        }
        if((this.trafficType || '').toUpperCase().includes('Gói SMS'.toUpperCase())){
            return this.shareList[index].data % 5 === 0;
        }
        return true;
    };

    onKeyDown(event, i){
        const key = event.key;
        const isNumber = /^[0-9]$/.test(key);

        if (key === "-"){
            return false;
        }
        return true;
    }

    deleteItem(i){
        this.messageCommonService.confirm(this.tranService.translate("datapool.button.delete"),
            this.tranService.translate("datapool.message.confirmDelete"),{
                ok: ()=>{
                    const a = this.shareList[i].data
                    if(a){
                        this.shareList[i].data = null
                        this.shareList[i].percent = null
                    }
                    this.shareList = this.shareList.filter((item,index) => index != i);
                }
            })
    }

    openNewTab() {
        // let userInfo = JSON.parse(localStorage.getItem("userInfo"));
        // let typeUser = 'cmp_admin'
        // if (userInfo.type == CONSTANTS.USER_TYPE.ADMIN) {
        //     typeUser = 'cmp_admin'
        // } else if (userInfo.type == CONSTANTS.USER_TYPE.PROVINCE) {
        //     typeUser = 'cmp_province'
        // } else if (userInfo.type == CONSTANTS.USER_TYPE.DISTRICT) {
        //     typeUser = 'cmp_teller'
        // } else if (userInfo.type == CONSTANTS.USER_TYPE.CUSTOMER) {
        //     typeUser = 'cmp_customer'
        // }
        // window.open(`/#/docs/${typeUser}/autoShare`, '_blank');
        window.open(`/#/docs/detailAutoShareGroup`, '_blank');
    }

    openModelAddShare() {
        this.isShowModalAddSharePhone = true;
        this.formAddShare.reset();
        this.formAddShare.get('typeShare').setValue(0);
        this.phoneReceiptSelect = null;
        this.shareList = []
        this.isValidPhone = true
        // this.controlPhone.reload()
    }

    handleBlurInput(event) {
        let me = this;
        me.messageCommonService.onload();
        this.autoShareService.updateAutoShareInfo({id: event.data.autoId, trafficShare : event.value}, (response) => {
            me.isShowModalDeleteAutoShare = false;
            me.messageCommonService.success(me.tranService.translate("global.message.saveSuccess"));
            me.messageCommonService.offload()
            me.search(me.pageNumber, me.pageSize, me.sort, me.searchInfo)
        }, null, () => {
            me.messageCommonService.offload()
        })
    }

    allowOnlyNumbers(event: KeyboardEvent): boolean {
        const key = event.key;

        // Kiểm tra ký tự: chỉ cho phép số 0-9
        if (/^\d$/.test(key)) {
            return true;
        }

        // Cho phép các phím đặc biệt như Backspace, ArrowLeft, ArrowRight
        const allowedKeys = ['Backspace', 'ArrowLeft', 'ArrowRight', 'Tab'];
        if (allowedKeys.includes(key)) {
            return true;
        }

        // Các phím khác sẽ bị chặn
        return false;
    }

    formatNumber(value: number): string {
        return new Intl.NumberFormat('vi-VN').format(value);
    }

    pagingDataError(pageNumber, pageSize){
        const startIndex = pageNumber * pageSize;
        const endIndex = startIndex + pageSize;
        this.dataSetError.content = this.FulldataSetError.content.slice(startIndex, endIndex);
        this.dataSetError = {...this.dataSetError}
    }

    downloadErrorFile() {
        this.exportToExcel(this.FulldataSetError.content);
    }

    exportToExcel(data) {
        // Chuẩn bị dữ liệu và tiêu đề cột
        const header = ['STT', 'SĐT', 'Mô tả'];
        const excelData = data.map((item, index) => [index+1, item.phoneReceipt, item.description]);

        // Tạo sheet và thêm tiêu đề
        const ws: XLSX.WorkSheet = XLSX.utils.aoa_to_sheet([header, ...excelData]);

        ws['!cols'] = [{wch: 5}, {wch :21}, {wch:30}]

        // Bôi đậm tiêu đề
        const headerCells = ['A1', 'B1', 'C1']; // Các ô tiêu đề trong sheet (tương ứng với cột A, B, C)
        headerCells.forEach(cell => {
            if (ws[cell]) {
                ws[cell].s = {
                    font: {
                        bold: true, // Bôi đậm chữ
                    },
                    alignment: {
                        horizontal: 'center', // Căn giữa theo chiều ngang
                        vertical: 'center', // Căn giữa theo chiều dọc
                    },
                };
            }
        });

        // Căn giữa cho các ô dữ liệu
        const rowCount = data.length;
        for (let row = 2; row <= rowCount + 1; row++) {
            for (let col = 0; col < header.length; col++) {
                const cellRef = XLSX.utils.encode_cell({ r: row - 1, c: col });
                if (ws[cellRef]) {
                    ws[cellRef].s = {
                        alignment: {
                            horizontal: 'center', // Căn giữa theo chiều ngang
                            vertical: 'center', // Căn giữa theo chiều dọc
                        },
                    };
                }
            }
        }

        // Tạo workbook và xuất file
        const wb: XLSX.WorkBook = {
            Sheets: { 'Danh sách SĐT chia sẻ lỗi': ws },
            SheetNames: ['Danh sách SĐT chia sẻ lỗi'],
        };

        const excelBuffer: any = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
        this.saveAsExcelFile(excelBuffer, 'Danh_sach_loi_');
    }

    private saveAsExcelFile(buffer: any, fileName: string): void {
        const data: Blob = new Blob([buffer], { type: EXCEL_TYPE });
        FileSaver.saveAs(data, fileName + '_' + formatDateToDDMMYYYYHHMMSS(new Date().getTime()) + EXCEL_EXTENSION);
    }
}

const EXCEL_TYPE = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
const EXCEL_EXTENSION = '.xlsx';

function formatDateToDDMMYYYYHHMMSS(timestamp: number): string {
    const date = new Date(timestamp);

    const dd = String(date.getDate()).padStart(2, '0');
    const MM = String(date.getMonth() + 1).padStart(2, '0'); // Tháng bắt đầu từ 0
    const yyyy = date.getFullYear();
    const HH = String(date.getHours()).padStart(2, '0');
    const mm = String(date.getMinutes()).padStart(2, '0');
    const ss = String(date.getSeconds()).padStart(2, '0');

    return `${dd}${MM}${yyyy}${HH}${mm}${ss}`;
}

